import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjMxMjg0MSwiZXhwIjoyMDYxODg4ODQxfQ.zwk3Wiay5jjeYOrg8B1M6T98B2esCqRFI43At-AFV3A';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkTableStructure() {
  console.log('🔍 Verificando estructura de tablas de pines...\n');

  // Verificar estructura de psychologist_usage_control
  console.log('📋 Estructura de psychologist_usage_control:');
  try {
    const { data, error } = await supabase
      .rpc('get_table_columns', { table_name: 'psychologist_usage_control' });

    if (error) {
      console.log('❌ Error obteniendo columnas:', error.message);
      
      // Método alternativo: insertar un registro de prueba para ver la estructura
      console.log('🔄 Intentando método alternativo...');
      const { error: insertError } = await supabase
        .from('psychologist_usage_control')
        .insert([{
          psychologist_id: '00000000-0000-0000-0000-000000000000',
          total_uses: 0,
          used_uses: 0,
          is_unlimited: false,
          is_active: false,
          reason: 'Test structure'
        }]);

      if (insertError) {
        console.log('Columnas detectadas por error de inserción:');
        console.log(insertError.message);
        
        // Eliminar el registro de prueba si se insertó
        await supabase
          .from('psychologist_usage_control')
          .delete()
          .eq('psychologist_id', '00000000-0000-0000-0000-000000000000');
      }
    } else {
      console.log('✅ Columnas encontradas:', data);
    }
  } catch (err) {
    console.log('❌ Error:', err.message);
  }

  // Verificar estructura de pin_recharge_requests
  console.log('\n📋 Estructura de pin_recharge_requests:');
  try {
    const { data: requests, error: reqError } = await supabase
      .from('pin_recharge_requests')
      .select('*')
      .limit(1);

    if (reqError) {
      console.log('❌ Error:', reqError.message);
    } else if (requests && requests.length > 0) {
      console.log('✅ Columnas encontradas:', Object.keys(requests[0]));
      console.log('📄 Ejemplo:', requests[0]);
    } else {
      console.log('⚠️ Tabla vacía');
    }
  } catch (err) {
    console.log('❌ Error:', err.message);
  }

  // Verificar estructura de pin_notifications
  console.log('\n📋 Estructura de pin_notifications:');
  try {
    const { data: notifications, error: notError } = await supabase
      .from('pin_notifications')
      .select('*')
      .limit(1);

    if (notError) {
      console.log('❌ Error:', notError.message);
    } else if (notifications && notifications.length > 0) {
      console.log('✅ Columnas encontradas:', Object.keys(notifications[0]));
      console.log('📄 Ejemplo:', notifications[0]);
    } else {
      console.log('⚠️ Tabla vacía');
    }
  } catch (err) {
    console.log('❌ Error:', err.message);
  }
}

checkTableStructure();
