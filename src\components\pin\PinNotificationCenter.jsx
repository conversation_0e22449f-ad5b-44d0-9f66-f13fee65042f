import React, { useState, useEffect, useCallback } from 'react';
import { 
  FaBell, 
  FaExclamationTriangle, 
  FaTimesCircle, 
  FaCheckCircle, 
  FaInfoCircle,
  FaTimes,
  FaCoins,
  FaClock
} from 'react-icons/fa';
import EnhancedNotificationService from '../../services/pin/EnhancedNotificationService';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';

/**
 * Centro de notificaciones para el sistema de pines
 * Muestra notificaciones en tiempo real y permite gestionarlas
 */
const PinNotificationCenter = ({ psychologistId, className = '' }) => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // Cargar notificaciones
  const loadNotifications = useCallback(async () => {
    if (!psychologistId) return;

    try {
      setLoading(true);
      const unreadNotifications = await EnhancedNotificationService.getUnreadNotifications(psychologistId);
      
      setNotifications(unreadNotifications);
      setUnreadCount(unreadNotifications.length);
      
    } catch (error) {
      console.error('Error cargando notificaciones:', error);
    } finally {
      setLoading(false);
    }
  }, [psychologistId]);

  // Cargar notificaciones al montar y cuando cambie el psicólogo
  useEffect(() => {
    loadNotifications();
    
    // Configurar polling para actualizaciones en tiempo real
    const interval = setInterval(loadNotifications, 30000); // Cada 30 segundos
    
    return () => clearInterval(interval);
  }, [loadNotifications]);

  // Marcar notificación como leída
  const markAsRead = async (notificationId) => {
    try {
      await EnhancedNotificationService.markAsRead(notificationId);
      
      // Actualizar estado local
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      setUnreadCount(prev => Math.max(0, prev - 1));
      
    } catch (error) {
      console.error('Error marcando notificación como leída:', error);
    }
  };

  // Marcar todas como leídas
  const markAllAsRead = async () => {
    try {
      const promises = notifications.map(n => EnhancedNotificationService.markAsRead(n.id));
      await Promise.all(promises);
      
      setNotifications([]);
      setUnreadCount(0);
      
    } catch (error) {
      console.error('Error marcando todas las notificaciones como leídas:', error);
    }
  };

  // Configuración de iconos por severidad
  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'error':
      case 'critical':
        return <FaTimesCircle className="text-red-500" />;
      case 'warning':
        return <FaExclamationTriangle className="text-yellow-500" />;
      case 'success':
        return <FaCheckCircle className="text-green-500" />;
      default:
        return <FaInfoCircle className="text-blue-500" />;
    }
  };

  // Configuración de colores por severidad
  const getSeverityColors = (severity) => {
    switch (severity) {
      case 'error':
      case 'critical':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Botón de notificaciones */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg transition-colors"
        title="Notificaciones de pines"
      >
        <FaBell className="h-5 w-5" />
        
        {/* Badge de contador */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* Panel de notificaciones */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Notificaciones
            </h3>
            <div className="flex items-center space-x-2">
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  Marcar todas como leídas
                </button>
              )}
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <FaTimes className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Contenido */}
          <div className="max-h-96 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
                <p className="text-sm text-gray-500">Cargando notificaciones...</p>
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-6 text-center">
                <FaBell className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                <p className="text-sm text-gray-500">No hay notificaciones nuevas</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {notifications.map((notification) => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onMarkAsRead={markAsRead}
                    getSeverityIcon={getSeverityIcon}
                    getSeverityColors={getSeverityColors}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="p-3 border-t border-gray-200 bg-gray-50 rounded-b-lg">
              <button
                onClick={loadNotifications}
                className="w-full text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                Actualizar notificaciones
              </button>
            </div>
          )}
        </div>
      )}

      {/* Overlay para cerrar */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

/**
 * Componente individual de notificación
 */
const NotificationItem = ({ 
  notification, 
  onMarkAsRead, 
  getSeverityIcon, 
  getSeverityColors 
}) => {
  const { id, title, message, severity, created_at, metadata } = notification;

  const handleMarkAsRead = () => {
    onMarkAsRead(id);
  };

  const formatTime = (dateString) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: es 
      });
    } catch (error) {
      return 'Hace un momento';
    }
  };

  return (
    <div className={`p-4 border-l-4 ${getSeverityColors(severity)} hover:bg-opacity-75 transition-colors`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-0.5">
          {getSeverityIcon(severity)}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {title}
            </h4>
            <button
              onClick={handleMarkAsRead}
              className="text-gray-400 hover:text-gray-600 ml-2"
              title="Marcar como leída"
            >
              <FaTimes className="h-3 w-3" />
            </button>
          </div>
          
          <p className="text-sm text-gray-700 mb-2">
            {message}
          </p>
          
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center">
              <FaClock className="h-3 w-3 mr-1" />
              <span>{formatTime(created_at)}</span>
            </div>
            
            {metadata?.remainingPins !== undefined && (
              <div className="flex items-center">
                <FaCoins className="h-3 w-3 mr-1" />
                <span>{metadata.remainingPins} pines</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PinNotificationCenter;
