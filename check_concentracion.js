import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://ydglduxhgwajqdseqzpy.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c'
);

async function checkConcentracion() {
  console.log('🔍 Verificando interpretaciones para Concentración (C)...');
  
  // Verificar interpretaciones para C
  const { data, error } = await supabase
    .from('interpretaciones_oficiales')
    .select('*')
    .eq('aptitud_codigo', 'C')
    .order('nivel_id');
  
  if (error) {
    console.error('❌ Error:', error);
  } else {
    console.log('📊 Interpretaciones encontradas para C:', data?.length || 0);
    
    if (data && data.length > 0) {
      data.forEach(interp => {
        console.log(`✅ C-${interp.nivel_id}: ${interp.rendimiento.substring(0, 100)}...`);
      });
    } else {
      console.log('⚠️ No se encontraron interpretaciones para código C');
    }
  }
  
  // Verificar si existe la aptitud C
  const { data: aptitudes, error: errorApt } = await supabase
    .from('aptitudes')
    .select('*')
    .eq('codigo', 'C');
    
  if (errorApt) {
    console.error('❌ Error verificando aptitud C:', errorApt);
  } else {
    console.log('📋 Aptitud C en tabla aptitudes:', aptitudes?.length || 0);
    if (aptitudes && aptitudes.length > 0) {
      console.log('   Nombre:', aptitudes[0].nombre);
    }
  }
  
  // Verificar todas las aptitudes disponibles
  const { data: todasAptitudes, error: errorTodas } = await supabase
    .from('aptitudes')
    .select('codigo, nombre')
    .order('codigo');
    
  if (!errorTodas && todasAptitudes) {
    console.log('\n📋 Todas las aptitudes disponibles:');
    todasAptitudes.forEach(apt => {
      console.log(`   ${apt.codigo}: ${apt.nombre}`);
    });
  }
  
  // Verificar interpretaciones por aptitud
  const { data: conteos, error: errorConteos } = await supabase
    .from('interpretaciones_oficiales')
    .select('aptitud_codigo')
    .eq('es_oficial', true);
    
  if (!errorConteos && conteos) {
    const conteoPorAptitud = {};
    conteos.forEach(item => {
      conteoPorAptitud[item.aptitud_codigo] = (conteoPorAptitud[item.aptitud_codigo] || 0) + 1;
    });
    
    console.log('\n📈 Interpretaciones por aptitud en Supabase:');
    Object.keys(conteoPorAptitud).sort().forEach(aptitud => {
      const count = conteoPorAptitud[aptitud];
      console.log(`   ${aptitud}: ${count} interpretaciones`);
    });
  }
}

checkConcentracion();
