import { supabase } from '../supabaseClient.js';

/**
 * API Endpoints para Notificaciones de Pines
 * Implementa gestión completa de notificaciones persistentes
 */
export class PinNotificationsAPI {

  /**
   * Obtener notificaciones de un usuario
   * GET /api/pin-notifications/:userId
   */
  static async getUserNotifications(userId, filters = {}) {
    try {
      const {
        read = null, // null = todas, true = leídas, false = no leídas
        type = null,
        limit = 50,
        offset = 0,
        include_expired = false
      } = filters;

      let query = supabase
        .from('pin_notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      // Aplicar filtros
      if (read !== null) {
        query = query.eq('read', read);
      }

      if (type) {
        query = query.eq('type', type);
      }

      if (!include_expired) {
        query = query.or('expires_at.is.null,expires_at.gt.' + new Date().toISOString());
      }

      const { data: notifications, error } = await query;

      if (error) {
        throw new Error(`Error al obtener notificaciones: ${error.message}`);
      }

      // Obtener conteo de no leídas
      const { count: unreadCount, error: countError } = await supabase
        .from('pin_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('read', false)
        .or('expires_at.is.null,expires_at.gt.' + new Date().toISOString());

      if (countError) {
        console.warn('Error getting unread count:', countError);
      }

      return {
        success: true,
        data: notifications || [],
        unread_count: unreadCount || 0,
        pagination: {
          limit,
          offset,
          has_more: (notifications?.length || 0) === limit
        }
      };

    } catch (error) {
      console.error('Error in getUserNotifications:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al obtener las notificaciones'
      };
    }
  }

  /**
   * Crear nueva notificación
   * POST /api/pin-notifications
   */
  static async createNotification(notificationData) {
    try {
      const {
        user_id,
        type,
        title,
        message,
        severity = 'info',
        channels = ['toast'],
        metadata = {},
        related_entity_type = null,
        related_entity_id = null,
        expires_at = null
      } = notificationData;

      // Validaciones
      if (!user_id || !type || !title || !message) {
        throw new Error('Faltan campos requeridos: user_id, type, title, message');
      }

      if (!['info', 'success', 'warning', 'error', 'critical'].includes(severity)) {
        throw new Error('Severidad debe ser: info, success, warning, error, o critical');
      }

      // Verificar que el usuario existe
      const { data: user, error: userError } = await supabase
        .from('usuarios')
        .select('id')
        .eq('id', user_id)
        .single();

      if (userError || !user) {
        throw new Error('Usuario no encontrado');
      }

      // Crear la notificación
      const { data: newNotification, error } = await supabase
        .from('pin_notifications')
        .insert([{
          user_id,
          type,
          title,
          message,
          severity,
          channels: JSON.stringify(channels),
          metadata,
          related_entity_type,
          related_entity_id,
          expires_at
        }])
        .select('*')
        .single();

      if (error) {
        throw new Error(`Error al crear notificación: ${error.message}`);
      }

      // Enviar notificación en tiempo real si incluye toast
      if (channels.includes('toast')) {
        await this._sendRealtimeNotification(newNotification);
      }

      return {
        success: true,
        data: newNotification,
        message: 'Notificación creada exitosamente'
      };

    } catch (error) {
      console.error('Error in createNotification:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al crear la notificación'
      };
    }
  }

  /**
   * Marcar notificación como leída
   * PUT /api/pin-notifications/:id/read
   */
  static async markAsRead(notificationId, userId = null) {
    try {
      let query = supabase
        .from('pin_notifications')
        .update({
          read: true,
          read_at: new Date().toISOString()
        })
        .eq('id', notificationId);

      // Si se proporciona userId, verificar que la notificación le pertenece
      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data: updatedNotification, error } = await query
        .select('*')
        .single();

      if (error) {
        throw new Error(`Error al marcar como leída: ${error.message}`);
      }

      return {
        success: true,
        data: updatedNotification,
        message: 'Notificación marcada como leída'
      };

    } catch (error) {
      console.error('Error in markAsRead:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al marcar la notificación como leída'
      };
    }
  }

  /**
   * Marcar todas las notificaciones como leídas
   * PUT /api/pin-notifications/user/:userId/mark-all-read
   */
  static async markAllAsRead(userId) {
    try {
      const { data: updatedNotifications, error } = await supabase
        .from('pin_notifications')
        .update({
          read: true,
          read_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('read', false)
        .select('*');

      if (error) {
        throw new Error(`Error al marcar todas como leídas: ${error.message}`);
      }

      return {
        success: true,
        data: updatedNotifications || [],
        count: updatedNotifications?.length || 0,
        message: `${updatedNotifications?.length || 0} notificaciones marcadas como leídas`
      };

    } catch (error) {
      console.error('Error in markAllAsRead:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al marcar todas las notificaciones como leídas'
      };
    }
  }

  /**
   * Eliminar notificación
   * DELETE /api/pin-notifications/:id
   */
  static async deleteNotification(notificationId, userId = null) {
    try {
      let query = supabase
        .from('pin_notifications')
        .delete()
        .eq('id', notificationId);

      // Si se proporciona userId, verificar que la notificación le pertenece
      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data: deletedNotification, error } = await query
        .select('*')
        .single();

      if (error) {
        throw new Error(`Error al eliminar notificación: ${error.message}`);
      }

      return {
        success: true,
        data: deletedNotification,
        message: 'Notificación eliminada exitosamente'
      };

    } catch (error) {
      console.error('Error in deleteNotification:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al eliminar la notificación'
      };
    }
  }

  /**
   * Limpiar notificaciones expiradas
   * DELETE /api/pin-notifications/cleanup
   */
  static async cleanupExpiredNotifications() {
    try {
      const { data: deletedNotifications, error } = await supabase
        .from('pin_notifications')
        .delete()
        .lt('expires_at', new Date().toISOString())
        .select('*');

      if (error) {
        throw new Error(`Error al limpiar notificaciones: ${error.message}`);
      }

      return {
        success: true,
        count: deletedNotifications?.length || 0,
        message: `${deletedNotifications?.length || 0} notificaciones expiradas eliminadas`
      };

    } catch (error) {
      console.error('Error in cleanupExpiredNotifications:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al limpiar notificaciones expiradas'
      };
    }
  }

  /**
   * Obtener estadísticas de notificaciones
   * GET /api/pin-notifications/stats/:userId
   */
  static async getNotificationStats(userId, filters = {}) {
    try {
      const { date_from = null, date_to = null } = filters;

      let query = supabase
        .from('pin_notifications')
        .select('type, severity, read, created_at')
        .eq('user_id', userId);

      if (date_from) {
        query = query.gte('created_at', date_from);
      }

      if (date_to) {
        query = query.lte('created_at', date_to);
      }

      const { data: notifications, error } = await query;

      if (error) {
        throw new Error(`Error al obtener estadísticas: ${error.message}`);
      }

      // Calcular estadísticas
      const stats = {
        total: notifications.length,
        unread: notifications.filter(n => !n.read).length,
        read: notifications.filter(n => n.read).length,
        by_type: {},
        by_severity: {
          info: notifications.filter(n => n.severity === 'info').length,
          success: notifications.filter(n => n.severity === 'success').length,
          warning: notifications.filter(n => n.severity === 'warning').length,
          error: notifications.filter(n => n.severity === 'error').length,
          critical: notifications.filter(n => n.severity === 'critical').length
        },
        recent_activity: {
          today: notifications.filter(n => 
            new Date(n.created_at).toDateString() === new Date().toDateString()
          ).length,
          this_week: notifications.filter(n => {
            const notificationDate = new Date(n.created_at);
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return notificationDate >= weekAgo;
          }).length
        }
      };

      // Contar por tipo
      notifications.forEach(notification => {
        stats.by_type[notification.type] = (stats.by_type[notification.type] || 0) + 1;
      });

      return {
        success: true,
        data: stats
      };

    } catch (error) {
      console.error('Error in getNotificationStats:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al obtener estadísticas de notificaciones'
      };
    }
  }

  /**
   * Crear notificación de pines bajos
   * POST /api/pin-notifications/low-pins
   */
  static async createLowPinNotification(userId, remainingPins, threshold = 5) {
    try {
      const severity = remainingPins === 0 ? 'critical' : remainingPins <= 2 ? 'error' : 'warning';
      const urgency = remainingPins === 0 ? 'urgent' : remainingPins <= 2 ? 'high' : 'normal';
      
      const title = remainingPins === 0 ? 'Sin pines disponibles' : 'Pines bajos';
      const message = remainingPins === 0 
        ? 'No tienes pines disponibles. Solicita una recarga para continuar generando informes.'
        : `Te quedan solo ${remainingPins} pines disponibles. Considera solicitar una recarga.`;

      return await this.createNotification({
        user_id: userId,
        type: 'low_pins',
        title,
        message,
        severity,
        channels: ['toast', 'in_app'],
        metadata: {
          remaining_pins: remainingPins,
          threshold,
          urgency,
          auto_generated: true
        },
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 horas
      });

    } catch (error) {
      console.error('Error in createLowPinNotification:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al crear notificación de pines bajos'
      };
    }
  }

  /**
   * Crear notificación de asignación de pines
   * POST /api/pin-notifications/pin-assignment
   */
  static async createPinAssignmentNotification(userId, assignedPins, isUnlimited = false) {
    try {
      const title = 'Pines asignados';
      const message = isUnlimited 
        ? 'Se te ha asignado un plan ilimitado de pines'
        : `Se han asignado ${assignedPins} pines a tu cuenta`;

      return await this.createNotification({
        user_id: userId,
        type: 'pin_assignment',
        title,
        message,
        severity: 'success',
        channels: ['toast', 'in_app'],
        metadata: {
          assigned_pins: assignedPins,
          is_unlimited: isUnlimited,
          auto_generated: true
        }
      });

    } catch (error) {
      console.error('Error in createPinAssignmentNotification:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al crear notificación de asignación'
      };
    }
  }

  // =====================================================
  // MÉTODOS PRIVADOS
  // =====================================================

  /**
   * Enviar notificación en tiempo real
   * @private
   */
  static async _sendRealtimeNotification(notification) {
    try {
      // Enviar a través del canal de Supabase Realtime
      const channel = supabase.channel(`notifications:${notification.user_id}`);
      
      channel.send({
        type: 'broadcast',
        event: 'new_notification',
        payload: notification
      });

      // También podríamos integrar con servicios externos como:
      // - Push notifications
      // - Email
      // - SMS
      // - Slack/Discord webhooks

    } catch (error) {
      console.error('Error sending realtime notification:', error);
    }
  }

  /**
   * Validar estructura de metadatos
   * @private
   */
  static _validateMetadata(metadata) {
    try {
      if (typeof metadata === 'string') {
        JSON.parse(metadata);
      }
      return true;
    } catch (error) {
      return false;
    }
  }
}

export default PinNotificationsAPI;
