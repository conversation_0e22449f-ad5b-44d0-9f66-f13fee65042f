var e=(e,s,t)=>new Promise((a,l)=>{var i=e=>{try{n(t.next(e))}catch(s){l(s)}},r=e=>{try{n(t.throw(e))}catch(s){l(s)}},n=e=>e.done?a(e.value):Promise.resolve(e.value).then(i,r);n((t=t.apply(e,s)).next())});import{r as s,j as t,J as a,G as l,$ as i,R as r,F as n,m as d,a0 as c,a1 as o,l as m}from"./vendor-BqMjyOVw.js";import{u as x,s as h}from"./index-Bdl1jgS_.js";import{e as u}from"./enhancedSupabaseService-D53fSbDl.js";import{p as g}from"./ImprovedPinControlService-BUPGzexy.js";import{a as j}from"./PinLogger-C2v3yGM1.js";import"./NotificationService-DiDbKBbI.js";class f{getDisplayInfo(e){throw new Error("getDisplayInfo must be implemented by subclass")}}class y extends f{getDisplayInfo(e){return{color:"bg-green-100 text-green-800 border-green-200",icon:"FaInfinity",text:"Pines Ilimitados",showProgress:!1,severity:"success"}}}class N extends f{getDisplayInfo(e){const s=e.remainingPins<=j.THRESHOLDS.LOW_PIN_WARNING;return{color:s?"bg-yellow-100 text-yellow-800 border-yellow-200":"bg-blue-100 text-blue-800 border-blue-200",icon:"FaCoins",text:`${e.remainingPins} Pines`,showProgress:!0,severity:s?"warning":"info"}}}class p extends f{getDisplayInfo(e){return{color:"bg-red-100 text-red-800 border-red-200",icon:"FaExclamationTriangle",text:"Sin Pines",showProgress:!1,severity:"error"}}}class b{static createStrategy(e){return e?e.isUnlimited?new y:e.canUse?new N:new p:new p}}const v=({psychologistId:n,className:d=""})=>{const[c,o]=s.useState(null),[m,x]=s.useState(!0),[h,u]=s.useState(!1);s.useEffect(()=>{n&&j()},[n,j]);const j=s.useCallback(()=>e(null,null,function*(){try{x(!0);const e=yield g.checkPsychologistUsage(n);o(e)}catch(e){}finally{x(!1)}}),[n]),f=s.useMemo(()=>{if(m||!c)return null;return b.createStrategy(c).getDisplayInfo(c)},[m,c]),y=s.useMemo(()=>{if(!f)return null;switch(f.icon){case"FaInfinity":return t.jsx(i,{className:"w-4 h-4"});case"FaExclamationTriangle":return t.jsx(l,{className:"w-4 h-4"});default:return t.jsx(a,{className:"w-4 h-4"})}},[f]);return!m&&c&&f?t.jsxs("div",{className:`relative ${d}`,children:[t.jsxs("div",{className:`inline-flex items-center space-x-2 px-3 py-2 rounded-lg border cursor-pointer transition-all ${f.color}`,onClick:()=>u(!h),children:[y,t.jsx("span",{className:"text-sm font-medium",children:f.text})]}),h&&t.jsxs("div",{className:"absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50",children:[t.jsxs("div",{className:"flex items-center justify-between mb-3",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Estado de Pines"}),t.jsx("button",{onClick:()=>u(!1),className:"text-gray-400 hover:text-gray-600",children:t.jsx(r,{className:"w-4 h-4"})})]}),t.jsxs("div",{className:"space-y-3",children:[t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"Estado:"}),t.jsx("span",{className:"text-sm font-medium "+(c.canUse?"text-green-600":"text-red-600"),children:c.reason})]}),!c.isUnlimited&&t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"Pines Totales:"}),t.jsx("span",{className:"text-sm font-medium text-gray-900",children:c.totalPins||0})]}),t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"Pines Usados:"}),t.jsx("span",{className:"text-sm font-medium text-gray-900",children:c.usedPins||0})]}),t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"Pines Restantes:"}),t.jsx("span",{className:"text-sm font-medium text-gray-900",children:c.remainingPins||0})]}),c.totalPins>0&&t.jsxs("div",{className:"mt-3",children:[t.jsxs("div",{className:"flex justify-between text-sm text-gray-600 mb-1",children:[t.jsx("span",{children:"Progreso de uso"}),t.jsxs("span",{children:[Math.round((c.usedPins||0)/c.totalPins*100),"%"]})]}),t.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:t.jsx("div",{className:"h-2 rounded-full transition-all "+(c.remainingPins<=5?"bg-red-500":c.remainingPins<=10?"bg-yellow-500":"bg-blue-500"),style:{width:`${Math.min((c.usedPins||0)/c.totalPins*100,100)}%`}})})]})]}),c.isUnlimited&&t.jsxs("div",{className:"text-center py-2",children:[t.jsx(i,{className:"w-8 h-8 text-green-500 mx-auto mb-2"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Tienes acceso ilimitado al sistema"})]}),!c.canUse&&t.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mt-3",children:t.jsxs("div",{className:"flex items-center",children:[t.jsx(l,{className:"w-4 h-4 text-red-500 mr-2"}),t.jsx("p",{className:"text-sm text-red-700",children:"Contacta al administrador para obtener más pines"})]})}),c.canUse&&c.remainingPins<=5&&!c.isUnlimited&&t.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-3",children:t.jsxs("div",{className:"flex items-center",children:[t.jsx(l,{className:"w-4 h-4 text-yellow-500 mr-2"}),t.jsx("p",{className:"text-sm text-yellow-700",children:"Quedan pocos pines disponibles"})]})})]}),t.jsx("div",{className:"mt-4 pt-3 border-t border-gray-200",children:t.jsx("button",{onClick:j,className:"w-full px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Actualizar Estado"})})]})]}):null},w=()=>{const{user:a,isAdmin:l}=x(),[i,r]=s.useState({totalPatients:0,totalPsychologists:0,totalInstitutions:0,totalPins:0,usedPins:0,activePsychologists:0,psychologistsWithLowPins:0}),[g,j]=s.useState(null),[f,y]=s.useState(!0);return s.useEffect(()=>{e(null,null,function*(){y(!0);try{const[e,s,t,i]=yield Promise.all([u.getPatients(),u.getPsychologists(),u.getInstitutions(),l?h.rpc("get_all_psychologists_with_stats"):h.rpc("get_psychologist_pin_stats_optimized")]),n=Array.isArray(e.data)?e.data.length:0,d=Array.isArray(s.data)?s.data.length:0,c=Array.isArray(t.data)?t.data.length:0;let o=0,m=0,x=0,g=0;if(l&&i.data)o=i.data.reduce((e,s)=>e+(s.total_uses||0),0),m=i.data.reduce((e,s)=>e+(s.used_uses||0),0),x=i.data.filter(e=>e.total_uses>0).length,g=i.data.filter(e=>{const s=e.total_uses-e.used_uses;return s<=5&&s>0}).length;else if(i.data){const e=i.data.find(e=>e.psychologist_id===(null==a?void 0:a.id));e&&(o=e.total_uses||0,m=e.used_uses||0,j(e))}r({totalPatients:n,totalPsychologists:d,totalInstitutions:c,totalPins:o,usedPins:m,activePsychologists:x,psychologistsWithLowPins:g})}catch(e){}finally{y(!1)}})},[l,null==a?void 0:a.id]),t.jsx("div",{className:"min-h-screen bg-gray-50",children:t.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t.jsxs("div",{className:"mb-8",children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),t.jsxs("p",{className:"mt-2 text-sm text-gray-600",children:["Bienvenido ",(null==a?void 0:a.full_name)||"al sistema"]})]}),f?t.jsx("div",{className:"flex justify-center items-center p-12",children:t.jsx(n,{className:"animate-spin text-blue-600 text-3xl"})}):t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8",children:[t.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:t.jsx("div",{className:"p-5",children:t.jsxs("div",{className:"flex items-center",children:[t.jsx("div",{className:"flex-shrink-0 bg-blue-500 rounded-md p-3",children:t.jsx(d,{className:"h-6 w-6 text-white"})}),t.jsx("div",{className:"ml-5 w-0 flex-1",children:t.jsxs("dl",{children:[t.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Pacientes"}),t.jsx("dd",{children:t.jsx("div",{className:"text-lg font-medium text-gray-900",children:i.totalPatients})})]})})]})})}),t.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:t.jsx("div",{className:"p-5",children:t.jsxs("div",{className:"flex items-center",children:[t.jsx("div",{className:"flex-shrink-0 bg-green-500 rounded-md p-3",children:t.jsx(c,{className:"h-6 w-6 text-white"})}),t.jsx("div",{className:"ml-5 w-0 flex-1",children:t.jsxs("dl",{children:[t.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Psicólogos"}),t.jsx("dd",{children:t.jsx("div",{className:"text-lg font-medium text-gray-900",children:i.totalPsychologists})})]})})]})})}),t.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:t.jsx("div",{className:"p-5",children:t.jsxs("div",{className:"flex items-center",children:[t.jsx("div",{className:"flex-shrink-0 bg-purple-500 rounded-md p-3",children:t.jsx(o,{className:"h-6 w-6 text-white"})}),t.jsx("div",{className:"ml-5 w-0 flex-1",children:t.jsxs("dl",{children:[t.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Instituciones"}),t.jsx("dd",{children:t.jsx("div",{className:"text-lg font-medium text-gray-900",children:i.totalInstitutions})})]})})]})})}),t.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:t.jsx("div",{className:"p-5",children:t.jsxs("div",{className:"flex items-center",children:[t.jsx("div",{className:"flex-shrink-0 bg-yellow-500 rounded-md p-3",children:t.jsx(m,{className:"h-6 w-6 text-white"})}),t.jsx("div",{className:"ml-5 w-0 flex-1",children:t.jsxs("dl",{children:[t.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Evaluaciones"}),t.jsx("dd",{children:t.jsx("div",{className:"text-lg font-medium text-gray-900",children:i.totalPins})})]})})]})})})]}),l?t.jsx("div",{className:"mb-8",children:t.jsxs("div",{className:"bg-white overflow-hidden shadow rounded-lg p-6",children:[t.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Estado de Pines"}),t.jsx(v,{psychologistId:null==a?void 0:a.id})]})}):t.jsxs("div",{className:"bg-white shadow rounded-lg p-6 mb-8",children:[t.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Estado de Pines"}),t.jsx(v,{psychologistId:null==a?void 0:a.id})]}),t.jsxs("div",{className:"bg-white shadow rounded-lg p-6",children:[t.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Acciones Rápidas"}),t.jsxs("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[t.jsxs("a",{href:"/patients",className:"relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[t.jsx(d,{className:"mx-auto h-12 w-12 text-gray-400"}),t.jsx("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Ver Pacientes"})]}),t.jsxs("a",{href:"/evaluations",className:"relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[t.jsx(m,{className:"mx-auto h-12 w-12 text-gray-400"}),t.jsx("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Ver Evaluaciones"})]}),t.jsxs("a",{href:"/reports",className:"relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[t.jsx(c,{className:"mx-auto h-12 w-12 text-gray-400"}),t.jsx("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Ver Reportes"})]})]})]})]})]})})};export{w as default};
