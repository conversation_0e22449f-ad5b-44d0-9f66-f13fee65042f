var e=Object.defineProperty,s=Object.defineProperties,t=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,r=(s,t,a)=>t in s?e(s,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):s[t]=a,n=(e,s)=>{for(var t in s||(s={}))i.call(s,t)&&r(e,t,s[t]);if(a)for(var t of a(s))l.call(s,t)&&r(e,t,s[t]);return e},c=(e,a)=>s(e,t(a)),d=(e,s,t)=>new Promise((a,i)=>{var l=e=>{try{n(t.next(e))}catch(s){i(s)}},r=e=>{try{n(t.throw(e))}catch(s){i(s)}},n=e=>e.done?a(e.value):Promise.resolve(e.value).then(l,r);n((t=t.apply(e,s)).next())});import{r as x,Q as o,j as m,F as p,G as h,H as g,I as u,m as y,J as j,l as N,K as b}from"./vendor-BqMjyOVw.js";import f from"./PinManagementService-DAtjQY2H.js";import"./index-Bdl1jgS_.js";const v=()=>{const[e,s]=x.useState([]),[t,a]=x.useState(!0),[i,l]=x.useState(null),[r,v]=x.useState({}),[w,_]=x.useState(null),[P,S]=x.useState(null),[k,C]=x.useState(!1),[A,O]=x.useState([]),E=x.useCallback(()=>d(null,null,function*(){try{a(!0),l(null);const[e,t]=yield Promise.all([f.getUsageStats(),f.getSystemSummary()]);s(e.statistics),S(t)}catch(e){l("No se pudieron cargar los datos. Intente de nuevo más tarde.")}finally{a(!1)}}),[]),D=x.useCallback(()=>d(null,null,function*(){try{const e=yield f.getTransactionHistory(null,20);O(e)}catch(e){o.error("Error al cargar el historial de transacciones.")}}),[]);x.useEffect(()=>{E()},[E]),x.useEffect(()=>{k&&D()},[k,D]);const T=(e,s)=>{const t={sin_pines:"bg-red-100 text-red-800",pocos_pines:"bg-yellow-100 text-yellow-800",activo:"bg-green-100 text-green-800"};return m.jsxs("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${t[e]||t.activo}`,children:[{sin_pines:"Sin pines",pocos_pines:"Pocos pines",activo:"Activo"}[e]||"Activo"," (",s,")"]})},H=e=>e?new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Nunca";return t?m.jsxs("div",{className:"flex items-center justify-center p-10",children:[m.jsx(p,{className:"animate-spin text-blue-500 text-4xl"}),m.jsx("span",{className:"ml-4 text-xl",children:"Cargando datos del sistema..."})]}):i?m.jsxs("div",{className:"flex flex-col items-center justify-center p-10 bg-red-50 border border-red-200 rounded-lg",children:[m.jsx(h,{className:"text-red-500 text-4xl"}),m.jsx("span",{className:"mt-4 text-xl text-red-700",children:i}),m.jsxs("button",{onClick:E,className:"mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors flex items-center",children:[m.jsx(g,{className:"mr-2"}),"Reintentar"]})]}):m.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[m.jsxs("div",{className:"flex justify-between items-center mb-6",children:[m.jsx("h1",{className:"text-3xl font-bold text-gray-800",children:"Gestión de Pines"}),m.jsxs("div",{className:"flex space-x-3",children:[m.jsxs("button",{onClick:()=>C(!k),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[m.jsx(u,{className:"mr-2"}),k?"Ocultar":"Ver"," Historial"]}),m.jsxs("button",{onClick:E,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center",children:[m.jsx(g,{className:"mr-2"}),"Actualizar"]})]})]}),P&&m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[m.jsx("div",{className:"bg-white p-4 rounded-lg shadow",children:m.jsxs("div",{className:"flex items-center",children:[m.jsx(y,{className:"text-blue-500 text-2xl mr-3"}),m.jsxs("div",{children:[m.jsx("p",{className:"text-sm text-gray-600",children:"Psicólogos Totales"}),m.jsx("p",{className:"text-2xl font-bold",children:P.total_psychologists})]})]})}),m.jsx("div",{className:"bg-white p-4 rounded-lg shadow",children:m.jsxs("div",{className:"flex items-center",children:[m.jsx(j,{className:"text-yellow-500 text-2xl mr-3"}),m.jsxs("div",{children:[m.jsx("p",{className:"text-sm text-gray-600",children:"Pines Disponibles"}),m.jsx("p",{className:"text-2xl font-bold text-green-600",children:P.total_pins_available})]})]})}),m.jsx("div",{className:"bg-white p-4 rounded-lg shadow",children:m.jsxs("div",{className:"flex items-center",children:[m.jsx(N,{className:"text-red-500 text-2xl mr-3"}),m.jsxs("div",{children:[m.jsx("p",{className:"text-sm text-gray-600",children:"Pines Consumidos"}),m.jsx("p",{className:"text-2xl font-bold text-red-600",children:P.total_pins_consumed})]})]})}),m.jsx("div",{className:"bg-white p-4 rounded-lg shadow",children:m.jsxs("div",{className:"flex items-center",children:[m.jsx(y,{className:"text-purple-500 text-2xl mr-3"}),m.jsxs("div",{children:[m.jsx("p",{className:"text-sm text-gray-600",children:"Con Pines / Sin Pines"}),m.jsxs("p",{className:"text-2xl font-bold",children:[m.jsx("span",{className:"text-green-600",children:P.psychologists_with_pins}),m.jsx("span",{className:"text-gray-400 mx-1",children:"/"}),m.jsx("span",{className:"text-red-600",children:P.psychologists_without_pins})]})]})]})})]}),k&&m.jsxs("div",{className:"bg-white rounded-lg shadow mb-6",children:[m.jsx("div",{className:"p-4 border-b",children:m.jsx("h2",{className:"text-xl font-semibold",children:"Historial de Transacciones"})}),m.jsx("div",{className:"p-4",children:0===A.length?m.jsx("p",{className:"text-gray-500 text-center py-4",children:"No hay transacciones registradas."}):m.jsx("div",{className:"overflow-x-auto",children:m.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsx("thead",{className:"bg-gray-50",children:m.jsxs("tr",{children:[m.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Fecha"}),m.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Psicólogo"}),m.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Tipo"}),m.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Cantidad"}),m.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Motivo"})]})}),m.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:A.map(e=>{var s,t;return m.jsxs("tr",{className:"hover:bg-gray-50",children:[m.jsx("td",{className:"px-4 py-2 text-sm text-gray-900",children:H(e.created_at)}),m.jsxs("td",{className:"px-4 py-2 text-sm text-gray-900",children:[null==(s=e.psicologos)?void 0:s.nombre," ",null==(t=e.psicologos)?void 0:t.apellido]}),m.jsx("td",{className:"px-4 py-2 text-sm",children:m.jsx("span",{className:"px-2 py-1 text-xs font-medium rounded-full "+("asignacion"===e.tipo?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:"asignacion"===e.tipo?"Asignación":"Consumo"})}),m.jsx("td",{className:"px-4 py-2 text-sm font-medium",children:m.jsxs("span",{className:"asignacion"===e.tipo?"text-green-600":"text-red-600",children:["asignacion"===e.tipo?"+":"",e.cantidad]})}),m.jsx("td",{className:"px-4 py-2 text-sm text-gray-600",children:e.motivo||"Sin motivo especificado"})]},e.id)})})]})})})]}),m.jsxs("div",{className:"bg-white shadow-md rounded-lg overflow-hidden",children:[m.jsx("div",{className:"p-4 border-b",children:m.jsx("h2",{className:"text-xl font-semibold",children:"Asignación de Pines por Psicólogo"})}),m.jsx("div",{className:"overflow-x-auto",children:m.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsx("thead",{className:"bg-gray-100",children:m.jsxs("tr",{children:[m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Psicólogo"}),m.jsx("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Estado"}),m.jsx("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Asignados"}),m.jsx("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Consumidos"}),m.jsx("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Disponibles"}),m.jsx("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pacientes"}),m.jsx("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tests"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Asignar Pines"})]})}),m.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>m.jsxs("tr",{className:"hover:bg-gray-50",children:[m.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[m.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.nombre_psicologo}),m.jsx("div",{className:"text-sm text-gray-500",children:e.email_psicologo}),e.ultima_transaccion&&m.jsxs("div",{className:"text-xs text-gray-400",children:["Última: ",H(e.ultima_transaccion)]})]}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:T(e.status,e.pines_restantes)}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center text-sm text-blue-600 font-semibold",children:e.total_asignado}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center text-sm text-red-600 font-semibold",children:e.total_consumido}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center text-sm text-green-600 font-bold text-lg",children:e.pines_restantes}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-700",children:e.pacientes_asignados}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-700",children:e.tests_completados}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsxs("div",{className:"flex items-center space-x-2",children:[m.jsx("input",{type:"number",min:"1",max:"1000",placeholder:"Cantidad",value:r[e.psicologo_id]||"",onChange:s=>((e,s)=>{const t=parseInt(s,10);v(s=>c(n({},s),{[e]:isNaN(t)?"":Math.max(0,t)}))})(e.psicologo_id,s.target.value),className:"w-24 px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm",disabled:w===e.psicologo_id}),m.jsxs("button",{onClick:()=>{return s=e.psicologo_id,d(null,null,function*(){const e=r[s];if(!e||e<=0)o.warn("Por favor, ingrese una cantidad válida de pines.");else{_(s);try{yield f.assignPins(s,e,`Asignación manual de ${e} pines`),v(e=>c(n({},e),{[s]:""})),yield E()}catch(t){}finally{_(null)}}});var s},className:"px-3 py-1.5 text-white rounded-md disabled:bg-gray-400 flex items-center justify-center transition-colors text-sm "+(e.pines_restantes>0?"bg-green-600 hover:bg-green-700":"bg-blue-600 hover:bg-blue-700"),disabled:w===e.psicologo_id||!r[e.psicologo_id]||r[e.psicologo_id]<=0,title:e.pines_restantes>0?"Agregar pines adicionales":"Asignar pines iniciales",children:[w===e.psicologo_id?m.jsx(p,{className:"animate-spin"}):m.jsx(b,{}),m.jsx("span",{className:"ml-1",children:e.pines_restantes>0?"Agregar":"Asignar"})]})]})})]},e.psicologo_id))})]})}),0===e.length&&m.jsxs("div",{className:"text-center py-8",children:[m.jsx(y,{className:"mx-auto text-gray-400 text-4xl mb-4"}),m.jsx("p",{className:"text-gray-500",children:"No hay psicólogos registrados en el sistema."})]})]})]})};export{v as default};
