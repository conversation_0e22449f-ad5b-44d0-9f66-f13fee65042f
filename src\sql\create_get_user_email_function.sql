-- Función para obtener el email de un usuario desde auth.users
-- EJECUTAR ESTE SCRIPT EN EL EDITOR SQL DE SUPABASE

-- =====================================================
-- FUNCIÓN PARA OBTENER EMAIL DE USUARIO
-- =====================================================

CREATE OR REPLACE FUNCTION get_user_email(user_id UUID)
RETURNS TEXT AS $$
DECLARE
  user_email TEXT;
BEGIN
  -- Obtener email del usuario desde auth.users
  SELECT email INTO user_email
  FROM auth.users
  WHERE id = user_id;
  
  RETURN user_email;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- OTORGAR PERMISOS
-- =====================================================

-- Otorgar permisos de ejecución
GRANT EXECUTE ON FUNCTION get_user_email(UUID) TO authenticated, anon;

-- =====================================================
-- VERIFICACIÓN
-- =====================================================

SELECT 'Función get_user_email creada exitosamente' as status;
