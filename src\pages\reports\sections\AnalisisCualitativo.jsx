import React, { useMemo, useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../../components/ui/Card';
import { InterpretacionCualitativaService } from '../../../services/interpretacionCualitativaService';
import {
  APTITUDES_CONFIG,
  INDICES_INTELIGENCIA,
  getLevelConfigByPercentile,
  getLevelNameByPercentile,
  calcularIndicesInteligencia
} from '../constants/reportConstants';
import {
  INTERPRETACIONES_INDICES,
  INTERPRETACIONES_APTITUDES,
  obtenerNivel,
  NIVELES_RENDIMIENTO
} from '../constants/interpretacionesCualitativas';

const AnalisisCualitativo = ({ resultados, paciente }) => {
  const [interpretacionPersonalizada, setInterpretacionPersonalizada] = useState(null);
  const [loading, setLoading] = useState(true);

  // Cargar interpretación personalizada de forma asíncrona
  useEffect(() => {
    const cargarInterpretacion = async () => {
      if (!resultados || !paciente) {
        setInterpretacionPersonalizada(null);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Debug: Verificar estructura de datos de entrada
        console.log('🔍 [AnalisisCualitativo] Datos de entrada:', {
          resultados: resultados?.map(r => ({
            codigo: r.aptitud?.codigo || r.aptitudes?.codigo,
            percentil: r.percentil,
            estructura: Object.keys(r)
          })),
          paciente: paciente?.nombre
        });

        const interpretacion = await InterpretacionCualitativaService.generarInterpretacionPersonalizada(resultados, paciente);

        // Debug: Verificar interpretación generada
        console.log('🔍 [AnalisisCualitativo] Interpretación generada:', {
          aptitudesEspecificas: interpretacion?.aptitudesEspecificas?.map(apt => ({
            codigo: apt.codigo,
            percentil: apt.percentil,
            tieneInterpretacion: !!apt.interpretacion,
            longitudRendimiento: apt.interpretacion?.rendimiento?.length,
            fuente: apt.fuente
          }))
        });

        setInterpretacionPersonalizada(interpretacion);
      } catch (error) {
        console.error('Error generando interpretación personalizada:', error);
        setInterpretacionPersonalizada(null);
      } finally {
        setLoading(false);
      }
    };

    cargarInterpretacion();
  }, [resultados, paciente]);

  // Memoizar el cálculo de índices de inteligencia
  const indicesInteligencia = useMemo(() => {
    return calcularIndicesInteligencia(resultados);
  }, [resultados]);

  // Mostrar indicador de carga mientras se obtienen las interpretaciones oficiales
  if (loading) {
    return (
      <Card className="mb-6">
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-800">Análisis Cualitativo</h3>
        </CardHeader>
        <CardBody>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Cargando interpretaciones oficiales...</span>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (!interpretacionPersonalizada) return null;

  return (
    <Card className="mb-6 print-analysis-card">
      <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white border-b-0 print-analysis-header">
        <div className="flex items-center">
          <div className="bg-white bg-opacity-20 p-3 rounded-full mr-4 print-icon">
            <i className="fas fa-brain text-2xl text-white print-icon-color"></i>
          </div>
          <div>
            <h2 className="text-xl font-bold print-title">Análisis Cualitativo Personalizado</h2>
            <p className="text-blue-100 text-sm print-subtitle">Interpretación profesional de aptitudes e índices de inteligencia</p>
          </div>
        </div>
      </CardHeader>
      <CardBody className="p-6">

        {/* Índices de Inteligencia */}
        <div className="mb-8">
          <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center border-b border-gray-200 pb-3">
            <i className="fas fa-lightbulb mr-3 text-purple-600"></i>
            Índices de Inteligencia
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {Object.entries(indicesInteligencia).map(([codigo, percentil]) => {
              const indiceConfig = INDICES_INTELIGENCIA[codigo];
              const nivelConfig = getLevelConfigByPercentile(percentil);
              const nivelNombre = getLevelNameByPercentile(percentil);

              // Obtener interpretación cualitativa profesional
              const nivel = obtenerNivel(percentil);
              const interpretacionIndice = INTERPRETACIONES_INDICES[codigo];
              const interpretacionNivel = interpretacionIndice?.interpretaciones[nivel];

              if (!indiceConfig) return null;

              return (
                <div key={codigo} className={`bg-white p-6 rounded-lg shadow-lg border-l-4 ${nivelConfig.borderColor} print-intelligence-card`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <i className={`fas ${indiceConfig.icon} text-2xl ${nivelConfig.textColor} mr-3`}></i>
                      <div>
                        <h4 className="font-bold text-gray-800 text-lg">{indiceConfig.nombre}</h4>
                        <p className="text-sm text-gray-600 print-intelligence-label">{codigo}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-2xl font-bold ${nivelConfig.textColor} print-intelligence-number`}>
                        {percentil}
                      </div>
                      <div className="text-xs text-gray-500 print-intelligence-label">Percentil</div>
                    </div>
                  </div>

                  {/* Descripción del índice */}
                  <div className="mb-4">
                    <p className="text-gray-700 text-sm font-medium mb-2">Definición:</p>
                    <p className="text-gray-600 text-sm">{interpretacionIndice?.descripcion || indiceConfig.descripcion}</p>
                  </div>

                  <div className={`px-3 py-2 rounded-lg ${nivelConfig.color} text-white text-center font-semibold mb-4 print-intelligence-level`}>
                    {nivelNombre}
                  </div>

                  {/* Interpretación cualitativa profesional */}
                  {interpretacionNivel && (
                    <div className="space-y-3">
                      <div>
                        <p className="text-gray-700 text-sm font-bold mb-1">Interpretación Integrada:</p>
                        <p className="text-gray-600 text-sm leading-relaxed">{interpretacionNivel.integrada}</p>
                      </div>

                      <div>
                        <p className="text-gray-700 text-sm font-bold mb-1">Implicaciones Generales:</p>
                        <p className="text-gray-600 text-sm leading-relaxed">{interpretacionNivel.implicaciones}</p>
                      </div>
                    </div>
                  )}

                  <div className="mt-4 pt-3 border-t border-gray-200">
                    <p className="text-xs text-gray-500">
                      <strong className="font-bold">Componentes:</strong> {indiceConfig.componentes.join(', ')}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Aptitudes Específicas */}
        <div className="mb-8">
          <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center border-b border-gray-200 pb-3">
            <i className="fas fa-cogs mr-3 text-indigo-600"></i>
            Interpretación por Aptitudes
          </h3>



          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {resultados.map((resultado, index) => {
              const aptitudConfig = APTITUDES_CONFIG[resultado.aptitud?.codigo];
              const nivelConfig = getLevelConfigByPercentile(resultado.percentil);
              const nivelNombre = getLevelNameByPercentile(resultado.percentil);

              // Obtener interpretación oficial desde las interpretaciones personalizadas cargadas
              const percentil = resultado.percentil || 0;
              const codigoAptitud = resultado.aptitud?.codigo;

              // Buscar la interpretación oficial en las interpretaciones personalizadas ya cargadas
              const interpretacionOficial = interpretacionPersonalizada?.aptitudesEspecificas?.find(
                apt => apt.codigo === codigoAptitud
              );

              // Debug: Solo mostrar si no hay interpretación
              if (!interpretacionOficial) {
                console.log('⚠️ [AnalisisCualitativo] No se encontró interpretación para:', {
                  codigoAptitud,
                  percentil,
                  aptitudesDisponibles: interpretacionPersonalizada?.aptitudesEspecificas?.map(apt => apt.codigo)
                });
              }

              const interpretacionNivel = interpretacionOficial?.interpretacion || {
                rendimiento: `Interpretación específica para ${codigoAptitud} en nivel ${nivelNombre} (PC: ${percentil}) en desarrollo.`,
                academico: 'Información académica específica en desarrollo.',
                vocacional: 'Información vocacional específica en desarrollo.'
              };

              // Debug log para verificar datos
              console.log('🔍 [AnalisisCualitativo] Datos de interpretación:', {
                codigoAptitud,
                percentil,
                nivelNombre,
                tieneInterpretacionOficial: !!interpretacionOficial,
                tieneInterpretacionNivel: !!interpretacionNivel,
                rendimientoLength: interpretacionNivel?.rendimiento?.length || 0,
                fuente: interpretacionOficial?.fuente
              });

              // Verificar si es aptitud alta (percentil >= 75)
              const esAptitudAlta = percentil >= 75;




              if (!aptitudConfig) return null;

              return (
                <div key={index} className={`bg-white p-6 rounded-lg shadow-lg border-l-4 ${nivelConfig.borderColor}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <i className={`fas ${aptitudConfig.icon} text-2xl ${nivelConfig.textColor} mr-3`}></i>
                      <div>
                        <h4 className="font-bold text-gray-800 text-lg">{aptitudConfig.nombre}</h4>
                        <div className="flex items-center">
                          <p className="text-sm text-gray-600 mr-2">{resultado.aptitud?.codigo}</p>
                          {interpretacionOficial?.fuente && (
                            <span className={`px-2 py-0.5 rounded-full text-xs font-semibold ${
                              interpretacionOficial.fuente.includes('Supabase') ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {interpretacionOficial.fuente}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-2xl font-bold ${nivelConfig.textColor}`}>
                        {resultado.percentil || 0}
                      </div>
                      <div className="text-xs text-gray-500">Percentil</div>
                    </div>
                  </div>

                  {/* Descripción de la aptitud */}
                  <div className="mb-4">
                    <p className="text-gray-700 text-base font-bold mb-3">Descripción:</p>
                    <p className="text-gray-600 text-sm leading-relaxed">{interpretacionOficial?.descripcion || aptitudConfig.descripcion}</p>
                  </div>

                  <div className={`px-3 py-2 rounded-lg ${nivelConfig.color} text-white text-center font-semibold mb-4`}>
                    {nivelNombre}
                  </div>

                  {/* Interpretación cualitativa profesional - SIEMPRE mostrar algo */}
                  <div className="space-y-4 mb-4">
                    <div>
                      <p className="text-gray-700 text-base font-bold mb-3">Interpretación del Rendimiento:</p>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {interpretacionNivel?.rendimiento || `Rendimiento en nivel ${nivelNombre} para ${codigoAptitud}. Interpretación específica en desarrollo.`}
                        {/* Incluir información específica de concentración para Atención */}
                        {codigoAptitud === 'A' && resultado.concentracion && (
                          <span className="block mt-3 p-3 bg-blue-50 border-l-4 border-blue-400 rounded">
                            <strong className="text-blue-800">Concentración:</strong>
                            <span className="ml-2 text-blue-700">
                              {typeof resultado.concentracion === 'number' ?
                                `${resultado.concentracion.toFixed(1)}%` : resultado.concentracion}
                            </span>
                            {typeof resultado.concentracion === 'number' && (
                              <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
                                resultado.concentracion >= 80 ? 'bg-green-100 text-green-800' :
                                resultado.concentracion >= 60 ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {resultado.concentracion >= 80 ? 'Excelente' :
                                 resultado.concentracion >= 60 ? 'Buena' : 'Necesita mejora'}
                              </span>
                            )}
                            <div className="text-xs text-blue-600 mt-1">
                              La concentración es un componente clave de la aptitud de atención y refleja la capacidad para mantener el foco durante tareas prolongadas.
                            </div>
                          </span>
                        )}
                      </p>
                    </div>

                    <div>
                      <p className="text-gray-700 text-base font-bold mb-3">Implicaciones Académicas:</p>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {interpretacionNivel?.academico || `Implicaciones académicas para nivel ${nivelNombre} en ${codigoAptitud}. Consulte con el profesional para detalles específicos.`}
                      </p>
                    </div>

                    <div>
                      <p className="text-gray-700 text-base font-bold mb-3">Implicaciones Vocacionales:</p>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {interpretacionNivel?.vocacional || `Implicaciones vocacionales para nivel ${nivelNombre} en ${codigoAptitud}. Consulte con el profesional para orientación específica.`}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-3 text-xs text-gray-600">
                    <div>
                      <strong>PD:</strong> {resultado.puntaje_directo || 'N/A'}
                    </div>
                    <div>
                      <strong>Errores:</strong> {resultado.errores || 0}
                    </div>
                    <div>
                      <strong>Tiempo:</strong> {resultado.tiempo_segundos ? `${Math.round(resultado.tiempo_segundos / 60)} min` : '0 min'}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Interpretación Textual Detallada */}
        {interpretacionPersonalizada.interpretaciones && (
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg">
            <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
              <i className="fas fa-file-alt mr-3 text-blue-600"></i>
              Interpretación Detallada
            </h3>

            <div className="space-y-4">
              {interpretacionPersonalizada.interpretaciones.map((interpretacion, index) => (
                <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-blue-100">
                  <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
                    <i className={`fas ${APTITUDES_CONFIG[interpretacion.aptitud]?.icon || 'fa-circle'} mr-2 text-blue-600`}></i>
                    {interpretacion.nombre}
                  </h4>
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {interpretacion.descripcion}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default AnalisisCualitativo;

// ✅ Títulos en negrita aplicados: Descripción, Interpretación del Rendimiento, Implicaciones Académicas, Implicaciones Vocacionales, Interpretación Integrada, Implicaciones Generales, Componentes