import React, { useState, useEffect } from 'react';
import { 
  FaChartBar, 
  FaUsers, 
  FaCoins, 
  FaExclamationTriangle,
  FaBell,
  FaHistory,
  FaPlus,
  FaCheck,
  FaTimes,
  FaEye,
  FaEdit,
  FaTrash,
  FaUserPlus,
  FaUserMinus,
  FaBroom,
  FaDownload,
  FaUpload,
  FaSearch,
  FaFilter,
  FaSync
} from 'react-icons/fa';
import { supabase } from '../../api/supabaseClient';
import PinRechargeRequestsAPI from '../../api/endpoints/pinRechargeRequests';
import PinNotificationsAPI from '../../api/endpoints/pinNotifications';
import CompletePinControlService from '../../services/pin/CompletePinControlService';

const UsageControlPanelComplete = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [requests, setRequests] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [psychologists, setPsychologists] = useState([]);
  const [selectedPsychologists, setSelectedPsychologists] = useState([]);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [showRechargeModal, setShowRechargeModal] = useState(false);
  const [assignmentData, setAssignmentData] = useState({
    psychologistId: '',
    pins: '',
    isUnlimited: false,
    reason: ''
  });

  const [showSubtractModal, setShowSubtractModal] = useState(false);
  const [subtractData, setSubtractData] = useState({
    psychologistId: '',
    pins: ''
  });

  // Estados para búsqueda y filtrado
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all'); // all, active, inactive, no-pins
  const [pinTypeFilter, setPinTypeFilter] = useState('all'); // all, limited, unlimited

  // Estados para paginación
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [requestsPage, setRequestsPage] = useState(1);
  const [requestsPerPage, setRequestsPerPage] = useState(10);

  const [stats, setStats] = useState({
    totalRequests: 0,
    pendingRequests: 0,
    totalNotifications: 0,
    unreadNotifications: 0,
    totalPsychologists: 0,
    totalPinsAssigned: 0,
    totalPinsUsed: 0
  });

  // Pestañas disponibles
  const tabs = [
    {
      id: 'overview',
      name: 'Resumen',
      icon: FaChartBar,
      description: 'Vista general del sistema de pines'
    },
    {
      id: 'psychologists',
      name: 'Gestión de Psicólogos',
      icon: FaUsers,
      description: 'Administrar pines de psicólogos'
    },
    {
      id: 'requests',
      name: 'Solicitudes de Recarga',
      icon: FaCoins,
      description: 'Gestionar solicitudes de recarga de pines'
    },
    {
      id: 'notifications',
      name: 'Centro de Notificaciones',
      icon: FaBell,
      description: 'Gestionar notificaciones del sistema'
    },
    {
      id: 'history',
      name: 'Historial',
      icon: FaHistory,
      description: 'Ver historial de transacciones'
    }
  ];

  // Cargar datos iniciales
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // Cargar psicólogos con información de pines usando el nuevo servicio
      const psychologistsData = await CompletePinControlService.getPsychologistsWithPinInfo();
      setPsychologists(psychologistsData);

      // Cargar solicitudes
      const requestsResult = await PinRechargeRequestsAPI.getRequests({
        limit: 10
      });
      
      if (requestsResult.success) {
        setRequests(requestsResult.data || []);
      }

      // Cargar notificaciones
      const notificationsResult = await PinNotificationsAPI.getUserNotifications(
        '74c8230e-6f01-4b5d-ae72-cf5ac61db33e',
        { limit: 10 }
      );
      
      if (notificationsResult.success) {
        setNotifications(notificationsResult.data || []);
      }

      // Calcular estadísticas
      setStats({
        totalRequests: requestsResult.data?.length || 0,
        pendingRequests: requestsResult.data?.filter(r => r.status === 'pending').length || 0,
        totalNotifications: notificationsResult.data?.length || 0,
        unreadNotifications: notificationsResult.unread_count || 0,
        totalPsychologists: psychologistsData?.length || 0,
        totalPinsAssigned: 0, // Se calculará con datos reales
        totalPinsUsed: 0 // Se calculará con datos reales
      });

    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAssignPins = async () => {
    if (!assignmentData.psychologistId || (!assignmentData.pins && !assignmentData.isUnlimited)) {
      alert('Por favor completa todos los campos requeridos');
      return;
    }

    try {
      setLoading(true);

      // Usar el nuevo servicio para asignar pines realmente
      const result = await CompletePinControlService.assignPins(
        assignmentData.psychologistId,
        parseInt(assignmentData.pins) || 0,
        assignmentData.isUnlimited
      );

      // También crear notificación
      try {
        await PinNotificationsAPI.createPinAssignmentNotification(
          assignmentData.psychologistId,
          parseInt(assignmentData.pins) || 0,
          assignmentData.isUnlimited
        );
      } catch (notifError) {
        console.warn('Error creando notificación:', notifError);
      }

      alert(result.message);
      setShowAssignModal(false);
      setAssignmentData({
        psychologistId: '',
        pins: '',
        isUnlimited: false
      });

      await loadData();

    } catch (error) {
      console.error('Error assigning pins:', error);
      alert('Error al asignar pines: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkAssignPins = async () => {
    if (selectedPsychologists.length === 0) {
      alert('Selecciona al menos un psicólogo');
      return;
    }

    const pins = prompt('¿Cuántos pines asignar a cada psicólogo seleccionado?');
    if (!pins || isNaN(pins) || parseInt(pins) <= 0) {
      alert('Cantidad de pines inválida');
      return;
    }



    try {
      setLoading(true);
      
      // Asignar pines a cada psicólogo seleccionado
      for (const psychId of selectedPsychologists) {
        await PinNotificationsAPI.createPinAssignmentNotification(
          psychId,
          parseInt(pins),
          false
        );
      }

      alert(`Pines asignados a ${selectedPsychologists.length} psicólogos`);
      setSelectedPsychologists([]);
      await loadData();
      
    } catch (error) {
      console.error('Error in bulk assignment:', error);
      alert('Error en asignación masiva');
    } finally {
      setLoading(false);
    }
  };

  const handleSubtractPins = async () => {
    if (!subtractData.psychologistId || !subtractData.pins) {
      alert('Por favor completa todos los campos requeridos');
      return;
    }

    try {
      setLoading(true);

      const result = await CompletePinControlService.subtractPins(
        subtractData.psychologistId,
        parseInt(subtractData.pins)
      );

      alert(result.message);
      setShowSubtractModal(false);
      setSubtractData({
        psychologistId: '',
        pins: ''
      });

      await loadData();

    } catch (error) {
      console.error('Error subtracting pins:', error);
      alert('Error al restar pines: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRemovePsychologist = async (psychologistId, psychologistName) => {
    const confirmed = confirm(`¿Estás seguro de que quieres eliminar completamente el control de pines de ${psychologistName}?`);

    if (!confirmed) return;

    try {
      setLoading(true);

      const result = await CompletePinControlService.removePsychologistPins(psychologistId);

      alert(result.message);
      await loadData();

    } catch (error) {
      console.error('Error removing psychologist pins:', error);
      alert('Error al eliminar psicólogo: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Función para filtrar psicólogos
  const getFilteredPsychologists = () => {
    let filtered = [...psychologists];

    // Filtro por término de búsqueda
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(psy =>
        psy.fullName.toLowerCase().includes(term) ||
        psy.email.toLowerCase().includes(term) ||
        psy.nombre.toLowerCase().includes(term) ||
        psy.apellido.toLowerCase().includes(term)
      );
    }

    // Filtro por estado
    if (statusFilter !== 'all') {
      filtered = filtered.filter(psy => {
        switch (statusFilter) {
          case 'active':
            return psy.hasControl && psy.isActive;
          case 'inactive':
            return psy.hasControl && !psy.isActive;
          case 'no-pins':
            return !psy.hasControl;
          default:
            return true;
        }
      });
    }

    // Filtro por tipo de pines
    if (pinTypeFilter !== 'all') {
      filtered = filtered.filter(psy => {
        switch (pinTypeFilter) {
          case 'limited':
            return psy.hasControl && !psy.isUnlimited;
          case 'unlimited':
            return psy.hasControl && psy.isUnlimited;
          default:
            return true;
        }
      });
    }

    return filtered;
  };

  // Función para limpiar filtros
  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setPinTypeFilter('all');
    setCurrentPage(1); // Reset pagination
  };

  // Efecto para resetear paginación cuando cambian los filtros
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter, pinTypeFilter]);

  // Función para obtener el nombre del psicólogo por ID
  const getPsychologistName = (psychologistId) => {
    const psychologist = psychologists.find(p => p.id === psychologistId);
    return psychologist ? psychologist.fullName : `ID: ${psychologistId.substring(0, 8)}...`;
  };

  // Funciones de paginación para psicólogos
  const getPaginatedPsychologists = () => {
    const filtered = getFilteredPsychologists();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filtered.slice(startIndex, endIndex);
  };

  const getTotalPages = () => {
    return Math.ceil(getFilteredPsychologists().length / itemsPerPage);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
  };

  // Funciones de paginación para solicitudes
  const getPaginatedRequests = () => {
    const startIndex = (requestsPage - 1) * requestsPerPage;
    const endIndex = startIndex + requestsPerPage;
    return requests.slice(startIndex, endIndex);
  };

  const getRequestsTotalPages = () => {
    return Math.ceil(requests.length / requestsPerPage);
  };

  const handleRequestsPageChange = (page) => {
    setRequestsPage(page);
  };

  const handleRequestsPerPageChange = (newRequestsPerPage) => {
    setRequestsPerPage(newRequestsPerPage);
    setRequestsPage(1); // Reset to first page
  };

  // Componente de paginación reutilizable
  const PaginationComponent = ({
    currentPage,
    totalPages,
    onPageChange,
    itemsPerPage,
    onItemsPerPageChange,
    totalItems,
    itemName = "elementos"
  }) => {
    const getPageNumbers = () => {
      const pages = [];
      const maxVisiblePages = 5;

      if (totalPages <= maxVisiblePages) {
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        for (let i = startPage; i <= endPage; i++) {
          pages.push(i);
        }
      }

      return pages;
    };

    if (totalPages <= 1) return null;

    return (
      <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Anterior
          </button>
          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Siguiente
          </button>
        </div>

        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div className="flex items-center space-x-4">
            <p className="text-sm text-gray-700">
              Mostrando{' '}
              <span className="font-medium">{Math.min((currentPage - 1) * itemsPerPage + 1, totalItems)}</span>
              {' '}a{' '}
              <span className="font-medium">{Math.min(currentPage * itemsPerPage, totalItems)}</span>
              {' '}de{' '}
              <span className="font-medium">{totalItems}</span>
              {' '}{itemName}
            </p>

            <select
              value={itemsPerPage}
              onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={5}>5 por página</option>
              <option value={10}>10 por página</option>
              <option value={25}>25 por página</option>
              <option value={50}>50 por página</option>
            </select>
          </div>

          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Anterior</span>
                <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </button>

              {getPageNumbers().map((page) => (
                <button
                  key={page}
                  onClick={() => onPageChange(page)}
                  className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                    page === currentPage
                      ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  {page}
                </button>
              ))}

              <button
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Siguiente</span>
                <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    );
  };

  const handleApproveRequest = async (requestId) => {
    try {
      setLoading(true);
      const result = await PinRechargeRequestsAPI.processRequest(requestId, {
        action: 'approve',
        admin_id: 'admin-test-id',
        admin_notes: 'Aprobado desde panel de control'
      });

      if (result.success) {
        await loadData();
        alert('Solicitud aprobada exitosamente');
      } else {
        alert('Error al aprobar solicitud: ' + result.error);
      }
    } catch (error) {
      console.error('Error approving request:', error);
      alert('Error al aprobar solicitud');
    } finally {
      setLoading(false);
    }
  };

  const handleRejectRequest = async (requestId) => {
    try {
      setLoading(true);
      const result = await PinRechargeRequestsAPI.processRequest(requestId, {
        action: 'reject',
        admin_id: 'admin-test-id',
        admin_notes: 'Rechazado desde panel de control'
      });

      if (result.success) {
        await loadData();
        alert('Solicitud rechazada');
      } else {
        alert('Error al rechazar solicitud: ' + result.error);
      }
    } catch (error) {
      console.error('Error rejecting request:', error);
      alert('Error al rechazar solicitud');
    } finally {
      setLoading(false);
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">Total Psicólogos</p>
              <p className="text-3xl font-bold text-gray-900">{stats.totalPsychologists}</p>
            </div>
            <FaUsers className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">Solicitudes Pendientes</p>
              <p className="text-3xl font-bold text-orange-600">{stats.pendingRequests}</p>
            </div>
            <FaExclamationTriangle className="w-8 h-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">Total Notificaciones</p>
              <p className="text-3xl font-bold text-gray-900">{stats.totalNotifications}</p>
            </div>
            <FaBell className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">No Leídas</p>
              <p className="text-3xl font-bold text-red-600">{stats.unreadNotifications}</p>
            </div>
            <FaBell className="w-8 h-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Acciones rápidas */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Acciones Rápidas</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => setShowAssignModal(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg flex items-center justify-center space-x-2 transition-colors"
          >
            <FaUserPlus className="w-5 h-5" />
            <span>Asignar Pines</span>
          </button>
          
          <button
            onClick={() => setActiveTab('requests')}
            className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-3 rounded-lg flex items-center justify-center space-x-2 transition-colors"
          >
            <FaCoins className="w-5 h-5" />
            <span>Ver Solicitudes</span>
          </button>
          
          <button
            onClick={loadData}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg flex items-center justify-center space-x-2 transition-colors"
          >
            <FaSync className="w-5 h-5" />
            <span>Actualizar Datos</span>
          </button>
        </div>
      </div>

      {/* Estado del sistema eliminado - No es necesario según solicitud del usuario */}
    </div>
  );

  const renderPsychologists = () => (
    <div className="space-y-6">
      {/* Controles superiores */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Gestión de Psicólogos</h3>
            <p className="text-sm text-gray-600 mt-1">Administra pines y permisos de psicólogos</p>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => setShowAssignModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              <FaUserPlus className="w-4 h-4" />
              <span>Asignar Pines</span>
            </button>

            {selectedPsychologists.length > 0 && (
              <button
                onClick={handleBulkAssignPins}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <FaCoins className="w-4 h-4" />
                <span>Asignar a Seleccionados ({selectedPsychologists.length})</span>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Barra de búsqueda y filtros */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          {/* Búsqueda */}
          <div className="flex-1 max-w-md">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Buscar por nombre, apellido o email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Filtros */}
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            {/* Filtro por estado */}
            <div className="flex items-center space-x-2">
              <FaFilter className="text-gray-400 w-4 h-4" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="all">Todos los estados</option>
                <option value="active">Activos</option>
                <option value="inactive">Inactivos</option>
                <option value="no-pins">Sin pines</option>
              </select>
            </div>

            {/* Filtro por tipo de pines */}
            <div className="flex items-center space-x-2">
              <FaCoins className="text-gray-400 w-4 h-4" />
              <select
                value={pinTypeFilter}
                onChange={(e) => setPinTypeFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="all">Todos los tipos</option>
                <option value="limited">Pines limitados</option>
                <option value="unlimited">Pines ilimitados</option>
              </select>
            </div>

            {/* Botón limpiar filtros */}
            <button
              onClick={clearFilters}
              className="px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors text-sm flex items-center space-x-1"
              title="Limpiar filtros"
            >
              <FaBroom className="w-4 h-4" />
              <span>Limpiar</span>
            </button>
          </div>
        </div>

        {/* Contador de resultados */}
        <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
          <span>
            Mostrando {getFilteredPsychologists().length} de {psychologists.length} psicólogos
          </span>
          {(searchTerm || statusFilter !== 'all' || pinTypeFilter !== 'all') && (
            <span className="text-blue-600">
              Filtros activos
            </span>
          )}
        </div>
      </div>

      {/* Tabla de psicólogos */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={getFilteredPsychologists().length > 0 && getFilteredPsychologists().every(p => selectedPsychologists.includes(p.id))}
                    onChange={(e) => {
                      if (e.target.checked) {
                        const filteredIds = getFilteredPsychologists().map(p => p.id);
                        setSelectedPsychologists([...new Set([...selectedPsychologists, ...filteredIds])]);
                      } else {
                        const filteredIds = getFilteredPsychologists().map(p => p.id);
                        setSelectedPsychologists(selectedPsychologists.filter(id => !filteredIds.includes(id)));
                      }
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Psicólogo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pines Cargados
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pines Gastados
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pines Restantes
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {getPaginatedPsychologists().map((psychologist) => (
                <tr key={psychologist.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedPsychologists.includes(psychologist.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedPsychologists([...selectedPsychologists, psychologist.id]);
                        } else {
                          setSelectedPsychologists(selectedPsychologists.filter(id => id !== psychologist.id));
                        }
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {psychologist.nombre} {psychologist.apellido}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {psychologist.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <FaCoins className="w-4 h-4 text-yellow-500 mr-1" />
                      {psychologist.isUnlimited ? '∞' : psychologist.totalPins}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <FaCoins className="w-4 h-4 text-red-500 mr-1" />
                      {psychologist.usedPins}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <FaCoins className="w-4 h-4 text-green-500 mr-1" />
                      {psychologist.isUnlimited ? '∞' : psychologist.remainingPins}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      psychologist.statusColor === 'green' ? 'bg-green-100 text-green-800' :
                      psychologist.statusColor === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {psychologist.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setAssignmentData({
                            ...assignmentData,
                            psychologistId: psychologist.id
                          });
                          setShowAssignModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                        title="Asignar Pines"
                      >
                        <FaCoins className="w-4 h-4" />
                      </button>

                      {psychologist.hasControl && (
                        <>
                          <button
                            onClick={() => {
                              setSubtractData({
                                psychologistId: psychologist.id,
                                pins: ''
                              });
                              setShowSubtractModal(true);
                            }}
                            className="text-orange-600 hover:text-orange-900"
                            title="Restar Pines"
                          >
                            <FaUserMinus className="w-4 h-4" />
                          </button>

                          <button
                            onClick={() => handleRemovePsychologist(
                              psychologist.id,
                              psychologist.fullName
                            )}
                            className="text-red-600 hover:text-red-900"
                            title="Eliminar Control de Pines"
                          >
                            <FaTrash className="w-4 h-4" />
                          </button>
                        </>
                      )}

                      <button
                        className="text-green-600 hover:text-green-900"
                        title="Ver Detalles"
                      >
                        <FaEye className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}

              {/* Mensaje cuando no hay resultados */}
              {getPaginatedPsychologists().length === 0 && getFilteredPsychologists().length === 0 && (
                <tr>
                  <td colSpan="7" className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center space-y-3">
                      <FaSearch className="w-12 h-12 text-gray-300" />
                      <div className="text-gray-500">
                        <p className="text-lg font-medium">No se encontraron psicólogos</p>
                        <p className="text-sm">
                          {searchTerm || statusFilter !== 'all' || pinTypeFilter !== 'all'
                            ? 'Intenta ajustar los filtros de búsqueda'
                            : 'No hay psicólogos registrados en el sistema'
                          }
                        </p>
                      </div>
                      {(searchTerm || statusFilter !== 'all' || pinTypeFilter !== 'all') && (
                        <button
                          onClick={clearFilters}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                        >
                          Limpiar filtros
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Paginación para psicólogos */}
        <PaginationComponent
          currentPage={currentPage}
          totalPages={getTotalPages()}
          onPageChange={handlePageChange}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={handleItemsPerPageChange}
          totalItems={getFilteredPsychologists().length}
          itemName="psicólogos"
        />
      </div>
    </div>
  );

  const renderRequests = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Solicitudes de Recarga</h3>
          <p className="text-sm text-gray-600 mt-1">Gestiona las solicitudes de recarga de pines</p>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Psicólogo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pines Solicitados
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Urgencia
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fecha
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {getPaginatedRequests().map((request) => (
                <tr key={request.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {getPsychologistName(request.psychologist_id)}
                    </div>
                    <div className="text-sm text-gray-500">
                      {request.reason?.substring(0, 50)}{request.reason?.length > 50 ? '...' : ''}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {request.requested_pins}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      request.urgency === 'urgent' ? 'bg-red-100 text-red-800' :
                      request.urgency === 'high' ? 'bg-orange-100 text-orange-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {request.urgency}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      request.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      request.status === 'approved' ? 'bg-green-100 text-green-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {request.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(request.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {request.status === 'pending' && (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleApproveRequest(request.id)}
                          disabled={loading}
                          className="text-green-600 hover:text-green-900 disabled:opacity-50"
                          title="Aprobar"
                        >
                          <FaCheck className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleRejectRequest(request.id)}
                          disabled={loading}
                          className="text-red-600 hover:text-red-900 disabled:opacity-50"
                          title="Rechazar"
                        >
                          <FaTimes className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Paginación para solicitudes */}
        <PaginationComponent
          currentPage={requestsPage}
          totalPages={getRequestsTotalPages()}
          onPageChange={handleRequestsPageChange}
          itemsPerPage={requestsPerPage}
          onItemsPerPageChange={handleRequestsPerPageChange}
          totalItems={requests.length}
          itemName="solicitudes"
        />
      </div>
    </div>
  );

  const renderNotifications = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Centro de Notificaciones</h3>
          <p className="text-sm text-gray-600 mt-1">Gestiona las notificaciones del sistema</p>
        </div>

        <div className="p-6">
          {notifications.length === 0 ? (
            <div className="text-center py-8">
              <FaBell className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No hay notificaciones</h3>
              <p className="mt-1 text-sm text-gray-500">
                Las notificaciones aparecerán aquí cuando se generen.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 rounded-lg border ${
                    notification.read ? 'bg-gray-50 border-gray-200' : 'bg-blue-50 border-blue-200'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">
                        {notification.title}
                      </h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        {new Date(notification.created_at).toLocaleString()}
                      </p>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      notification.severity === 'success' ? 'bg-green-100 text-green-800' :
                      notification.severity === 'warning' ? 'bg-orange-100 text-orange-800' :
                      notification.severity === 'error' ? 'bg-red-100 text-red-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {notification.severity}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderHistory = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Historial de Transacciones</h3>
        <div className="text-center py-8">
          <FaHistory className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Historial en Desarrollo</h3>
          <p className="mt-1 text-sm text-gray-500">
            Esta funcionalidad se implementará próximamente.
          </p>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'psychologists':
        return renderPsychologists();
      case 'requests':
        return renderRequests();
      case 'notifications':
        return renderNotifications();
      case 'history':
        return renderHistory();
      default:
        return <div>Pestaña no encontrada</div>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Navegación de pestañas */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Contenido de la pestaña activa */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        renderTabContent()
      )}

      {/* Modal de Asignación de Pines */}
      {showAssignModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Asignar Pines</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Psicólogo
                  </label>
                  <select
                    value={assignmentData.psychologistId}
                    onChange={(e) => setAssignmentData({...assignmentData, psychologistId: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Seleccionar psicólogo</option>
                    {psychologists.map((psychologist) => (
                      <option key={psychologist.id} value={psychologist.id}>
                        {psychologist.nombre} {psychologist.apellido}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cantidad de Pines
                  </label>
                  <input
                    type="number"
                    value={assignmentData.pins}
                    onChange={(e) => setAssignmentData({...assignmentData, pins: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Ej: 50"
                    min="1"
                    max="1000"
                  />
                </div>

                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={assignmentData.isUnlimited}
                      onChange={(e) => setAssignmentData({...assignmentData, isUnlimited: e.target.checked})}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Pines ilimitados</span>
                  </label>
                </div>


              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowAssignModal(false);
                    setAssignmentData({
                      psychologistId: '',
                      pins: '',
                      isUnlimited: false,
                      reason: ''
                    });
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleAssignPins}
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  {loading ? 'Asignando...' : 'Asignar Pines'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Restar Pines */}
      {showSubtractModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Restar Pines</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Psicólogo
                  </label>
                  <select
                    value={subtractData.psychologistId}
                    onChange={(e) => setSubtractData({...subtractData, psychologistId: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Seleccionar psicólogo...</option>
                    {psychologists.filter(p => p.hasControl && !p.isUnlimited).map((psychologist) => (
                      <option key={psychologist.id} value={psychologist.id}>
                        {psychologist.fullName} - {psychologist.remainingPins} pines disponibles
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cantidad de pines a restar
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={subtractData.pins}
                    onChange={(e) => setSubtractData({...subtractData, pins: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Ej: 10"
                  />
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                  <div className="flex">
                    <FaExclamationTriangle className="w-5 h-5 text-yellow-400 mr-2 mt-0.5" />
                    <div className="text-sm text-yellow-700">
                      <p className="font-medium">Advertencia:</p>
                      <p>Esta acción restará pines del total asignado. Si los pines restados hacen que el total sea menor que los ya consumidos, se ajustarán automáticamente.</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowSubtractModal(false);
                    setSubtractData({
                      psychologistId: '',
                      pins: ''
                    });
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleSubtractPins}
                  disabled={loading}
                  className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 transition-colors"
                >
                  {loading ? 'Restando...' : 'Restar Pines'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UsageControlPanelComplete;
