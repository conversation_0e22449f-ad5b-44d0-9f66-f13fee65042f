-- Agregar columna updated_at a la tabla usuarios
-- EJEC<PERSON>AR ESTE SCRIPT EN EL EDITOR SQL DE SUPABASE

-- =====================================================
-- AGREGAR COLUMNA UPDATED_AT
-- =====================================================

-- Verificar si la columna ya existe
DO $$
DECLARE
  column_exists BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'usuarios' 
    AND column_name = 'updated_at'
  ) INTO column_exists;
  
  IF NOT column_exists THEN
    -- Agregar la columna updated_at
    ALTER TABLE public.usuarios 
    ADD COLUMN updated_at TIMESTAMPTZ DEFAULT now();
    
    -- Actualizar registros existentes con la fecha actual
    UPDATE public.usuarios 
    SET updated_at = COALESCE(ultimo_acceso, fecha_creacion, now())
    WHERE updated_at IS NULL;
    
    RAISE NOTICE 'Columna updated_at agregada exitosamente';
  ELSE
    RAISE NOTICE 'La columna updated_at ya existe';
  END IF;
END $$;

-- =====================================================
-- CREAR TRIGGER PARA ACTUALIZAR AUTOMÁTICAMENTE
-- =====================================================

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Crear trigger si no existe
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'update_usuarios_updated_at'
  ) THEN
    CREATE TRIGGER update_usuarios_updated_at
      BEFORE UPDATE ON public.usuarios
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
    
    RAISE NOTICE 'Trigger update_usuarios_updated_at creado exitosamente';
  ELSE
    RAISE NOTICE 'El trigger update_usuarios_updated_at ya existe';
  END IF;
END $$;

-- =====================================================
-- VERIFICACIÓN
-- =====================================================

-- Mostrar estructura actualizada de la tabla
SELECT 
  'ESTRUCTURA ACTUALIZADA DE LA TABLA USUARIOS:' as info,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'usuarios'
ORDER BY ordinal_position;

-- Mostrar algunos registros con la nueva columna
SELECT 
  'REGISTROS CON UPDATED_AT:' as info,
  nombre,
  apellido,
  fecha_creacion,
  updated_at
FROM public.usuarios
LIMIT 3;
