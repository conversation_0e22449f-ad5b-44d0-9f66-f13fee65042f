import { PIN_CONSTANTS } from './PinConstants.js';
import { PinLogger } from './PinLogger.js';
import { supabase } from '../../api/supabaseClient.js';
import { toast } from 'react-toastify';

/**
 * Servicio avanzado de validación de pines
 * Proporciona validaciones robustas antes de generar informes
 */
export class PinValidationService {
  
  /**
   * Valida si un psicólogo puede generar informes
   * @param {string} psychologistId - ID del psicólogo
   * @param {number} requiredPins - Número de pines requeridos (default: 1)
   * @param {Object} currentUser - Usuario actual (opcional, para verificar rol de admin)
   * @returns {Promise<Object>} Resultado de la validación
   */
  static async validateReportGeneration(psychologistId, requiredPins = 1, currentUser = null) {
    try {
      PinLogger.logInfo('Validating report generation', { psychologistId, requiredPins, userRole: currentUser?.rol });

      // Validar parámetros de entrada
      if (!psychologistId || typeof psychologistId !== 'string') {
        return {
          isValid: false,
          canProceed: false,
          reason: 'INVALID_PSYCHOLOGIST_ID',
          message: 'ID de psicólogo inválido',
          userMessage: 'Error en la identificación del psicólogo',
          severity: 'error'
        };
      }

      if (!requiredPins || requiredPins < 1 || !Number.isInteger(requiredPins)) {
        return {
          isValid: false,
          canProceed: false,
          reason: 'INVALID_PIN_REQUIREMENT',
          message: 'Número de pines requeridos inválido',
          userMessage: 'Error en la configuración del sistema',
          severity: 'error'
        };
      }

      // Verificar si el usuario actual es administrador (bypass del sistema de pines)
      if (currentUser && (currentUser.rol === 'administrador' || currentUser.rol === 'admin')) {
        console.log('✅ [PinValidation] Usuario administrador detectado - bypass de validación de pines');
        return {
          isValid: true,
          canProceed: true,
          reason: 'ADMIN_BYPASS',
          message: 'Acceso de administrador - sin restricción de pines',
          userMessage: 'Puede generar informes (Acceso de administrador)',
          severity: 'success',
          isAdminBypass: true,
          requiredPins,
          remainingPins: null
        };
      }

      // Obtener estado actual de pines directamente de la base de datos
      console.log('🔍 [PinValidation] Verificando pines para psicólogo:', psychologistId);

      const { data: controlData, error: controlError } = await supabase
        .from('psychologist_usage_control')
        .select('*')
        .eq('psychologist_id', psychologistId)
        .eq('is_active', true)
        .maybeSingle();

      if (controlError) {
        console.error('❌ [PinValidation] Error consultando control:', controlError);
        throw new Error('Error al consultar datos de control de pines');
      }

      console.log('📊 [PinValidation] Datos de control encontrados:', controlData);

      let pinStatus;
      if (!controlData) {
        console.log('⚠️ [PinValidation] No hay datos de control para el psicólogo');
        pinStatus = {
          canUse: false,
          reason: 'No pins assigned',
          remainingPins: 0,
          isUnlimited: false
        };
      } else if (controlData.is_unlimited) {
        console.log('✅ [PinValidation] Psicólogo tiene plan ilimitado');
        pinStatus = {
          canUse: true,
          reason: 'Unlimited plan',
          remainingPins: null,
          isUnlimited: true
        };
      } else {
        const remainingPins = controlData.total_uses - controlData.used_uses;
        console.log('📊 [PinValidation] Cálculo de pines:', {
          total: controlData.total_uses,
          used: controlData.used_uses,
          remaining: remainingPins
        });

        pinStatus = {
          canUse: remainingPins > 0,
          reason: remainingPins > 0 ? 'Pins available' : 'No pins available',
          remainingPins,
          isUnlimited: false,
          totalPins: controlData.total_uses,
          usedPins: controlData.used_uses
        };
      }

      // Validar si el psicólogo existe en el sistema
      if (!pinStatus) {
        return {
          isValid: false,
          canProceed: false,
          reason: 'PSYCHOLOGIST_NOT_FOUND',
          message: 'Psicólogo no encontrado en el sistema',
          userMessage: 'No se encontró información del psicólogo en el sistema',
          severity: 'error'
        };
      }

      // Validar plan ilimitado
      if (pinStatus.isUnlimited) {
        return {
          isValid: true,
          canProceed: true,
          reason: 'UNLIMITED_PLAN',
          message: 'Plan ilimitado activo',
          userMessage: 'Puede generar informes (Plan ilimitado)',
          severity: 'success',
          pinStatus,
          requiredPins,
          isUnlimited: true
        };
      }

      // Validar pines disponibles
      const remainingPins = pinStatus.remainingPins || 0;
      
      if (remainingPins < requiredPins) {
        const shortfall = requiredPins - remainingPins;
        return {
          isValid: false,
          canProceed: false,
          reason: 'INSUFFICIENT_PINS',
          message: `Pines insuficientes. Disponibles: ${remainingPins}, Requeridos: ${requiredPins}`,
          userMessage: `No cuenta con suficientes pines. Disponibles: ${remainingPins}. Requeridos: ${requiredPins}. Faltan: ${shortfall}`,
          severity: 'error',
          pinStatus,
          requiredPins,
          remainingPins,
          shortfall,
          isUnlimited: false
        };
      }

      // Validar si quedan pocos pines después de la operación
      const pinsAfterOperation = remainingPins - requiredPins;
      let severity = 'success';
      let warningMessage = '';

      if (pinsAfterOperation <= PIN_CONSTANTS.THRESHOLDS.CRITICAL_PIN_WARNING) {
        severity = 'critical';
        warningMessage = `CRÍTICO: Después de esta operación quedarán solo ${pinsAfterOperation} pines`;
      } else if (pinsAfterOperation <= PIN_CONSTANTS.THRESHOLDS.LOW_PIN_WARNING) {
        severity = 'warning';
        warningMessage = `ADVERTENCIA: Después de esta operación quedarán solo ${pinsAfterOperation} pines`;
      }

      // Validación exitosa
      return {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE',
        message: `Validación exitosa. Pines disponibles: ${remainingPins}`,
        userMessage: warningMessage || `Puede generar el informe. Pines disponibles: ${remainingPins}`,
        severity,
        pinStatus,
        requiredPins,
        remainingPins,
        pinsAfterOperation,
        isUnlimited: false,
        hasWarning: !!warningMessage
      };

    } catch (error) {
      PinLogger.logError('Error in validateReportGeneration', error);
      return {
        isValid: false,
        canProceed: false,
        reason: 'VALIDATION_ERROR',
        message: `Error durante la validación: ${error.message}`,
        userMessage: 'Error al verificar los permisos. Intente nuevamente.',
        severity: 'error',
        error: error.message
      };
    }
  }

  /**
   * Valida múltiples informes (generación en lote)
   * @param {string} psychologistId - ID del psicólogo
   * @param {Array} patientIds - Array de IDs de pacientes
   * @returns {Promise<Object>} Resultado de la validación en lote
   */
  static async validateBatchReportGeneration(psychologistId, patientIds) {
    try {
      const requiredPins = patientIds.length;
      PinLogger.logInfo('Validating batch report generation', { 
        psychologistId, 
        patientCount: patientIds.length,
        requiredPins 
      });

      // Validar parámetros
      if (!Array.isArray(patientIds) || patientIds.length === 0) {
        return {
          isValid: false,
          canProceed: false,
          reason: 'INVALID_PATIENT_LIST',
          message: 'Lista de pacientes inválida',
          userMessage: 'No se han seleccionado pacientes válidos',
          severity: 'error'
        };
      }

      // Usar validación individual con el número total de pines
      const validation = await this.validateReportGeneration(psychologistId, requiredPins);
      
      // Agregar información específica del lote
      return {
        ...validation,
        batchInfo: {
          patientCount: patientIds.length,
          requiredPins,
          patientIds
        }
      };

    } catch (error) {
      PinLogger.logError('Error in validateBatchReportGeneration', error);
      return {
        isValid: false,
        canProceed: false,
        reason: 'BATCH_VALIDATION_ERROR',
        message: `Error durante la validación en lote: ${error.message}`,
        userMessage: 'Error al verificar los permisos para generación en lote',
        severity: 'error',
        error: error.message
      };
    }
  }

  /**
   * Muestra alertas apropiadas basadas en el resultado de validación
   * @param {Object} validationResult - Resultado de la validación
   * @param {boolean} showToast - Si mostrar toast notifications
   */
  static displayValidationAlerts(validationResult, showToast = true) {
    if (!validationResult) return;

    const { severity, userMessage, hasWarning } = validationResult;

    if (!showToast) return validationResult;

    switch (severity) {
      case 'error':
        toast.error(userMessage);
        break;
      case 'critical':
        toast.error(userMessage, { autoClose: 8000 });
        break;
      case 'warning':
        toast.warning(userMessage, { autoClose: 6000 });
        break;
      case 'success':
        if (hasWarning) {
          toast.warning(userMessage, { autoClose: 5000 });
        } else {
          toast.success(userMessage);
        }
        break;
      default:
        toast.info(userMessage);
    }

    return validationResult;
  }

  /**
   * Crea un resumen de validación para mostrar en UI
   * @param {Object} validationResult - Resultado de la validación
   * @returns {Object} Resumen formateado para UI
   */
  static createValidationSummary(validationResult) {
    if (!validationResult) return null;

    const {
      isValid,
      canProceed,
      reason,
      userMessage,
      severity,
      remainingPins,
      requiredPins,
      pinsAfterOperation,
      isUnlimited,
      batchInfo
    } = validationResult;

    return {
      status: canProceed ? 'allowed' : 'blocked',
      title: canProceed ? 'Operación Permitida' : 'Operación Bloqueada',
      message: userMessage,
      severity,
      details: {
        isUnlimited,
        currentPins: remainingPins,
        requiredPins,
        pinsAfter: pinsAfterOperation,
        isBatch: !!batchInfo,
        batchSize: batchInfo?.patientCount
      },
      actionRequired: !canProceed,
      suggestions: this._generateSuggestions(validationResult)
    };
  }

  /**
   * Genera sugerencias basadas en el resultado de validación
   * @private
   */
  static _generateSuggestions(validationResult) {
    const { reason, shortfall, remainingPins } = validationResult;
    const suggestions = [];

    switch (reason) {
      case 'INSUFFICIENT_PINS':
        suggestions.push(`Necesita ${shortfall} pines adicionales`);
        suggestions.push('Contacte al administrador para obtener más pines');
        if (remainingPins > 0) {
          suggestions.push(`Puede generar ${remainingPins} informes con los pines actuales`);
        }
        break;
      case 'PSYCHOLOGIST_NOT_FOUND':
        suggestions.push('Verifique que su cuenta esté correctamente configurada');
        suggestions.push('Contacte al administrador del sistema');
        break;
      default:
        if (validationResult.severity === 'warning' || validationResult.severity === 'critical') {
          suggestions.push('Considere recargar pines antes de que se agoten');
        }
    }

    return suggestions;
  }
}

export default PinValidationService;
