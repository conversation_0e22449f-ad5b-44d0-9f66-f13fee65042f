import { useState, useEffect, useMemo } from 'react';
import { FILTER_OPTIONS, SEARCH_CONFIG } from '../constants/userManagement';

/**
 * Hook personalizado para manejar filtros de usuarios
 * Incluye debouncing para la búsqueda
 */
export const useUserFilters = () => {
  // Estados de filtros
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState(FILTER_OPTIONS.ALL);
  const [statusFilter, setStatusFilter] = useState(FILTER_OPTIONS.ALL);

  // Debouncing para la búsqueda
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, SEARCH_CONFIG.DEBOUNCE_DELAY);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Función para limpiar todos los filtros
  const clearFilters = () => {
    setSearchTerm('');
    setDebouncedSearchTerm('');
    setRoleFilter(FILTER_OPTIONS.ALL);
    setStatusFilter(FILTER_OPTIONS.ALL);
  };

  // Verificar si hay filtros activos
  const hasActiveFilters = useMemo(() => {
    return debouncedSearchTerm.trim() !== '' || 
           roleFilter !== FILTER_OPTIONS.ALL || 
           statusFilter !== FILTER_OPTIONS.ALL;
  }, [debouncedSearchTerm, roleFilter, statusFilter]);

  // Construir objeto de filtros para la consulta
  const buildFilters = useMemo(() => {
    const filters = {};

    // Filtro de búsqueda
    if (debouncedSearchTerm.trim() && debouncedSearchTerm.length >= SEARCH_CONFIG.MIN_SEARCH_LENGTH) {
      filters.search = debouncedSearchTerm.trim();
    }

    // Filtro de rol
    if (roleFilter !== FILTER_OPTIONS.ALL) {
      filters.rol = roleFilter;
    }

    // Filtro de estado
    if (statusFilter !== FILTER_OPTIONS.ALL) {
      filters.activo = statusFilter === FILTER_OPTIONS.ACTIVE;
    }

    return filters;
  }, [debouncedSearchTerm, roleFilter, statusFilter]);

  return {
    // Estados
    searchTerm,
    debouncedSearchTerm,
    roleFilter,
    statusFilter,
    
    // Setters
    setSearchTerm,
    setRoleFilter,
    setStatusFilter,
    
    // Utilidades
    clearFilters,
    hasActiveFilters,
    buildFilters,
    
    // Estado de búsqueda
    isSearching: searchTerm !== debouncedSearchTerm
  };
};
