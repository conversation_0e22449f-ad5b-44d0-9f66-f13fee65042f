/**
 * Interpretaciones cualitativas mejoradas para las aptitudes del BAT-7
 * ACTUALIZADO: Ahora usa interpretaciones profesionales completas y mejoradas
 * 
 * Estructura: aptitud -> nivel -> {rendimiento, academico, vocacional}
 * Niveles: 1 (muy bajo) a 7 (muy alto)
 * 
 * IMPORTANTE: Este archivo ahora importa las interpretaciones consolidadas
 * que incluyen textos profesionales para todos los niveles de todas las aptitudes
 */

import { INTERPRETACIONES_OFICIALES_CONSOLIDADAS, obtenerInterpretacionOficial } from './interpretacionesOficialesConsolidadas.js';

// Interpretaciones oficiales para BAT-7 - AH<PERSON>A CON TEXTOS OFICIALES EXACTOS
export const INTERPRETACIONES_HARDCODED = {
  // Función para obtener nivel por percentil - OFICIAL
  obtenerNivelPorPercentil: INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerNivelPorPercentil,

  // Interpretaciones por aptitud y nivel - AHORA CON TEXTOS OFICIALES EXACTOS
  interpretaciones: INTERPRETACIONES_OFICIALES_CONSOLIDADAS.interpretaciones,

  // Función principal para obtener interpretación - OFICIAL
  obtenerInterpretacionAptitud: INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerInterpretacionAptitud,

  // Funciones adicionales disponibles
  obtenerTodasInterpretacionesAptitud: INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerTodasInterpretacionesAptitud,
  existeInterpretacion: INTERPRETACIONES_OFICIALES_CONSOLIDADAS.existeInterpretacion,
  obtenerResumenCompletitud: INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerResumenCompletitud,
  obtenerInterpretacionesMultiples: INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerInterpretacionesMultiples,

  // NOTA: Las interpretaciones anteriores han sido completamente reemplazadas
  // Ahora todas las aptitudes (V, E, A, R, N, M, O) tienen interpretaciones OFICIALES
  // completas para todos los niveles (1-7) copiadas a pie de letra del documento oficial:
  // - Rendimiento: Descripción oficial del nivel de habilidad
  // - Académico: Implicaciones académicas oficiales
  // - Vocacional: Orientación vocacional oficial

  // Las interpretaciones están disponibles a través de INTERPRETACIONES_OFICIALES_CONSOLIDADAS
};

// Función de compatibilidad para mantener la interfaz existente
export const obtenerInterpretacionCualitativa = obtenerInterpretacionOficial;

// Exportar las interpretaciones oficiales consolidadas para uso directo
export { INTERPRETACIONES_OFICIALES_CONSOLIDADAS };

// Función para obtener interpretación específica (mantiene compatibilidad)
export const obtenerInterpretacion = (aptitudCodigo, percentil) => {
  return INTERPRETACIONES_HARDCODED.obtenerInterpretacionAptitud(aptitudCodigo, percentil);
};

// Función para verificar completitud de interpretaciones
export const verificarCompletitud = () => {
  const resumen = INTERPRETACIONES_HARDCODED.obtenerResumenCompletitud();
  console.log('📊 Resumen de Interpretaciones Disponibles:');
  
  Object.keys(resumen).forEach(aptitud => {
    const info = resumen[aptitud];
    console.log(`${aptitud}: ${info.disponibles}/${info.total} (${info.porcentaje}%) - Faltantes: ${info.faltantes.join(', ') || 'Ninguno'}`);
  });
  
  return resumen;
};

export default INTERPRETACIONES_HARDCODED;
