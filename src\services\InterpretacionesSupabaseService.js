/**
 * @file InterpretacionesSupabaseService.js
 * @description Servicio para obtener interpretaciones oficiales desde Supabase
 * Reemplaza las interpretaciones hardcodeadas con datos oficiales de la base de datos
 */

import supabase from '../api/supabaseClient';
import { INTERPRETACIONES_OFICIALES_CONSOLIDADAS } from '../utils/interpretacionesOficialesConsolidadas.js';

class InterpretacionesSupabaseService {
  /**
   * Obtener interpretación oficial por aptitud y percentil desde Supabase
   * @param {string} aptitudCodigo - Código de la aptitud (V, E, A, R, N, M, O)
   * @param {number} percentil - Percentil obtenido (0-100)
   * @returns {Promise<Object>} Interpretación oficial
   */
  static async obtenerInterpretacionOficial(aptitudCodigo, percentil) {
    try {
      console.log(`🔍 [InterpretacionesSupabaseService] Obteniendo interpretación desde Supabase para ${aptitudCodigo}-${percentil}`);

      // Determinar el nivel basado en el percentil
      const nivel = this.obtenerNivelPorPercentil(percentil);

      // Obtener interpretación directamente de la tabla
      const { data, error } = await supabase
        .from('interpretaciones_oficiales')
        .select('*')
        .eq('aptitud_codigo', aptitudCodigo)
        .eq('nivel_id', nivel.id)
        .eq('es_oficial', true)
        .single();

      if (error) {
        console.error('Error obteniendo interpretación desde Supabase:', error);
        // Fallback a interpretaciones locales
        return this.obtenerInterpretacionLocal(aptitudCodigo, percentil);
      }

      if (!data) {
        console.warn(`No se encontró interpretación para ${aptitudCodigo}-${percentil} (nivel ${nivel.id})`);
        // Fallback a interpretaciones locales
        return this.obtenerInterpretacionLocal(aptitudCodigo, percentil);
      }

      console.log(`✅ [InterpretacionesSupabaseService] Interpretación encontrada en Supabase para ${aptitudCodigo}-${percentil}`);

      return {
        aptitud_codigo: data.aptitud_codigo,
        aptitud_nombre: this.getNombreAptitud(data.aptitud_codigo),
        nivel_id: data.nivel_id,
        nivel_nombre: nivel.nombre,
        percentil_rango: nivel.rango,
        percentil_valor: percentil,
        rendimiento: data.rendimiento,
        academico: data.academico,
        vocacional: data.vocacional,
        fuente: 'Supabase - Oficial'
      };

    } catch (error) {
      console.error('Error en obtenerInterpretacionOficial:', error);
      // Fallback a interpretaciones locales
      return this.obtenerInterpretacionLocal(aptitudCodigo, percentil);
    }
  }

  /**
   * Obtener todas las interpretaciones de una aptitud desde Supabase
   * @param {string} aptitudCodigo - Código de la aptitud
   * @returns {Promise<Array>} Array de interpretaciones por nivel
   */
  static async obtenerTodasInterpretacionesAptitud(aptitudCodigo) {
    try {
      const { data, error } = await supabase
        .from('interpretaciones_oficiales')
        .select(`
          *,
          niveles_percentil!nivel_id(nombre, rango_descripcion),
          aptitudes!aptitud_codigo(nombre)
        `)
        .eq('aptitud_codigo', aptitudCodigo)
        .eq('es_oficial', true)
        .order('nivel_id');

      if (error) {
        console.error('Error obteniendo todas las interpretaciones:', error);
        return this.obtenerTodasInterpretacionesLocal(aptitudCodigo);
      }

      return data.map(item => ({
        nivel_id: item.nivel_id,
        nivel_nombre: item.niveles_percentil.nombre,
        percentil_rango: item.niveles_percentil.rango_descripcion,
        rendimiento: item.rendimiento,
        academico: item.academico,
        vocacional: item.vocacional,
        fuente: 'Supabase - Oficial'
      }));

    } catch (error) {
      console.error('Error en obtenerTodasInterpretacionesAptitud:', error);
      return this.obtenerTodasInterpretacionesLocal(aptitudCodigo);
    }
  }

  /**
   * Obtener interpretaciones múltiples desde Supabase
   * @param {Array} resultados - Array de objetos con {aptitud, percentil}
   * @returns {Promise<Array>} Array de interpretaciones
   */
  static async obtenerInterpretacionesMultiples(resultados) {
    try {
      const interpretaciones = await Promise.all(
        resultados.map(async (resultado) => {
          const interpretacion = await this.obtenerInterpretacionOficial(
            resultado.aptitud, 
            resultado.percentil
          );
          
          return {
            aptitud: resultado.aptitud,
            percentil: resultado.percentil,
            interpretacion
          };
        })
      );

      return interpretaciones;

    } catch (error) {
      console.error('Error en obtenerInterpretacionesMultiples:', error);
      // Fallback a interpretaciones locales
      return INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerInterpretacionesMultiples(resultados);
    }
  }

  /**
   * Verificar si existe interpretación en Supabase
   * @param {string} aptitudCodigo - Código de la aptitud
   * @param {number} nivel - Nivel (1-7)
   * @returns {Promise<boolean>} True si existe la interpretación
   */
  static async existeInterpretacion(aptitudCodigo, nivel) {
    try {
      const { data, error } = await supabase
        .from('interpretaciones_oficiales')
        .select('id')
        .eq('aptitud_codigo', aptitudCodigo)
        .eq('nivel_id', nivel)
        .eq('es_oficial', true)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error verificando existencia:', error);
        return false;
      }

      return !!data;

    } catch (error) {
      console.error('Error en existeInterpretacion:', error);
      return false;
    }
  }

  /**
   * Obtener resumen de completitud desde Supabase
   * @returns {Promise<Object>} Resumen de interpretaciones disponibles
   */
  static async obtenerResumenCompletitud() {
    try {
      const { data, error } = await supabase
        .from('interpretaciones_oficiales')
        .select('aptitud_codigo, nivel_id')
        .eq('es_oficial', true);

      if (error) {
        console.error('Error obteniendo resumen:', error);
        return INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerResumenCompletitud();
      }

      const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
      const niveles = [1, 2, 3, 4, 5, 6, 7];
      const resumen = {};

      aptitudes.forEach(aptitud => {
        const interpretacionesAptitud = data.filter(item => item.aptitud_codigo === aptitud);
        const nivelesDisponibles = interpretacionesAptitud.map(item => item.nivel_id);
        const faltantes = niveles.filter(nivel => !nivelesDisponibles.includes(nivel));

        resumen[aptitud] = {
          total: niveles.length,
          disponibles: nivelesDisponibles.length,
          faltantes: faltantes,
          porcentaje: Math.round((nivelesDisponibles.length / niveles.length) * 100)
        };
      });

      return resumen;

    } catch (error) {
      console.error('Error en obtenerResumenCompletitud:', error);
      return INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerResumenCompletitud();
    }
  }

  /**
   * Obtener nivel por percentil desde Supabase
   * @param {number} percentil - Valor del percentil
   * @returns {Promise<Object>} Información del nivel
   */
  static async obtenerNivelPorPercentil(percentil) {
    try {
      const { data, error } = await supabase
        .rpc('obtener_nivel_por_percentil', {
          percentil_valor: percentil
        });

      if (error || !data || data.length === 0) {
        console.error('Error obteniendo nivel por percentil:', error);
        return INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerNivelPorPercentil(percentil);
      }

      const nivel = data[0];
      return {
        id: nivel.nivel_id,
        nombre: nivel.nivel_nombre,
        rango: nivel.rango_descripcion,
        descripcion: nivel.rango_descripcion
      };

    } catch (error) {
      console.error('Error en obtenerNivelPorPercentil:', error);
      return INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerNivelPorPercentil(percentil);
    }
  }

  /**
   * Obtener nivel por percentil (función local)
   * @param {number} percentil - Valor del percentil
   * @returns {Object} Información del nivel
   */
  static obtenerNivelPorPercentil(percentil) {
    if (percentil > 95) return { id: 7, nombre: 'Muy Alto', rango: 'Percentil > 95' };
    if (percentil >= 81) return { id: 6, nombre: 'Alto', rango: 'Percentil 81 - 95' };
    if (percentil >= 61) return { id: 5, nombre: 'Medio-Alto', rango: 'Percentil 61 - 80' };
    if (percentil >= 41) return { id: 4, nombre: 'Medio', rango: 'Percentil 41 - 60' };
    if (percentil >= 21) return { id: 3, nombre: 'Medio-Bajo', rango: 'Percentil 21 - 40' };
    if (percentil >= 6) return { id: 2, nombre: 'Bajo', rango: 'Percentil 6 - 20' };
    return { id: 1, nombre: 'Muy Bajo', rango: 'Percentil ≤ 5' };
  }

  // Métodos de fallback que usan interpretaciones locales
  static obtenerInterpretacionLocal(aptitudCodigo, percentil) {
    const interpretacion = INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerInterpretacionAptitud(aptitudCodigo, percentil);

    // Debug logging
    console.log(`🔍 [InterpretacionesSupabaseService] Interpretación local para ${aptitudCodigo}-${percentil}:`, {
      interpretacion,
      tieneRendimiento: !!interpretacion?.rendimiento,
      longitudRendimiento: interpretacion?.rendimiento?.length
    });

    return {
      ...interpretacion,
      aptitud_codigo: aptitudCodigo,
      aptitud_nombre: this.getNombreAptitud(aptitudCodigo),
      percentil_valor: percentil,
      fuente: 'Local - Oficial'
    };
  }

  static getNombreAptitud(codigo) {
    const nombres = {
      'V': 'Aptitud Verbal',
      'E': 'Aptitud Espacial',
      'A': 'Atención',
      'C': 'Concentración',
      'R': 'Razonamiento',
      'N': 'Aptitud Numérica',
      'M': 'Aptitud Mecánica',
      'O': 'Ortografía'
    };
    return nombres[codigo] || 'Aptitud Desconocida';
  }

  static obtenerTodasInterpretacionesLocal(aptitudCodigo) {
    const interpretaciones = INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerTodasInterpretacionesAptitud(aptitudCodigo);
    return Object.keys(interpretaciones).map(nivel => ({
      nivel_id: parseInt(nivel),
      ...interpretaciones[nivel],
      fuente: 'Local - Oficial'
    }));
  }

  /**
   * Verificar conectividad con Supabase
   * @returns {Promise<boolean>} True si la conexión es exitosa
   */
  static async verificarConectividad() {
    try {
      const { data, error } = await supabase
        .from('aptitudes')
        .select('codigo')
        .limit(1);

      return !error;

    } catch (error) {
      console.error('Error verificando conectividad:', error);
      return false;
    }
  }

  /**
   * Obtener estadísticas de la base de datos
   * @returns {Promise<Object>} Estadísticas de interpretaciones en Supabase
   */
  static async obtenerEstadisticas() {
    try {
      const [
        { count: totalInterpretaciones },
        { count: totalAptitudes },
        { count: totalNiveles }
      ] = await Promise.all([
        supabase.from('interpretaciones_oficiales').select('*', { count: 'exact', head: true }),
        supabase.from('aptitudes').select('*', { count: 'exact', head: true }),
        supabase.from('niveles_percentil').select('*', { count: 'exact', head: true })
      ]);

      return {
        interpretaciones: totalInterpretaciones || 0,
        aptitudes: totalAptitudes || 0,
        niveles: totalNiveles || 0,
        completitud: Math.round(((totalInterpretaciones || 0) / 49) * 100) // 7 aptitudes × 7 niveles = 49
      };

    } catch (error) {
      console.error('Error obteniendo estadísticas:', error);
      return {
        interpretaciones: 0,
        aptitudes: 0,
        niveles: 0,
        completitud: 0
      };
    }
  }
}

// Exportar tanto como default como exportación nombrada para compatibilidad
export { InterpretacionesSupabaseService };
export default InterpretacionesSupabaseService;
