const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/PinControlService-Cx97559i.js","assets/vendor-BqMjyOVw.js","assets/vendor-B4zyQOk2.css","assets/index-Bdl1jgS_.js","assets/index-Csy2uUlu.css","assets/PinLogger-C2v3yGM1.js","assets/NotificationService-DiDbKBbI.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,r=Object.defineProperties,i=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable,t=(r,i,s)=>i in r?e(r,i,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[i]=s,o=(e,r,i)=>new Promise((s,n)=>{var a=e=>{try{o(i.next(e))}catch(r){n(r)}},t=e=>{try{o(i.throw(e))}catch(r){n(r)}},o=e=>e.done?s(e.value):Promise.resolve(e.value).then(a,t);o((i=i.apply(e,r)).next())});import{_ as c,Q as l}from"./vendor-BqMjyOVw.js";import{P as u,a as d}from"./PinLogger-C2v3yGM1.js";import"./index-Bdl1jgS_.js";class g{static validateReportGeneration(e,r=1){return o(this,null,function*(){try{if(u.logInfo("Validating report generation",{psychologistId:e,requiredPins:r}),!e||"string"!=typeof e)return{isValid:!1,canProceed:!1,reason:"INVALID_PSYCHOLOGIST_ID",message:"ID de psicólogo inválido",userMessage:"Error en la identificación del psicólogo",severity:"error"};if(!r||r<1||!Number.isInteger(r))return{isValid:!1,canProceed:!1,reason:"INVALID_PIN_REQUIREMENT",message:"Número de pines requeridos inválido",userMessage:"Error en la configuración del sistema",severity:"error"};const{PinControlService:i}=yield c(()=>o(null,null,function*(){const{PinControlService:e}=yield import("./PinControlService-Cx97559i.js");return{PinControlService:e}}),__vite__mapDeps([0,1,2,3,4,5,6])),s=yield i.checkPsychologistUsage(e);if(!s)return{isValid:!1,canProceed:!1,reason:"PSYCHOLOGIST_NOT_FOUND",message:"Psicólogo no encontrado en el sistema",userMessage:"No se encontró información del psicólogo en el sistema",severity:"error"};if(s.isUnlimited)return{isValid:!0,canProceed:!0,reason:"UNLIMITED_PLAN",message:"Plan ilimitado activo",userMessage:"Puede generar informes (Plan ilimitado)",severity:"success",pinStatus:s,requiredPins:r,isUnlimited:!0};const n=s.remainingPins||0;if(n<r){const e=r-n;return{isValid:!1,canProceed:!1,reason:"INSUFFICIENT_PINS",message:`Pines insuficientes. Disponibles: ${n}, Requeridos: ${r}`,userMessage:`No cuenta con suficientes pines. Disponibles: ${n}. Requeridos: ${r}. Faltan: ${e}`,severity:"error",pinStatus:s,requiredPins:r,remainingPins:n,shortfall:e,isUnlimited:!1}}const a=n-r;let t="success",l="";return a<=d.THRESHOLDS.CRITICAL_PIN_WARNING?(t="critical",l=`CRÍTICO: Después de esta operación quedarán solo ${a} pines`):a<=d.THRESHOLDS.LOW_PIN_WARNING&&(t="warning",l=`ADVERTENCIA: Después de esta operación quedarán solo ${a} pines`),{isValid:!0,canProceed:!0,reason:"PINS_AVAILABLE",message:`Validación exitosa. Pines disponibles: ${n}`,userMessage:l||`Puede generar el informe. Pines disponibles: ${n}`,severity:t,pinStatus:s,requiredPins:r,remainingPins:n,pinsAfterOperation:a,isUnlimited:!1,hasWarning:!!l}}catch(i){return u.logError("Error in validateReportGeneration",i),{isValid:!1,canProceed:!1,reason:"VALIDATION_ERROR",message:`Error durante la validación: ${i.message}`,userMessage:"Error al verificar los permisos. Intente nuevamente.",severity:"error",error:i.message}}})}static validateBatchReportGeneration(e,c){return o(this,null,function*(){try{const d=c.length;if(u.logInfo("Validating batch report generation",{psychologistId:e,patientCount:c.length,requiredPins:d}),!Array.isArray(c)||0===c.length)return{isValid:!1,canProceed:!1,reason:"INVALID_PATIENT_LIST",message:"Lista de pacientes inválida",userMessage:"No se han seleccionado pacientes válidos",severity:"error"};const g=yield this.validateReportGeneration(e,d);return o=((e,r)=>{for(var i in r||(r={}))n.call(r,i)&&t(e,i,r[i]);if(s)for(var i of s(r))a.call(r,i)&&t(e,i,r[i]);return e})({},g),l={batchInfo:{patientCount:c.length,requiredPins:d,patientIds:c}},r(o,i(l))}catch(d){return u.logError("Error in validateBatchReportGeneration",d),{isValid:!1,canProceed:!1,reason:"BATCH_VALIDATION_ERROR",message:`Error durante la validación en lote: ${d.message}`,userMessage:"Error al verificar los permisos para generación en lote",severity:"error",error:d.message}}var o,l})}static displayValidationAlerts(e,r=!0){if(!e)return;const{severity:i,userMessage:s,hasWarning:n}=e;if(!r)return e;switch(i){case"error":l.error(s);break;case"critical":l.error(s,{autoClose:8e3});break;case"warning":l.warning(s,{autoClose:6e3});break;case"success":n?l.warning(s,{autoClose:5e3}):l.success(s);break;default:l.info(s)}return e}static createValidationSummary(e){if(!e)return null;const{isValid:r,canProceed:i,reason:s,userMessage:n,severity:a,remainingPins:t,requiredPins:o,pinsAfterOperation:c,isUnlimited:l,batchInfo:u}=e;return{status:i?"allowed":"blocked",title:i?"Operación Permitida":"Operación Bloqueada",message:n,severity:a,details:{isUnlimited:l,currentPins:t,requiredPins:o,pinsAfter:c,isBatch:!!u,batchSize:null==u?void 0:u.patientCount},actionRequired:!i,suggestions:this._generateSuggestions(e)}}static _generateSuggestions(e){const{reason:r,shortfall:i,remainingPins:s}=e,n=[];switch(r){case"INSUFFICIENT_PINS":n.push(`Necesita ${i} pines adicionales`),n.push("Contacte al administrador para obtener más pines"),s>0&&n.push(`Puede generar ${s} informes con los pines actuales`);break;case"PSYCHOLOGIST_NOT_FOUND":n.push("Verifique que su cuenta esté correctamente configurada"),n.push("Contacte al administrador del sistema");break;default:"warning"!==e.severity&&"critical"!==e.severity||n.push("Considere recargar pines antes de que se agoten")}return n}}export{g as PinValidationService,g as default};
