/**
 * ========================================
 * GENERADOR DE PDF ULTRA-PRECISO
 * html2canvas + jsPDF - Fidelidad Visual 100%
 * ========================================
 * 
 * Reemplaza react-to-print con una solución más precisa
 * que genera PDFs idénticos al contenido original en pantalla
 */

import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

/**
 * Configuración optimizada para tamaño natural (sin escalado forzado)
 */
const HTML2CANVAS_CONFIG = {
  // Resolución natural (sin sobre-escalado)
  scale: 1, // Escala 1:1 para tamaño real
  useCORS: true, // Permitir recursos externos
  allowTaint: false, // Evitar problemas de seguridad

  // Renderizado preciso
  backgroundColor: '#ffffff', // Fondo blanco explícito
  removeContainer: true, // Limpiar después del renderizado
  imageTimeout: 15000, // Timeout generoso para imágenes

  // Dimensiones naturales del elemento
  width: null, // Usar ancho natural del elemento
  height: null, // Usar altura natural del elemento
  scrollX: 0, // Sin scroll horizontal
  scrollY: 0, // Sin scroll vertical

  // Preservación de estilos
  ignoreElements: (element) => {
    // Ignorar elementos que pueden causar problemas
    return element.classList.contains('no-pdf') ||
           element.classList.contains('print-hide') ||
           element.tagName === 'SCRIPT' ||
           element.tagName === 'NOSCRIPT';
  },

  // Configuración avanzada
  logging: process.env.NODE_ENV === 'development', // Debug en desarrollo
  onclone: (clonedDoc, element) => {
    // Aplicar optimizaciones al documento clonado
    optimizeClonedDocument(clonedDoc, element);
  }
};

/**
 * Configuración de jsPDF optimizada para A4
 */
const JSPDF_CONFIG = {
  orientation: 'portrait',
  unit: 'mm',
  format: 'a4',
  compress: true,
  precision: 2
};

/**
 * Dimensiones A4 en píxeles (para cálculos)
 */
const A4_DIMENSIONS = {
  width: 210, // mm
  height: 297, // mm
  pixelRatio: 3.779527559 // píxeles por mm a 96 DPI
};

/**
 * Genera un PDF ultra-preciso del contenido especificado
 * @param {HTMLElement} element - Elemento a convertir a PDF
 * @param {string} filename - Nombre del archivo PDF
 * @param {Object} options - Opciones adicionales
 * @returns {Promise<void>}
 */
export const generatePrecisePDF = async (element, filename = 'documento.pdf', options = {}) => {
  if (!element) {
    throw new Error('❌ [pdfGenerator] Elemento no proporcionado');
  }

  console.log('🎯 [pdfGenerator] Iniciando generación de PDF ultra-preciso...');
  console.log(`📄 [pdfGenerator] Archivo: ${filename}`);

  try {
    // FASE 1: Preparar elemento para captura
    console.log('🔧 [pdfGenerator] FASE 1: Preparando elemento...');
    await prepareElementForCapture(element, options);

    // FASE 2: Capturar con html2canvas
    console.log('📸 [pdfGenerator] FASE 2: Capturando con html2canvas...');
    const canvas = await captureElementAsCanvas(element, options);

    // FASE 3: Generar PDF con jsPDF
    console.log('📋 [pdfGenerator] FASE 3: Generando PDF con jsPDF...');
    await generatePDFFromCanvas(canvas, filename, options);

    // FASE 4: Limpiar recursos
    console.log('🧹 [pdfGenerator] FASE 4: Limpiando recursos...');
    cleanupResources(canvas);

    console.log('✅ [pdfGenerator] PDF generado exitosamente');

  } catch (error) {
    console.error('❌ [pdfGenerator] Error generando PDF:', error);
    throw new Error(`Error generando PDF: ${error.message}`);
  }
};

/**
 * Prepara el elemento para captura optimizada
 */
const prepareElementForCapture = async (element, options) => {
  // Forzar renderizado completo
  await new Promise(resolve => {
    requestAnimationFrame(() => {
      requestAnimationFrame(resolve);
    });
  });

  // Aplicar estilos de preparación si se proporcionan
  if (options.onBeforeCapture) {
    await options.onBeforeCapture(element);
  }

  // Asegurar que todas las imágenes estén cargadas
  const images = element.querySelectorAll('img');
  const imagePromises = Array.from(images).map(img => {
    return new Promise((resolve) => {
      if (img.complete) {
        resolve();
      } else {
        img.onload = resolve;
        img.onerror = resolve; // Continuar aunque falle una imagen
      }
    });
  });

  await Promise.all(imagePromises);
  console.log(`🖼️ [pdfGenerator] ${images.length} imágenes verificadas`);
};

/**
 * Captura el elemento como canvas con configuración optimizada
 */
const captureElementAsCanvas = async (element, options) => {
  const config = {
    ...HTML2CANVAS_CONFIG,
    ...options.html2canvasOptions
  };

  console.log('📸 [pdfGenerator] Configuración html2canvas:', {
    scale: config.scale,
    width: element.offsetWidth,
    height: element.offsetHeight,
    backgroundColor: config.backgroundColor
  });

  const canvas = await html2canvas(element, config);
  
  console.log('📸 [pdfGenerator] Canvas generado:', {
    width: canvas.width,
    height: canvas.height,
    ratio: (canvas.width / canvas.height).toFixed(2)
  });

  return canvas;
};

/**
 * Genera PDF desde canvas SIN escalado automático
 * Permite que el usuario use las opciones nativas de la impresora
 */
const generatePDFFromCanvas = async (canvas, filename, options) => {
  // Calcular dimensiones naturales del contenido
  const canvasWidth = canvas.width;
  const canvasHeight = canvas.height;

  // Convertir píxeles a mm usando DPI estándar (96 DPI = 3.779527559 px/mm)
  const mmPerPixel = 25.4 / 96; // Conversión estándar
  const contentWidthMM = canvasWidth * mmPerPixel;
  const contentHeightMM = canvasHeight * mmPerPixel;

  console.log('📋 [pdfGenerator] Dimensiones naturales del contenido:', {
    canvas: `${canvasWidth}x${canvasHeight}px`,
    content: `${contentWidthMM.toFixed(1)}x${contentHeightMM.toFixed(1)}mm`,
    dpi: '96 DPI (estándar web)'
  });

  // Crear PDF con dimensiones que se ajusten al contenido
  // Si el contenido es más grande que A4, usar las dimensiones del contenido
  const pdfWidth = Math.max(contentWidthMM, A4_DIMENSIONS.width);
  const pdfHeight = Math.max(contentHeightMM, A4_DIMENSIONS.height);

  // Configuración de PDF con dimensiones dinámicas
  const pdfConfig = {
    ...JSPDF_CONFIG,
    format: [pdfWidth, pdfHeight] // Formato personalizado basado en contenido
  };

  const pdf = new jsPDF(pdfConfig);

  console.log('📋 [pdfGenerator] Configuración PDF:', {
    format: `${pdfWidth.toFixed(1)}x${pdfHeight.toFixed(1)}mm`,
    orientation: pdfConfig.orientation,
    noAutoScale: true
  });

  // Posicionar contenido sin escalado
  const margin = options.margin || 0; // Sin márgenes por defecto para tamaño real
  const x = margin;
  const y = margin;

  // Usar dimensiones reales del contenido (1:1)
  const finalWidth = contentWidthMM;
  const finalHeight = contentHeightMM;

  console.log('📋 [pdfGenerator] Posicionamiento sin escalado:', {
    position: `${x}, ${y}mm`,
    size: `${finalWidth.toFixed(1)}x${finalHeight.toFixed(1)}mm`,
    scale: '1:1 (sin escalado)'
  });

  // Convertir canvas a imagen y agregar al PDF SIN escalado
  const imgData = canvas.toDataURL('image/png', 1.0);
  pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight, undefined, 'FAST');

  // Guardar PDF
  pdf.save(filename);

  console.log(`💾 [pdfGenerator] PDF guardado sin escalado: ${filename}`);
  console.log('🖨️ [pdfGenerator] El usuario puede usar opciones de impresora para escalar/posicionar');
};

/**
 * Optimiza el documento clonado para mejor renderizado
 */
const optimizeClonedDocument = (clonedDoc, element) => {
  console.log('🔧 [pdfGenerator] Optimizando documento clonado...');
  
  // Forzar estilos de impresión en el clon
  const style = clonedDoc.createElement('style');
  style.textContent = `
    * {
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
      color-adjust: exact !important;
    }
    
    .print-content {
      background: white !important;
      color: black !important;
      font-family: Arial, sans-serif !important;
    }
  `;
  clonedDoc.head.appendChild(style);
  
  // Remover elementos problemáticos
  const problematicElements = clonedDoc.querySelectorAll('script, noscript, .no-pdf, .print-hide');
  problematicElements.forEach(el => el.remove());
  
  console.log('🔧 [pdfGenerator] Documento clonado optimizado');
};

/**
 * Limpia recursos para evitar memory leaks
 */
const cleanupResources = (canvas) => {
  if (canvas && canvas.remove) {
    canvas.remove();
  }
  
  // Forzar garbage collection si está disponible
  if (window.gc) {
    window.gc();
  }
  
  console.log('🧹 [pdfGenerator] Recursos limpiados');
};

/**
 * Función de conveniencia para generar PDF de informe SIN escalado automático
 */
export const generateInformePDF = async (element, patientData, options = {}) => {
  const timestamp = new Date().toISOString().split('T')[0];
  const patientName = patientData?.nombre || 'Paciente';
  const filename = `Informe_BAT7_${patientName}_${timestamp}.pdf`;

  const defaultOptions = {
    margin: 0, // Sin márgenes para tamaño natural
    autoScale: false, // Deshabilitar escalado automático
    onBeforeCapture: async (element) => {
      // Aplicar estilos específicos del informe si están disponibles
      if (window.applyAllInlineStyles) {
        window.applyAllInlineStyles(element);
      }
    }
  };

  return generatePrecisePDF(element, filename, { ...defaultOptions, ...options });
};

/**
 * Función alternativa para generar PDF con escalado automático (legacy)
 */
export const generateInformePDFScaled = async (element, patientData, options = {}) => {
  const timestamp = new Date().toISOString().split('T')[0];
  const patientName = patientData?.nombre || 'Paciente';
  const filename = `Informe_BAT7_${patientName}_${timestamp}_escalado.pdf`;

  const scaledOptions = {
    margin: 10,
    autoScale: true, // Habilitar escalado automático
    html2canvasOptions: {
      scale: 2, // Alta resolución
      ...options.html2canvasOptions
    },
    onBeforeCapture: async (element) => {
      if (window.applyAllInlineStyles) {
        window.applyAllInlineStyles(element);
      }
    }
  };

  return generatePrecisePDFWithScaling(element, filename, { ...scaledOptions, ...options });
};

/**
 * Versión con escalado automático (para compatibilidad)
 */
const generatePrecisePDFWithScaling = async (element, filename, options) => {
  // Implementación con escalado automático (código anterior)
  // Esta función mantiene el comportamiento anterior para casos específicos
  console.log('📏 [pdfGenerator] Usando modo con escalado automático');

  // Usar configuración con escalado
  const scaledConfig = {
    ...HTML2CANVAS_CONFIG,
    scale: 2, // Alta resolución
    ...options.html2canvasOptions
  };

  const canvas = await html2canvas(element, scaledConfig);

  // Aplicar escalado automático como antes
  const pdf = new jsPDF(JSPDF_CONFIG);
  const canvasWidth = canvas.width;
  const canvasHeight = canvas.height;

  const pageWidth = A4_DIMENSIONS.width;
  const pageHeight = A4_DIMENSIONS.height;
  const margin = options.margin || 10;
  const availableWidth = pageWidth - (margin * 2);
  const availableHeight = pageHeight - (margin * 2);

  const scaleX = availableWidth / (canvasWidth / A4_DIMENSIONS.pixelRatio);
  const scaleY = availableHeight / (canvasHeight / A4_DIMENSIONS.pixelRatio);
  const scale = Math.min(scaleX, scaleY, 1);

  const finalWidth = (canvasWidth / A4_DIMENSIONS.pixelRatio) * scale;
  const finalHeight = (canvasHeight / A4_DIMENSIONS.pixelRatio) * scale;

  const x = (pageWidth - finalWidth) / 2;
  const y = (pageHeight - finalHeight) / 2;

  const imgData = canvas.toDataURL('image/png', 1.0);
  pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight, undefined, 'FAST');
  pdf.save(filename);

  console.log(`💾 [pdfGenerator] PDF escalado guardado: ${filename}`);
};
