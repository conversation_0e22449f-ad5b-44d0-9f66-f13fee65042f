# Sistema Mejorado de Pines - Tests

Este directorio contiene las pruebas unitarias y de integración para el sistema mejorado de validación y control de pines.

## Estructura de Tests

```
src/tests/
├── pin/                          # Tests de servicios de pines
│   ├── PinValidationService.test.js
│   ├── BatchProcessingService.test.js
│   ├── SessionControlService.test.js
│   ├── EnhancedNotificationService.test.js
│   └── RechargeRequestService.test.js
├── hooks/                        # Tests de hooks
│   ├── useAdvancedPinValidation.test.js
│   ├── usePinNotifications.test.js
│   └── useQuickPinValidation.test.js
├── components/                   # Tests de componentes
│   ├── ValidatedReportButton.test.js
│   ├── PinValidationAlert.test.js
│   ├── PinNotificationCenter.test.js
│   └── BatchStrategySelector.test.js
├── integration/                  # Tests de integración
│   ├── PinSystemIntegration.test.js
│   ├── BatchProcessingFlow.test.js
│   └── NotificationFlow.test.js
└── README.md                     # Este archivo
```

## Ejecutar Tests

### Todos los tests
```bash
npm test
```

### Tests específicos
```bash
# Tests de servicios de pines
npm test src/tests/pin/

# Tests de hooks
npm test src/tests/hooks/

# Tests de componentes
npm test src/tests/components/

# Tests de integración
npm test src/tests/integration/
```

### Tests con cobertura
```bash
npm run test:coverage
```

### Tests en modo watch
```bash
npm run test:watch
```

## Cobertura de Tests

### Servicios de Pines
- ✅ **PinValidationService**: Validación individual y en lote
- ✅ **BatchProcessingService**: Estrategias de procesamiento en lote
- ⏳ **SessionControlService**: Control por sesión de paciente
- ⏳ **EnhancedNotificationService**: Sistema de notificaciones mejorado
- ⏳ **RechargeRequestService**: Gestión de solicitudes de recarga

### Hooks
- ✅ **useAdvancedPinValidation**: Hook principal de validación
- ⏳ **usePinNotifications**: Gestión de notificaciones
- ⏳ **useQuickPinValidation**: Validación rápida

### Componentes
- ⏳ **ValidatedReportButton**: Botón inteligente con validación
- ⏳ **PinValidationAlert**: Alertas de validación
- ⏳ **PinNotificationCenter**: Centro de notificaciones
- ⏳ **BatchStrategySelector**: Selector de estrategias

### Integración
- ✅ **PinSystemIntegration**: Flujo completo del sistema
- ⏳ **BatchProcessingFlow**: Flujo de procesamiento en lote
- ⏳ **NotificationFlow**: Flujo de notificaciones

## Casos de Prueba Principales

### 1. Validación de Pines
- ✅ Validación con pines suficientes
- ✅ Validación con pines insuficientes
- ✅ Validación con plan ilimitado
- ✅ Validación con advertencias de pines bajos
- ✅ Manejo de errores de servicio

### 2. Procesamiento en Lote
- ✅ Estrategia individual
- ✅ Estrategia bulk upfront
- ✅ Estrategia bulk success
- ✅ Estrategia optimista
- ✅ Recomendación automática de estrategia

### 3. Interfaz de Usuario
- ✅ Botones habilitados/deshabilitados según validación
- ✅ Alertas y confirmaciones apropiadas
- ✅ Estados de carga durante operaciones
- ✅ Manejo de errores en UI

### 4. Notificaciones
- ⏳ Notificaciones de pines bajos
- ⏳ Notificaciones de consumo
- ⏳ Notificaciones de asignación
- ⏳ Centro de notificaciones

### 5. Solicitudes de Recarga
- ⏳ Creación de solicitudes
- ⏳ Aprobación/rechazo por administradores
- ⏳ Historial de solicitudes
- ⏳ Notificaciones de estado

## Mocks y Utilidades

### Servicios Mockeados
```javascript
// PinValidationService
vi.mock('../../services/pin/PinValidationService', () => ({
  default: {
    validateReportGeneration: vi.fn(),
    validateBatchReportGeneration: vi.fn(),
    createValidationSummary: vi.fn(),
    displayValidationAlerts: vi.fn()
  }
}));

// InformesService
vi.mock('../../services/InformesService', () => ({
  default: {
    generarInformeCompleto: vi.fn(),
    generarInformesEnLote: vi.fn()
  }
}));
```

### Datos de Prueba
```javascript
// Resultado de validación exitosa
const mockSuccessValidation = {
  isValid: true,
  canProceed: true,
  reason: 'PINS_AVAILABLE',
  userMessage: 'Puede generar el informe',
  severity: 'success',
  remainingPins: 10,
  requiredPins: 1,
  pinsAfterOperation: 9,
  isUnlimited: false
};

// Resultado de validación fallida
const mockFailedValidation = {
  isValid: false,
  canProceed: false,
  reason: 'INSUFFICIENT_PINS',
  userMessage: 'No hay suficientes pines',
  severity: 'error',
  remainingPins: 0,
  requiredPins: 1,
  shortfall: 1
};
```

## Configuración de Testing

### Vitest Configuration
```javascript
// vitest.config.js
export default {
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/tests/setup.js'],
    coverage: {
      reporter: ['text', 'html', 'lcov'],
      exclude: [
        'node_modules/',
        'src/tests/',
        '**/*.test.js'
      ]
    }
  }
};
```

### Setup File
```javascript
// src/tests/setup.js
import { vi } from 'vitest';
import '@testing-library/jest-dom';

// Mock global objects
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock toast notifications
vi.mock('react-toastify', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}));
```

## Mejores Prácticas

### 1. Estructura de Tests
- Usar `describe` para agrupar tests relacionados
- Usar `it` para casos de prueba específicos
- Usar `beforeEach` para setup común
- Limpiar mocks entre tests

### 2. Naming Conventions
- Nombres descriptivos que expliquen qué se está probando
- Usar "should" para describir comportamiento esperado
- Incluir contexto en los nombres de grupos

### 3. Assertions
- Una assertion principal por test
- Usar matchers específicos de jest-dom
- Verificar tanto estados positivos como negativos

### 4. Mocking
- Mockear dependencias externas
- Usar mocks específicos para cada test
- Verificar que los mocks se llamen correctamente

### 5. Async Testing
- Usar `waitFor` para operaciones asíncronas
- Manejar promesas correctamente
- Probar tanto éxito como fallo

## Comandos Útiles

```bash
# Ejecutar tests específicos
npm test -- --grep "PinValidationService"

# Ejecutar tests en modo debug
npm test -- --inspect-brk

# Generar reporte de cobertura
npm run test:coverage

# Ejecutar tests con output verbose
npm test -- --reporter=verbose

# Ejecutar solo tests que fallaron
npm test -- --retry-failed
```

## Próximos Pasos

1. ⏳ Completar tests de componentes faltantes
2. ⏳ Agregar tests de performance para lotes grandes
3. ⏳ Implementar tests E2E con Playwright
4. ⏳ Agregar tests de accesibilidad
5. ⏳ Configurar CI/CD con tests automáticos

## Contribuir

Al agregar nuevos tests:

1. Seguir la estructura existente
2. Incluir casos de éxito y fallo
3. Mockear dependencias apropiadamente
4. Documentar casos de prueba complejos
5. Mantener cobertura > 80%
