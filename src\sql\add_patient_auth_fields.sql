-- Migración para agregar campos de autenticación a la tabla pacientes
-- Esto permite que los pacientes se autentiquen directamente contra su tabla

-- Agregar campos de autenticación a la tabla pacientes
ALTER TABLE public.pacientes 
ADD COLUMN IF NOT EXISTS password_hash TEXT,
ADD COLUMN IF NOT EXISTS password_temp TEXT,
ADD COLUMN IF NOT EXISTS require_password_change BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS last_login TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS login_attempts INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS locked_until TIMESTAMPTZ;

-- <PERSON><PERSON><PERSON> índices para mejorar el rendimiento de las consultas de autenticación
CREATE INDEX IF NOT EXISTS idx_pacientes_email ON public.pacientes(email);
CREATE INDEX IF NOT EXISTS idx_pacientes_documento ON public.pacientes(documento);
CREATE INDEX IF NOT EXISTS idx_pacientes_email_activo ON public.pacientes(email, activo);
CREATE INDEX IF NOT EXISTS idx_pacientes_documento_activo ON public.pacientes(documento, activo);

-- Función para hashear contraseñas usando crypt
CREATE OR REPLACE FUNCTION hash_password(password TEXT)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Usar crypt con salt aleatorio para hashear la contraseña
  RETURN crypt(password, gen_salt('bf', 10));
END;
$$;

-- Función para verificar contraseñas
CREATE OR REPLACE FUNCTION verify_password(password TEXT, hash TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Verificar si la contraseña coincide con el hash
  RETURN crypt(password, hash) = hash;
END;
$$;

-- Función para autenticar pacientes
CREATE OR REPLACE FUNCTION authenticate_patient(
  identifier TEXT,
  password TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  patient_record RECORD;
  auth_result JSONB;
  is_email BOOLEAN;
BEGIN
  -- Determinar si el identificador es email o documento
  is_email := identifier LIKE '%@%';
  
  -- Buscar el paciente por email o documento
  IF is_email THEN
    SELECT * INTO patient_record
    FROM public.pacientes
    WHERE email = identifier AND activo = TRUE;
  ELSE
    SELECT * INTO patient_record
    FROM public.pacientes
    WHERE documento = identifier AND activo = TRUE;
  END IF;
  
  -- Si no se encuentra el paciente
  IF patient_record IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Paciente no encontrado o inactivo'
    );
  END IF;
  
  -- Verificar si la cuenta está bloqueada
  IF patient_record.locked_until IS NOT NULL AND patient_record.locked_until > NOW() THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Cuenta temporalmente bloqueada. Intente más tarde.'
    );
  END IF;
  
  -- Verificar la contraseña
  IF patient_record.password_hash IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'No se ha establecido una contraseña para este paciente'
    );
  END IF;
  
  -- Verificar contraseña temporal si existe
  IF patient_record.password_temp IS NOT NULL THEN
    IF NOT verify_password(password, patient_record.password_temp) THEN
      -- Incrementar intentos fallidos
      UPDATE public.pacientes 
      SET login_attempts = COALESCE(login_attempts, 0) + 1,
          locked_until = CASE 
            WHEN COALESCE(login_attempts, 0) + 1 >= 5 THEN NOW() + INTERVAL '15 minutes'
            ELSE NULL
          END
      WHERE id = patient_record.id;
      
      RETURN jsonb_build_object(
        'success', false,
        'error', 'Contraseña incorrecta'
      );
    END IF;
  ELSE
    -- Verificar contraseña principal
    IF NOT verify_password(password, patient_record.password_hash) THEN
      -- Incrementar intentos fallidos
      UPDATE public.pacientes 
      SET login_attempts = COALESCE(login_attempts, 0) + 1,
          locked_until = CASE 
            WHEN COALESCE(login_attempts, 0) + 1 >= 5 THEN NOW() + INTERVAL '15 minutes'
            ELSE NULL
          END
      WHERE id = patient_record.id;
      
      RETURN jsonb_build_object(
        'success', false,
        'error', 'Contraseña incorrecta'
      );
    END IF;
  END IF;
  
  -- Autenticación exitosa - actualizar último login y resetear intentos
  UPDATE public.pacientes 
  SET last_login = NOW(),
      login_attempts = 0,
      locked_until = NULL
  WHERE id = patient_record.id;
  
  -- Retornar datos del paciente (sin contraseñas)
  RETURN jsonb_build_object(
    'success', true,
    'patient', jsonb_build_object(
      'id', patient_record.id,
      'nombre', patient_record.nombre,
      'apellido', patient_record.apellido,
      'email', patient_record.email,
      'documento', patient_record.documento,
      'genero', patient_record.genero,
      'fecha_nacimiento', patient_record.fecha_nacimiento,
      'telefono', patient_record.telefono,
      'psicologo_id', patient_record.psicologo_id,
      'require_password_change', COALESCE(patient_record.require_password_change, false),
      'has_temp_password', patient_record.password_temp IS NOT NULL,
      'tipo_usuario', 'paciente'
    )
  );
END;
$$;

-- Función para establecer contraseña temporal (solo para psicólogos/admins)
CREATE OR REPLACE FUNCTION set_patient_temp_password(
  patient_id UUID,
  temp_password TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Actualizar la contraseña temporal
  UPDATE public.pacientes 
  SET password_temp = hash_password(temp_password),
      require_password_change = TRUE,
      login_attempts = 0,
      locked_until = NULL
  WHERE id = patient_id AND activo = TRUE;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Paciente no encontrado'
    );
  END IF;
  
  RETURN jsonb_build_object(
    'success', true,
    'message', 'Contraseña temporal establecida correctamente'
  );
END;
$$;

-- Función para cambiar contraseña del paciente
CREATE OR REPLACE FUNCTION change_patient_password(
  patient_id UUID,
  old_password TEXT,
  new_password TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  patient_record RECORD;
BEGIN
  -- Obtener datos del paciente
  SELECT * INTO patient_record
  FROM public.pacientes
  WHERE id = patient_id AND activo = TRUE;
  
  IF patient_record IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Paciente no encontrado'
    );
  END IF;
  
  -- Verificar contraseña actual (temporal o principal)
  IF patient_record.password_temp IS NOT NULL THEN
    IF NOT verify_password(old_password, patient_record.password_temp) THEN
      RETURN jsonb_build_object(
        'success', false,
        'error', 'Contraseña actual incorrecta'
      );
    END IF;
  ELSE
    IF NOT verify_password(old_password, patient_record.password_hash) THEN
      RETURN jsonb_build_object(
        'success', false,
        'error', 'Contraseña actual incorrecta'
      );
    END IF;
  END IF;
  
  -- Actualizar con la nueva contraseña
  UPDATE public.pacientes 
  SET password_hash = hash_password(new_password),
      password_temp = NULL,
      require_password_change = FALSE,
      login_attempts = 0,
      locked_until = NULL
  WHERE id = patient_id;
  
  RETURN jsonb_build_object(
    'success', true,
    'message', 'Contraseña actualizada correctamente'
  );
END;
$$;

-- Comentarios sobre las funciones
COMMENT ON FUNCTION authenticate_patient(TEXT, TEXT) IS 'Autentica un paciente usando email/documento y contraseña';
COMMENT ON FUNCTION set_patient_temp_password(UUID, TEXT) IS 'Establece una contraseña temporal para un paciente';
COMMENT ON FUNCTION change_patient_password(UUID, TEXT, TEXT) IS 'Permite a un paciente cambiar su contraseña';
COMMENT ON FUNCTION hash_password(TEXT) IS 'Hashea una contraseña usando bcrypt';
COMMENT ON FUNCTION verify_password(TEXT, TEXT) IS 'Verifica una contraseña contra su hash';
