/**
 * @file migrarInterpretacionesSupabase.js
 * @description Script para migrar las interpretaciones oficiales del BAT-7 a Supabase
 * Inserta todas las interpretaciones copiadas a pie de letra del documento oficial
 */

import { createClient } from '@supabase/supabase-js';
import { INTERPRETACIONES_OFICIALES_CONSOLIDADAS } from '../utils/interpretacionesOficialesConsolidadas.js';

// Configuración de Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Función principal para migrar todas las interpretaciones oficiales
 */
async function migrarInterpretacionesOficiales() {
  console.log('🚀 Iniciando migración de interpretaciones oficiales a Supabase...');
  
  try {
    // Obtener todas las interpretaciones
    const interpretaciones = INTERPRETACIONES_OFICIALES_CONSOLIDADAS.interpretaciones;
    const aptitudes = Object.keys(interpretaciones);
    
    console.log(`📊 Migrando interpretaciones para ${aptitudes.length} aptitudes...`);
    
    let totalInsertadas = 0;
    let errores = 0;
    
    // Procesar cada aptitud
    for (const aptitudCodigo of aptitudes) {
      console.log(`\n📝 Procesando aptitud: ${aptitudCodigo}`);
      
      const nivelesAptitud = interpretaciones[aptitudCodigo];
      const niveles = Object.keys(nivelesAptitud);
      
      // Procesar cada nivel de la aptitud
      for (const nivelId of niveles) {
        const interpretacion = nivelesAptitud[nivelId];
        
        try {
          // Insertar interpretación en Supabase
          const { data, error } = await supabase
            .from('interpretaciones_oficiales')
            .upsert({
              aptitud_codigo: aptitudCodigo,
              nivel_id: parseInt(nivelId),
              rendimiento: interpretacion.rendimiento,
              academico: interpretacion.academico,
              vocacional: interpretacion.vocacional,
              es_oficial: true,
              fuente: 'Documento oficial BAT-7 - Interpretacion de aptitudes y Generalidaes.txt'
            }, {
              onConflict: 'aptitud_codigo,nivel_id'
            });
          
          if (error) {
            console.error(`❌ Error insertando ${aptitudCodigo}-${nivelId}:`, error);
            errores++;
          } else {
            console.log(`✅ Insertada interpretación ${aptitudCodigo}-${nivelId}`);
            totalInsertadas++;
          }
          
        } catch (err) {
          console.error(`❌ Error procesando ${aptitudCodigo}-${nivelId}:`, err);
          errores++;
        }
      }
    }
    
    console.log('\n📊 Resumen de migración:');
    console.log(`✅ Interpretaciones insertadas: ${totalInsertadas}`);
    console.log(`❌ Errores: ${errores}`);
    
    if (errores === 0) {
      console.log('🎉 ¡Migración completada exitosamente!');
    } else {
      console.log('⚠️ Migración completada con algunos errores.');
    }
    
    // Verificar la migración
    await verificarMigracion();
    
  } catch (error) {
    console.error('💥 Error durante la migración:', error);
    process.exit(1);
  }
}

/**
 * Función para verificar que la migración fue exitosa
 */
async function verificarMigracion() {
  console.log('\n🔍 Verificando migración...');
  
  try {
    // Contar interpretaciones por aptitud
    const { data: conteos, error } = await supabase
      .from('interpretaciones_oficiales')
      .select('aptitud_codigo, nivel_id')
      .eq('es_oficial', true);
    
    if (error) {
      console.error('❌ Error verificando migración:', error);
      return;
    }
    
    // Agrupar por aptitud
    const conteoPorAptitud = {};
    conteos.forEach(item => {
      if (!conteoPorAptitud[item.aptitud_codigo]) {
        conteoPorAptitud[item.aptitud_codigo] = 0;
      }
      conteoPorAptitud[item.aptitud_codigo]++;
    });
    
    console.log('\n📈 Interpretaciones por aptitud en Supabase:');
    Object.keys(conteoPorAptitud).sort().forEach(aptitud => {
      const count = conteoPorAptitud[aptitud];
      const status = count === 7 ? '✅' : '⚠️';
      console.log(`${status} ${aptitud}: ${count}/7 interpretaciones`);
    });
    
    // Probar función de obtener interpretación
    console.log('\n🧪 Probando función obtener_interpretacion_oficial...');
    
    const { data: pruebaInterpretacion, error: errorPrueba } = await supabase
      .rpc('obtener_interpretacion_oficial', {
        aptitud_codigo_param: 'V',
        percentil_valor: 85
      });
    
    if (errorPrueba) {
      console.error('❌ Error probando función:', errorPrueba);
    } else if (pruebaInterpretacion && pruebaInterpretacion.length > 0) {
      console.log('✅ Función obtener_interpretacion_oficial funciona correctamente');
      console.log(`   Ejemplo: V-85 → Nivel ${pruebaInterpretacion[0].nivel_nombre}`);
    } else {
      console.log('⚠️ Función no retornó resultados');
    }
    
  } catch (error) {
    console.error('💥 Error durante verificación:', error);
  }
}

/**
 * Función para limpiar interpretaciones existentes (opcional)
 */
async function limpiarInterpretacionesExistentes() {
  console.log('🧹 Limpiando interpretaciones existentes...');
  
  try {
    const { error } = await supabase
      .from('interpretaciones_oficiales')
      .delete()
      .eq('es_oficial', true);
    
    if (error) {
      console.error('❌ Error limpiando interpretaciones:', error);
    } else {
      console.log('✅ Interpretaciones existentes limpiadas');
    }
  } catch (error) {
    console.error('💥 Error durante limpieza:', error);
  }
}

/**
 * Función para mostrar estadísticas de interpretaciones locales
 */
function mostrarEstadisticasLocales() {
  console.log('📊 Estadísticas de interpretaciones locales:');
  
  const resumen = INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerResumenCompletitud();
  
  Object.keys(resumen).forEach(aptitud => {
    const info = resumen[aptitud];
    const status = info.porcentaje === 100 ? '✅' : '⚠️';
    console.log(`${status} ${aptitud}: ${info.disponibles}/${info.total} (${info.porcentaje}%)`);
    
    if (info.faltantes.length > 0) {
      console.log(`   Faltantes: ${info.faltantes.join(', ')}`);
    }
  });
}

// Ejecutar migración si se ejecuta directamente
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🎯 Script de migración de interpretaciones oficiales BAT-7');
  console.log('📄 Fuente: Interpretacion de aptitudes y Generalidaes.txt\n');
  
  // Mostrar estadísticas locales
  mostrarEstadisticasLocales();
  
  // Preguntar si limpiar datos existentes
  const args = process.argv.slice(2);
  if (args.includes('--clean')) {
    await limpiarInterpretacionesExistentes();
  }
  
  // Ejecutar migración
  await migrarInterpretacionesOficiales();
}

export {
  migrarInterpretacionesOficiales,
  verificarMigracion,
  limpiarInterpretacionesExistentes,
  mostrarEstadisticasLocales
};
