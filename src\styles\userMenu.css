/* Mejoras para el menú desplegable del usuario */

/* Animaciones de entrada */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Clase para animación de entrada del menú */
.animate-in {
  animation: slideInFromTop 0.2s ease-out;
}

/* Mejoras de accesibilidad para focus */
.menu-item-focus:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
  border-radius: 6px;
}

/* Efectos hover mejorados */
.menu-item-hover {
  position: relative;
  overflow: hidden;
}

.menu-item-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.menu-item-hover:hover::before {
  left: 100%;
}

/* Indicador de estado activo mejorado */
.status-indicator {
  position: relative;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -8px;
  width: 4px;
  height: 4px;
  background-color: #10b981;
  border-radius: 50%;
  transform: translateY(-50%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translateY(-50%) scale(1.2);
  }
}

/* Mejoras para el icono de cerrar sesión */
.logout-icon {
  transition: transform 0.2s ease-in-out;
}

.logout-button:hover .logout-icon {
  transform: translateX(2px);
}

/* Separador mejorado */
.menu-separator {
  background: linear-gradient(to right, transparent, #e5e7eb, transparent);
  height: 1px;
  margin: 8px 16px;
}

/* Sombra mejorada para el menú */
.menu-shadow {
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* Responsive improvements */
@media (max-width: 640px) {
  .user-menu-dropdown {
    width: calc(100vw - 32px);
    right: 16px;
    left: 16px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .menu-item-hover:hover {
    background-color: #000000 !important;
    color: #ffffff !important;
  }
  
  .status-indicator {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-in {
    animation: none;
  }
  
  .status-indicator::before {
    animation: none;
  }
  
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
  }
}

/* Dark mode support (if implemented in the future) */
@media (prefers-color-scheme: dark) {
  .user-menu-dropdown {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .menu-item-text {
    color: #f9fafb;
  }
  
  .menu-item-hover:hover {
    background-color: #374151;
  }
}
