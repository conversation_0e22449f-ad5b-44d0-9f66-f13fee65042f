/**
 * Script para arreglar todos los problemas de gestión de usuarios
 * 
 * Problemas que soluciona:
 * 1. <PERSON>umna updated_at faltante en tabla usuarios
 * 2. Funciones SQL faltantes para CRUD de usuarios
 * 3. Problemas de autenticación con usuarios creados
 * 
 * Ejecutar con: node src/scripts/fixUserManagementIssues.js
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Ejecuta un script SQL desde archivo
 */
async function executeSQL(filePath, description) {
  try {
    console.log(`\n📄 Ejecutando: ${description}`);
    
    const sqlContent = fs.readFileSync(filePath, 'utf8');
    
    // Dividir el SQL en comandos individuales (separados por ;)
    const commands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));
    
    for (const command of commands) {
      if (command.trim()) {
        const { error } = await supabase.rpc('execute_sql', {
          sql_query: command
        });
        
        if (error) {
          console.error(`❌ Error ejecutando comando SQL:`, error);
          console.error(`Comando: ${command.substring(0, 100)}...`);
          return false;
        }
      }
    }
    
    console.log(`✅ ${description} ejecutado exitosamente`);
    return true;
  } catch (error) {
    console.error(`❌ Error ejecutando ${description}:`, error);
    return false;
  }
}

/**
 * Verifica la estructura de la tabla usuarios
 */
async function verifyTableStructure() {
  console.log('\n🔍 Verificando estructura de la tabla usuarios...');
  
  try {
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_schema', 'public')
      .eq('table_name', 'usuarios')
      .order('ordinal_position');
    
    if (error) {
      console.error('❌ Error al verificar estructura:', error);
      return false;
    }
    
    console.log('📋 Columnas actuales en la tabla usuarios:');
    data.forEach(col => {
      console.log(`   - ${col.column_name} (${col.data_type}) ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    // Verificar columnas críticas
    const requiredColumns = ['id', 'documento', 'nombre', 'apellido', 'tipo_usuario', 'activo', 'updated_at'];
    const existingColumns = data.map(col => col.column_name);
    const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));
    
    if (missingColumns.length > 0) {
      console.log('⚠️  Columnas faltantes:', missingColumns);
      return false;
    }
    
    console.log('✅ Estructura de tabla verificada correctamente');
    return true;
  } catch (error) {
    console.error('❌ Error verificando estructura:', error);
    return false;
  }
}

/**
 * Verifica que las funciones SQL existan
 */
async function verifyFunctions() {
  console.log('\n🔍 Verificando funciones SQL...');
  
  const requiredFunctions = [
    'authenticate_user',
    'create_user',
    'update_user',
    'delete_user',
    'list_users'
  ];
  
  try {
    for (const funcName of requiredFunctions) {
      const { data, error } = await supabase.rpc('check_function_exists', {
        function_name: funcName
      });
      
      if (error) {
        // Si la función check_function_exists no existe, intentar crear las funciones
        console.log(`⚠️  No se puede verificar función ${funcName}, procediendo con creación...`);
        return false;
      }
      
      if (!data) {
        console.log(`❌ Función faltante: ${funcName}`);
        return false;
      }
      
      console.log(`✅ Función ${funcName} existe`);
    }
    
    console.log('✅ Todas las funciones SQL verificadas');
    return true;
  } catch (error) {
    console.error('❌ Error verificando funciones:', error);
    return false;
  }
}

/**
 * Prueba la creación de un usuario de prueba
 */
async function testUserCreation() {
  console.log('\n🧪 Probando creación de usuario...');
  
  const testUser = {
    nombre: 'Usuario',
    apellido: 'Prueba',
    email: `test_${Date.now()}@example.com`,
    documento: `TEST${Date.now()}`,
    rol: 'candidato',
    password: 'TestPassword123!'
  };
  
  try {
    // Intentar crear usuario usando la función SQL
    const { data, error } = await supabase.rpc('create_user', {
      p_nombre: testUser.nombre,
      p_apellido: testUser.apellido,
      p_email: testUser.email,
      p_documento: testUser.documento,
      p_rol: testUser.rol,
      p_password: testUser.password
    });
    
    if (error) {
      console.error('❌ Error en prueba de creación:', error);
      return false;
    }
    
    if (!data.success) {
      console.error('❌ Fallo en creación de usuario de prueba:', data.error);
      return false;
    }
    
    console.log('✅ Usuario de prueba creado exitosamente');
    
    // Limpiar usuario de prueba
    await supabase.rpc('delete_user', {
      p_user_id: data.user_id
    });
    
    console.log('✅ Usuario de prueba eliminado');
    return true;
  } catch (error) {
    console.error('❌ Error en prueba de usuario:', error);
    return false;
  }
}

/**
 * Función principal
 */
async function main() {
  console.log('🔧 ARREGLANDO PROBLEMAS DE GESTIÓN DE USUARIOS');
  console.log('==============================================\n');
  
  try {
    // Paso 1: Arreglar estructura de tabla
    console.log('📋 PASO 1: Arreglando estructura de tabla usuarios');
    const tableFixed = await executeSQL(
      'src/sql/fix_usuarios_table_updated_at.sql',
      'Arreglo de tabla usuarios (columna updated_at)'
    );
    
    if (!tableFixed) {
      console.error('❌ Falló el arreglo de la tabla usuarios');
      process.exit(1);
    }
    
    // Paso 2: Crear funciones SQL
    console.log('\n📋 PASO 2: Creando funciones de gestión de usuarios');
    const functionsCreated = await executeSQL(
      'src/sql/create_user_management_functions.sql',
      'Creación de funciones SQL para gestión de usuarios'
    );
    
    if (!functionsCreated) {
      console.error('❌ Falló la creación de funciones SQL');
      process.exit(1);
    }
    
    // Paso 3: Verificar estructura
    console.log('\n📋 PASO 3: Verificando configuración');
    const structureOK = await verifyTableStructure();
    
    if (!structureOK) {
      console.error('❌ La estructura de la tabla no es correcta');
      process.exit(1);
    }
    
    // Paso 4: Probar funcionalidad
    console.log('\n📋 PASO 4: Probando funcionalidad');
    const testOK = await testUserCreation();
    
    if (!testOK) {
      console.error('❌ Las pruebas de funcionalidad fallaron');
      process.exit(1);
    }
    
    console.log('\n🎉 ¡TODOS LOS PROBLEMAS ARREGLADOS EXITOSAMENTE!');
    console.log('=============================================');
    console.log('✅ Tabla usuarios reparada');
    console.log('✅ Columna updated_at agregada');
    console.log('✅ Funciones SQL creadas');
    console.log('✅ Triggers configurados');
    console.log('✅ Gestión de usuarios funcionando');
    console.log('\n📝 PRÓXIMOS PASOS:');
    console.log('1. El botón mostrar/ocultar contraseña ya está agregado al login');
    console.log('2. La funcionalidad de eliminar usuarios ya está disponible');
    console.log('3. Los usuarios creados ahora deberían poder autenticarse correctamente');
    console.log('4. Prueba crear un nuevo usuario desde el panel de administración');
    
  } catch (error) {
    console.error('\n❌ Error fatal durante el proceso:', error);
    process.exit(1);
  }
}

// Ejecutar el script
main();
