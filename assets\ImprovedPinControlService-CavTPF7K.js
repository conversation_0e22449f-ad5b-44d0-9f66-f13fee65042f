var t=(t,i,s)=>new Promise((e,n)=>{var o=t=>{try{l(s.next(t))}catch(i){n(i)}},r=t=>{try{l(s.throw(t))}catch(i){n(i)}},l=t=>t.done?e(t.value):Promise.resolve(t.value).then(o,r);l((s=s.apply(t,i)).next())});import{s as i}from"./index-CrXvaDRr.js";const s={development:{CACHE_TTL:6e4,BATCH_SIZE:10,LOG_LEVEL:"debug"},production:{CACHE_TTL:3e5,BATCH_SIZE:100,LOG_LEVEL:"info"}},e=s.development||s.development,n={ENV:e,RPC_FUNCTIONS:{GET_ALL_PSYCHOLOGISTS_PIN_BALANCE:"get_all_psychologists_pin_balance",GET_PIN_CONSUMPTION_STATS:"get_pin_consumption_stats",CREATE_LOW_PIN_NOTIFICATION:"create_low_pin_notification",CREATE_PIN_EXHAUSTED_NOTIFICATION:"create_pin_exhausted_notification"},DEFAULTS:{ASSIGNED_PATIENTS:0,COMPLETED_TESTS:0,PLAN_TYPE:"none",TOTAL_PINS:0,USED_PINS:0},THRESHOLDS:{LOW_PIN_WARNING:5,CRITICAL_PIN_WARNING:2,NO_PINS:0,BULK_ASSIGNMENT_MIN:10,BULK_ASSIGNMENT_MAX:1e3},STATUS:{UNLIMITED:"unlimited",ACTIVE:"active",LOW_PINS:"low_pins",NO_PINS:"no_pins",INACTIVE:"inactive"},PLAN_TYPES:{UNLIMITED:"unlimited",ASSIGNED:"assigned",TRIAL:"trial",NONE:"none"},ACTION_TYPES:{PIN_ASSIGNED:"pin_assigned",PIN_CONSUMED:"pin_consumed",TEST_COMPLETED:"test_completed",REPORT_GENERATED:"report_generated",NOTIFICATION_CREATED:"notification_created"},ERROR_CODES:{PSYCHOLOGIST_NOT_FOUND:"PSYCHOLOGIST_NOT_FOUND",NO_PINS_AVAILABLE:"NO_PINS_AVAILABLE",INVALID_PARAMETERS:"INVALID_PARAMETERS",SERVICE_UNAVAILABLE:"SERVICE_UNAVAILABLE"},DEFAULTS:{HISTORY_LIMIT:50,CACHE_TTL:e.CACHE_TTL,RETRY_ATTEMPTS:3,BATCH_SIZE:e.BATCH_SIZE,LOG_LEVEL:e.LOG_LEVEL,CONNECTION_TIMEOUT:3e4,QUERY_TIMEOUT:1e4},PERFORMANCE:{ENABLE_CACHING:!0,ENABLE_BATCH_OPERATIONS:!0,MAX_CONCURRENT_OPERATIONS:10,DEBOUNCE_DELAY:300,THROTTLE_LIMIT:100},MONITORING:{HEALTH_CHECK_INTERVAL:6e4,METRICS_COLLECTION_INTERVAL:3e5,ERROR_THRESHOLD:.05,RESPONSE_TIME_THRESHOLD:2e3}};class o{static validateAssignPins(t,i,s,e){const o=[];return t&&"string"==typeof t||o.push("psychologistId must be a valid string"),s||i&&!(i<0)&&Number.isInteger(i)||o.push("pins must be a positive integer when not unlimited"),e&&!Object.values(n.PLAN_TYPES).includes(e)&&o.push("planType must be a valid plan type"),{isValid:0===o.length,errors:o}}static validateConsumePin(t,i,s,e){const n=[];return t&&"string"==typeof t||n.push("psychologistId must be a valid string"),i&&"string"!=typeof i&&n.push("patientId must be a string if provided"),s&&"string"!=typeof s&&n.push("testSessionId must be a string if provided"),e&&"string"!=typeof e&&n.push("reportId must be a string if provided"),{isValid:0===n.length,errors:n}}}class r{static logAction(s,e){return t(this,arguments,function*(t,s,e={},o=null,r=null,l=null){try{const{error:a}=yield i.from("pin_usage_logs").insert({psychologist_id:t,patient_id:o,test_session_id:r,report_id:l,action_type:s,pins_before:e.pins_before||0,pins_after:e.pins_after||0,pins_consumed:s===n.ACTION_TYPES.PIN_CONSUMED?1:0,description:this.getActionDescription(s,e),metadata:e});if(a)throw a}catch(a){throw a}})}static getActionDescription(t,i){switch(t){case n.ACTION_TYPES.PIN_ASSIGNED:return`Assigned ${i.pins_assigned||0} pins${i.is_unlimited?" (unlimited plan)":""}`;case n.ACTION_TYPES.PIN_CONSUMED:return i.is_unlimited?"Pin consumed (unlimited plan)":`Pin consumed. ${i.pins_after||0} pins remaining`;case n.ACTION_TYPES.TEST_COMPLETED:return"Test completed - Pin consumed automatically";case n.ACTION_TYPES.REPORT_GENERATED:return"Report generated - Pin consumed automatically";default:return`Action: ${t}`}}static logInfo(t,i=null){}static logError(t,i=null){}static logSuccess(t,i=null){}}class l{getPsychologistUsage(s){return t(this,null,function*(){const{data:t,error:e}=yield i.from("psychologist_usage_control").select("*").eq("psychologist_id",s).eq("is_active",!0).single();if(e&&"PGRST116"!==e.code)throw e;return t})}upsertPsychologistUsage(i,s,e,n){return t(this,null,function*(){const t=yield this.getPsychologistUsage(i);return t?yield this._updateExistingUsage(t,s,e,n):yield this._createNewUsage(i,s,e,n)})}incrementUsedPins(s){return t(this,null,function*(){const{data:t,error:e}=yield i.rpc("consume_pin",{p_control_id:s});if(e)throw e;return t})}getPinUsageHistory(){return t(this,arguments,function*(t=null,s=n.DEFAULT_HISTORY_LIMIT){let e=i.from("pin_usage_logs").select("\n        *,\n        psychologist:psicologos(nombre, apellido, email),\n        patient:pacientes(nombre, apellido, documento)\n      ").order("created_at",{ascending:!1}).limit(s);t&&(e=e.eq("psychologist_id",t));const{data:o,error:r}=yield e;if(r)throw r;return o||[]})}_updateExistingUsage(s,e,n,o){return t(this,null,function*(){const t=n?s.total_uses:s.total_uses+e,{data:r,error:l}=yield i.from("psychologist_usage_control").update({total_uses:t,is_unlimited:n,plan_type:o,updated_at:(new Date).toISOString(),is_active:!0}).eq("id",s.id).select().single();if(l)throw l;return r})}_createNewUsage(s,e,n,o){return t(this,null,function*(){const{data:t,error:r}=yield i.from("psychologist_usage_control").insert({psychologist_id:s,total_uses:n?0:e,used_uses:0,is_unlimited:n,plan_type:o,is_active:!0}).select().single();if(r)throw r;return t})}}class a{createLowPinNotification(s,e){return t(this,null,function*(){try{const{error:t}=yield i.rpc("create_low_pin_notification",{p_psychologist_id:s,p_remaining_pins:e});if(t)throw r.logError("Error creating low pin notification",t),t;r.logInfo(`Low pin notification created for psychologist ${s}`)}catch(t){throw r.logError("Error in createLowPinNotification",t),t}})}createPinExhaustedNotification(s){return t(this,null,function*(){try{const{error:t}=yield i.rpc("create_pin_exhausted_notification",{p_psychologist_id:s});if(t)throw r.logError("Error creating pin exhausted notification",t),t;r.logInfo(`Pin exhausted notification created for psychologist ${s}`)}catch(t){throw r.logError("Error in createPinExhaustedNotification",t),t}})}}const _=new class{constructor(){this.repository=new l,this.notificationService=new a}getPinConsumptionStats(){return t(this,null,function*(){try{r.logInfo("Getting pin consumption statistics...");const t=(yield this._fetchOptimizedPsychologistStats()).map(t=>this._transformPsychologistStats(t));return r.logSuccess(`Pin statistics retrieved: ${t.length} psychologists`),t}catch(t){throw r.logError("Error getting pin consumption stats",t),t}})}getAllPsychologists(){return t(this,null,function*(){try{r.logInfo("Getting all psychologists...");const t=(yield this._fetchAllPsychologistsOptimized()).map(t=>this._transformPsychologistStats(t,!0));return r.logSuccess(`All psychologists retrieved: ${t.length}`),t}catch(t){throw r.logError("Error getting all psychologists",t),t}})}assignPins(i,s){return t(this,arguments,function*(t,i,s=!1,e=n.PLAN_TYPES.ASSIGNED){try{const l=o.validateAssignPins(t,i,s,e);if(!l.isValid)throw new Error(l.errors.join(", "));r.logInfo("Assigning pins",{psychologistId:t,pins:i,isUnlimited:s,planType:e});const a=yield this.repository.upsertPsychologistUsage(t,i,s,e);return yield r.logAction(t,n.ACTION_TYPES.PIN_ASSIGNED,{pins_assigned:i,is_unlimited:s,plan_type:e}),r.logSuccess("Pins assigned successfully"),a}catch(l){throw r.logError("Error assigning pins",l),l}})}consumePin(i,s=null,e=null,l=null){return t(this,null,function*(){const t=o.validateConsumePin(i,s,e,l);if(!t.isValid)throw new Error(`Validation failed: ${t.errors.join(", ")}`);try{r.logInfo("Consuming pin",{psychologistId:i,patientId:s,testSessionId:e,reportId:l});const t=yield this.repository.getPsychologistUsage(i);if(!t)throw new Error(n.ERROR_CODES.PSYCHOLOGIST_NOT_FOUND);return t.is_unlimited?yield this._handleUnlimitedPinConsumption(i,s,e,l):yield this._handleLimitedPinConsumption(t,i,s,e,l)}catch(a){throw r.logError("Error consuming pin",a),a}})}checkPsychologistUsage(i){return t(this,null,function*(){try{const t=yield this.repository.getPsychologistUsage(i);if(!t)return{canUse:!1,reason:"No pins assigned",remainingPins:0,isUnlimited:!1};if(t.is_unlimited)return{canUse:!0,reason:"Unlimited plan",remainingPins:null,isUnlimited:!0};const s=t.total_uses-t.used_uses;return{canUse:s>0,reason:s>0?"Pins available":"No pins available",remainingPins:s,isUnlimited:!1,totalPins:t.total_uses,usedPins:t.used_uses}}catch(t){throw r.logError("Error checking psychologist usage",t),t}})}getPinUsageHistory(){return t(this,arguments,function*(t=null,i=n.DEFAULTS.HISTORY_LIMIT){try{return yield this.repository.getPinUsageHistory(t,i)}catch(s){throw r.logError("Error getting pin usage history",s),s}})}getPinConsumptionAlerts(){return t(this,null,function*(){try{const t=yield this.getPinConsumptionStats();return this._generateAlertsFromStats(t)}catch(t){throw r.logError("Error getting pin consumption alerts",t),t}})}getSystemSummary(){return t(this,null,function*(){try{const t=yield this.getPinConsumptionStats();return this._calculateSystemSummary(t)}catch(t){throw r.logError("Error getting system summary",t),t}})}_fetchOptimizedPsychologistStats(){return t(this,null,function*(){const{data:t,error:s}=yield i.rpc("get_all_psychologists_pin_balance");return s?yield this._fetchPsychologistsManual(!0):t||[]})}_fetchAllPsychologistsOptimized(){return t(this,null,function*(){const{data:t,error:s}=yield i.rpc("get_all_psychologists_pin_balance");return s?yield this._fetchPsychologistsManual(!1):t||[]})}_fetchPsychologistsManual(s=!1){return t(this,null,function*(){let t=i.from("psicologos").select("\n        id,\n        nombre,\n        apellido,\n        email,\n        psychologist_usage_control!left (\n          total_uses,\n          used_uses,\n          is_unlimited,\n          plan_type,\n          updated_at,\n          is_active\n        )\n      ");s&&(t=t.not("psychologist_usage_control.id","is",null));const{data:e,error:n}=yield t;if(n)throw n;return(e||[]).map(t=>{var i;const s=null==(i=t.psychologist_usage_control)?void 0:i[0];return{psychologist_id:t.id,psych_id:t.id,psych_name:`${t.nombre} ${t.apellido}`,psych_email:t.email,nombre:t.nombre,apellido:t.apellido,email:t.email,total_uses:(null==s?void 0:s.total_uses)||0,used_uses:(null==s?void 0:s.used_uses)||0,is_unlimited:(null==s?void 0:s.is_unlimited)||!1,plan_type:(null==s?void 0:s.plan_type)||"none",updated_at:null==s?void 0:s.updated_at,assigned_patients:0,completed_tests:0,total_asignado:(null==s?void 0:s.total_uses)||0,total_consumido:(null==s?void 0:s.used_uses)||0,pines_disponibles:s?Math.max(0,(s.total_uses||0)-(s.used_uses||0)):0}})})}_transformPsychologistStats(t,i=!1){const s=t.total_uses||t.total_asignado||0,e=t.used_uses||t.total_consumido||0,n=t.is_unlimited?null:Math.max(0,s-e),o=t.psych_name||`${t.nombre} ${t.apellido}`,r=t.psych_email||t.email,l={psychologist_id:t.psychologist_id||t.psych_id||t.id,psychologist_name:o,psychologist_email:r,total_pins:s,used_pins:e,remaining_pins:n,is_unlimited:t.is_unlimited||!1,plan_type:t.plan_type||"none",usage_percentage:this._calculateUsagePercentage(e,s,t.is_unlimited),assigned_patients:t.assigned_patients||t.pacientes_asignados||0,completed_tests:t.completed_tests||t.tests_completados||0,status:this._determineStatus(t.is_unlimited,s,n),last_activity:t.updated_at||t.ultima_transaccion};return i&&(l.has_control=!!(s>0||t.is_unlimited)),l}_calculateUsagePercentage(t,i,s){return s||0===i?0:Math.round(t/i*100*100)/100}_determineStatus(t,i,s){return t?n.STATUS.UNLIMITED:0===i||s<=0?n.STATUS.NO_PINS:s<=n.THRESHOLDS.LOW_PIN_WARNING?n.STATUS.LOW_PINS:n.STATUS.ACTIVE}_handleUnlimitedPinConsumption(i,s,e,o){return t(this,null,function*(){return yield r.logAction(i,n.ACTION_TYPES.PIN_CONSUMED,{patient_id:s,test_session_id:e,report_id:o,is_unlimited:!0},s,e,o),r.logSuccess("Pin consumed (unlimited plan)"),!0})}_handleLimitedPinConsumption(i,s,e,o,l){return t(this,null,function*(){const t=i.total_uses-i.used_uses;if(t<=0)throw new Error(n.ERROR_CODES.NO_PINS_AVAILABLE);try{yield this.repository.incrementUsedPins(i.id)}catch(_){if("P0001"===(null==_?void 0:_.code))throw new Error(n.ERROR_CODES.NO_PINS_AVAILABLE);throw _}yield r.logAction(s,n.ACTION_TYPES.PIN_CONSUMED,{pins_before:t,pins_after:t-1,patient_id:e,test_session_id:o,report_id:l},e,o,l);const a=t-1;return a<=n.THRESHOLDS.LOW_PIN_WARNING&&a>0&&(yield this.notificationService.createLowPinNotification(s,a)),r.logSuccess(`Pin consumed. Remaining pins: ${a}`),!0})}_generateAlertsFromStats(t){const i=[];return t.forEach(t=>{t.status===n.STATUS.LOW_PINS?i.push({type:"warning",psychologist_id:t.psychologist_id,psychologist_name:t.psychologist_name,message:`${t.psychologist_name} has only ${t.remaining_pins} pins remaining`,severity:"warning"}):t.status===n.STATUS.NO_PINS&&i.push({type:"error",psychologist_id:t.psychologist_id,psychologist_name:t.psychologist_name,message:`${t.psychologist_name} has no pins available`,severity:"error"})}),i}_calculateSystemSummary(t){return{totalPsychologists:t.length,totalPinsAssigned:t.reduce((t,i)=>t+(i.is_unlimited?0:i.total_pins),0),totalPinsUsed:t.reduce((t,i)=>t+i.used_pins,0),totalPinsRemaining:t.reduce((t,i)=>t+(i.is_unlimited?0:i.remaining_pins||0),0),unlimitedPsychologists:t.filter(t=>t.is_unlimited).length,activePsychologists:t.filter(t=>t.status===n.STATUS.ACTIVE).length,lowPinsPsychologists:t.filter(t=>t.status===n.STATUS.LOW_PINS).length,noPinsPsychologists:t.filter(t=>t.status===n.STATUS.NO_PINS).length,totalPatients:t.reduce((t,i)=>t+i.assigned_patients,0),totalTests:t.reduce((t,i)=>t+i.completed_tests,0)}}};export{n as P,_ as p};
