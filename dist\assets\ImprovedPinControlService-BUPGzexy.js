var t=(t,s,i)=>new Promise((e,n)=>{var o=t=>{try{r(i.next(t))}catch(s){n(s)}},l=t=>{try{r(i.throw(t))}catch(s){n(s)}},r=t=>t.done?e(t.value):Promise.resolve(t.value).then(o,l);r((i=i.apply(t,s)).next())});import{s}from"./index-Bdl1jgS_.js";import{P as i,a as e}from"./PinLogger-C2v3yGM1.js";import{P as n,N as o,a as l}from"./NotificationService-DiDbKBbI.js";const r=new class{constructor(){this.repository=new n,this.notificationService=new o}getPinConsumptionStats(){return t(this,null,function*(){try{i.logInfo("Getting pin consumption statistics...");const t=(yield this._fetchOptimizedPsychologistStats()).map(t=>this._transformPsychologistStats(t));return i.logSuccess(`Pin statistics retrieved: ${t.length} psychologists`),t}catch(t){throw i.logError("Error getting pin consumption stats",t),t}})}getAllPsychologists(){return t(this,null,function*(){try{i.logInfo("Getting all psychologists...");const t=(yield this._fetchAllPsychologistsOptimized()).map(t=>this._transformPsychologistStats(t,!0));return i.logSuccess(`All psychologists retrieved: ${t.length}`),t}catch(t){throw i.logError("Error getting all psychologists",t),t}})}assignPins(s,n){return t(this,arguments,function*(t,s,n=!1,o=e.PLAN_TYPES.ASSIGNED){try{const r=l.validateAssignPins(t,s,n,o);if(!r.isValid)throw new Error(r.errors.join(", "));i.logInfo("Assigning pins",{psychologistId:t,pins:s,isUnlimited:n,planType:o});const a=yield this.repository.upsertPsychologistUsage(t,s,n,o);return yield i.logAction(t,e.ACTION_TYPES.PIN_ASSIGNED,{pins_assigned:s,is_unlimited:n,plan_type:o}),i.logSuccess("Pins assigned successfully"),a}catch(r){throw i.logError("Error assigning pins",r),r}})}consumePin(s,n=null,o=null,r=null){return t(this,null,function*(){const t=l.validateConsumePin(s,n,o,r);if(!t.isValid)throw new Error(`Validation failed: ${t.errors.join(", ")}`);try{i.logInfo("Consuming pin",{psychologistId:s,patientId:n,testSessionId:o,reportId:r});const t=yield this.repository.getPsychologistUsage(s);if(!t)throw new Error(e.ERROR_CODES.PSYCHOLOGIST_NOT_FOUND);return t.is_unlimited?yield this._handleUnlimitedPinConsumption(s,n,o,r):yield this._handleLimitedPinConsumption(t,s,n,o,r)}catch(a){throw i.logError("Error consuming pin",a),a}})}checkPsychologistUsage(s){return t(this,null,function*(){try{const t=yield this.repository.getPsychologistUsage(s);if(!t)return{canUse:!1,reason:"No pins assigned",remainingPins:0,isUnlimited:!1};if(t.is_unlimited)return{canUse:!0,reason:"Unlimited plan",remainingPins:null,isUnlimited:!0};const i=t.total_uses-t.used_uses;return{canUse:i>0,reason:i>0?"Pins available":"No pins available",remainingPins:i,isUnlimited:!1,totalPins:t.total_uses,usedPins:t.used_uses}}catch(t){throw i.logError("Error checking psychologist usage",t),t}})}getPinUsageHistory(){return t(this,arguments,function*(t=null,s=e.DEFAULTS.HISTORY_LIMIT){try{return yield this.repository.getPinUsageHistory(t,s)}catch(n){throw i.logError("Error getting pin usage history",n),n}})}getPinConsumptionAlerts(){return t(this,null,function*(){try{const t=yield this.getPinConsumptionStats();return this._generateAlertsFromStats(t)}catch(t){throw i.logError("Error getting pin consumption alerts",t),t}})}getSystemSummary(){return t(this,null,function*(){try{const t=yield this.getPinConsumptionStats();return this._calculateSystemSummary(t)}catch(t){throw i.logError("Error getting system summary",t),t}})}_fetchOptimizedPsychologistStats(){return t(this,null,function*(){const{data:t,error:i}=yield s.rpc("get_all_psychologists_pin_balance");return i?yield this._fetchPsychologistsManual(!0):t||[]})}_fetchAllPsychologistsOptimized(){return t(this,null,function*(){const{data:t,error:i}=yield s.rpc("get_all_psychologists_pin_balance");return i?yield this._fetchPsychologistsManual(!1):t||[]})}_fetchPsychologistsManual(i=!1){return t(this,null,function*(){let t=s.from("psicologos").select("\n        id,\n        nombre,\n        apellido,\n        email,\n        psychologist_usage_control!left (\n          total_uses,\n          used_uses,\n          is_unlimited,\n          plan_type,\n          updated_at,\n          is_active\n        )\n      ");i&&(t=t.not("psychologist_usage_control.id","is",null));const{data:e,error:n}=yield t;if(n)throw n;return(e||[]).map(t=>{var s;const i=null==(s=t.psychologist_usage_control)?void 0:s[0];return{psychologist_id:t.id,psych_id:t.id,psych_name:`${t.nombre} ${t.apellido}`,psych_email:t.email,nombre:t.nombre,apellido:t.apellido,email:t.email,total_uses:(null==i?void 0:i.total_uses)||0,used_uses:(null==i?void 0:i.used_uses)||0,is_unlimited:(null==i?void 0:i.is_unlimited)||!1,plan_type:(null==i?void 0:i.plan_type)||"none",updated_at:null==i?void 0:i.updated_at,assigned_patients:0,completed_tests:0,total_asignado:(null==i?void 0:i.total_uses)||0,total_consumido:(null==i?void 0:i.used_uses)||0,pines_disponibles:i?Math.max(0,(i.total_uses||0)-(i.used_uses||0)):0}})})}_transformPsychologistStats(t,s=!1){const i=t.total_uses||t.total_asignado||0,e=t.used_uses||t.total_consumido||0,n=t.is_unlimited?null:Math.max(0,i-e),o=t.psych_name||`${t.nombre} ${t.apellido}`,l=t.psych_email||t.email,r={psychologist_id:t.psychologist_id||t.psych_id||t.id,psychologist_name:o,psychologist_email:l,total_pins:i,used_pins:e,remaining_pins:n,is_unlimited:t.is_unlimited||!1,plan_type:t.plan_type||"none",usage_percentage:this._calculateUsagePercentage(e,i,t.is_unlimited),assigned_patients:t.assigned_patients||t.pacientes_asignados||0,completed_tests:t.completed_tests||t.tests_completados||0,status:this._determineStatus(t.is_unlimited,i,n),last_activity:t.updated_at||t.ultima_transaccion};return s&&(r.has_control=!!(i>0||t.is_unlimited)),r}_calculateUsagePercentage(t,s,i){return i||0===s?0:Math.round(t/s*100*100)/100}_determineStatus(t,s,i){return t?e.STATUS.UNLIMITED:0===s||i<=0?e.STATUS.NO_PINS:i<=e.THRESHOLDS.LOW_PIN_WARNING?e.STATUS.LOW_PINS:e.STATUS.ACTIVE}_handleUnlimitedPinConsumption(s,n,o,l){return t(this,null,function*(){return yield i.logAction(s,e.ACTION_TYPES.PIN_CONSUMED,{patient_id:n,test_session_id:o,report_id:l,is_unlimited:!0},n,o,l),i.logSuccess("Pin consumed (unlimited plan)"),!0})}_handleLimitedPinConsumption(s,n,o,l,r){return t(this,null,function*(){const t=s.total_uses-s.used_uses;if(t<=0)throw new Error(e.ERROR_CODES.NO_PINS_AVAILABLE);try{yield this.repository.incrementUsedPins(s.id)}catch(c){if("P0001"===(null==c?void 0:c.code))throw new Error(e.ERROR_CODES.NO_PINS_AVAILABLE);throw c}yield i.logAction(n,e.ACTION_TYPES.PIN_CONSUMED,{pins_before:t,pins_after:t-1,patient_id:o,test_session_id:l,report_id:r},o,l,r);const a=t-1;return a<=e.THRESHOLDS.LOW_PIN_WARNING&&a>0&&(yield this.notificationService.createLowPinNotification(n,a)),i.logSuccess(`Pin consumed. Remaining pins: ${a}`),!0})}_generateAlertsFromStats(t){const s=[];return t.forEach(t=>{t.status===e.STATUS.LOW_PINS?s.push({type:"warning",psychologist_id:t.psychologist_id,psychologist_name:t.psychologist_name,message:`${t.psychologist_name} has only ${t.remaining_pins} pins remaining`,severity:"warning"}):t.status===e.STATUS.NO_PINS&&s.push({type:"error",psychologist_id:t.psychologist_id,psychologist_name:t.psychologist_name,message:`${t.psychologist_name} has no pins available`,severity:"error"})}),s}_calculateSystemSummary(t){return{totalPsychologists:t.length,totalPinsAssigned:t.reduce((t,s)=>t+(s.is_unlimited?0:s.total_pins),0),totalPinsUsed:t.reduce((t,s)=>t+s.used_pins,0),totalPinsRemaining:t.reduce((t,s)=>t+(s.is_unlimited?0:s.remaining_pins||0),0),unlimitedPsychologists:t.filter(t=>t.is_unlimited).length,activePsychologists:t.filter(t=>t.status===e.STATUS.ACTIVE).length,lowPinsPsychologists:t.filter(t=>t.status===e.STATUS.LOW_PINS).length,noPinsPsychologists:t.filter(t=>t.status===e.STATUS.NO_PINS).length,totalPatients:t.reduce((t,s)=>t+s.assigned_patients,0),totalTests:t.reduce((t,s)=>t+s.completed_tests,0)}}};export{r as p};
