import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import ValidatedReportButton, { 
  CompactValidatedReportButton, 
  BatchValidatedReportButton 
} from '../../components/reports/ValidatedReportButton';
import PinValidationAlert from '../../components/pin/PinValidationAlert';
import { useAdvancedPinValidation } from '../../hooks/useAdvancedPinValidation';
import InformesService from '../../services/InformesService';
import { toast } from 'react-toastify';
import { FaUser, FaUsers, FaFileAlt } from 'react-icons/fa';

/**
 * Página de demostración del nuevo sistema de validación de pines
 * Muestra cómo usar los componentes mejorados para generar informes
 */
const ReportGenerationDemo = () => {
  const [selectedPatients, setSelectedPatients] = useState([]);
  const [availablePatients, setAvailablePatients] = useState([]);
  const [psychologistId, setPsychologistId] = useState('');
  const [loading, setLoading] = useState(true);

  // Hook de validación avanzada
  const {
    validationResult,
    isValidating,
    canProceed,
    validateSingleReport,
    validateBatchReports
  } = useAdvancedPinValidation(psychologistId, {
    showToastAlerts: false,
    autoValidate: true
  });

  useEffect(() => {
    loadDemoData();
  }, []);

  const loadDemoData = async () => {
    try {
      setLoading(true);
      
      // Simular datos de pacientes para la demo
      const mockPatients = [
        { id: 'patient-1', name: 'Juan Pérez', hasResults: true },
        { id: 'patient-2', name: 'María García', hasResults: true },
        { id: 'patient-3', name: 'Carlos López', hasResults: true },
        { id: 'patient-4', name: 'Ana Martínez', hasResults: true },
        { id: 'patient-5', name: 'Luis Rodríguez', hasResults: true }
      ];
      
      setAvailablePatients(mockPatients);
      
      // Simular ID de psicólogo (en producción vendría del contexto de usuario)
      setPsychologistId('demo-psychologist-id');
      
    } catch (error) {
      console.error('Error cargando datos de demo:', error);
      toast.error('Error cargando datos de demostración');
    } finally {
      setLoading(false);
    }
  };

  const handlePatientSelection = (patientId) => {
    setSelectedPatients(prev => {
      if (prev.includes(patientId)) {
        return prev.filter(id => id !== patientId);
      } else {
        return [...prev, patientId];
      }
    });
  };

  const handleGenerateSingleReport = async (patientId) => {
    try {
      const patient = availablePatients.find(p => p.id === patientId);
      
      // Simular generación de informe
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success(`Informe generado para ${patient.name}`);
      
    } catch (error) {
      console.error('Error generando informe:', error);
      toast.error('Error al generar el informe');
    }
  };

  const handleGenerateBatchReports = async (patientIds) => {
    try {
      // Simular generación en lote
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      toast.success(`${patientIds.length} informes generados exitosamente`);
      setSelectedPatients([]);
      
    } catch (error) {
      console.error('Error generando informes en lote:', error);
      toast.error('Error al generar los informes');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando demostración...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Sistema Mejorado de Generación de Informes
        </h1>
        <p className="text-gray-600">
          Demostración del nuevo sistema de validación de pines con controles automáticos
        </p>
      </div>

      {/* Estado general de validación */}
      {validationResult && (
        <div className="mb-6">
          <PinValidationAlert 
            validationResult={validationResult}
            showDetails={true}
          />
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Generación Individual */}
        <Card>
          <CardHeader className="bg-blue-50 border-b">
            <div className="flex items-center">
              <FaUser className="text-blue-600 mr-2" />
              <h2 className="text-lg font-semibold text-gray-800">
                Generación Individual
              </h2>
            </div>
          </CardHeader>
          <CardBody className="space-y-4">
            <p className="text-sm text-gray-600 mb-4">
              Generar informes individuales con validación automática de pines
            </p>
            
            {availablePatients.map(patient => (
              <div key={patient.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h3 className="font-medium text-gray-900">{patient.name}</h3>
                    <p className="text-sm text-gray-500">ID: {patient.id}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {patient.hasResults && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                        Con resultados
                      </span>
                    )}
                  </div>
                </div>
                
                <ValidatedReportButton
                  psychologistId={psychologistId}
                  patientId={patient.id}
                  onGenerateReport={handleGenerateSingleReport}
                  buttonText="Generar Informe"
                  size="sm"
                  showValidationDetails={false}
                />
              </div>
            ))}
          </CardBody>
        </Card>

        {/* Generación en Lote */}
        <Card>
          <CardHeader className="bg-green-50 border-b">
            <div className="flex items-center">
              <FaUsers className="text-green-600 mr-2" />
              <h2 className="text-lg font-semibold text-gray-800">
                Generación en Lote
              </h2>
            </div>
          </CardHeader>
          <CardBody className="space-y-4">
            <p className="text-sm text-gray-600 mb-4">
              Seleccionar múltiples pacientes para generar informes en lote
            </p>
            
            <div className="space-y-2">
              {availablePatients.map(patient => (
                <label key={patient.id} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded">
                  <input
                    type="checkbox"
                    checked={selectedPatients.includes(patient.id)}
                    onChange={() => handlePatientSelection(patient.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-900">{patient.name}</span>
                </label>
              ))}
            </div>

            {selectedPatients.length > 0 && (
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800 mb-3">
                  {selectedPatients.length} pacientes seleccionados
                </p>
                
                <BatchValidatedReportButton
                  psychologistId={psychologistId}
                  patientIds={selectedPatients}
                  onGenerateReport={handleGenerateBatchReports}
                  showValidationDetails={true}
                />
              </div>
            )}

            {selectedPatients.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <FaFileAlt className="mx-auto h-12 w-12 text-gray-300 mb-2" />
                <p>Seleccione pacientes para generar informes en lote</p>
              </div>
            )}
          </CardBody>
        </Card>
      </div>

      {/* Información adicional */}
      <Card className="mt-6">
        <CardHeader className="bg-yellow-50 border-b">
          <h2 className="text-lg font-semibold text-gray-800">
            Características del Sistema Mejorado
          </h2>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">✅ Validaciones Implementadas</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Verificación previa de pines disponibles</li>
                <li>• Bloqueo automático sin pines</li>
                <li>• Alertas de pines bajos</li>
                <li>• Validación en lote</li>
                <li>• Confirmación para operaciones críticas</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">🚀 Mejoras Adicionales</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Notificaciones automáticas</li>
                <li>• Control por sesión de paciente</li>
                <li>• Historial de consumo transparente</li>
                <li>• Sistema de recarga de pines</li>
                <li>• Reportes detallados de uso</li>
              </ul>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default ReportGenerationDemo;
