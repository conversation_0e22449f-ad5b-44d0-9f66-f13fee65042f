import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { 
  FaCoins, 
  FaPlus, 
  FaUser, 
  FaCalendarAlt,
  FaCheck,
  FaTimes,
  FaClock,
  FaSearch,
  FaFilter,
  FaDownload,
  FaEye
} from 'react-icons/fa';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { toast } from 'react-toastify';

/**
 * Página de gestión de recargas de pines para administradores
 * Permite procesar solicitudes y gestionar asignaciones de pines
 */
const PinRechargeManagement = () => {
  const [rechargeRequests, setRechargeRequests] = useState([]);
  const [psychologists, setPsychologists] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('pending'); // 'pending', 'processed', 'manual'
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [showManualForm, setShowManualForm] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    urgency: 'all',
    dateRange: 'all'
  });

  // Cargar datos
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Simular carga de datos
      const mockRequests = [
        {
          id: '1',
          psychologistId: 'psy-001',
          psychologistName: 'Dr. Juan Pérez',
          psychologistEmail: '<EMAIL>',
          requestedPins: 100,
          currentPins: 5,
          urgency: 'high',
          reason: 'Necesito generar informes urgentes para mis pacientes',
          status: 'pending',
          createdAt: new Date().toISOString(),
          metadata: {
            lastActivity: '2024-01-15',
            totalReports: 45,
            averageMonthly: 25
          }
        },
        {
          id: '2',
          psychologistId: 'psy-002',
          psychologistName: 'Dra. María García',
          psychologistEmail: '<EMAIL>',
          requestedPins: 50,
          currentPins: 0,
          urgency: 'urgent',
          reason: 'Se agotaron mis pines y tengo pacientes esperando',
          status: 'pending',
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          metadata: {
            lastActivity: '2024-01-14',
            totalReports: 78,
            averageMonthly: 35
          }
        }
      ];

      const mockPsychologists = [
        { id: 'psy-001', name: 'Dr. Juan Pérez', email: '<EMAIL>', currentPins: 5 },
        { id: 'psy-002', name: 'Dra. María García', email: '<EMAIL>', currentPins: 0 },
        { id: 'psy-003', name: 'Dr. Carlos López', email: '<EMAIL>', currentPins: 25 }
      ];

      setRechargeRequests(mockRequests);
      setPsychologists(mockPsychologists);

    } catch (error) {
      console.error('Error cargando datos:', error);
      toast.error('Error al cargar los datos');
    } finally {
      setLoading(false);
    }
  };

  const handleApproveRequest = async (requestId, approvedPins) => {
    try {
      // Simular aprobación
      setRechargeRequests(prev => 
        prev.map(req => 
          req.id === requestId 
            ? { ...req, status: 'approved', approvedPins, processedAt: new Date().toISOString() }
            : req
        )
      );

      toast.success(`Solicitud aprobada: ${approvedPins} pines asignados`);
      setSelectedRequest(null);

    } catch (error) {
      console.error('Error aprobando solicitud:', error);
      toast.error('Error al aprobar la solicitud');
    }
  };

  const handleRejectRequest = async (requestId, reason) => {
    try {
      // Simular rechazo
      setRechargeRequests(prev => 
        prev.map(req => 
          req.id === requestId 
            ? { ...req, status: 'rejected', rejectionReason: reason, processedAt: new Date().toISOString() }
            : req
        )
      );

      toast.success('Solicitud rechazada');
      setSelectedRequest(null);

    } catch (error) {
      console.error('Error rechazando solicitud:', error);
      toast.error('Error al rechazar la solicitud');
    }
  };

  const handleManualAssignment = async (psychologistId, pins, reason) => {
    try {
      // Simular asignación manual
      toast.success(`${pins} pines asignados manualmente`);
      setShowManualForm(false);
      
      // Actualizar lista de psicólogos
      setPsychologists(prev => 
        prev.map(psy => 
          psy.id === psychologistId 
            ? { ...psy, currentPins: psy.currentPins + pins }
            : psy
        )
      );

    } catch (error) {
      console.error('Error en asignación manual:', error);
      toast.error('Error en la asignación manual');
    }
  };

  const filteredRequests = rechargeRequests.filter(req => {
    if (activeTab === 'pending' && req.status !== 'pending') return false;
    if (activeTab === 'processed' && req.status === 'pending') return false;
    
    if (filters.search && !req.psychologistName.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }
    
    if (filters.urgency !== 'all' && req.urgency !== filters.urgency) {
      return false;
    }
    
    return true;
  });

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Gestión de Recargas de Pines
            </h1>
            <p className="text-gray-600">
              Administrar solicitudes y asignaciones de pines
            </p>
          </div>
          
          <Button
            onClick={() => setShowManualForm(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <FaPlus className="mr-2" />
            Asignación Manual
          </Button>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('pending')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'pending'
                ? 'bg-white text-blue-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FaClock className="inline mr-2" />
            Pendientes ({rechargeRequests.filter(r => r.status === 'pending').length})
          </button>
          <button
            onClick={() => setActiveTab('processed')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'processed'
                ? 'bg-white text-blue-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FaCheck className="inline mr-2" />
            Procesadas ({rechargeRequests.filter(r => r.status !== 'pending').length})
          </button>
          <button
            onClick={() => setActiveTab('manual')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'manual'
                ? 'bg-white text-blue-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FaUser className="inline mr-2" />
            Psicólogos ({psychologists.length})
          </button>
        </div>
      </div>

      {/* Filters */}
      {(activeTab === 'pending' || activeTab === 'processed') && (
        <Card className="mb-6">
          <CardBody className="py-4">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center space-x-2">
                <FaSearch className="text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar psicólogo..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="px-3 py-1 border border-gray-300 rounded text-sm"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <FaFilter className="text-gray-400" />
                <select
                  value={filters.urgency}
                  onChange={(e) => setFilters(prev => ({ ...prev, urgency: e.target.value }))}
                  className="px-3 py-1 border border-gray-300 rounded text-sm"
                >
                  <option value="all">Todas las urgencias</option>
                  <option value="low">Baja</option>
                  <option value="normal">Normal</option>
                  <option value="high">Alta</option>
                  <option value="urgent">Urgente</option>
                </select>
              </div>
              
              <Button size="sm" variant="outline">
                <FaDownload className="mr-1" />
                Exportar
              </Button>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Content */}
      {activeTab === 'pending' && (
        <PendingRequestsTab 
          requests={filteredRequests}
          onApprove={handleApproveRequest}
          onReject={handleRejectRequest}
          onViewDetails={setSelectedRequest}
          loading={loading}
        />
      )}

      {activeTab === 'processed' && (
        <ProcessedRequestsTab 
          requests={filteredRequests}
          loading={loading}
        />
      )}

      {activeTab === 'manual' && (
        <PsychologistManagementTab 
          psychologists={psychologists}
          onManualAssign={handleManualAssignment}
          loading={loading}
        />
      )}

      {/* Modal de detalles de solicitud */}
      {selectedRequest && (
        <RequestDetailsModal
          request={selectedRequest}
          onClose={() => setSelectedRequest(null)}
          onApprove={handleApproveRequest}
          onReject={handleRejectRequest}
        />
      )}

      {/* Modal de asignación manual */}
      {showManualForm && (
        <ManualAssignmentModal
          psychologists={psychologists}
          onClose={() => setShowManualForm(false)}
          onAssign={handleManualAssignment}
        />
      )}
    </div>
  );
};

/**
 * Tab de solicitudes pendientes
 */
const PendingRequestsTab = ({ requests, onApprove, onReject, onViewDetails, loading }) => {
  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardBody className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </CardBody>
          </Card>
        ))}
      </div>
    );
  }

  if (requests.length === 0) {
    return (
      <Card>
        <CardBody className="text-center py-12">
          <FaClock className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500">No hay solicitudes pendientes</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {requests.map((request) => (
        <RequestCard
          key={request.id}
          request={request}
          onApprove={onApprove}
          onReject={onReject}
          onViewDetails={onViewDetails}
        />
      ))}
    </div>
  );
};

/**
 * Tab de solicitudes procesadas
 */
const ProcessedRequestsTab = ({ requests, loading }) => {
  if (loading) {
    return <div className="text-center py-8">Cargando...</div>;
  }

  if (requests.length === 0) {
    return (
      <Card>
        <CardBody className="text-center py-12">
          <FaCheck className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500">No hay solicitudes procesadas</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {requests.map((request) => (
        <ProcessedRequestCard key={request.id} request={request} />
      ))}
    </div>
  );
};

/**
 * Tab de gestión de psicólogos
 */
const PsychologistManagementTab = ({ psychologists, onManualAssign, loading }) => {
  if (loading) {
    return <div className="text-center py-8">Cargando...</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {psychologists.map((psychologist) => (
        <PsychologistCard 
          key={psychologist.id} 
          psychologist={psychologist}
          onManualAssign={onManualAssign}
        />
      ))}
    </div>
  );
};

/**
 * Componente de tarjeta de solicitud
 */
const RequestCard = ({ request, onApprove, onReject, onViewDetails }) => {
  const getUrgencyColor = (urgency) => {
    switch (urgency) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'normal': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getUrgencyText = (urgency) => {
    switch (urgency) {
      case 'urgent': return 'Urgente';
      case 'high': return 'Alta';
      case 'normal': return 'Normal';
      case 'low': return 'Baja';
      default: return 'Normal';
    }
  };

  return (
    <Card>
      <CardBody>
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">
                {request.psychologistName}
              </h3>
              <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getUrgencyColor(request.urgency)}`}>
                {getUrgencyText(request.urgency)}
              </span>
            </div>

            <p className="text-sm text-gray-600 mb-2">{request.psychologistEmail}</p>

            <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
              <div className="flex items-center">
                <FaCoins className="mr-1" />
                <span>Actual: {request.currentPins}</span>
              </div>
              <div className="flex items-center">
                <FaPlus className="mr-1" />
                <span>Solicita: {request.requestedPins}</span>
              </div>
              <div className="flex items-center">
                <FaCalendarAlt className="mr-1" />
                <span>{format(new Date(request.createdAt), 'dd/MM/yyyy HH:mm', { locale: es })}</span>
              </div>
            </div>

            <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">
              {request.reason}
            </p>
          </div>
        </div>

        <div className="flex space-x-3">
          <Button
            onClick={() => onViewDetails(request)}
            size="sm"
            variant="outline"
          >
            <FaEye className="mr-1" />
            Ver Detalles
          </Button>

          <Button
            onClick={() => onApprove(request.id, request.requestedPins)}
            size="sm"
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <FaCheck className="mr-1" />
            Aprobar
          </Button>

          <Button
            onClick={() => onReject(request.id, 'Rechazado por administrador')}
            size="sm"
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            <FaTimes className="mr-1" />
            Rechazar
          </Button>
        </div>
      </CardBody>
    </Card>
  );
};

/**
 * Componente de tarjeta de solicitud procesada
 */
const ProcessedRequestCard = ({ request }) => {
  const isApproved = request.status === 'approved';

  return (
    <Card>
      <CardBody>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">
                {request.psychologistName}
              </h3>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                isApproved
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {isApproved ? 'Aprobado' : 'Rechazado'}
              </span>
            </div>

            <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
              <span>Solicitó: {request.requestedPins} pines</span>
              {isApproved && (
                <span>Aprobado: {request.approvedPins} pines</span>
              )}
              <span>Procesado: {format(new Date(request.processedAt), 'dd/MM/yyyy HH:mm', { locale: es })}</span>
            </div>

            {request.rejectionReason && (
              <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
                Motivo: {request.rejectionReason}
              </p>
            )}
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

/**
 * Componente de tarjeta de psicólogo
 */
const PsychologistCard = ({ psychologist, onManualAssign }) => {
  const [showAssignForm, setShowAssignForm] = useState(false);
  const [assignPins, setAssignPins] = useState(50);
  const [assignReason, setAssignReason] = useState('');

  const handleAssign = () => {
    if (assignPins > 0) {
      onManualAssign(psychologist.id, assignPins, assignReason);
      setShowAssignForm(false);
      setAssignPins(50);
      setAssignReason('');
    }
  };

  return (
    <Card>
      <CardBody>
        <div className="text-center mb-4">
          <h3 className="font-semibold text-gray-900 mb-1">{psychologist.name}</h3>
          <p className="text-sm text-gray-600 mb-3">{psychologist.email}</p>

          <div className="flex items-center justify-center space-x-2 mb-4">
            <FaCoins className="text-blue-600" />
            <span className="text-2xl font-bold text-blue-600">{psychologist.currentPins}</span>
            <span className="text-sm text-gray-500">pines</span>
          </div>
        </div>

        {!showAssignForm ? (
          <Button
            onClick={() => setShowAssignForm(true)}
            size="sm"
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            <FaPlus className="mr-1" />
            Asignar Pines
          </Button>
        ) : (
          <div className="space-y-3">
            <input
              type="number"
              value={assignPins}
              onChange={(e) => setAssignPins(parseInt(e.target.value) || 0)}
              placeholder="Cantidad de pines"
              className="w-full px-3 py-2 border border-gray-300 rounded text-sm"
              min="1"
            />

            <textarea
              value={assignReason}
              onChange={(e) => setAssignReason(e.target.value)}
              placeholder="Motivo (opcional)"
              className="w-full px-3 py-2 border border-gray-300 rounded text-sm"
              rows="2"
            />

            <div className="flex space-x-2">
              <Button
                onClick={handleAssign}
                size="sm"
                className="flex-1 bg-green-600 hover:bg-green-700 text-white"
              >
                Asignar
              </Button>
              <Button
                onClick={() => setShowAssignForm(false)}
                size="sm"
                variant="outline"
                className="flex-1"
              >
                Cancelar
              </Button>
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

/**
 * Modal de detalles de solicitud
 */
const RequestDetailsModal = ({ request, onClose, onApprove, onReject }) => {
  const [approvedPins, setApprovedPins] = useState(request.requestedPins);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectForm, setShowRejectForm] = useState(false);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
        <Card>
          <CardHeader className="bg-blue-50 border-b">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Detalles de Solicitud
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <FaTimes />
              </button>
            </div>
          </CardHeader>

          <CardBody className="space-y-6">
            {/* Información del psicólogo */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Información del Psicólogo</h4>
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <p><strong>Nombre:</strong> {request.psychologistName}</p>
                <p><strong>Email:</strong> {request.psychologistEmail}</p>
                <p><strong>Pines actuales:</strong> {request.currentPins}</p>
                <p><strong>Última actividad:</strong> {request.metadata?.lastActivity}</p>
                <p><strong>Total informes:</strong> {request.metadata?.totalReports}</p>
                <p><strong>Promedio mensual:</strong> {request.metadata?.averageMonthly}</p>
              </div>
            </div>

            {/* Detalles de la solicitud */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Detalles de la Solicitud</h4>
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <p><strong>Pines solicitados:</strong> {request.requestedPins}</p>
                <p><strong>Urgencia:</strong> {request.urgency}</p>
                <p><strong>Fecha:</strong> {format(new Date(request.createdAt), 'dd/MM/yyyy HH:mm', { locale: es })}</p>
                <p><strong>Motivo:</strong></p>
                <p className="bg-white p-3 rounded border">{request.reason}</p>
              </div>
            </div>

            {/* Acciones */}
            <div className="space-y-4">
              {!showRejectForm ? (
                <div className="flex space-x-3">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Pines a aprobar
                    </label>
                    <input
                      type="number"
                      value={approvedPins}
                      onChange={(e) => setApprovedPins(parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded"
                      min="1"
                    />
                  </div>
                  <div className="flex flex-col justify-end space-y-2">
                    <Button
                      onClick={() => onApprove(request.id, approvedPins)}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      <FaCheck className="mr-1" />
                      Aprobar
                    </Button>
                    <Button
                      onClick={() => setShowRejectForm(true)}
                      className="bg-red-600 hover:bg-red-700 text-white"
                    >
                      <FaTimes className="mr-1" />
                      Rechazar
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-gray-700">
                    Motivo del rechazo
                  </label>
                  <textarea
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded"
                    rows="3"
                    placeholder="Explique el motivo del rechazo..."
                  />
                  <div className="flex space-x-3">
                    <Button
                      onClick={() => onReject(request.id, rejectionReason)}
                      className="bg-red-600 hover:bg-red-700 text-white"
                      disabled={!rejectionReason.trim()}
                    >
                      Confirmar Rechazo
                    </Button>
                    <Button
                      onClick={() => setShowRejectForm(false)}
                      variant="outline"
                    >
                      Cancelar
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

/**
 * Modal de asignación manual
 */
const ManualAssignmentModal = ({ psychologists, onClose, onAssign }) => {
  const [selectedPsychologist, setSelectedPsychologist] = useState('');
  const [pins, setPins] = useState(50);
  const [reason, setReason] = useState('');

  const handleAssign = () => {
    if (selectedPsychologist && pins > 0) {
      onAssign(selectedPsychologist, pins, reason);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full">
        <Card>
          <CardHeader className="bg-blue-50 border-b">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Asignación Manual de Pines
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <FaTimes />
              </button>
            </div>
          </CardHeader>

          <CardBody className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Psicólogo
              </label>
              <select
                value={selectedPsychologist}
                onChange={(e) => setSelectedPsychologist(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded"
              >
                <option value="">Seleccionar psicólogo...</option>
                {psychologists.map(psy => (
                  <option key={psy.id} value={psy.id}>
                    {psy.name} ({psy.currentPins} pines actuales)
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Cantidad de pines
              </label>
              <input
                type="number"
                value={pins}
                onChange={(e) => setPins(parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded"
                min="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Motivo
              </label>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded"
                rows="3"
                placeholder="Motivo de la asignación..."
              />
            </div>

            <div className="flex space-x-3">
              <Button
                onClick={handleAssign}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                disabled={!selectedPsychologist || pins <= 0}
              >
                Asignar Pines
              </Button>
              <Button
                onClick={onClose}
                variant="outline"
                className="flex-1"
              >
                Cancelar
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default PinRechargeManagement;
