import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import SessionHistoryPanel from '../../components/pin/SessionHistoryPanel';
import GlobalPinStatus from '../../components/pin/GlobalPinStatus';
import PinNotificationCenter from '../../components/pin/PinNotificationCenter';
import SessionControlService from '../../services/pin/SessionControlService';
import { useAdvancedPinValidation } from '../../hooks/useAdvancedPinValidation';
import { 
  FaChartLine, 
  FaDownload, 
  FaCalendarAlt, 
  FaFilter,
  FaCoins,
  FaUsers,
  FaFileAlt,
  FaClock,
  FaCheckCircle,
  FaExclamationTriangle
} from 'react-icons/fa';
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns';
import { es } from 'date-fns/locale';

/**
 * Página completa de reportes de uso de pines
 * Proporciona transparencia total sobre el consumo de pines
 */
const PinUsageReports = ({ psychologistId }) => {
  const [dateRange, setDateRange] = useState({
    startDate: format(subDays(new Date(), 30), 'yyyy-MM-dd'),
    endDate: format(new Date(), 'yyyy-MM-dd')
  });
  const [statistics, setStatistics] = useState(null);
  const [consumptionHistory, setConsumptionHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeView, setActiveView] = useState('overview'); // 'overview', 'history', 'analytics'

  // Hook de validación para estado actual
  const {
    validationResult,
    isValidating
  } = useAdvancedPinValidation(psychologistId, {
    showToastAlerts: false,
    autoValidate: true
  });

  // Cargar datos
  const loadData = async () => {
    if (!psychologistId) return;

    try {
      setLoading(true);

      const [stats, history] = await Promise.all([
        SessionControlService.getSessionStatistics(psychologistId),
        SessionControlService.getConsumptionHistory(psychologistId, {
          limit: 100,
          startDate: dateRange.startDate,
          endDate: dateRange.endDate
        })
      ]);

      setStatistics(stats);
      setConsumptionHistory(history);

    } catch (error) {
      console.error('Error cargando datos de reportes:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [psychologistId, dateRange]);

  const handleDateRangeChange = (field, value) => {
    setDateRange(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const setPresetRange = (preset) => {
    const today = new Date();
    let startDate, endDate;

    switch (preset) {
      case 'today':
        startDate = endDate = format(today, 'yyyy-MM-dd');
        break;
      case 'week':
        startDate = format(subDays(today, 7), 'yyyy-MM-dd');
        endDate = format(today, 'yyyy-MM-dd');
        break;
      case 'month':
        startDate = format(startOfMonth(today), 'yyyy-MM-dd');
        endDate = format(endOfMonth(today), 'yyyy-MM-dd');
        break;
      case 'quarter':
        startDate = format(subDays(today, 90), 'yyyy-MM-dd');
        endDate = format(today, 'yyyy-MM-dd');
        break;
      default:
        return;
    }

    setDateRange({ startDate, endDate });
  };

  const exportData = () => {
    // Simular exportación de datos
    const csvContent = [
      ['Fecha', 'Paciente', 'Acción', 'Pines'],
      ...consumptionHistory.map(item => [
        format(new Date(item.pin_consumed_at), 'dd/MM/yyyy HH:mm'),
        `${item.pacientes.nombre} ${item.pacientes.apellido}`,
        'Pin Consumido',
        '1'
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `reporte-pines-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Reportes de Uso de Pines
            </h1>
            <p className="text-gray-600">
              Historial detallado y transparente de tu consumo de pines
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <GlobalPinStatus 
              psychologistId={psychologistId}
              showNotifications={false}
            />
            <PinNotificationCenter psychologistId={psychologistId} />
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setActiveView('overview')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeView === 'overview'
                ? 'bg-white text-blue-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FaChartLine className="inline mr-2" />
            Resumen
          </button>
          <button
            onClick={() => setActiveView('history')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeView === 'history'
                ? 'bg-white text-blue-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FaClock className="inline mr-2" />
            Historial
          </button>
          <button
            onClick={() => setActiveView('analytics')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeView === 'analytics'
                ? 'bg-white text-blue-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FaChartLine className="inline mr-2" />
            Análisis
          </button>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardBody className="py-4">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <FaCalendarAlt className="text-gray-400" />
                <span className="text-sm font-medium text-gray-700">Período:</span>
              </div>
              
              <input
                type="date"
                value={dateRange.startDate}
                onChange={(e) => handleDateRangeChange('startDate', e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded text-sm"
              />
              
              <span className="text-gray-500">hasta</span>
              
              <input
                type="date"
                value={dateRange.endDate}
                onChange={(e) => handleDateRangeChange('endDate', e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded text-sm"
              />
            </div>

            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                <Button size="sm" variant="outline" onClick={() => setPresetRange('today')}>
                  Hoy
                </Button>
                <Button size="sm" variant="outline" onClick={() => setPresetRange('week')}>
                  7 días
                </Button>
                <Button size="sm" variant="outline" onClick={() => setPresetRange('month')}>
                  Este mes
                </Button>
                <Button size="sm" variant="outline" onClick={() => setPresetRange('quarter')}>
                  90 días
                </Button>
              </div>
              
              <Button size="sm" onClick={exportData}>
                <FaDownload className="mr-1" />
                Exportar
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Content based on active view */}
      {activeView === 'overview' && (
        <OverviewTab 
          statistics={statistics}
          validationResult={validationResult}
          consumptionHistory={consumptionHistory}
          loading={loading}
        />
      )}

      {activeView === 'history' && (
        <HistoryTab 
          psychologistId={psychologistId}
          dateRange={dateRange}
        />
      )}

      {activeView === 'analytics' && (
        <AnalyticsTab 
          statistics={statistics}
          consumptionHistory={consumptionHistory}
          loading={loading}
        />
      )}
    </div>
  );
};

/**
 * Tab de resumen general
 */
const OverviewTab = ({ statistics, validationResult, consumptionHistory, loading }) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardBody className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </CardBody>
          </Card>
        ))}
      </div>
    );
  }

  const currentPins = validationResult?.remainingPins || 0;
  const isUnlimited = validationResult?.isUnlimited || false;

  return (
    <div className="space-y-6">
      {/* Métricas principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Pines Actuales"
          value={isUnlimited ? 'Ilimitado' : currentPins}
          icon={FaCoins}
          color="blue"
          subtitle={isUnlimited ? 'Plan ilimitado' : 'Disponibles'}
        />
        
        <MetricCard
          title="Sesiones Completadas"
          value={statistics?.totalCompleted || 0}
          icon={FaUsers}
          color="green"
          subtitle="Total histórico"
        />
        
        <MetricCard
          title="Informes Generados"
          value={statistics?.alreadyConsumed || 0}
          icon={FaFileAlt}
          color="purple"
          subtitle="Pines consumidos"
        />
        
        <MetricCard
          title="Tasa de Uso"
          value={`${statistics?.consumptionRate || 0}%`}
          icon={FaChartLine}
          color="orange"
          subtitle="Eficiencia"
        />
      </div>

      {/* Actividad reciente */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">
            Actividad Reciente
          </h3>
        </CardHeader>
        <CardBody>
          {consumptionHistory.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No hay actividad reciente
            </div>
          ) : (
            <div className="space-y-3">
              {consumptionHistory.slice(0, 5).map((item) => (
                <div key={item.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center space-x-3">
                    <FaCheckCircle className="text-green-500 h-4 w-4" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        Informe generado para {item.pacientes.nombre} {item.pacientes.apellido}
                      </p>
                      <p className="text-xs text-gray-500">
                        {format(new Date(item.pin_consumed_at), 'dd/MM/yyyy HH:mm', { locale: es })}
                      </p>
                    </div>
                  </div>
                  <div className="text-sm text-blue-600 font-medium">
                    -1 pin
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Tab de historial detallado
 */
const HistoryTab = ({ psychologistId, dateRange }) => {
  return (
    <SessionHistoryPanel 
      psychologistId={psychologistId}
      className="w-full"
    />
  );
};

/**
 * Tab de análisis y métricas
 */
const AnalyticsTab = ({ statistics, consumptionHistory, loading }) => {
  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardBody className="animate-pulse">
            <div className="h-64 bg-gray-200 rounded"></div>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">
            Análisis de Uso
          </h3>
        </CardHeader>
        <CardBody>
          <div className="text-center py-8 text-gray-500">
            <FaChartLine className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>Gráficos de análisis en desarrollo</p>
            <p className="text-sm">Próximamente: tendencias, patrones de uso y predicciones</p>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Componente de tarjeta de métrica
 */
const MetricCard = ({ title, value, icon: Icon, color, subtitle }) => {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-100',
    green: 'text-green-600 bg-green-100',
    purple: 'text-purple-600 bg-purple-100',
    orange: 'text-orange-600 bg-orange-100'
  };

  return (
    <Card>
      <CardBody>
        <div className="flex items-center">
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            <Icon className="h-6 w-6" />
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {subtitle && (
              <p className="text-xs text-gray-500">{subtitle}</p>
            )}
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

/**
 * Componente de resumen ejecutivo para administradores
 */
export const AdminPinSummary = ({ psychologistIds = [] }) => {
  const [summaryData, setSummaryData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadSummaryData = async () => {
      if (psychologistIds.length === 0) return;

      try {
        setLoading(true);

        const summaries = await Promise.all(
          psychologistIds.map(async (id) => {
            try {
              const stats = await SessionControlService.getSessionStatistics(id);
              return { psychologistId: id, ...stats };
            } catch (error) {
              return { psychologistId: id, error: error.message };
            }
          })
        );

        setSummaryData(summaries);
      } catch (error) {
        console.error('Error cargando resumen administrativo:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSummaryData();
  }, [psychologistIds]);

  if (loading) {
    return (
      <Card>
        <CardBody className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando resumen administrativo...</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold text-gray-900">
          Resumen Ejecutivo - Uso de Pines
        </h3>
      </CardHeader>
      <CardBody>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Psicólogo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sesiones
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pines Usados
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pendientes
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Eficiencia
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {summaryData.map((item) => (
                <tr key={item.psychologistId}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.psychologistId.slice(-8)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.totalCompleted || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.alreadyConsumed || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.pendingConsumption || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.consumptionRate || 0}%
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardBody>
    </Card>
  );
};

export default PinUsageReports;
