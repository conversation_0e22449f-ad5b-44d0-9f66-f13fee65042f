-- Función para eliminar usuarios de forma segura
-- EJECUTAR ESTE SCRIPT EN EL EDITOR SQL DE SUPABASE

-- =====================================================
-- FUNCIÓN PARA ELIMINAR USUARIOS PERMANENTEMENTE
-- =====================================================

CREATE OR REPLACE FUNCTION delete_user(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
  user_record RECORD;
  result JSON;
BEGIN
  -- Verificar que el usuario existe
  SELECT * INTO user_record
  FROM public.usuarios
  WHERE id = p_user_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Usuario no encontrado'
    );
  END IF;
  
  -- Verificar que no es el último administrador
  IF user_record.tipo_usuario = 'administrador' OR user_record.rol = 'administrador' THEN
    IF (SELECT COUNT(*) FROM public.usuarios 
        WHERE (tipo_usuario = 'administrador' OR rol = 'administrador') 
        AND activo = true 
        AND id != p_user_id) = 0 THEN
      RETURN json_build_object(
        'success', false,
        'error', 'No se puede eliminar el último administrador activo'
      );
    END IF;
  END IF;
  
  BEGIN
    -- Eliminar de la tabla usuarios
    DELETE FROM public.usuarios WHERE id = p_user_id;
    
    -- Intentar eliminar de auth.users (puede fallar si no tenemos permisos)
    -- Esto es opcional ya que Supabase maneja auth.users internamente
    
    RETURN json_build_object(
      'success', true,
      'message', 'Usuario eliminado exitosamente'
    );
    
  EXCEPTION
    WHEN OTHERS THEN
      RETURN json_build_object(
        'success', false,
        'error', 'Error al eliminar usuario: ' || SQLERRM
      );
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FUNCIÓN ALTERNATIVA PARA DESACTIVAR USUARIOS
-- =====================================================

CREATE OR REPLACE FUNCTION deactivate_user(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
  user_record RECORD;
  result JSON;
BEGIN
  -- Verificar que el usuario existe
  SELECT * INTO user_record
  FROM public.usuarios
  WHERE id = p_user_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Usuario no encontrado'
    );
  END IF;
  
  -- Verificar que no es el último administrador activo
  IF (user_record.tipo_usuario = 'administrador' OR user_record.rol = 'administrador') 
     AND user_record.activo = true THEN
    IF (SELECT COUNT(*) FROM public.usuarios 
        WHERE (tipo_usuario = 'administrador' OR rol = 'administrador') 
        AND activo = true 
        AND id != p_user_id) = 0 THEN
      RETURN json_build_object(
        'success', false,
        'error', 'No se puede desactivar el último administrador activo'
      );
    END IF;
  END IF;
  
  BEGIN
    -- Desactivar usuario
    UPDATE public.usuarios 
    SET activo = false, 
        updated_at = now()
    WHERE id = p_user_id;
    
    RETURN json_build_object(
      'success', true,
      'message', 'Usuario desactivado exitosamente'
    );
    
  EXCEPTION
    WHEN OTHERS THEN
      RETURN json_build_object(
        'success', false,
        'error', 'Error al desactivar usuario: ' || SQLERRM
      );
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- PERMISOS PARA LAS FUNCIONES
-- =====================================================

-- Otorgar permisos de ejecución
GRANT EXECUTE ON FUNCTION delete_user(UUID) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION deactivate_user(UUID) TO authenticated, anon;

-- =====================================================
-- VERIFICACIÓN DE LAS FUNCIONES
-- =====================================================

-- Verificar que las funciones fueron creadas
SELECT 
  'FUNCIONES CREADAS:' as info,
  proname as function_name,
  pg_get_function_arguments(oid) as arguments
FROM pg_proc 
WHERE proname IN ('delete_user', 'deactivate_user')
  AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');

-- Mostrar usuarios actuales para referencia
SELECT 
  'USUARIOS ACTUALES:' as info,
  id,
  nombre,
  apellido,
  documento,
  COALESCE(tipo_usuario, rol, 'sin_rol') as rol,
  activo
FROM public.usuarios
ORDER BY nombre;

-- =====================================================
-- INSTRUCCIONES DE USO
-- =====================================================

/*
INSTRUCCIONES:

1. EJECUTAR ESTE SCRIPT COMPLETO en el Editor SQL de Supabase

2. PARA ELIMINAR UN USUARIO PERMANENTEMENTE:
   SELECT delete_user('uuid-del-usuario');

3. PARA DESACTIVAR UN USUARIO:
   SELECT deactivate_user('uuid-del-usuario');

4. AMBAS FUNCIONES RETORNAN JSON:
   {
     "success": true/false,
     "message": "mensaje de éxito",
     "error": "mensaje de error"
   }

5. PROTECCIONES INCLUIDAS:
   - No permite eliminar el último administrador activo
   - Verifica que el usuario existe antes de eliminar
   - Manejo de errores con try/catch
   - Funciones seguras con SECURITY DEFINER

6. LA APLICACIÓN USARÁ delete_user() AUTOMÁTICAMENTE
*/
