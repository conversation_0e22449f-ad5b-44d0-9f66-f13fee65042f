import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://ydglduxhgwajqdseqzpy.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c'
);

async function checkResultados() {
  console.log('🔍 Verificando resultados por aptitud...');
  
  // Verificar todas las aptitudes con resultados
  const { data: resultados, error } = await supabase
    .from('resultados')
    .select('aptitudes(codigo, nombre)')
    .not('aptitudes', 'is', null)
    .limit(100);
  
  if (error) {
    console.error('❌ Error:', error);
    return;
  }
  
  // Agrupar por código de aptitud
  const conteos = {};
  resultados.forEach(item => {
    const codigo = item.aptitudes?.codigo;
    if (codigo) {
      conteos[codigo] = (conteos[codigo] || 0) + 1;
    }
  });
  
  console.log('📊 Resultados por aptitud:');
  Object.keys(conteos).sort().forEach(codigo => {
    console.log(`   ${codigo}: ${conteos[codigo]} resultados`);
  });
  
  // Verificar si hay resultados con código C específicamente
  const { data: resultadosC, error: errorC } = await supabase
    .from('resultados')
    .select('*, aptitudes(codigo, nombre)')
    .eq('aptitudes.codigo', 'C')
    .limit(5);
    
  if (!errorC) {
    console.log(`\n📋 Resultados con código C: ${resultadosC?.length || 0}`);
    if (resultadosC && resultadosC.length > 0) {
      resultadosC.forEach(r => {
        console.log(`   ID: ${r.id}, PD: ${r.puntaje_directo}, PC: ${r.percentil}`);
      });
    }
  }
  
  // Verificar resultados de atención (A) que podrían tener concentración
  const { data: resultadosA, error: errorA } = await supabase
    .from('resultados')
    .select('*, aptitudes(codigo, nombre)')
    .eq('aptitudes.codigo', 'A')
    .limit(3);
    
  if (!errorA && resultadosA) {
    console.log(`\n📋 Resultados de Atención (A): ${resultadosA.length}`);
    resultadosA.forEach(r => {
      console.log(`   ID: ${r.id}, PD: ${r.puntaje_directo}, PC: ${r.percentil}, Errores: ${r.errores || 0}`);
      // Calcular concentración como lo hace el sistema
      const pd = r.puntaje_directo || 0;
      const errores = r.errores || 0;
      const concentracion = pd + errores > 0 ? Math.round((pd / (pd + errores)) * 100) : 0;
      console.log(`   Concentración calculada: ${concentracion}%`);
    });
  }
}

checkResultados();
