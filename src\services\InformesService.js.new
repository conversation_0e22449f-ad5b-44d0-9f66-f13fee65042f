/**
 * @file InformesService.js
 * @description Servicio para gestionar informes de evaluaciones psicológicas
 */

import supabase from '../api/supabaseClient';
import { toast } from 'react-toastify';
import { calculateAge } from '../utils/dateUtils';
import InterpretacionesService from './InterpretacionesService';
import pinControlService from './pin/ImprovedPinControlService';

const InformesService = {
  /**
   * Ejecuta una operación de base de datos
   * @param {Function} operation - Función que ejecuta la operación
   * @param {string} operationName - Nombre de la operación para logging
   * @returns {Promise} - Resultado de la operación
   */
  async executeOperation(operation, operationName) {
    try {
      console.log(`🔄 [InformesService] Ejecutando operación: ${operationName}`);
      const result = await operation();
      
      if (result.error) {
        console.error(`❌ [InformesService] Error en ${operationName}:`, result.error);
        throw result.error;
      }
      
      return result;
    } catch (error) {
      console.error(`❌ [InformesService] Error en ${operationName}:`, error.message);
      throw error;
    }
  },

  // ... (resto del código se mantiene igual hasta la parte de generación de informes)

  /**
   * Generar informe completo para un paciente
   */
  async generarInformeCompleto(pacienteId, titulo = null, descripcion = null, incluirInterpretaciones = true, skipValidation = false) {
    try {
      console.log('📊 [InformesService] Generando informe completo para paciente:', pacienteId);

      // ... (código de validación se mantiene igual hasta la creación del informe)

      // Crear registro del informe
      const informeResult = await this.executeOperation(async () => {
        return await supabase
          .from('informes_generados')
          .insert({
            paciente_id: pacienteId,
            tipo_informe: 'completo',
            titulo: titulo || `Informe Completo - ${paciente.nombre} ${paciente.apellido}`,
            descripcion: descripcion || 'Informe completo de evaluación psicológica',
            contenido: contenido,
            estado: 'generado',
            fecha_generacion: new Date().toISOString(),
            metadatos: {
              version: '2.0',
              generado_por: 'sistema',
              total_resultados: resultados?.length || 0,
              incluye_interpretaciones: !!interpretacionesCualitativas
            }
          })
          .select()
          .single();
      }, 'INSERT_INFORME');

      if (!informeResult.data) {
        throw new Error('No se pudo crear el informe en la base de datos');
      }

      const informe = informeResult.data;

      // Lógica de consumo de pines se mantiene igual
      // ... (resto del código de consumo de pines)

      console.log('✅ [InformesService] Informe completo generado:', informe.id);
      toast.success('Informe completo generado exitosamente');
      return informe.id;

    } catch (error) {
      console.error('❌ [InformesService] Error generando informe completo:', error);
      toast.error(error.message || 'Error al generar el informe completo');
      throw error;
    }
  },

  /**
   * Generar informe individual para un resultado específico
   */
  async generarInformeIndividual(resultadoId, titulo = null, descripcion = null) {
    try {
      console.log('📄 [InformesService] Generando informe individual para resultado:', resultadoId);
      
      // Código de obtención de datos se mantiene igual hasta la creación del informe

      // Crear registro del informe
      const informeResult = await this.executeOperation(async () => {
        return await supabase
          .from('informes_generados')
          .insert({
            paciente_id: resultado.paciente_id,
            tipo_informe: 'individual',
            titulo: titulo || `Informe Individual - ${resultado.pacientes.nombre} ${resultado.pacientes.apellido}`,
            descripcion: descripcion || 'Informe individual de evaluación psicológica',
            contenido: contenido,
            estado: 'generado',
            fecha_generacion: new Date().toISOString(),
            metadatos: {
              version: '1.0',
              generado_por: 'sistema',
              resultado_id: resultadoId
            }
          })
          .select()
          .single();
      }, 'INSERT_INFORME_INDIVIDUAL');

      if (!informeResult.data) {
        throw new Error('No se pudo crear el informe individual en la base de datos');
      }

      const informe = informeResult.data;

      // Lógica de consumo de pines se mantiene igual
      // ... (resto del código de consumo de pines)

      console.log('✅ [InformesService] Informe individual generado:', informe.id);
      toast.success('Informe individual generado exitosamente');
      return informe.id;

    } catch (error) {
      console.error('❌ [InformesService] Error generando informe individual:', error);
      toast.error(error.message || 'Error al generar el informe individual');
      throw error;
    }
  },

  // ... (resto del código se mantiene igual)
};

export default InformesService;
