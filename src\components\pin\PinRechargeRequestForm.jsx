import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { Button } from '../ui/Button';
import { 
  FaCoins, 
  FaPaperPlane, 
  FaExclamationTriangle,
  FaInfoCircle,
  FaEnvelope,
  FaPhone,
  FaComments,
  FaHistory
} from 'react-icons/fa';
import RechargeRequestService from '../../services/pin/RechargeRequestService';
import { useAdvancedPinValidation } from '../../hooks/useAdvancedPinValidation';
import { toast } from 'react-toastify';

/**
 * Formulario para solicitar recarga de pines
 * Permite a los psicólogos enviar solicitudes estructuradas
 */
const PinRechargeRequestForm = ({ 
  psychologistId, 
  onRequestSent = null,
  onClose = null,
  className = '' 
}) => {
  const [formData, setFormData] = useState({
    requestedPins: 50,
    urgency: RechargeRequestService.URGENCY_LEVELS.NORMAL,
    reason: '',
    contactMethod: 'email',
    additionalInfo: {
      expectedUsage: '',
      timeframe: 'immediate'
    }
  });

  const [loading, setLoading] = useState(false);
  const [requestHistory, setRequestHistory] = useState([]);
  const [showHistory, setShowHistory] = useState(false);

  const {
    validationResult
  } = useAdvancedPinValidation(psychologistId, {
    showToastAlerts: false,
    autoValidate: true
  });

  // Cargar historial de solicitudes
  useEffect(() => {
    if (psychologistId) {
      loadRequestHistory();
    }
  }, [psychologistId]);

  const loadRequestHistory = async () => {
    try {
      const history = await RechargeRequestService.getPsychologistRequestHistory(psychologistId, 5);
      setRequestHistory(history);
    } catch (error) {
      console.error('Error cargando historial:', error);
    }
  };

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.reason.trim()) {
      toast.error('Por favor, explica el motivo de tu solicitud');
      return;
    }

    if (formData.requestedPins < 1 || formData.requestedPins > 1000) {
      toast.error('La cantidad debe estar entre 1 y 1000 pines');
      return;
    }

    try {
      setLoading(true);

      const result = await RechargeRequestService.createRechargeRequest(
        psychologistId,
        formData
      );

      if (result.success) {
        toast.success('Solicitud enviada exitosamente');
        
        // Limpiar formulario
        setFormData({
          requestedPins: 50,
          urgency: RechargeRequestService.URGENCY_LEVELS.NORMAL,
          reason: '',
          contactMethod: 'email',
          additionalInfo: {
            expectedUsage: '',
            timeframe: 'immediate'
          }
        });

        // Recargar historial
        await loadRequestHistory();

        // Callback
        if (onRequestSent) {
          onRequestSent(result.request);
        }
      }

    } catch (error) {
      console.error('Error enviando solicitud:', error);
      toast.error(error.message || 'Error al enviar la solicitud');
    } finally {
      setLoading(false);
    }
  };

  const currentPins = validationResult?.remainingPins || 0;
  const isUnlimited = validationResult?.isUnlimited || false;
  const hasPendingRequest = requestHistory.some(r => r.status === 'pending');

  // Sugerencias de cantidad basadas en el estado actual
  const getSuggestedAmounts = () => {
    if (currentPins === 0) return [25, 50, 100];
    if (currentPins < 10) return [25, 50, 75];
    return [50, 100, 150];
  };

  const urgencyOptions = [
    { value: 'low', label: 'Baja', description: 'Puedo esperar varios días', color: 'text-gray-600' },
    { value: 'normal', label: 'Normal', description: 'Necesito en 1-2 días', color: 'text-blue-600' },
    { value: 'high', label: 'Alta', description: 'Necesito hoy o mañana', color: 'text-orange-600' },
    { value: 'urgent', label: 'Urgente', description: 'Necesito inmediatamente', color: 'text-red-600' }
  ];

  const contactOptions = [
    { value: 'email', label: 'Email', icon: FaEnvelope, description: 'Respuesta por correo electrónico' },
    { value: 'phone', label: 'Teléfono', icon: FaPhone, description: 'Llamada telefónica' },
    { value: 'platform', label: 'Plataforma', icon: FaComments, description: 'Notificación en la plataforma' }
  ];

  if (isUnlimited) {
    return (
      <Card className={className}>
        <CardBody className="text-center py-8">
          <FaCoins className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Plan Ilimitado Activo
          </h3>
          <p className="text-gray-600">
            Tienes acceso ilimitado a la generación de informes.
          </p>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Estado actual */}
      <Card>
        <CardHeader className="bg-blue-50 border-b">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Solicitar Recarga de Pines
            </h3>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="flex items-center text-sm text-gray-600">
                  <FaCoins className="mr-1" />
                  <span>{currentPins} pines disponibles</span>
                </div>
              </div>
              {requestHistory.length > 0 && (
                <button
                  onClick={() => setShowHistory(!showHistory)}
                  className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                >
                  <FaHistory className="mr-1" />
                  Historial
                </button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardBody>
          {/* Alerta si hay solicitud pendiente */}
          {hasPendingRequest && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <FaExclamationTriangle className="text-yellow-600 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-900">
                    Solicitud Pendiente
                  </h4>
                  <p className="text-sm text-yellow-700">
                    Ya tienes una solicitud en proceso. Espera a que sea revisada antes de enviar otra.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Historial (si está visible) */}
          {showHistory && requestHistory.length > 0 && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-3">Solicitudes Recientes</h4>
              <div className="space-y-2">
                {requestHistory.slice(0, 3).map((request) => (
                  <div key={request.id} className="flex items-center justify-between text-sm">
                    <span>{request.requested_pins} pines</span>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      request.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      request.status === 'approved' ? 'bg-green-100 text-green-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {request.status === 'pending' ? 'Pendiente' :
                       request.status === 'approved' ? 'Aprobado' : 'Rechazado'}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Cantidad de pines */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cantidad de pines solicitados
              </label>
              <div className="flex items-center space-x-4 mb-3">
                <input
                  type="number"
                  value={formData.requestedPins}
                  onChange={(e) => handleInputChange('requestedPins', parseInt(e.target.value) || 0)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  min="1"
                  max="1000"
                  required
                />
                <span className="text-sm text-gray-500">pines</span>
              </div>
              
              {/* Sugerencias rápidas */}
              <div className="flex space-x-2">
                <span className="text-xs text-gray-500">Sugerencias:</span>
                {getSuggestedAmounts().map(amount => (
                  <button
                    key={amount}
                    type="button"
                    onClick={() => handleInputChange('requestedPins', amount)}
                    className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded transition-colors"
                  >
                    {amount}
                  </button>
                ))}
              </div>
            </div>

            {/* Urgencia */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nivel de urgencia
              </label>
              <div className="space-y-2">
                {urgencyOptions.map(option => (
                  <label key={option.value} className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="urgency"
                      value={option.value}
                      checked={formData.urgency === option.value}
                      onChange={(e) => handleInputChange('urgency', e.target.value)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <div className="flex-1">
                      <div className={`font-medium ${option.color}`}>
                        {option.label}
                      </div>
                      <div className="text-sm text-gray-500">
                        {option.description}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Motivo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Motivo de la solicitud *
              </label>
              <textarea
                value={formData.reason}
                onChange={(e) => handleInputChange('reason', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows="4"
                placeholder="Explica por qué necesitas estos pines (ej: tengo 15 pacientes esperando informes, se agotaron mis pines actuales, etc.)"
                required
              />
              <div className="text-xs text-gray-500 mt-1">
                {formData.reason.length}/500 caracteres
              </div>
            </div>

            {/* Método de contacto preferido */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Método de contacto preferido
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {contactOptions.map(option => {
                  const IconComponent = option.icon;
                  return (
                    <label key={option.value} className="flex items-center space-x-3 cursor-pointer p-3 border rounded-lg hover:bg-gray-50">
                      <input
                        type="radio"
                        name="contactMethod"
                        value={option.value}
                        checked={formData.contactMethod === option.value}
                        onChange={(e) => handleInputChange('contactMethod', e.target.value)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <IconComponent className="text-gray-400" />
                      <div>
                        <div className="font-medium text-gray-900">{option.label}</div>
                        <div className="text-xs text-gray-500">{option.description}</div>
                      </div>
                    </label>
                  );
                })}
              </div>
            </div>

            {/* Información adicional */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Uso esperado (opcional)
              </label>
              <input
                type="text"
                value={formData.additionalInfo.expectedUsage}
                onChange={(e) => handleInputChange('additionalInfo.expectedUsage', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Ej: 20 informes por semana, uso intensivo por 2 meses"
              />
            </div>

            {/* Información importante */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <FaInfoCircle className="text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-800">
                  <h5 className="font-medium mb-2">Información Importante:</h5>
                  <ul className="space-y-1 list-disc list-inside">
                    <li>Las solicitudes se procesan en 24-48 horas hábiles</li>
                    <li>Solicitudes urgentes se priorizan pero requieren justificación</li>
                    <li>Recibirás una notificación cuando tu solicitud sea procesada</li>
                    <li>Solo puedes tener una solicitud pendiente a la vez</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Botones */}
            <div className="flex space-x-3">
              <Button
                type="submit"
                disabled={loading || hasPendingRequest || !formData.reason.trim()}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Enviando...
                  </>
                ) : (
                  <>
                    <FaPaperPlane className="mr-2" />
                    Enviar Solicitud
                  </>
                )}
              </Button>
              
              {onClose && (
                <Button
                  type="button"
                  onClick={onClose}
                  variant="outline"
                  className="px-6"
                >
                  Cancelar
                </Button>
              )}
            </div>
          </form>
        </CardBody>
      </Card>
    </div>
  );
};

export default PinRechargeRequestForm;
