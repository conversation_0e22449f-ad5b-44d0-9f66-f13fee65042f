import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ValidatedReportButton from '../../components/reports/ValidatedReportButton';
import PinValidationService from '../../services/pin/PinValidationService';
import InformesService from '../../services/InformesService';
import { toast } from 'react-toastify';

// Mocks
vi.mock('../../services/pin/PinValidationService');
vi.mock('../../services/InformesService');
vi.mock('react-toastify', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}));

// Mock del hook
vi.mock('../../hooks/useAdvancedPinValidation', () => ({
  useAdvancedPinValidation: vi.fn()
}));

import { useAdvancedPinValidation } from '../../hooks/useAdvancedPinValidation';

describe('Pin System Integration Tests', () => {
  const mockPsychologistId = 'psy-123';
  const mockPatientId = 'patient-456';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('ValidatedReportButton Integration', () => {
    it('should complete full report generation flow with sufficient pins', async () => {
      // Mock validation result with sufficient pins
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE',
        userMessage: 'Puede generar el informe',
        severity: 'success',
        remainingPins: 10,
        requiredPins: 1,
        pinsAfterOperation: 9,
        isUnlimited: false,
        hasWarning: false
      };

      // Mock hook return
      useAdvancedPinValidation.mockReturnValue({
        validationResult: mockValidationResult,
        isValidating: false,
        canProceed: true,
        hasWarning: false,
        isBlocked: false,
        validateSingleReport: vi.fn().mockResolvedValue(mockValidationResult),
        validateBatchReports: vi.fn(),
        getValidationSummary: vi.fn().mockReturnValue({
          status: 'allowed',
          title: 'Operación Permitida'
        })
      });

      // Mock report generation
      const mockOnGenerateReport = vi.fn().mockResolvedValue('report-123');

      render(
        <ValidatedReportButton
          psychologistId={mockPsychologistId}
          patientId={mockPatientId}
          onGenerateReport={mockOnGenerateReport}
          buttonText="Generar Informe"
        />
      );

      // Should show validation alert with success
      expect(screen.getByText('Operación Permitida')).toBeInTheDocument();
      expect(screen.getByText('Puede generar el informe')).toBeInTheDocument();

      // Button should be enabled
      const generateButton = screen.getByRole('button', { name: /generar informe/i });
      expect(generateButton).toBeEnabled();

      // Click the button
      fireEvent.click(generateButton);

      // Should call the report generation function
      await waitFor(() => {
        expect(mockOnGenerateReport).toHaveBeenCalledWith(mockPatientId);
      });

      // Should show success toast
      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Informe generado exitosamente');
      });
    });

    it('should block report generation with insufficient pins', async () => {
      // Mock validation result with insufficient pins
      const mockValidationResult = {
        isValid: false,
        canProceed: false,
        reason: 'INSUFFICIENT_PINS',
        userMessage: 'No cuenta con suficientes pines. Disponibles: 0. Requeridos: 1. Faltan: 1',
        severity: 'error',
        remainingPins: 0,
        requiredPins: 1,
        shortfall: 1,
        isUnlimited: false
      };

      useAdvancedPinValidation.mockReturnValue({
        validationResult: mockValidationResult,
        isValidating: false,
        canProceed: false,
        hasWarning: false,
        isBlocked: true,
        validateSingleReport: vi.fn().mockResolvedValue(mockValidationResult),
        validateBatchReports: vi.fn(),
        getValidationSummary: vi.fn().mockReturnValue({
          status: 'blocked',
          title: 'Operación Bloqueada'
        })
      });

      const mockOnGenerateReport = vi.fn();

      render(
        <ValidatedReportButton
          psychologistId={mockPsychologistId}
          patientId={mockPatientId}
          onGenerateReport={mockOnGenerateReport}
          buttonText="Generar Informe"
        />
      );

      // Should show validation alert with error
      expect(screen.getByText('Operación Bloqueada')).toBeInTheDocument();
      expect(screen.getByText(/no cuenta con suficientes pines/i)).toBeInTheDocument();

      // Button should be disabled
      const generateButton = screen.getByRole('button', { name: /bloqueado/i });
      expect(generateButton).toBeDisabled();

      // Should show recharge prompt
      expect(screen.getByText(/pines insuficientes/i)).toBeInTheDocument();
    });

    it('should show warning confirmation for low pins', async () => {
      // Mock validation result with warning
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE',
        userMessage: 'ADVERTENCIA: Después de esta operación quedarán solo 1 pines',
        severity: 'warning',
        remainingPins: 2,
        requiredPins: 1,
        pinsAfterOperation: 1,
        isUnlimited: false,
        hasWarning: true
      };

      const mockValidateFunction = vi.fn().mockResolvedValue(mockValidationResult);

      useAdvancedPinValidation.mockReturnValue({
        validationResult: mockValidationResult,
        isValidating: false,
        canProceed: true,
        hasWarning: true,
        isBlocked: false,
        validateSingleReport: mockValidateFunction,
        validateBatchReports: vi.fn(),
        getValidationSummary: vi.fn()
      });

      const mockOnGenerateReport = vi.fn().mockResolvedValue('report-123');

      render(
        <ValidatedReportButton
          psychologistId={mockPsychologistId}
          patientId={mockPatientId}
          onGenerateReport={mockOnGenerateReport}
          buttonText="Generar Informe"
        />
      );

      // Click the button
      const generateButton = screen.getByRole('button', { name: /generar informe/i });
      fireEvent.click(generateButton);

      // Should show confirmation dialog
      await waitFor(() => {
        expect(screen.getByText('Confirmar Operación')).toBeInTheDocument();
      });

      // Confirm the operation
      const confirmButton = screen.getByRole('button', { name: /continuar/i });
      fireEvent.click(confirmButton);

      // Should proceed with generation
      await waitFor(() => {
        expect(mockOnGenerateReport).toHaveBeenCalledWith(mockPatientId);
      });
    });

    it('should handle batch report generation', async () => {
      const mockPatientIds = ['patient-1', 'patient-2', 'patient-3'];
      
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE',
        userMessage: 'Puede generar los informes',
        severity: 'success',
        remainingPins: 10,
        requiredPins: 3,
        pinsAfterOperation: 7,
        isUnlimited: false,
        batchInfo: {
          patientCount: 3,
          requiredPins: 3,
          patientIds: mockPatientIds
        }
      };

      const mockValidateBatch = vi.fn().mockResolvedValue(mockValidationResult);

      useAdvancedPinValidation.mockReturnValue({
        validationResult: mockValidationResult,
        isValidating: false,
        canProceed: true,
        hasWarning: false,
        isBlocked: false,
        validateSingleReport: vi.fn(),
        validateBatchReports: mockValidateBatch,
        getValidationSummary: vi.fn()
      });

      const mockOnGenerateReport = vi.fn().mockResolvedValue(['report-1', 'report-2', 'report-3']);

      render(
        <ValidatedReportButton
          psychologistId={mockPsychologistId}
          patientIds={mockPatientIds}
          onGenerateReport={mockOnGenerateReport}
          buttonText="Generar Informes"
        />
      );

      // Should show batch information
      expect(screen.getByText('Generación en lote: 3 pacientes')).toBeInTheDocument();

      // Click the button
      const generateButton = screen.getByRole('button', { name: /generar informes/i });
      fireEvent.click(generateButton);

      // Should call batch validation
      expect(mockValidateBatch).toHaveBeenCalledWith(mockPatientIds);

      // Should call batch generation
      await waitFor(() => {
        expect(mockOnGenerateReport).toHaveBeenCalledWith(mockPatientIds);
      });

      // Should show success message for batch
      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('3 informes generados exitosamente');
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle validation service errors gracefully', async () => {
      const mockError = new Error('Validation service unavailable');
      
      useAdvancedPinValidation.mockReturnValue({
        validationResult: {
          isValid: false,
          canProceed: false,
          reason: 'VALIDATION_ERROR',
          userMessage: 'Error al verificar los permisos. Intente nuevamente.',
          severity: 'error',
          error: 'Validation service unavailable'
        },
        isValidating: false,
        canProceed: false,
        hasWarning: false,
        isBlocked: true,
        validateSingleReport: vi.fn().mockRejectedValue(mockError),
        validateBatchReports: vi.fn(),
        getValidationSummary: vi.fn()
      });

      render(
        <ValidatedReportButton
          psychologistId={mockPsychologistId}
          patientId={mockPatientId}
          onGenerateReport={vi.fn()}
          buttonText="Generar Informe"
        />
      );

      // Should show error message
      expect(screen.getByText(/error al verificar los permisos/i)).toBeInTheDocument();

      // Button should be disabled
      const generateButton = screen.getByRole('button', { name: /bloqueado/i });
      expect(generateButton).toBeDisabled();
    });

    it('should handle report generation errors', async () => {
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE',
        userMessage: 'Puede generar el informe',
        severity: 'success',
        remainingPins: 10
      };

      useAdvancedPinValidation.mockReturnValue({
        validationResult: mockValidationResult,
        isValidating: false,
        canProceed: true,
        hasWarning: false,
        isBlocked: false,
        validateSingleReport: vi.fn().mockResolvedValue(mockValidationResult),
        validateBatchReports: vi.fn(),
        getValidationSummary: vi.fn()
      });

      const mockOnGenerateReport = vi.fn().mockRejectedValue(new Error('Report generation failed'));

      render(
        <ValidatedReportButton
          psychologistId={mockPsychologistId}
          patientId={mockPatientId}
          onGenerateReport={mockOnGenerateReport}
          buttonText="Generar Informe"
        />
      );

      // Click the button
      const generateButton = screen.getByRole('button', { name: /generar informe/i });
      fireEvent.click(generateButton);

      // Should show error toast
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Error al generar el informe');
      });
    });
  });

  describe('Loading States Integration', () => {
    it('should show loading states during validation', () => {
      useAdvancedPinValidation.mockReturnValue({
        validationResult: null,
        isValidating: true,
        canProceed: false,
        hasWarning: false,
        isBlocked: false,
        validateSingleReport: vi.fn(),
        validateBatchReports: vi.fn(),
        getValidationSummary: vi.fn()
      });

      render(
        <ValidatedReportButton
          psychologistId={mockPsychologistId}
          patientId={mockPatientId}
          onGenerateReport={vi.fn()}
          buttonText="Generar Informe"
        />
      );

      // Should show validating state
      expect(screen.getByText('Validando...')).toBeInTheDocument();
      expect(screen.getByRole('button')).toBeDisabled();
    });

    it('should show loading states during report generation', async () => {
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE',
        userMessage: 'Puede generar el informe',
        severity: 'success',
        remainingPins: 10
      };

      useAdvancedPinValidation.mockReturnValue({
        validationResult: mockValidationResult,
        isValidating: false,
        canProceed: true,
        hasWarning: false,
        isBlocked: false,
        validateSingleReport: vi.fn().mockResolvedValue(mockValidationResult),
        validateBatchReports: vi.fn(),
        getValidationSummary: vi.fn()
      });

      // Mock slow report generation
      const mockOnGenerateReport = vi.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve('report-123'), 1000))
      );

      render(
        <ValidatedReportButton
          psychologistId={mockPsychologistId}
          patientId={mockPatientId}
          onGenerateReport={mockOnGenerateReport}
          buttonText="Generar Informe"
        />
      );

      // Click the button
      const generateButton = screen.getByRole('button', { name: /generar informe/i });
      fireEvent.click(generateButton);

      // Should show generating state
      await waitFor(() => {
        expect(screen.getByText('Generando informe...')).toBeInTheDocument();
      });

      // Button should be disabled during generation
      expect(screen.getByRole('button')).toBeDisabled();
    });
  });
});
