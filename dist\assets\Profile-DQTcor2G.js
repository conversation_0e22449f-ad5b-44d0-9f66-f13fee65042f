var e=Object.defineProperty,s=Object.defineProperties,r=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,n=(s,r,a)=>r in s?e(s,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):s[r]=a,l=(e,s)=>{for(var r in s||(s={}))t.call(s,r)&&n(e,r,s[r]);if(a)for(var r of a(s))o.call(s,r)&&n(e,r,s[r]);return e},i=(e,a)=>s(e,r(a)),d=(e,s,r)=>new Promise((a,t)=>{var o=e=>{try{l(r.next(e))}catch(s){t(s)}},n=e=>{try{l(r.throw(e))}catch(s){t(s)}},l=e=>e.done?a(e.value):Promise.resolve(e.value).then(o,n);l((r=r.apply(e,s)).next())});import{Q as c,r as m,j as u,F as x,O as h,s as f,f as b,X as p,G as w}from"./vendor-BqMjyOVw.js";import{s as g}from"./index-Bdl1jgS_.js";class y{static getCurrentUser(){return d(this,null,function*(){try{const{data:{session:e}}=yield g.auth.getSession();if(!(null==e?void 0:e.user))return null;const{data:s,error:r}=yield g.from("usuarios").select("*").eq("id",e.user.id).single();return r?e.user:l(l({},e.user),s)}catch(e){return null}})}static login(e){return d(this,arguments,function*({identifier:e,password:s}){try{let r;if(e.includes("@")){const{data:a,error:t}=yield g.auth.signInWithPassword({email:e,password:s});if(t)throw t;r=a}else{const{data:a,error:t}=yield g.from("usuarios").select("id").eq("documento",e).single();if(t||!a)throw new Error("Usuario no encontrado con ese documento");const{data:o,error:n}=yield g.auth.admin.getUserById(a.id);if(n||!o.user)throw new Error("Error al obtener datos de autenticación");const{data:l,error:i}=yield g.auth.signInWithPassword({email:o.user.email,password:s});if(i)throw i;r=l}const a=yield this.getCurrentUser();return a&&(yield g.from("usuarios").update({ultimo_acceso:(new Date).toISOString()}).eq("id",a.id)),c.success("Inicio de sesión exitoso"),{success:!0,user:a,session:r.session}}catch(r){return c.error(r.message||"Error al iniciar sesión"),{success:!1,message:r.message}}})}static register(e){return d(this,null,function*(){try{const{email:s,password:r,nombre:a,apellido:t,documento:o,rol:n="estudiante"}=e;if(o){const{data:e}=yield g.from("usuarios").select("id").eq("documento",o).single();if(e)throw new Error("Ya existe un usuario con ese documento")}const{data:l,error:i}=yield g.auth.signUp({email:s,password:r,options:{data:{nombre:a,apellido:t}}});if(i)throw i;const{error:d}=yield g.from("usuarios").insert([{id:l.user.id,documento:o,nombre:a,apellido:t,rol:n,activo:!0,fecha_creacion:(new Date).toISOString()}]);if(d)throw d;return c.success("Registro exitoso"),{success:!0,user:l.user,message:"Registro exitoso. Revisa tu email para confirmar tu cuenta."}}catch(s){return c.error(s.message||"Error al registrar usuario"),{success:!1,message:s.message}}})}static logout(){return d(this,null,function*(){try{const{error:e}=yield g.auth.signOut();if(e)throw e;return c.info("Sesión cerrada correctamente"),{success:!0}}catch(e){return c.error(e.message||"Error al cerrar sesión"),{success:!1,message:e.message}}})}static resetPassword(e){return d(this,null,function*(){try{const{error:s}=yield g.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/reset-password`});if(s)throw s;return c.success("Se ha enviado un enlace para restablecer la contraseña"),{success:!0}}catch(s){return c.error(s.message||"Error al restablecer contraseña"),{success:!1,message:s.message}}})}static updatePassword(e){return d(this,null,function*(){try{const{error:s}=yield g.auth.updateUser({password:e});if(s)throw s;return c.success("Contraseña actualizada correctamente"),{success:!0}}catch(s){return c.error(s.message||"Error al actualizar contraseña"),{success:!1,message:s.message}}})}static hasRole(e,s){var r;if(!e)return!1;return((null==(r=e.rol)?void 0:r.toLowerCase())||"")===s.toLowerCase()}static isAdmin(e){return this.hasRole(e,"administrador")}static isPsychologist(e){return this.hasRole(e,"psicologo")}static isStudent(e){return this.hasRole(e,"estudiante")}}const j=()=>{var e;const[s,r]=m.useState(null),[a,t]=m.useState({nombre:"",apellidos:"",telefono:""}),[o,n]=m.useState(!0),[j,v]=m.useState(!1),[N,P]=m.useState(!1),[C,E]=m.useState({currentPassword:"",newPassword:"",confirmPassword:""}),[S,k]=m.useState({});m.useEffect(()=>{d(null,null,function*(){n(!0);try{const e=yield y.getCurrentUser();if(r(e),e){const{data:s,error:r}=yield g.from("perfiles").select("*").eq("user_id",e.id).single();s&&!r&&t({nombre:s.nombre||"",apellidos:s.apellidos||"",telefono:s.telefono||""})}}catch(e){c.error("No se pudieron cargar los datos del perfil")}finally{n(!1)}})},[]);const O=e=>{const{name:s,value:r}=e.target;t(i(l({},a),{[s]:r}))},F=e=>{const{name:s,value:r}=e.target;E(i(l({},C),{[s]:r}))};return o?u.jsxs("div",{className:"flex justify-center items-center p-12",children:[u.jsx(x,{className:"animate-spin text-blue-600 text-3xl"}),u.jsx("span",{className:"ml-2",children:"Cargando perfil..."})]}):u.jsxs("div",{className:"max-w-3xl mx-auto",children:[u.jsxs("header",{className:"mb-6",children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Mi Perfil"}),u.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Administra tu información personal y de acceso"})]}),u.jsxs("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:[u.jsxs("div",{className:"p-6 border-b border-gray-200",children:[u.jsxs("div",{className:"flex items-center mb-6",children:[u.jsx("div",{className:"h-16 w-16 rounded-full bg-blue-600 flex items-center justify-center text-white text-xl font-semibold",children:(null==(e=null==s?void 0:s.email)?void 0:e.charAt(0).toUpperCase())||u.jsx(h,{className:"h-8 w-8"})}),u.jsxs("div",{className:"ml-4",children:[u.jsx("h2",{className:"text-lg font-medium text-gray-900",children:a.nombre?`${a.nombre} ${a.apellidos}`:"Usuario"}),u.jsx("p",{className:"text-sm text-gray-500",children:null==s?void 0:s.email})]})]}),u.jsxs("form",{onSubmit:e=>d(null,null,function*(){if(e.preventDefault(),(()=>{const e={};return a.nombre&&a.nombre.length<2&&(e.nombre="El nombre debe tener al menos 2 caracteres"),a.apellidos&&a.apellidos.length<2&&(e.apellidos="Los apellidos deben tener al menos 2 caracteres"),k(e),0===Object.keys(e).length})()){v(!0);try{if(!s)throw new Error("No hay usuario autenticado");const{error:e}=yield g.from("perfiles").upsert({user_id:s.id,nombre:a.nombre,apellidos:a.apellidos,telefono:a.telefono,updated_at:new Date});if(e)throw e;c.success("Perfil actualizado correctamente")}catch(r){c.error("Error al guardar los cambios del perfil")}finally{v(!1)}}}),children:[u.jsxs("div",{className:"grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6",children:[u.jsxs("div",{className:"sm:col-span-3",children:[u.jsx("label",{htmlFor:"nombre",className:"block text-sm font-medium text-gray-700",children:"Nombre"}),u.jsxs("div",{className:"mt-1 relative",children:[u.jsx("input",{type:"text",name:"nombre",id:"nombre",value:a.nombre,onChange:O,className:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm "+(S.nombre?"border-red-500":"")}),S.nombre&&u.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.nombre})]})]}),u.jsxs("div",{className:"sm:col-span-3",children:[u.jsx("label",{htmlFor:"apellidos",className:"block text-sm font-medium text-gray-700",children:"Apellidos"}),u.jsxs("div",{className:"mt-1",children:[u.jsx("input",{type:"text",name:"apellidos",id:"apellidos",value:a.apellidos,onChange:O,className:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm "+(S.apellidos?"border-red-500":"")}),S.apellidos&&u.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.apellidos})]})]}),u.jsxs("div",{className:"sm:col-span-6",children:[u.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),u.jsxs("div",{className:"mt-1 relative rounded-md shadow-sm",children:[u.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:u.jsx(f,{className:"h-5 w-5 text-gray-400"})}),u.jsx("input",{type:"text",name:"email",id:"email",disabled:!0,value:(null==s?void 0:s.email)||"",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 bg-gray-50 rounded-md leading-5 text-gray-500 sm:text-sm"})]}),u.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"El email no se puede modificar"})]}),u.jsxs("div",{className:"sm:col-span-4",children:[u.jsx("label",{htmlFor:"telefono",className:"block text-sm font-medium text-gray-700",children:"Teléfono"}),u.jsxs("div",{className:"mt-1",children:[u.jsx("input",{type:"text",name:"telefono",id:"telefono",value:a.telefono,onChange:O,className:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm "+(S.telefono?"border-red-500":"")}),S.telefono&&u.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.telefono})]})]})]}),u.jsx("div",{className:"mt-6",children:u.jsx("button",{type:"submit",disabled:j,className:`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${j?"bg-blue-400":"bg-blue-600 hover:bg-blue-700"} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`,children:j?u.jsxs(u.Fragment,{children:[u.jsx(x,{className:"animate-spin mr-2 h-4 w-4"}),"Guardando..."]}):u.jsxs(u.Fragment,{children:[u.jsx(b,{className:"mr-2 h-4 w-4"}),"Guardar Cambios"]})})})]})]}),u.jsxs("div",{className:"p-6",children:[u.jsxs("div",{className:"flex items-center justify-between mb-4",children:[u.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Seguridad"}),u.jsx("button",{type:"button",onClick:()=>P(!N),className:"text-sm font-medium text-blue-600 hover:text-blue-500",children:N?"Cancelar":"Cambiar contraseña"})]}),N?u.jsx("form",{onSubmit:e=>d(null,null,function*(){if(e.preventDefault(),(()=>{const e={};return C.currentPassword||(e.currentPassword="La contraseña actual es requerida"),C.newPassword?C.newPassword.length<6&&(e.newPassword="La nueva contraseña debe tener al menos 6 caracteres"):e.newPassword="La nueva contraseña es requerida",C.newPassword!==C.confirmPassword&&(e.confirmPassword="Las contraseñas no coinciden"),k(e),0===Object.keys(e).length})()){v(!0);try{const{error:e}=yield g.auth.updateUser({password:C.newPassword});if(e)throw e;E({currentPassword:"",newPassword:"",confirmPassword:""}),P(!1),c.success("Contraseña actualizada correctamente")}catch(s){c.error("Error al cambiar la contraseña")}finally{v(!1)}}}),children:u.jsxs("div",{className:"space-y-4",children:[u.jsxs("div",{children:[u.jsx("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-700",children:"Contraseña actual"}),u.jsxs("div",{className:"mt-1 relative rounded-md shadow-sm",children:[u.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:u.jsx(p,{className:"h-5 w-5 text-gray-400"})}),u.jsx("input",{type:"password",name:"currentPassword",id:"currentPassword",value:C.currentPassword,onChange:F,className:`block w-full pl-10 pr-3 py-2 border ${S.currentPassword?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}),S.currentPassword&&u.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.currentPassword})]})]}),u.jsxs("div",{children:[u.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700",children:"Nueva contraseña"}),u.jsxs("div",{className:"mt-1 relative rounded-md shadow-sm",children:[u.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:u.jsx(p,{className:"h-5 w-5 text-gray-400"})}),u.jsx("input",{type:"password",name:"newPassword",id:"newPassword",value:C.newPassword,onChange:F,className:`block w-full pl-10 pr-3 py-2 border ${S.newPassword?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}),S.newPassword&&u.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.newPassword})]})]}),u.jsxs("div",{children:[u.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirmar nueva contraseña"}),u.jsxs("div",{className:"mt-1 relative rounded-md shadow-sm",children:[u.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:u.jsx(p,{className:"h-5 w-5 text-gray-400"})}),u.jsx("input",{type:"password",name:"confirmPassword",id:"confirmPassword",value:C.confirmPassword,onChange:F,className:`block w-full pl-10 pr-3 py-2 border ${S.confirmPassword?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}),S.confirmPassword&&u.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.confirmPassword})]})]}),u.jsx("div",{className:"rounded-md bg-yellow-50 p-4",children:u.jsxs("div",{className:"flex",children:[u.jsx("div",{className:"flex-shrink-0",children:u.jsx(w,{className:"h-5 w-5 text-yellow-400"})}),u.jsxs("div",{className:"ml-3",children:[u.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"Información importante"}),u.jsx("div",{className:"mt-2 text-sm text-yellow-700",children:u.jsx("p",{children:"Por seguridad, cierre todas las sesiones activas después de cambiar su contraseña."})})]})]})}),u.jsx("div",{className:"mt-4",children:u.jsx("button",{type:"submit",disabled:j,className:`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${j?"bg-blue-400":"bg-blue-600 hover:bg-blue-700"} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`,children:j?u.jsxs(u.Fragment,{children:[u.jsx(x,{className:"animate-spin mr-2 h-4 w-4"}),"Actualizando..."]}):"Actualizar contraseña"})})]})}):u.jsx("p",{className:"text-sm text-gray-600",children:"Es recomendable cambiar su contraseña regularmente para mantener la seguridad de su cuenta."})]})]})]})};export{j as default};
