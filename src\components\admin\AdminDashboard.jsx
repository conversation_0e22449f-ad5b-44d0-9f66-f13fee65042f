import React, { useState, useEffect } from 'react';
import { FaUsers, FaChartLine, FaUserPlus, FaClipboardCheck, FaLock, FaCoins, FaFileAlt } from 'react-icons/fa';
import { supabase } from '../../api/supabaseClient';
import PinManagementService from '../../services/pin/PinManagementService';

// Componente para las tarjetas de estadísticas principales
const StatCard = ({ title, value, icon: Icon, color, percentage, trend, subtitle }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-3xl font-bold text-gray-900">{value}</p>
          {subtitle && (
            <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
          )}
        </div>
        <div className="flex-shrink-0">
          <div
            className="w-12 h-12 rounded-full flex items-center justify-center text-white"
            style={{ backgroundColor: color }}
          >
            <Icon className="w-6 h-6" />
          </div>
        </div>
      </div>
      {percentage && (
        <div className="mt-4 flex items-center">
          <span
            className={`text-sm font-medium px-2 py-1 rounded ${
              trend === 'up' ? 'text-green-700 bg-green-100' : 'text-red-700 bg-red-100'
            }`}
          >
            {trend === 'up' ? '+' : ''}{percentage}%
          </span>
          <span className="text-sm text-gray-500 ml-2">{subtitle || 'vs sem. anterior'}</span>
        </div>
      )}
    </div>
  );
};

// Componente principal del Dashboard
const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalPacientes: 0,
    totalEvaluaciones: 0,
    totalInformes: 0,
    evaluacionesHoy: 0,
    informesGenerados: 0,
    promedioPercentil: 0,
    aptitudesAltas: 0
  });
  const [loading, setLoading] = useState(true);

  console.log('AdminDashboard: Renderizando componente');

  // Función para cargar estadísticas reales
  const cargarEstadisticasReales = async () => {
    try {
      setLoading(true);

      // 1. Total de usuarios
      const { data: usuarios, error: errorUsuarios } = await supabase
        .from('usuarios')
        .select('id', { count: 'exact' });

      // 2. Total de pacientes
      const { data: pacientes, error: errorPacientes } = await supabase
        .from('pacientes')
        .select('id', { count: 'exact' });

      // 3. Total de evaluaciones
      const { data: evaluaciones, error: errorEvaluaciones } = await supabase
        .from('evaluaciones')
        .select('id', { count: 'exact' });

      // 4. Total de informes generados
      const { data: informes, error: errorInformes } = await supabase
        .from('informes_generados')
        .select('id', { count: 'exact' })
        .neq('estado', 'eliminado');

      // 5. Evaluaciones de hoy
      const hoy = new Date().toISOString().split('T')[0];
      const { data: evaluacionesHoy, error: errorEvaluacionesHoy } = await supabase
        .from('evaluaciones')
        .select('id', { count: 'exact' })
        .gte('fecha_inicio', `${hoy}T00:00:00`)
        .lt('fecha_inicio', `${hoy}T23:59:59`);

      // 6. Estadísticas de resultados
      const { data: resultados, error: errorResultados } = await supabase
        .from('resultados')
        .select('percentil');

      let promedioPercentil = 0;
      let aptitudesAltas = 0;

      if (resultados && resultados.length > 0) {
        const percentiles = resultados.map(r => r.percentil || 0);
        promedioPercentil = Math.round(percentiles.reduce((sum, p) => sum + p, 0) / percentiles.length);
        aptitudesAltas = resultados.filter(r => (r.percentil || 0) >= 75).length;
      }

      setStats({
        totalUsers: usuarios?.length || 0,
        totalPacientes: pacientes?.length || 0,
        totalEvaluaciones: evaluaciones?.length || 0,
        totalInformes: informes?.length || 0,
        evaluacionesHoy: evaluacionesHoy?.length || 0,
        informesGenerados: informes?.length || 0,
        promedioPercentil,
        aptitudesAltas
      });

    } catch (error) {
      console.error('Error cargando estadísticas:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    cargarEstadisticasReales();
  }, []);

  // Datos de estadísticas principales con datos reales
  const mainStats = [
    {
      title: 'Usuarios Registrados',
      value: stats.totalUsers,
      icon: FaUsers,
      color: '#3B82F6', // blue-500
      subtitle: 'Total en el sistema'
    },
    {
      title: 'Pacientes Evaluados',
      value: stats.totalPacientes,
      icon: FaUserPlus,
      color: '#10B981', // green-500
      subtitle: 'Registros de pacientes'
    },
    {
      title: 'Evaluaciones Realizadas',
      value: stats.totalEvaluaciones,
      icon: FaClipboardCheck,
      color: '#F59E0B', // amber-500
      subtitle: 'Total completadas'
    },
    {
      title: 'Informes Generados',
      value: stats.totalInformes,
      icon: FaFileAlt,
      color: '#8B5CF6', // purple-500
      subtitle: 'Documentos creados'
    }
  ];

  // Estadísticas adicionales
  const additionalStats = [
    {
      title: 'Evaluaciones Hoy',
      value: stats.evaluacionesHoy,
      icon: FaChartLine,
      color: '#06B6D4', // cyan-500
      subtitle: 'Actividad del día'
    },
    {
      title: 'Promedio Percentil',
      value: `${stats.promedioPercentil}%`,
      icon: FaCoins,
      color: '#F97316', // orange-500
      subtitle: 'Rendimiento general'
    },
    {
      title: 'Aptitudes Altas',
      value: stats.aptitudesAltas,
      icon: FaChartLine,
      color: '#22C55E', // green-500
      subtitle: 'Percentil ≥ 75'
    }
  ];

  // Datos de actividad reciente basados en estadísticas reales
  const recentActivities = [
    {
      id: 1,
      type: 'Sistema BAT-7 Activo',
      user: `${stats.totalUsers} usuarios registrados`,
      time: 'En tiempo real',
      icon: FaUsers,
      color: '#10B981'
    },
    {
      id: 2,
      type: 'Evaluaciones Completadas',
      user: `${stats.totalEvaluaciones} evaluaciones realizadas`,
      time: 'Total acumulado',
      icon: FaClipboardCheck,
      color: '#F59E0B'
    },
    {
      id: 3,
      type: 'Informes Generados',
      user: `${stats.totalInformes} documentos creados`,
      time: 'Disponibles',
      icon: FaFileAlt,
      color: '#8B5CF6'
    },
    {
      id: 4,
      type: 'Rendimiento Promedio',
      user: `${stats.promedioPercentil}% percentil general`,
      time: 'Estadística',
      icon: FaChartLine,
      color: '#06B6D4'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Cargando...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Título del Dashboard */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Dashboard de Administración</h2>
        <p className="text-gray-600">Resumen general del sistema BAT-7</p>
      </div>

      {/* Tarjetas de estadísticas principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {mainStats.map((stat, index) => (
          <StatCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            subtitle={stat.subtitle}
          />
        ))}
      </div>

      {/* Estadísticas adicionales */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {additionalStats.map((stat, index) => (
          <StatCard
            key={`additional-${index}`}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            subtitle={stat.subtitle}
          />
        ))}
      </div>

      {/* Gráfico de estadísticas del sistema */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribución de Datos del Sistema</h3>
          <div className="h-48 flex items-end justify-between space-x-2">
            {[
              { label: 'Usuarios', value: stats.totalUsers, color: '#3B82F6' },
              { label: 'Pacientes', value: stats.totalPacientes, color: '#10B981' },
              { label: 'Evaluaciones', value: Math.min(stats.totalEvaluaciones, 50), color: '#F59E0B' },
              { label: 'Informes', value: stats.totalInformes, color: '#8B5CF6' },
              { label: 'Hoy', value: stats.evaluacionesHoy, color: '#06B6D4' },
              { label: 'Aptitudes+', value: Math.min(stats.aptitudesAltas, 50), color: '#22C55E' }
            ].map((item, index) => {
              const maxValue = Math.max(stats.totalUsers, stats.totalPacientes, 50);
              const height = maxValue > 0 ? (item.value / maxValue) * 100 : 0;
              return (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div className="text-xs font-medium text-gray-700 mb-1">{item.value}</div>
                  <div
                    className="w-full rounded-t-lg"
                    style={{
                      backgroundColor: item.color,
                      height: `${Math.max(height, 10)}%`,
                      minHeight: '20px'
                    }}
                  ></div>
                  <span className="text-xs text-gray-500 mt-2 text-center">
                    {item.label}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Actividad reciente */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Actividad Reciente</h3>
          <div className="space-y-4">
            {recentActivities.map((activity) => {
              const Icon = activity.icon;
              return (
                <div key={activity.id} className="flex items-center space-x-3">
                  <div
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white flex-shrink-0"
                    style={{ backgroundColor: activity.color }}
                  >
                    <Icon className="w-4 h-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{activity.type}</p>
                    <p className="text-sm text-gray-500 truncate">{activity.user}</p>
                  </div>
                  <div className="text-sm text-gray-500">{activity.time}</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
