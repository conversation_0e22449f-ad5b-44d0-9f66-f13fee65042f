var e=Object.defineProperty,a=Object.defineProperties,s=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,i=(a,s,l)=>s in a?e(a,s,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[s]=l,n=(e,a)=>{for(var s in a||(a={}))r.call(a,s)&&i(e,s,a[s]);if(l)for(var s of l(a))t.call(a,s)&&i(e,s,a[s]);return e},o=(e,l)=>a(e,s(l)),d=(e,a)=>{var s={};for(var i in e)r.call(e,i)&&a.indexOf(i)<0&&(s[i]=e[i]);if(null!=e&&l)for(var i of l(e))a.indexOf(i)<0&&t.call(e,i)&&(s[i]=e[i]);return s};import{j as c,r as m}from"./vendor-BqMjyOVw.js";import{B as x,C as u,b as h,a as p}from"./index-Bdl1jgS_.js";import{u as g}from"./useToast-Du4vz6Q_.js";const f=e=>{var a=e,{type:s="text",name:l,value:r,onChange:t,placeholder:i,disabled:o=!1,readOnly:m=!1,error:x,className:u=""}=a,h=d(a,["type","name","value","onChange","placeholder","disabled","readOnly","error","className"]);const p=`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm ${x?"border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300"} ${o?"bg-gray-100 cursor-not-allowed":""} ${u}`;return c.jsx("input",n({type:s,name:l,value:r,onChange:t,placeholder:i,disabled:o,readOnly:m,className:p,"aria-invalid":x?"true":"false"},h))},b=e=>{var a=e,{options:s=[],value:l,onChange:r,placeholder:t="Seleccionar...",displayKey:i="label",valueKey:x="value",name:u,disabled:h=!1,error:p,className:g=""}=a,f=d(a,["options","value","onChange","placeholder","displayKey","valueKey","name","disabled","error","className"]);const[b,j]=m.useState(!1),N=m.useRef(null),v=s.find(e=>e[x]===l),y=v?v[i]:t;m.useEffect(()=>{const e=e=>{N.current&&!N.current.contains(e.target)&&j(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);const w=`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm ${p?"border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500":"border-gray-300"} ${h?"bg-gray-100 cursor-not-allowed":"cursor-pointer"} ${g}`;return c.jsxs("div",{className:"relative",ref:N,children:[c.jsx("div",o(n({className:w,onClick:()=>{h||j(!b)},tabIndex:0,role:"button","aria-haspopup":"listbox","aria-expanded":b},f),{children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsx("span",{className:v?"":"text-gray-500",children:y}),c.jsx("svg",{className:"w-5 h-5 text-gray-400 transition-transform "+(b?"transform rotate-180":""),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:c.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})),b&&c.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md max-h-60 overflow-auto border border-gray-200",children:c.jsxs("ul",{className:"py-1",role:"listbox",children:[s.map((e,a)=>c.jsx("li",{className:"px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 "+(e[x]===l?"bg-primary-50 text-primary-700":""),onClick:()=>(e=>{r(e[x],u),j(!1)})(e),role:"option","aria-selected":e[x]===l,children:e[i]},a)),0===s.length&&c.jsx("li",{className:"px-3 py-2 text-sm text-gray-500",children:"No hay opciones disponibles"})]})})]})},j=e=>{var a=e,{data:s=[],columns:l=[],pagination:r=null,searchable:t=!1,className:i=""}=a,x=d(a,["data","columns","pagination","searchable","className"]);const[u,h]=m.useState(1),[p,g]=m.useState(""),[b,j]=m.useState(s),N=(null==r?void 0:r.pageSize)||10,v=Math.ceil(b.length/N);m.useEffect(()=>{if(t&&p){const e=s.filter(e=>l.some(a=>{if(!a.accessor)return!1;const s=e[a.accessor];return null!=s&&String(s).toLowerCase().includes(p.toLowerCase())}));j(e),h(1)}else j(s)},[p,s,l,t]);const y=r?b.slice((u-1)*N,u*N):b,w=e=>{h(e)};return c.jsxs("div",{className:"overflow-hidden",children:[t&&c.jsx("div",{className:"mb-4",children:c.jsx(f,{type:"text",placeholder:"Buscar...",value:p,onChange:e=>g(e.target.value),className:"max-w-xs"})}),c.jsx("div",{className:"overflow-x-auto",children:c.jsxs("table",o(n({className:`min-w-full divide-y divide-gray-200 ${i}`},x),{children:[c.jsx("thead",{className:"bg-gray-50",children:c.jsx("tr",{children:l.map((e,a)=>c.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e.header},a))})}),c.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:y.length>0?y.map((e,a)=>c.jsx("tr",{className:"hover:bg-gray-50",children:l.map((a,s)=>c.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:a.cell?a.cell({value:e[a.accessor],row:e}):e[a.accessor]},s))},a)):c.jsx("tr",{children:c.jsx("td",{colSpan:l.length,className:"px-6 py-4 text-center text-sm text-gray-500",children:"No hay datos disponibles"})})})]}))}),r&&v>1&&c.jsxs("nav",{className:"flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4",children:[c.jsx("div",{className:"hidden sm:block",children:c.jsxs("p",{className:"text-sm text-gray-700",children:["Mostrando ",c.jsx("span",{className:"font-medium",children:(u-1)*N+1})," ","a"," ",c.jsx("span",{className:"font-medium",children:Math.min(u*N,b.length)})," ","de ",c.jsx("span",{className:"font-medium",children:b.length})," resultados"]})}),c.jsxs("div",{className:"flex-1 flex justify-between sm:justify-end",children:[c.jsx("button",{onClick:()=>w(u-1),disabled:1===u,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md "+(1===u?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white text-gray-700 hover:bg-gray-50"),children:"Anterior"}),c.jsx("div",{className:"hidden md:flex mx-2",children:[...Array(v)].map((e,a)=>c.jsx("button",{onClick:()=>w(a+1),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium "+(u===a+1?"bg-primary-50 border-primary-500 text-primary-700":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"),children:a+1},a))}),c.jsx("button",{onClick:()=>w(u+1),disabled:u===v,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md "+(u===v?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white text-gray-700 hover:bg-gray-50"),children:"Siguiente"})]})]})]})},N=()=>{const[e,a]=m.useState([]),[s,l]=m.useState(!1),[r,t]=m.useState(!1),[i,d]=m.useState(null),[N,v]=m.useState({id:null,firstName:"",lastName:"",email:"",gender:"",age:"",phone:"",education:"",position:""}),{showSuccess:y,showError:w,showInfo:C}=g();m.useEffect(()=>{l(!0),setTimeout(()=>{a([{id:1,firstName:"Juan",lastName:"Pérez",email:"<EMAIL>",gender:"male",age:28,phone:"+591 72345678",education:"Licenciatura en Psicología",position:"Psicólogo Clínico"},{id:2,firstName:"María",lastName:"González",email:"<EMAIL>",gender:"female",age:32,phone:"+591 73456789",education:"Maestría en Recursos Humanos",position:"Especialista en RRHH"},{id:3,firstName:"Carlos",lastName:"Rodríguez",email:"<EMAIL>",gender:"male",age:25,phone:"+591 74567890",education:"Ingeniería en Sistemas",position:"Desarrollador Web"}]),l(!1)},800)},[]);const k=e=>{const{name:a,value:s}=e.target;v(o(n({},N),{[a]:s}))},E=()=>{v({id:null,firstName:"",lastName:"",email:"",gender:"",age:"",phone:"",education:"",position:""}),d(null)},S=(e=null)=>{e?(v({id:e.id,firstName:e.firstName,lastName:e.lastName,email:e.email,gender:e.gender,age:e.age.toString(),phone:e.phone,education:e.education,position:e.position}),d(e)):E(),t(!0)},z=()=>{t(!1),E()},O=[{header:"",accessor:"gender",cell:({value:e})=>c.jsx("div",{className:"flex justify-center",children:"male"===e?c.jsx("div",{className:"w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center",children:c.jsx("i",{className:"fas fa-mars text-blue-600"})}):"female"===e?c.jsx("div",{className:"w-8 h-8 rounded-lg bg-pink-100 flex items-center justify-center",children:c.jsx("i",{className:"fas fa-venus text-pink-600"})}):c.jsx("div",{className:"w-8 h-8 rounded-lg bg-purple-100 flex items-center justify-center",children:c.jsx("i",{className:"fas fa-genderless text-purple-600"})})})},{header:"Nombre",accessor:"firstName",cell:({value:e,row:a})=>c.jsxs("div",{children:[c.jsxs("div",{className:"font-medium text-gray-900",children:[e," ",a.lastName]}),c.jsx("div",{className:"text-xs text-gray-500",children:a.position||"Sin cargo asignado"})]})},{header:"Email",accessor:"email",cell:({value:e})=>c.jsxs("div",{children:[c.jsx("div",{className:"text-gray-900",children:e}),c.jsxs("div",{className:"text-xs text-gray-500",children:[c.jsx("i",{className:"fas fa-envelope mr-1"})," Contacto principal"]})]})},{header:"Teléfono",accessor:"phone"},{header:"Edad",accessor:"age",cell:({value:e})=>c.jsx("div",{className:"text-center bg-gray-100 rounded-lg py-1 px-2 w-12",children:e})},{header:"Formación",accessor:"education"},{header:"Acciones",accessor:"id",cell:({value:s,row:r})=>c.jsxs("div",{className:"flex space-x-2",children:[c.jsx("button",{onClick:()=>S(r),className:"text-blue-600 hover:text-sky-800 bg-blue-100 p-2 rounded-lg focus:outline-none transition-all duration-200",title:"Editar",children:c.jsx("i",{className:"fas fa-edit"})}),c.jsx("button",{onClick:()=>{return r=s,void(window.confirm("¿Está seguro de eliminar este candidato?")&&(l(!0),setTimeout(()=>{a(e.filter(e=>e.id!==r)),C("Candidato eliminado correctamente"),l(!1)},600)));var r},className:"text-red-600 hover:text-red-800 bg-red-100 p-2 rounded-lg focus:outline-none transition-all duration-200",title:"Eliminar",children:c.jsx("i",{className:"fas fa-trash-alt"})})]})}];return c.jsxs("div",{className:"container mx-auto py-6",children:[c.jsxs("div",{className:"mb-6 flex flex-col md:flex-row md:items-center md:justify-between",children:[c.jsxs("div",{children:[c.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Gestión de Candidatos"}),c.jsx("p",{className:"text-gray-600",children:"Administre la información de los candidatos para pruebas psicométricas"})]}),c.jsx("div",{className:"mt-4 md:mt-0",children:c.jsxs(x,{variant:"primary",onClick:()=>S(),className:"flex items-center",children:[c.jsx("i",{className:"fas fa-plus mr-2"}),"Nuevo Candidato"]})})]}),c.jsxs(u,{className:"overflow-hidden shadow-lg border-0 rounded-xl",children:[c.jsx(h,{className:"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white border-0",children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"bg-white/20 p-2 rounded-lg mr-3",children:c.jsx("i",{className:"fas fa-user-tie text-xl"})}),c.jsx("h2",{className:"text-xl font-semibold",children:"Lista de Candidatos"})]})}),c.jsx(p,{children:s?c.jsx("div",{className:"py-16 text-center",children:c.jsxs("div",{className:"flex flex-col items-center justify-center",children:[c.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),c.jsx("p",{className:"text-gray-500",children:"Cargando datos..."})]})}):c.jsx(j,{data:e,columns:O,pagination:{pageSize:5},searchable:!0})})]}),r&&c.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center",children:c.jsx("div",{className:"relative mx-auto p-5 w-full max-w-md md:max-w-lg",children:c.jsxs("div",{className:"bg-white rounded-xl shadow-2xl overflow-hidden",children:[c.jsxs("div",{className:"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white py-4 px-6 flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"bg-white/20 h-8 w-8 rounded-lg flex items-center justify-center mr-3",children:c.jsx("i",{className:"fas fa-user-tie"})}),c.jsx("h3",{className:"text-lg font-medium",children:i?"Editar Candidato":"Nuevo Candidato"})]}),c.jsx("button",{onClick:z,className:"text-white hover:text-gray-200 focus:outline-none",children:c.jsx("i",{className:"fas fa-times"})})]}),c.jsxs("form",{onSubmit:s=>{s.preventDefault(),(N.firstName.trim()?N.lastName.trim()?N.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(N.email)?N.gender?N.age.trim()||(w("La edad es obligatoria"),0):(w("El género es obligatorio"),0):(w("El formato de email no es válido"),0):(w("El email es obligatorio"),0):(w("El apellido es obligatorio"),0):(w("El nombre es obligatorio"),0))&&(l(!0),setTimeout(()=>{const s=o(n({},N),{id:N.id||Date.now(),age:parseInt(N.age,10)});i?(a(e.map(e=>e.id===s.id?s:e)),y("Candidato actualizado correctamente")):(a([...e,s]),y("Candidato creado correctamente")),l(!1),z()},600))},className:"p-6",children:[c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:["Nombre ",c.jsx("span",{className:"text-red-500",children:"*"})]}),c.jsx(f,{type:"text",id:"firstName",name:"firstName",value:N.firstName,onChange:k,placeholder:"Nombre"})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:["Apellido ",c.jsx("span",{className:"text-red-500",children:"*"})]}),c.jsx(f,{type:"text",id:"lastName",name:"lastName",value:N.lastName,onChange:k,placeholder:"Apellido"})]})]}),c.jsxs("div",{className:"mb-4",children:[c.jsxs("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:["Email ",c.jsx("span",{className:"text-red-500",children:"*"})]}),c.jsx(f,{type:"email",id:"email",name:"email",value:N.email,onChange:k,placeholder:"Email"})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"gender",className:"block text-sm font-medium text-gray-700 mb-1",children:["Género ",c.jsx("span",{className:"text-red-500",children:"*"})]}),c.jsx(b,{id:"gender",name:"gender",value:N.gender,onChange:(e,a)=>{v(o(n({},N),{[a]:e}))},options:[{value:"male",label:"Masculino"},{value:"female",label:"Femenino"},{value:"other",label:"Otro"}],placeholder:"Seleccionar género"})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"age",className:"block text-sm font-medium text-gray-700 mb-1",children:["Edad ",c.jsx("span",{className:"text-red-500",children:"*"})]}),c.jsx(f,{type:"number",id:"age",name:"age",value:N.age,onChange:k,placeholder:"Edad",min:"18",max:"100"})]})]}),c.jsxs("div",{className:"mb-4",children:[c.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"Teléfono"}),c.jsx(f,{type:"text",id:"phone",name:"phone",value:N.phone,onChange:k,placeholder:"Teléfono"})]}),c.jsxs("div",{className:"mb-4",children:[c.jsx("label",{htmlFor:"education",className:"block text-sm font-medium text-gray-700 mb-1",children:"Formación Académica"}),c.jsx(f,{type:"text",id:"education",name:"education",value:N.education,onChange:k,placeholder:"Formación académica"})]}),c.jsxs("div",{className:"mb-6",children:[c.jsx("label",{htmlFor:"position",className:"block text-sm font-medium text-gray-700 mb-1",children:"Cargo o Posición"}),c.jsx(f,{type:"text",id:"position",name:"position",value:N.position,onChange:k,placeholder:"Cargo o posición"})]}),c.jsxs("div",{className:"flex justify-end space-x-3",children:[c.jsx(x,{type:"button",variant:"outline",onClick:z,children:"Cancelar"}),c.jsx(x,{type:"submit",variant:"primary",disabled:s,children:s?c.jsxs(c.Fragment,{children:[c.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[c.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),c.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Guardando..."]}):"Guardar"})]})]})]})})})]})};export{N as default};
