/**
 * @file debugInterpretaciones.js
 * @description Script para debuggear las interpretaciones que se están mostrando
 */

import { InterpretacionCualitativaService } from '../services/interpretacionCualitativaService.js';
import { InterpretacionesSupabaseService } from '../services/InterpretacionesSupabaseService.js';

// Datos de prueba basados en la imagen
const resultadosPrueba = [
  {
    aptitud: { codigo: 'R', nombre: 'Razonamiento' },
    percentil: 5,
    puntaje_directo: 6,
    errores: 24
  },
  {
    aptitud: { codigo: 'V', nombre: 'Aptitud Verbal' },
    percentil: 50,
    puntaje_directo: 22,
    errores: 10
  },
  {
    aptitud: { codigo: 'N', nombre: 'Aptitud Numérica' },
    percentil: 5,
    puntaje_directo: 6,
    errores: 26
  },
  {
    aptitud: { codigo: 'O', nombre: 'Ortografía' },
    percentil: 15,
    puntaje_directo: 18,
    errores: 14
  }
];

const pacientePrueba = {
  nombre: 'Paciente de Prueba',
  edad: 16
};

console.log('🔍 DEBUG: Verificando interpretaciones...\n');

async function debugInterpretaciones() {
  try {
    // 1. Probar interpretaciones directas desde Supabase
    console.log('📊 1. Probando interpretaciones directas desde Supabase:');
    
    for (const resultado of resultadosPrueba) {
      try {
        const interpretacionDirecta = await InterpretacionesSupabaseService.obtenerInterpretacionOficial(
          resultado.aptitud.codigo, 
          resultado.percentil
        );
        
        console.log(`✅ ${resultado.aptitud.codigo}-${resultado.percentil}:`);
        console.log(`   Nivel: ${interpretacionDirecta.nivel_nombre}`);
        console.log(`   Rendimiento (${interpretacionDirecta.rendimiento?.length || 0} chars): ${interpretacionDirecta.rendimiento?.substring(0, 100)}...`);
        console.log(`   Fuente: ${interpretacionDirecta.fuente}`);
        console.log('');
      } catch (error) {
        console.log(`❌ ${resultado.aptitud.codigo}-${resultado.percentil}: Error - ${error.message}`);
      }
    }

    // 2. Probar el servicio completo
    console.log('\n🎯 2. Probando servicio completo:');
    
    const interpretacionCompleta = await InterpretacionCualitativaService.generarInterpretacionPersonalizada(
      resultadosPrueba, 
      pacientePrueba
    );

    console.log('✅ Interpretación completa generada');
    console.log(`   Aptitudes específicas: ${interpretacionCompleta.aptitudesEspecificas?.length || 0}`);
    
    interpretacionCompleta.aptitudesEspecificas?.forEach((apt, index) => {
      console.log(`\n📋 ${apt.codigo} - ${apt.nombre}:`);
      console.log(`   Percentil: ${apt.percentil}`);
      console.log(`   Nivel: ${apt.nivel}`);
      console.log(`   Fuente: ${apt.fuente}`);
      console.log(`   Rendimiento (${apt.interpretacion?.rendimiento?.length || 0} chars):`);
      console.log(`   "${apt.interpretacion?.rendimiento?.substring(0, 150)}..."`);
      
      // Verificar si es texto oficial o genérico
      const esTextoOficial = apt.interpretacion?.rendimiento?.length > 100 && 
                            apt.interpretacion?.rendimiento?.includes('evaluado');
      console.log(`   ${esTextoOficial ? '✅ TEXTO OFICIAL' : '⚠️ TEXTO GENÉRICO'}`);
    });

  } catch (error) {
    console.error('💥 Error en debug:', error);
  }
}

// Ejecutar debug
debugInterpretaciones().then(() => {
  console.log('\n🎯 Debug completado');
}).catch(error => {
  console.error('💥 Error ejecutando debug:', error);
});

export default {};
