/**
 * Script para migrar interpretaciones de Concentración (C) a Supabase
 * Basado en el documento oficial "Interpretacion de aptitudes y Generalidaes.txt"
 */

import { createClient } from '@supabase/supabase-js';

// Configuración directa de Supabase con service role key
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjMxMjg0MSwiZXhwIjoyMDYxODg4ODQxfQ.zwk3Wiay5jjeYOrg8B1M6T98B2esCqRFI43At-AFV3A';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Interpretaciones de Concentración (CON) del documento oficial
const interpretacionesConcentracion = {
  1: {
    rendimiento: 'El evaluado presenta una velocidad de procesamiento de las operaciones mentales muy lenta, y una capacidad de concentración marcadamente imprecisa. Es muy probable que su estilo atencional sea Lento-Impreciso (LI), lo que indica un nivel muy bajo de destreza para percibir y discriminar con rapidez la configuración perceptiva de las figuras.',
    academico: 'Se esperan grandes dificultades para mantener la activación ante estímulos repetitivos y monótonos. Su rendimiento académico en tareas que requieren rapidez y precisión (como exámenes contrarreloj o ejercicios de mecanizado) se verá muy afectado. Podría requerir una supervisión constante para evitar la impulsividad y el descuido.',
    vocacional: 'La lentitud y falta de precisión en el procesamiento de información pueden ser un obstáculo para la resolución de problemas cotidianos y para la adaptación a entornos de trabajo o estudio rápidos. Se debe explorar si esta puntuación es consecuencia de una falta de motivación o de un déficit atencional.'
  },
  2: {
    rendimiento: 'La velocidad de procesamiento es lenta, y la capacidad de concentración es inferior a la media. Esto se refleja en un estilo atencional que puede ser Lento-Impreciso (LI) o Impreciso (I). Hay una menor capacidad para percibir y discriminar la configuración perceptiva de las figuras, y el procesamiento mental simple carece de precisión.',
    academico: 'El evaluado puede tener un rendimiento inferior al esperado en tareas que exigen rapidez y precisión. Su habilidad para atender selectiva y secuencialmente a figuras similares es inferior a lo que cabría esperar. Puede beneficiarse de estrategias que minimicen las distracciones y le permitan trabajar a su propio ritmo.',
    vocacional: 'La tendencia a la impulsividad puede generar errores por descuido. En el ámbito vocacional, se debe considerar que profesiones que demanden una atención sostenida y una alta velocidad de respuesta pueden ser una fuente de estrés.'
  },
  3: {
    rendimiento: 'El evaluado presenta un nivel de velocidad de procesamiento de las operaciones mentales simple ligeramente inferior a la media, con una capacidad para atender selectiva y secuencialmente que es algo inferior a lo esperado. Su puntuación en concentración puede revelar una falta de precisión que caracteriza el procesamiento mental.',
    academico: 'La habilidad para mantener la activación en tareas repetitivas y monótonas podría ser un desafío. Se recomienda el uso de descansos cortos y tareas variadas para mantener el foco.',
    vocacional: 'Es posible que su rendimiento se vea afectado por la presión del tiempo o la falta de motivación. A nivel vocacional, se sugiere la exploración de áreas que valoren más la profundidad del trabajo que la velocidad o el volumen de producción.'
  },
  4: {
    rendimiento: 'El evaluado muestra una velocidad de procesamiento y una capacidad de concentración promedio, con un estilo atencional Normal (N). Su habilidad para atender a estímulos y procesar operaciones mentales simples se encuentra dentro de lo esperado para su edad. Su destreza para percibir y discriminar la configuración de las figuras es adecuada.',
    academico: 'Su rendimiento en tareas que requieren atención y concentración es satisfactorio. Es capaz de mantener el foco en el estudio y completar tareas de manera eficiente y con un nivel de precisión aceptable.',
    vocacional: 'Se adapta bien a los entornos de trabajo o estudio que demandan una atención sostenida y un ritmo normal. Su potencial para el desempeño en una amplia variedad de campos es adecuado.'
  },
  5: {
    rendimiento: 'La velocidad de procesamiento del evaluado es superior a la media, con una capacidad de atención notable. Su estilo atencional tiende a ser Rápido (R) o Preciso (P). Es particularmente hábil para atender selectiva y secuencialmente a figuras similares y para percibir y discriminar la configuración perceptiva con un alto grado de precisión.',
    academico: 'Su rendimiento académico es destacado en tareas que requieren rapidez y precisión. Completa exámenes y ejercicios en un tiempo menor que sus pares, manteniendo un bajo índice de errores.',
    vocacional: 'Muestra una gran capacidad para el trabajo meticuloso y la atención al detalle. Su potencial es elevado en profesiones que exigen una alta velocidad de reacción y una precisión constante, como la programación, la cirugía o el pilotaje.'
  },
  6: {
    rendimiento: 'El evaluado presenta una excelente velocidad de procesamiento y una capacidad de concentración muy alta. Su estilo atencional es Rápido-Preciso (RP), lo que indica un nivel muy elevado de destreza para procesar operaciones mentales simples con gran agilidad y sin cometer errores por descuido.',
    academico: 'Su rendimiento en todas las tareas académicas que demandan atención es sobresaliente. Es capaz de manejar grandes volúmenes de información en poco tiempo, con un alto grado de precisión.',
    vocacional: 'La combinación de rapidez y precisión es una gran fortaleza. Se adapta excelentemente a entornos laborales de alta demanda. Su potencial es óptimo para profesiones que exigen una capacidad de respuesta rápida y un bajo margen de error, como la aviación o la investigación científica.'
  },
  7: {
    rendimiento: 'El evaluado exhibe una velocidad de procesamiento y una capacidad de concentración excepcionales, muy por encima de la de sus pares. Su estilo atencional es Rápido-Preciso (RP), llevando estas cualidades al máximo nivel. No solo es rápido, sino también extraordinariamente preciso, lo que le permite detectar detalles que otros pasarían por alto.',
    academico: 'Su rendimiento académico es sobresaliente en todas las áreas, especialmente en aquellas que requieren una gran cantidad de datos para procesar en un tiempo limitado. Puede destacar en áreas como la informática, la física o la estadística.',
    vocacional: 'Su capacidad excepcional para el procesamiento rápido y preciso de información lo convierte en un candidato ideal para profesiones de alta exigencia cognitiva. Su potencial es óptimo para campos como la investigación avanzada, la medicina de precisión o la ingeniería aeroespacial.'
  }
};

async function migrateConcentracion() {
  console.log('🚀 Iniciando migración de interpretaciones de Concentración (C)...');
  
  let totalInserted = 0;
  let errors = 0;
  
  for (const [nivelId, interpretacion] of Object.entries(interpretacionesConcentracion)) {
    try {
      const { data, error } = await supabase
        .from('interpretaciones_oficiales')
        .upsert({
          aptitud_codigo: 'C',
          nivel_id: parseInt(nivelId),
          rendimiento: interpretacion.rendimiento,
          academico: interpretacion.academico,
          vocacional: interpretacion.vocacional,
          es_oficial: true,
          fuente: 'Documento oficial BAT-7 - Concentración (CON)'
        }, {
          onConflict: 'aptitud_codigo,nivel_id'
        });
      
      if (error) {
        console.error(`❌ Error insertando C-${nivelId}:`, error);
        errors++;
      } else {
        console.log(`✅ Insertada interpretación C-${nivelId}`);
        totalInserted++;
      }
      
    } catch (err) {
      console.error(`❌ Error procesando C-${nivelId}:`, err);
      errors++;
    }
  }
  
  console.log('\n📊 Resumen de migración:');
  console.log(`✅ Interpretaciones insertadas: ${totalInserted}`);
  console.log(`❌ Errores: ${errors}`);
  
  // Verificar migración
  await verifyMigration();
}

async function verifyMigration() {
  console.log('\n🔍 Verificando migración de Concentración...');
  
  try {
    const { data: conteos, error } = await supabase
      .from('interpretaciones_oficiales')
      .select('aptitud_codigo, nivel_id')
      .eq('aptitud_codigo', 'C')
      .eq('es_oficial', true)
      .order('nivel_id');
    
    if (error) {
      console.error('❌ Error verificando migración:', error);
      return;
    }
    
    console.log(`📈 Interpretaciones de Concentración (C): ${conteos?.length || 0}/7`);
    
    if (conteos && conteos.length > 0) {
      conteos.forEach(item => {
        console.log(`   ✅ C-${item.nivel_id}`);
      });
    }
    
    // Probar una interpretación específica
    console.log('\n🧪 Probando obtener interpretación C-5...');
    const { data: prueba, error: errorPrueba } = await supabase
      .from('interpretaciones_oficiales')
      .select('*')
      .eq('aptitud_codigo', 'C')
      .eq('nivel_id', 5)
      .single();
    
    if (errorPrueba) {
      console.error('❌ Error probando interpretación:', errorPrueba);
    } else if (prueba) {
      console.log('✅ Interpretación C-5 encontrada');
      console.log(`   Rendimiento: ${prueba.rendimiento.substring(0, 100)}...`);
    }
    
  } catch (error) {
    console.error('💥 Error durante verificación:', error);
  }
}

// Ejecutar migración
migrateConcentracion().then(() => {
  console.log('\n🎉 Migración de Concentración completada');
}).catch(error => {
  console.error('💥 Error en migración:', error);
});
