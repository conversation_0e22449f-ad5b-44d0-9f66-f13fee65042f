import React, { useState } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { Button } from '../ui/Button';
import { 
  FaCoins, 
  FaExclamationTriangle, 
  FaEnvelope, 
  FaPhone, 
  FaUser,
  FaCreditCard,
  FaShoppingCart,
  FaInfoCircle,
  FaTimes
} from 'react-icons/fa';
import { toast } from 'react-toastify';

/**
 * Componente que muestra sugerencias de recarga cuando no hay pines disponibles
 * Incluye opciones de contacto y información sobre planes
 */
const PinRechargePrompt = ({ 
  psychologistId,
  currentPins = 0,
  requiredPins = 1,
  onClose = null,
  showCloseButton = true,
  variant = 'modal' // 'modal', 'inline', 'banner'
}) => {
  const [contactMethod, setContactMethod] = useState('email');
  const [showContactForm, setShowContactForm] = useState(false);

  // Configuración de planes sugeridos
  const suggestedPlans = [
    {
      id: 'basic',
      name: 'Plan Básico',
      pins: 50,
      price: '$29.99',
      description: 'Ideal para uso ocasional',
      recommended: false
    },
    {
      id: 'professional',
      name: 'Plan Profesional',
      pins: 150,
      price: '$79.99',
      description: 'Para uso regular',
      recommended: true
    },
    {
      id: 'premium',
      name: 'Plan Premium',
      pins: 300,
      price: '$149.99',
      description: 'Para uso intensivo',
      recommended: false
    },
    {
      id: 'unlimited',
      name: 'Plan Ilimitado',
      pins: 'Ilimitado',
      price: '$299.99/mes',
      description: 'Sin límites de uso',
      recommended: false
    }
  ];

  const handleContactAdmin = (method) => {
    switch (method) {
      case 'email':
        window.location.href = 'mailto:<EMAIL>?subject=Solicitud de Recarga de Pines&body=Hola, necesito recargar mis pines para continuar generando informes.';
        break;
      case 'phone':
        toast.info('Contacte al administrador al teléfono: +****************');
        break;
      case 'form':
        setShowContactForm(true);
        break;
      default:
        toast.info('Por favor contacte al administrador del sistema');
    }
  };

  const handlePlanSelection = (plan) => {
    toast.info(`Plan ${plan.name} seleccionado. Contacte al administrador para proceder con la compra.`);
    handleContactAdmin('email');
  };

  // Configuración de estilos por variante
  const variantStyles = {
    modal: 'max-w-2xl mx-auto',
    inline: 'w-full',
    banner: 'w-full border-l-4 border-red-500'
  };

  const containerClass = `${variantStyles[variant]} ${variant === 'banner' ? 'bg-red-50' : ''}`;

  return (
    <Card className={containerClass}>
      <CardHeader className={`${variant === 'banner' ? 'bg-red-100' : 'bg-red-50'} border-b`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FaExclamationTriangle className="text-red-600 mr-3 h-6 w-6" />
            <div>
              <h3 className="text-lg font-semibold text-red-900">
                Pines Insuficientes
              </h3>
              <p className="text-sm text-red-700">
                No puede generar informes sin pines disponibles
              </p>
            </div>
          </div>
          
          {showCloseButton && onClose && (
            <button
              onClick={onClose}
              className="text-red-400 hover:text-red-600 transition-colors"
            >
              <FaTimes className="h-5 w-5" />
            </button>
          )}
        </div>
      </CardHeader>

      <CardBody className="space-y-6">
        {/* Estado actual */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Estado Actual:</span>
            <div className="flex items-center text-red-600">
              <FaCoins className="mr-1 h-4 w-4" />
              <span className="font-semibold">{currentPins} pines disponibles</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Requeridos:</span>
            <div className="flex items-center text-blue-600">
              <FaCoins className="mr-1 h-4 w-4" />
              <span className="font-semibold">{requiredPins} pines</span>
            </div>
          </div>
          
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Faltan:</span>
              <div className="flex items-center text-orange-600">
                <FaCoins className="mr-1 h-4 w-4" />
                <span className="font-semibold">{Math.max(0, requiredPins - currentPins)} pines</span>
              </div>
            </div>
          </div>
        </div>

        {/* Opciones de recarga */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4 flex items-center">
            <FaShoppingCart className="mr-2 text-blue-600" />
            Planes de Recarga Disponibles
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {suggestedPlans.map((plan) => (
              <div
                key={plan.id}
                className={`relative border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md ${
                  plan.recommended 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handlePlanSelection(plan)}
              >
                {plan.recommended && (
                  <div className="absolute -top-2 left-4 bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                    Recomendado
                  </div>
                )}
                
                <div className="text-center">
                  <h5 className="font-semibold text-gray-900 mb-1">{plan.name}</h5>
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    {typeof plan.pins === 'number' ? `${plan.pins} pines` : plan.pins}
                  </div>
                  <div className="text-lg text-gray-600 mb-2">{plan.price}</div>
                  <p className="text-sm text-gray-500">{plan.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Opciones de contacto */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4 flex items-center">
            <FaUser className="mr-2 text-green-600" />
            Contactar Administrador
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <Button
              onClick={() => handleContactAdmin('email')}
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center justify-center"
            >
              <FaEnvelope className="mr-2" />
              Enviar Email
            </Button>
            
            <Button
              onClick={() => handleContactAdmin('phone')}
              className="bg-green-600 hover:bg-green-700 text-white flex items-center justify-center"
            >
              <FaPhone className="mr-2" />
              Llamar
            </Button>
            
            <Button
              onClick={() => handleContactAdmin('form')}
              className="bg-purple-600 hover:bg-purple-700 text-white flex items-center justify-center"
            >
              <FaInfoCircle className="mr-2" />
              Formulario
            </Button>
          </div>
        </div>

        {/* Información adicional */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <FaInfoCircle className="text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <h5 className="font-medium mb-2">Información Importante:</h5>
              <ul className="space-y-1 list-disc list-inside">
                <li>Los pines se consumen automáticamente al generar informes</li>
                <li>Cada informe requiere exactamente 1 pin</li>
                <li>Los pines no utilizados no caducan</li>
                <li>El administrador procesará su solicitud en 24-48 horas</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Formulario de contacto (si está activo) */}
        {showContactForm && (
          <ContactForm 
            psychologistId={psychologistId}
            onClose={() => setShowContactForm(false)}
            requiredPins={requiredPins}
          />
        )}
      </CardBody>
    </Card>
  );
};

/**
 * Formulario de contacto integrado
 */
const ContactForm = ({ psychologistId, onClose, requiredPins }) => {
  const [formData, setFormData] = useState({
    subject: 'Solicitud de Recarga de Pines',
    message: `Hola,\n\nNecesito recargar mis pines para continuar generando informes.\n\nPines requeridos: ${requiredPins}\nID de psicólogo: ${psychologistId}\n\nGracias.`,
    urgency: 'normal'
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Simular envío del formulario
    toast.success('Solicitud enviada exitosamente. El administrador se contactará pronto.');
    onClose();
  };

  const handleChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <div className="border-t pt-6">
      <h5 className="font-medium text-gray-900 mb-4">Formulario de Contacto</h5>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Asunto
          </label>
          <input
            type="text"
            name="subject"
            value={formData.subject}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Urgencia
          </label>
          <select
            name="urgency"
            value={formData.urgency}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="low">Baja</option>
            <option value="normal">Normal</option>
            <option value="high">Alta</option>
            <option value="urgent">Urgente</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Mensaje
          </label>
          <textarea
            name="message"
            value={formData.message}
            onChange={handleChange}
            rows={6}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
          />
        </div>
        
        <div className="flex space-x-3">
          <Button type="submit" className="bg-blue-600 hover:bg-blue-700 text-white">
            Enviar Solicitud
          </Button>
          <Button type="button" onClick={onClose} variant="outline">
            Cancelar
          </Button>
        </div>
      </form>
    </div>
  );
};

/**
 * Versión compacta para usar como banner
 */
export const CompactPinRechargePrompt = (props) => {
  return (
    <PinRechargePrompt
      {...props}
      variant="banner"
      showCloseButton={false}
    />
  );
};

export default PinRechargePrompt;
