import{j as i}from"./vendor-CIyllXGj.js";import{C as e,b as s,a}from"./index-CrXvaDRr.js";const n=()=>i.jsxDEV("div",{className:"container mx-auto py-6",children:[i.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Administración de Instituciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:7,columnNumber:7},void 0),i.jsxDEV(e,{children:[i.jsxDEV(s,{children:i.jsxDEV("h2",{className:"text-lg font-medium",children:"Lista de Instituciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:11,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:10,columnNumber:9},void 0),i.jsxDEV(a,{children:i.jsxDEV("p",{className:"text-gray-600",children:"Esta sección permitirá gestionar las instituciones registradas en el sistema (componente en desarrollo)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:14,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:13,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:9,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:6,columnNumber:5},void 0);export{n as default};
