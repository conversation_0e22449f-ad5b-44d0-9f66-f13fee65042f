-- Script completo para arreglar el usuario administrador
-- EJECUT<PERSON> ESTE SCRIPT EN EL EDITOR SQL DE SUPABASE

-- =====================================================
-- PASO 1: VERIFICAR ESTRUCTURA DE LA TABLA USUARIOS
-- =====================================================

-- Mostrar columnas actuales de la tabla usuarios
SELECT 
  'COLUMNAS ACTUALES EN LA TABLA USUARIOS:' as info,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'usuarios'
ORDER BY ordinal_position;

-- =====================================================
-- PASO 2: VERIFICAR SI EL USUARIO ADMINISTRADOR EXISTE
-- =====================================================

-- Buscar el usuario administrador en auth.users
SELECT 
  'USUARIO EN AUTH.USERS:' as info,
  id,
  email,
  created_at,
  email_confirmed_at,
  raw_user_meta_data
FROM auth.users 
WHERE email = '<EMAIL>';

-- Buscar el usuario administrador en tabla usuarios
SELECT 
  'USUARIO EN TABLA USUARIOS:' as info,
  *
FROM public.usuarios 
WHERE id = '4116c661-bd7d-4af7-8117-966a00d63152';

-- =====================================================
-- PASO 3: INSERTAR USUARIO ADMINISTRADOR
-- =====================================================

-- Función para insertar el usuario administrador con la estructura correcta
DO $$
DECLARE
  admin_user_id UUID := '4116c661-bd7d-4af7-8117-966a00d63152';
  user_exists BOOLEAN;
  has_tipo_usuario BOOLEAN;
  has_rol BOOLEAN;
BEGIN
  -- Verificar si el usuario ya existe
  SELECT EXISTS (
    SELECT 1 FROM public.usuarios WHERE id = admin_user_id
  ) INTO user_exists;
  
  IF user_exists THEN
    RAISE NOTICE 'El usuario administrador ya existe en la tabla usuarios';
    RETURN;
  END IF;
  
  -- Verificar qué columna de rol existe
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'usuarios' 
    AND column_name = 'tipo_usuario'
  ) INTO has_tipo_usuario;
  
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'usuarios' 
    AND column_name = 'rol'
  ) INTO has_rol;
  
  -- Insertar usuario con la columna correcta
  IF has_tipo_usuario THEN
    INSERT INTO public.usuarios (
      id,
      documento,
      nombre,
      apellido,
      tipo_usuario,
      activo,
      fecha_creacion
    ) VALUES (
      admin_user_id,
      '13716261',
      'Administrador',
      'Principal',
      'administrador',
      true,
      now()
    );
    RAISE NOTICE 'Usuario administrador insertado con columna tipo_usuario';
    
  ELSIF has_rol THEN
    INSERT INTO public.usuarios (
      id,
      documento,
      nombre,
      apellido,
      rol,
      activo,
      fecha_creacion
    ) VALUES (
      admin_user_id,
      '13716261',
      'Administrador',
      'Principal',
      'administrador',
      true,
      now()
    );
    RAISE NOTICE 'Usuario administrador insertado con columna rol';
    
  ELSE
    -- Insertar sin columna de rol
    INSERT INTO public.usuarios (
      id,
      documento,
      nombre,
      apellido,
      activo,
      fecha_creacion
    ) VALUES (
      admin_user_id,
      '13716261',
      'Administrador',
      'Principal',
      true,
      now()
    );
    RAISE NOTICE 'Usuario administrador insertado sin columna de rol (agregar manualmente)';
  END IF;
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error al insertar usuario: %', SQLERRM;
END $$;

-- =====================================================
-- PASO 4: VERIFICAR INSERCIÓN
-- =====================================================

-- Verificar que el usuario fue insertado correctamente
SELECT 
  'VERIFICACIÓN FINAL:' as info,
  u.*,
  au.email
FROM public.usuarios u
JOIN auth.users au ON u.id = au.id
WHERE u.id = '4116c661-bd7d-4af7-8117-966a00d63152';

-- =====================================================
-- PASO 5: AGREGAR COLUMNAS FALTANTES SI ES NECESARIO
-- =====================================================

-- Agregar columna tipo_usuario si no existe
DO $$
DECLARE
  column_exists BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'usuarios' 
    AND column_name = 'tipo_usuario'
  ) INTO column_exists;
  
  IF NOT column_exists THEN
    ALTER TABLE public.usuarios ADD COLUMN tipo_usuario TEXT;
    RAISE NOTICE 'Columna tipo_usuario agregada';
    
    -- Actualizar el usuario administrador
    UPDATE public.usuarios 
    SET tipo_usuario = 'administrador'
    WHERE id = '4116c661-bd7d-4af7-8117-966a00d63152';
    RAISE NOTICE 'Usuario administrador actualizado con tipo_usuario';
  ELSE
    RAISE NOTICE 'La columna tipo_usuario ya existe';
  END IF;
END $$;

-- Agregar columna activo si no existe
DO $$
DECLARE
  column_exists BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'usuarios' 
    AND column_name = 'activo'
  ) INTO column_exists;
  
  IF NOT column_exists THEN
    ALTER TABLE public.usuarios ADD COLUMN activo BOOLEAN DEFAULT true;
    RAISE NOTICE 'Columna activo agregada';
  ELSE
    RAISE NOTICE 'La columna activo ya existe';
  END IF;
END $$;

-- Agregar columna fecha_creacion si no existe
DO $$
DECLARE
  column_exists BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'usuarios' 
    AND column_name = 'fecha_creacion'
  ) INTO column_exists;
  
  IF NOT column_exists THEN
    ALTER TABLE public.usuarios ADD COLUMN fecha_creacion TIMESTAMPTZ DEFAULT now();
    RAISE NOTICE 'Columna fecha_creacion agregada';
  ELSE
    RAISE NOTICE 'La columna fecha_creacion ya existe';
  END IF;
END $$;

-- =====================================================
-- PASO 6: VERIFICACIÓN FINAL COMPLETA
-- =====================================================

-- Mostrar estructura final de la tabla
SELECT 
  'ESTRUCTURA FINAL DE LA TABLA USUARIOS:' as info,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'usuarios'
ORDER BY ordinal_position;

-- Mostrar el usuario administrador final
SELECT 
  'USUARIO ADMINISTRADOR FINAL:' as info,
  u.*,
  au.email,
  au.created_at as auth_created_at
FROM public.usuarios u
JOIN auth.users au ON u.id = au.id
WHERE au.email = '<EMAIL>';

-- =====================================================
-- RESUMEN
-- =====================================================

SELECT 
  'RESUMEN:' as info,
  'Usuario administrador configurado correctamente' as status,
  'Puede hacer <NAME_EMAIL>' as login_email,
  'Contraseña: 13716261' as password,
  'También puede usar documento: 13716261' as login_documento;
