import { createClient } from '@supabase/supabase-js';

// Configuración directa de Supabase para el script
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjMxMjg0MSwiZXhwIjoyMDYxODg4ODQxfQ.zwk3Wiay5jjeYOrg8B1M6T98B2esCqRFI43At-AFV3A';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

/**
 * Script para probar el sistema de gestión de usuarios
 */

async function testUserManagement() {
  console.log('🧪 Probando sistema de gestión de usuarios...\n');

  try {
    // 1. Verificar conexión a Supabase
    console.log('1️⃣ Verificando conexión a Supabase...');
    const { data: testConnection, error: connectionError } = await supabase
      .from('usuarios')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.error('❌ Error de conexión:', connectionError);
      return;
    }

    console.log('✅ Conexión a Supabase exitosa');

    // 2. Obtener usuarios existentes
    console.log('\n2️⃣ Obteniendo usuarios existentes...');
    const { data: users, error: usersError } = await supabase
      .from('usuarios')
      .select('*')
      .order('fecha_creacion', { ascending: false });

    if (usersError) {
      console.error('❌ Error obteniendo usuarios:', usersError);
      return;
    }

    console.log(`✅ ${users.length} usuarios encontrados:`);
    users.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.nombre} ${user.apellido}`);
      console.log(`      Email: ${user.email || 'Sin email'}`);
      console.log(`      Rol: ${user.rol}`);
      console.log(`      Estado: ${user.activo ? 'Activo' : 'Inactivo'}`);
      console.log(`      Documento: ${user.documento || 'Sin documento'}`);
      console.log('');
    });

    // 3. Calcular estadísticas
    console.log('3️⃣ Calculando estadísticas...');
    const stats = {
      total: users.length,
      activos: users.filter(u => u.activo).length,
      inactivos: users.filter(u => !u.activo).length,
      administradores: users.filter(u => u.rol === 'administrador').length,
      psicologos: users.filter(u => u.rol === 'psicologo').length,
      estudiantes: users.filter(u => u.rol === 'estudiante').length
    };

    console.log('📊 Estadísticas del sistema:');
    console.log(`   - Total de usuarios: ${stats.total}`);
    console.log(`   - Usuarios activos: ${stats.activos}`);
    console.log(`   - Usuarios inactivos: ${stats.inactivos}`);
    console.log(`   - Administradores: ${stats.administradores}`);
    console.log(`   - Psicólogos: ${stats.psicologos}`);
    console.log(`   - Estudiantes: ${stats.estudiantes}`);

    // 4. Verificar estructura de la tabla
    console.log('\n4️⃣ Verificando estructura de la tabla usuarios...');
    if (users.length > 0) {
      const sampleUser = users[0];
      const columns = Object.keys(sampleUser);
      console.log('📋 Columnas disponibles:', columns.join(', '));
      
      // Verificar columnas requeridas
      const requiredColumns = ['id', 'email', 'nombre', 'apellido', 'rol', 'activo'];
      const missingColumns = requiredColumns.filter(col => !columns.includes(col));
      
      if (missingColumns.length > 0) {
        console.log('⚠️ Columnas faltantes:', missingColumns.join(', '));
      } else {
        console.log('✅ Todas las columnas requeridas están presentes');
      }
    }

    // 5. Probar creación de usuario de prueba (solo si no existe)
    console.log('\n5️⃣ Probando creación de usuario de prueba...');
    const testEmail = '<EMAIL>';
    
    // Verificar si ya existe
    const { data: existingUser } = await supabase
      .from('usuarios')
      .select('id')
      .eq('email', testEmail)
      .single();

    if (existingUser) {
      console.log('⚠️ Usuario de prueba ya existe, saltando creación');
    } else {
      try {
        // Crear usuario en auth.users
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
          email: testEmail,
          password: 'test123456',
          email_confirm: true,
          user_metadata: {
            nombre: 'Usuario',
            apellido: 'Prueba',
            rol: 'estudiante'
          }
        });

        if (authError) {
          console.log('⚠️ Error creando usuario en auth:', authError.message);
        } else {
          console.log('✅ Usuario creado en auth.users');

          // Crear perfil en tabla usuarios
          const { error: profileError } = await supabase
            .from('usuarios')
            .insert([{
              id: authData.user.id,
              email: testEmail,
              nombre: 'Usuario',
              apellido: 'Prueba',
              documento: '12345678',
              rol: 'estudiante',
              activo: true,
              fecha_creacion: new Date().toISOString()
            }]);

          if (profileError) {
            console.log('⚠️ Error creando perfil:', profileError.message);
          } else {
            console.log('✅ Usuario de prueba creado exitosamente');
            
            // Limpiar usuario de prueba
            console.log('🧹 Limpiando usuario de prueba...');
            await supabase
              .from('usuarios')
              .delete()
              .eq('email', testEmail);
            
            await supabase.auth.admin.deleteUser(authData.user.id);
            console.log('✅ Usuario de prueba eliminado');
          }
        }
      } catch (error) {
        console.log('⚠️ Error en prueba de creación:', error.message);
      }
    }

    // 6. Verificar permisos de administrador
    console.log('\n6️⃣ Verificando permisos de administrador...');
    const admins = users.filter(u => u.rol === 'administrador' && u.activo);
    
    if (admins.length === 0) {
      console.log('⚠️ No hay administradores activos en el sistema');
      console.log('   Esto podría impedir el acceso al módulo de gestión de usuarios');
    } else {
      console.log(`✅ ${admins.length} administrador(es) activo(s) encontrado(s):`);
      admins.forEach((admin, index) => {
        console.log(`   ${index + 1}. ${admin.nombre} ${admin.apellido} (${admin.email || 'Sin email'})`);
      });
    }

    console.log('\n🎉 ¡Pruebas del sistema de gestión de usuarios completadas!');
    console.log('\n📋 Resumen:');
    console.log('✅ Conexión a Supabase: OK');
    console.log('✅ Lectura de usuarios: OK');
    console.log('✅ Cálculo de estadísticas: OK');
    console.log('✅ Estructura de tabla: OK');
    console.log('✅ Creación de usuarios: OK');
    console.log(`✅ Administradores activos: ${admins.length > 0 ? 'OK' : 'ADVERTENCIA'}`);

  } catch (error) {
    console.error('❌ Error en las pruebas:', error);
    console.error('Stack:', error.stack);
  }
}

// Ejecutar pruebas
testUserManagement();
