import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkPsychologists() {
  console.log('👨‍⚕️ Verificando tabla psicologos...');

  try {
    // Verificar tabla psicologos
    const { data: psicologos, error: psicologosError } = await supabase
      .from('psicologos')
      .select('*')
      .limit(5);

    if (psicologosError) {
      console.error('❌ Error consultando psicologos:', psicologosError);
    } else if (psicologos && psicologos.length > 0) {
      console.log('✅ Psicólogos encontrados:', psicologos.length);
      console.log('📋 Columnas:', Object.keys(psicologos[0]));
      console.log('\n👥 Lista de psicólogos:');
      psicologos.forEach((psy, index) => {
        console.log(`${index + 1}. ${psy.nombre} ${psy.apellidos || psy.apellido} (${psy.email || 'Sin email'})`);
      });
    } else {
      console.log('⚠️ No hay psicólogos en la tabla');
      
      // Verificar si hay usuarios con rol psicologo
      console.log('\n🔍 Verificando usuarios con rol psicologo...');
      const { data: usuarios, error: usuariosError } = await supabase
        .from('usuarios')
        .select('*')
        .eq('rol', 'psicologo');

      if (!usuariosError && usuarios) {
        console.log(`📊 Usuarios con rol psicologo: ${usuarios.length}`);
        usuarios.forEach((user, index) => {
          console.log(`${index + 1}. ${user.nombre} ${user.apellido} (${user.email})`);
        });
      }
    }

    // Verificar si podemos insertar un psicólogo de prueba
    console.log('\n🧪 Intentando crear psicólogo de prueba...');
    const { data: newPsy, error: insertError } = await supabase
      .from('usuarios')
      .insert([
        {
          documento: 'TEST123',
          nombre: 'Dr. Test',
          apellido: 'Psicólogo',
          rol: 'psicologo',
          email: '<EMAIL>',
          activo: true
        }
      ])
      .select()
      .single();

    if (insertError) {
      console.error('❌ Error insertando psicólogo de prueba:', insertError);
    } else {
      console.log('✅ Psicólogo de prueba creado:', newPsy);
      
      // Verificar si aparece en la vista psicologos
      const { data: psyInView, error: viewError } = await supabase
        .from('psicologos')
        .select('*')
        .eq('id', newPsy.id)
        .single();

      if (viewError) {
        console.error('❌ Error consultando vista psicologos:', viewError);
      } else {
        console.log('✅ Psicólogo aparece en vista psicologos:', psyInView);
      }
    }

  } catch (error) {
    console.error('❌ Error inesperado:', error);
  }
}

checkPsychologists();
