/**
 * Script para verificar la sintaxis de los archivos principales
 */

console.log('🔍 Verificando sintaxis de archivos principales...');

try {
  // Verificar main.jsx
  console.log('📄 Verificando main.jsx...');
  await import('../main.jsx');
  console.log('✅ main.jsx - OK');
} catch (error) {
  console.error('❌ Error en main.jsx:', error.message);
}

try {
  // Verificar AuthContext
  console.log('📄 Verificando AuthContext.jsx...');
  await import('../context/AuthContext.jsx');
  console.log('✅ AuthContext.jsx - OK');
} catch (error) {
  console.error('❌ Error en AuthContext.jsx:', error.message);
}

try {
  // Verificar App.jsx
  console.log('📄 Verificando App.jsx...');
  await import('../App.jsx');
  console.log('✅ App.jsx - OK');
} catch (error) {
  console.error('❌ Error en App.jsx:', error.message);
}

console.log('\n🏁 Verificación completada.');
