const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/InformesService-3ys5QOjE.js","assets/vendor-CIyllXGj.js","assets/vendor-B4zyQOk2.css","assets/ImprovedPinControlService-CavTPF7K.js","assets/Dashboard-By-A3w0U.js","assets/enhancedSupabaseService-CmvqXOxn.js","assets/AuthContext-BuWOeHC-.js","assets/Profile-CmhsO56d.js","assets/Settings-D29HDXcH.js","assets/Home-C8IC7kLs.js","assets/Help-C1P9aIaX.js","assets/PageHeader-W_yV9pKP.js","assets/Configuracion-DDoyuVvc.js","assets/PinManagementService-D5etXbQs.js","assets/Candidates-CMjiwpbT.js","assets/useToast-C0wO38N6.js","assets/VerbalInfo-BUE3FkkE.js","assets/Users-DqDIbwFi.js","assets/Institutions-CS_6dbxn.js","assets/Reports-OAjgqATU.js","assets/Reports-Dl_zACwQ.css","assets/Patients-CI8AwP-m.js","assets/Administration-ouykJIdO.js","assets/TestPage-Dzn6P88L.js","assets/CompleteReport-BYdz7KY5.js","assets/interpretacionesAptitudes-Bt_sak-B.js","assets/SavedReports-CALKB9OU.js","assets/ViewSavedReport-1RHPfgYI.js","assets/PinAssignmentPanel-GP7D23rK.js","assets/Students-CBhBRc-M.js","assets/Tests-BvRoNzkc.js","assets/Reports-CYHl12v3.js","assets/Tests-TiOHsE2T.js","assets/TestCard-DWMYHxS2.js","assets/TestCard-BIeKcx_6.css","assets/Results-Cb2ZYrtP.js","assets/Patients-DFxJmHQx.js","assets/Patients-TixZtU9H.css","assets/Questionnaire-Bv8A40Ij.js","assets/InformePaciente-DJgp1Ajm.js","assets/BasicLogin-Dj7A0Hte.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,a=Object.defineProperties,i=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,o=(a,i,r)=>i in a?e(a,i,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[i]=r,n=(e,a)=>{for(var i in a||(a={}))s.call(a,i)&&o(e,i,a[i]);if(r)for(var i of r(a))t.call(a,i)&&o(e,i,a[i]);return e},l=(e,r)=>a(e,i(r)),c=(e,a)=>{var i={};for(var o in e)s.call(e,o)&&a.indexOf(o)<0&&(i[o]=e[o]);if(null!=e&&r)for(var o of r(e))a.indexOf(o)<0&&t.call(e,o)&&(i[o]=e[o]);return i},u=(e,a,i)=>o(e,"symbol"!=typeof a?a+"":a,i),m=(e,a,i)=>new Promise((r,s)=>{var t=e=>{try{n(i.next(e))}catch(a){s(a)}},o=e=>{try{n(i.throw(e))}catch(a){s(a)}},n=e=>e.done?r(e.value):Promise.resolve(e.value).then(t,o);n((i=i.apply(e,a)).next())});import{Q as d,aB as p,aC as b,aD as N,aE as g,r as f,j as x,u as h,L as v,aF as j,aG as V,a0 as E,aH as C,w as y,x as D,aI as B,v as w,aJ as A,aK as R,P as T,aL as I,aa as z,aM as S,C as q,k as P,ab as M,ad as _,l as k,J as O,aN as L,aO as $,H as F,h as Q,T as U,ac as H,aP as J,aQ as G,aR as W,aS as Y,F as Z,aT as X,aU as K,aV as ee}from"./vendor-CIyllXGj.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))a(e);new MutationObserver(e=>{for(const i of e)if("childList"===i.type)for(const e of i.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&a(e)}).observe(document,{childList:!0,subtree:!0})}function a(e){if(e.ep)return;e.ep=!0;const a=function(e){const a={};return e.integrity&&(a.integrity=e.integrity),e.referrerPolicy&&(a.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?a.credentials="include":"anonymous"===e.crossOrigin?a.credentials="omit":a.credentials="same-origin",a}(e);fetch(e.href,a)}}();const ae="validation",ie="authentication",re="authorization",se="network",te="server",oe="client",ne="unknown",le="low",ce="medium",ue="high",me="critical";class de extends Error{constructor({message:e,type:a=ne,severity:i=ce,code:r=null,details:s=null,userMessage:t=null,shouldLog:o=!0,shouldNotify:n=!0,retryable:l=!1,originalError:c=null}){super(e),this.name="AppError",this.type=a,this.severity=i,this.code=r,this.details=s,this.userMessage=t||this.generateUserMessage(),this.shouldLog=o,this.shouldNotify=n,this.retryable=l,this.originalError=c,this.timestamp=(new Date).toISOString(),this.stack=(null==c?void 0:c.stack)||this.stack}generateUserMessage(){const e={[ae]:"Por favor, verifique los datos ingresados.",[ie]:"Error de autenticación. Verifique sus credenciales.",[re]:"No tiene permisos para realizar esta acción.",[se]:"Error de conexión. Verifique su conexión a internet.",[te]:"Error del servidor. Intente nuevamente en unos momentos.",[oe]:"Error en la aplicación. Recargue la página.",[ne]:"Ha ocurrido un error inesperado."};return e[this.type]||e[ne]}toJSON(){return{name:this.name,message:this.message,type:this.type,severity:this.severity,code:this.code,details:this.details,userMessage:this.userMessage,timestamp:this.timestamp,stack:this.stack}}}const pe=new class{constructor(){this.errorLog=[],this.maxLogSize=100,this.retryAttempts=new Map,this.maxRetries=3}handle(e,a={}){const i=this.normalizeError(e,a);return i.shouldLog&&this.logError(i,a),i.shouldNotify&&this.notifyUser(i),i}normalizeError(e,a={}){return e instanceof de?e:(null==e?void 0:e.response)?this.handleHttpError(e,a):(null==e?void 0:e.code)?this.handleServiceError(e,a):e instanceof TypeError&&e.message.includes("fetch")?new de({message:e.message,type:se,severity:ue,userMessage:"Error de conexión. Verifique su conexión a internet.",retryable:!0,originalError:e}):new de({message:e.message||"Error desconocido",type:ne,severity:ce,originalError:e})}handleHttpError(e,a={}){var i,r;const s=null==(i=e.response)?void 0:i.status,t=null==(r=e.response)?void 0:r.data,o={400:{type:ae,severity:le,userMessage:"Datos inválidos. Verifique la información ingresada."},401:{type:ie,severity:ce,userMessage:"Sesión expirada. Por favor, inicie sesión nuevamente."},403:{type:re,severity:ce,userMessage:"No tiene permisos para realizar esta acción."},404:{type:oe,severity:le,userMessage:"Recurso no encontrado."},422:{type:ae,severity:le,userMessage:"Datos inválidos. Verifique la información ingresada."},429:{type:te,severity:ce,userMessage:"Demasiadas solicitudes. Intente nuevamente en unos momentos.",retryable:!0},500:{type:te,severity:ue,userMessage:"Error del servidor. Intente nuevamente en unos momentos.",retryable:!0},502:{type:te,severity:ue,userMessage:"Servicio temporalmente no disponible.",retryable:!0},503:{type:te,severity:ue,userMessage:"Servicio temporalmente no disponible.",retryable:!0}}[s]||{type:te,severity:ce,userMessage:"Error del servidor. Intente nuevamente."};return new de(n({message:(null==t?void 0:t.message)||e.message||`HTTP ${s} Error`,code:s,details:t,originalError:e},o))}handleServiceError(e,a={}){const i={PGRST116:{type:ae,userMessage:"Datos inválidos o faltantes."},PGRST301:{type:re,userMessage:"No tiene permisos para acceder a este recurso."},23505:{type:ae,userMessage:"Ya existe un registro con estos datos."},23503:{type:ae,userMessage:"Referencia inválida en los datos."},invalid_credentials:{type:ie,userMessage:"Credenciales inválidas."},email_not_confirmed:{type:ie,userMessage:"Email no confirmado. Verifique su correo electrónico."},signup_disabled:{type:re,userMessage:"Registro de usuarios deshabilitado."}}[e.code]||{type:te,userMessage:"Error del servicio. Intente nuevamente."};return new de(n({message:e.message||"Service error",code:e.code,details:e.details,originalError:e},i))}logError(e,a={}){const i=l(n({},e.toJSON()),{context:a,url:window.location.href,userAgent:navigator.userAgent,timestamp:(new Date).toISOString()});this.errorLog.unshift(i),this.errorLog.length>this.maxLogSize&&this.errorLog.pop(),e.severity}notifyUser(e){const a={position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0};switch(e.severity){case me:case ue:d.error(e.userMessage,l(n({},a),{autoClose:8e3}));break;case ce:d.warning(e.userMessage,a);break;case le:d.info(e.userMessage,l(n({},a),{autoClose:3e3}));break;default:d(e.userMessage,a)}}showSuccess(e,a={}){d.success(e,n({position:"top-right",autoClose:3e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0},a))}showInfo(e,a={}){d.info(e,n({position:"top-right",autoClose:4e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0},a))}retry(e,a){return m(this,arguments,function*(e,a,i=this.maxRetries){const r=this.retryAttempts.get(a)||0;if(r>=i)throw this.retryAttempts.delete(a),new de({message:"Maximum retry attempts exceeded",type:oe,severity:ue,userMessage:"Operación fallida después de varios intentos."});try{const i=yield e();return this.retryAttempts.delete(a),i}catch(s){this.retryAttempts.set(a,r+1);const e=Math.min(1e3*Math.pow(2,r),1e4);throw yield new Promise(a=>setTimeout(a,e)),s}})}sendToMonitoring(e){return m(this,null,function*(){})}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[],this.retryAttempts.clear()}createErrorBoundaryHandler(){return(e,a)=>{const i=new de({message:e.message,type:oe,severity:ue,details:a,userMessage:"Ha ocurrido un error en la aplicación. La página se recargará automáticamente.",originalError:e});this.handle(i,{errorBoundary:!0}),setTimeout(()=>{window.location.reload()},3e3)}}},be=(e,a)=>pe.handle(e,a),Ne="not_started",ge="in_progress",fe="paused",xe="completed",he="expired",ve=p("test/startTestSession",(e,a)=>m(null,[e,a],function*({testId:e,userId:a},{rejectWithValue:i}){try{const i=yield fetch(`/api/tests/${e}/start`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:a})});if(!i.ok)throw new Error("Failed to start test");return yield i.json()}catch(r){return improvedErrorHandler.handleError(r,{context:"startTestSession",userId:a,testId:e}),i(r.message)}})),je=p("test/submitAnswer",(e,a)=>m(null,[e,a],function*({sessionId:e,questionId:a,answer:i,timeSpent:r},{rejectWithValue:s}){try{const s=yield fetch(`/api/test-sessions/${e}/answers`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({questionId:a,answer:i,timeSpent:r,timestamp:Date.now()})});if(!s.ok)throw new Error("Failed to submit answer");return yield s.json()}catch(t){return be(t,{context:"submitAnswer",sessionId:e,questionId:a}),s(t.message)}})),Ve=p("test/completeTestSession",(e,a)=>m(null,[e,a],function*({sessionId:e},{getState:a,rejectWithValue:i}){try{const i=a(),{answers:r,startTime:s}=i.test.currentSession,t=yield fetch(`/api/test-sessions/${e}/complete`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({answers:r,completedAt:Date.now(),totalTime:Date.now()-s})});if(!t.ok)throw new Error("Failed to complete test");return yield t.json()}catch(r){return be(r,{context:"completeTestSession",sessionId:e}),i(r.message)}})),Ee=p("test/fetchTestHistory",(e,a)=>m(null,[e,a],function*({userId:e,limit:a=10,offset:i=0},{rejectWithValue:r}){try{const r=yield fetch(`/api/users/${e}/test-history?limit=${a}&offset=${i}`);if(!r.ok)throw new Error("Failed to fetch test history");return yield r.json()}catch(s){return be(s,{context:"fetchTestHistory",userId:e}),r(s.message)}})),Ce=p("test/fetchTestResults",(e,a)=>m(null,[e,a],function*({sessionId:e},{rejectWithValue:a}){try{const a=yield fetch(`/api/test-sessions/${e}/results`);if(!a.ok)throw new Error("Failed to fetch test results");return yield a.json()}catch(i){return be(i,{context:"fetchTestResults",sessionId:e}),a(i.message)}})),ye={currentSession:{id:null,testId:null,userId:null,status:Ne,startTime:null,endTime:null,currentQuestionIndex:0,totalQuestions:0,answers:{},timeSpent:{},progress:0,allowedTime:null,timeRemaining:null,isPaused:!1,pauseStartTime:null,totalPauseTime:0},testConfig:{id:null,name:"",description:"",type:null,version:"1.0",timeLimit:null,questionsPerPage:1,allowBackNavigation:!1,allowPause:!0,randomizeQuestions:!1,randomizeAnswers:!1,showProgress:!0,showTimeRemaining:!0,autoSubmit:!0,passingScore:null},questions:[],currentQuestion:null,results:{sessionId:null,scores:{},percentile:null,interpretation:"",recommendations:[],detailedAnalysis:{},comparisonData:null,generatedAt:null},history:{tests:[],totalCount:0,currentPage:1,hasMore:!1},availableTests:[],loading:{starting:!1,submitting:!1,completing:!1,fetchingHistory:!1,fetchingResults:!1,fetchingTests:!1},errors:{start:null,submit:null,complete:null,history:null,results:null,general:null},ui:{showInstructions:!0,showConfirmation:!1,showResults:!1,highlightedAnswers:[],flaggedQuestions:[],reviewMode:!1,fullscreen:!1},statistics:{totalTestsTaken:0,averageScore:0,bestScore:0,lastTestDate:null,testsByType:{},monthlyProgress:[]},currentTest:null,testResults:null,answeredQuestions:{},timeRemaining:0,testStarted:!1,testCompleted:!1,error:null},De=b({name:"test",initialState:ye,reducers:{initializeSession:(e,a)=>{const{sessionId:i,testConfig:r,questions:s}=a.payload;e.currentSession=l(n({},ye.currentSession),{id:i,testId:r.id,status:Ne,totalQuestions:s.length,allowedTime:r.timeLimit,timeRemaining:r.timeLimit}),e.testConfig=r,e.questions=s,e.currentQuestion=s[0]||null,e.currentTest=r,e.timeRemaining=r.timeLimit||0},startSession:e=>{e.currentSession.status=ge,e.currentSession.startTime=Date.now(),e.ui.showInstructions=!1,e.testStarted=!0},pauseSession:e=>{e.testConfig.allowPause&&e.currentSession.status===ge&&(e.currentSession.status=fe,e.currentSession.isPaused=!0,e.currentSession.pauseStartTime=Date.now())},resumeSession:e=>{e.currentSession.isPaused&&(e.currentSession.status=ge,e.currentSession.isPaused=!1,e.currentSession.pauseStartTime&&(e.currentSession.totalPauseTime+=Date.now()-e.currentSession.pauseStartTime,e.currentSession.pauseStartTime=null))},goToQuestion:(e,a)=>{const i=a.payload;i>=0&&i<e.questions.length&&(e.currentSession.currentQuestionIndex=i,e.currentQuestion=e.questions[i],e.currentSession.progress=(i+1)/e.questions.length*100)},nextQuestion:e=>{const a=e.currentSession.currentQuestionIndex+1;a<e.questions.length&&(e.currentSession.currentQuestionIndex=a,e.currentQuestion=e.questions[a],e.currentSession.progress=(a+1)/e.questions.length*100)},previousQuestion:e=>{if(e.testConfig.allowBackNavigation){const a=e.currentSession.currentQuestionIndex-1;a>=0&&(e.currentSession.currentQuestionIndex=a,e.currentQuestion=e.questions[a],e.currentSession.progress=(a+1)/e.questions.length*100)}},setAnswer:(e,a)=>{const{questionId:i,answer:r,timeSpent:s}=a.payload;e.currentSession.answers[i]={value:r,timestamp:Date.now(),timeSpent:s||0},s&&(e.currentSession.timeSpent[i]=s),e.answeredQuestions[i]=r},clearAnswer:(e,a)=>{const i=a.payload;delete e.currentSession.answers[i],delete e.currentSession.timeSpent[i],delete e.answeredQuestions[i]},flagQuestion:(e,a)=>{const i=a.payload;e.ui.flaggedQuestions.includes(i)||e.ui.flaggedQuestions.push(i)},unflagQuestion:(e,a)=>{const i=a.payload;e.ui.flaggedQuestions=e.ui.flaggedQuestions.filter(e=>e!==i)},updateTimeRemaining:(e,a)=>{const i=a.payload;e.currentSession.timeRemaining=Math.max(0,i),e.timeRemaining=i,i<=0&&e.testConfig.autoSubmit&&(e.currentSession.status=he)},toggleInstructions:e=>{e.ui.showInstructions=!e.ui.showInstructions},setShowConfirmation:(e,a)=>{e.ui.showConfirmation=a.payload},setReviewMode:(e,a)=>{e.ui.reviewMode=a.payload},toggleFullscreen:e=>{e.ui.fullscreen=!e.ui.fullscreen},highlightAnswers:(e,a)=>{e.ui.highlightedAnswers=a.payload},setResults:(e,a)=>{e.results=l(n(n({},e.results),a.payload),{generatedAt:Date.now()}),e.ui.showResults=!0,e.testResults=a.payload},clearResults:e=>{e.results=ye.results,e.ui.showResults=!1,e.testResults=null},updateStatistics:(e,a)=>{e.statistics=n(n({},e.statistics),a.payload)},setError:(e,a)=>{const{type:i,error:r}=a.payload;void 0!==e.errors[i]&&(e.errors[i]=r),e.error=r},clearError:(e,a)=>{const i=a.payload;void 0!==e.errors[i]&&(e.errors[i]=null),"general"===i&&(e.error=null)},clearAllErrors:e=>{e.errors=ye.errors,e.error=null},setCurrentTest:(e,a)=>{var i;e.currentTest=a.payload,e.answeredQuestions={},e.testStarted=!1,e.testCompleted=!1,e.timeRemaining=(null==(i=a.payload)?void 0:i.duration)?60*a.payload.duration:0,a.payload&&(e.testConfig=l(n(n({},e.testConfig),a.payload),{timeLimit:a.payload.duration?60*a.payload.duration:null}),e.currentSession.timeRemaining=e.timeRemaining)},startTest:e=>{e.testStarted=!0,e.currentSession.status=ge,e.currentSession.startTime=Date.now()},answerQuestion:(e,a)=>{const{questionId:i,answerId:r}=a.payload;e.answeredQuestions[i]=r,e.currentSession.answers[i]={value:r,timestamp:Date.now(),timeSpent:0}},completeTest:e=>{e.testCompleted=!0,e.currentSession.status=xe,e.currentSession.endTime=Date.now()},setTestResults:(e,a)=>{e.testResults=a.payload,e.results=l(n(n({},e.results),a.payload),{generatedAt:Date.now()}),e.ui.showResults=!0},setLoading:(e,a)=>{e.loading=a.payload,Object.keys(e.loading).forEach(i=>{e.loading[i]=a.payload})},resetSession:e=>{e.currentSession=ye.currentSession,e.currentQuestion=null,e.questions=[],e.testConfig=ye.testConfig,e.ui=ye.ui,e.errors=ye.errors},resetTestState:()=>ye,resetTest:()=>ye},extraReducers:e=>{e.addCase(ve.pending,e=>{e.loading.starting=!0,e.errors.start=null,e.loading=!0}).addCase(ve.fulfilled,(e,a)=>{e.loading.starting=!1,e.loading=!1;const{session:i,config:r,questions:s}=a.payload;e.currentSession=l(n(n({},e.currentSession),i),{status:ge,startTime:Date.now()}),e.testConfig=r,e.questions=s,e.currentQuestion=s[0]||null,e.currentTest=r,e.testStarted=!0}).addCase(ve.rejected,(e,a)=>{e.loading.starting=!1,e.loading=!1,e.errors.start=a.payload,e.error=a.payload}).addCase(je.pending,e=>{e.loading.submitting=!0,e.errors.submit=null}).addCase(je.fulfilled,(e,a)=>{e.loading.submitting=!1}).addCase(je.rejected,(e,a)=>{e.loading.submitting=!1,e.errors.submit=a.payload}).addCase(Ve.pending,e=>{e.loading.completing=!0,e.errors.complete=null,e.loading=!0}).addCase(Ve.fulfilled,(e,a)=>{e.loading.completing=!1,e.loading=!1,e.currentSession.status=xe,e.currentSession.endTime=Date.now(),e.testCompleted=!0,a.payload.results&&(e.results=l(n(n({},e.results),a.payload.results),{sessionId:e.currentSession.id,generatedAt:Date.now()}),e.ui.showResults=!0,e.testResults=a.payload.results)}).addCase(Ve.rejected,(e,a)=>{e.loading.completing=!1,e.loading=!1,e.errors.complete=a.payload,e.error=a.payload}).addCase(Ee.pending,e=>{e.loading.fetchingHistory=!0,e.errors.history=null}).addCase(Ee.fulfilled,(e,a)=>{e.loading.fetchingHistory=!1;const{tests:i,totalCount:r,page:s,hasMore:t}=a.payload;1===s?e.history.tests=i:e.history.tests.push(...i),e.history.totalCount=r,e.history.currentPage=s,e.history.hasMore=t}).addCase(Ee.rejected,(e,a)=>{e.loading.fetchingHistory=!1,e.errors.history=a.payload}).addCase(Ce.pending,e=>{e.loading.fetchingResults=!0,e.errors.results=null}).addCase(Ce.fulfilled,(e,a)=>{e.loading.fetchingResults=!1,e.results=l(n(n({},e.results),a.payload),{generatedAt:Date.now()}),e.ui.showResults=!0,e.testResults=a.payload}).addCase(Ce.rejected,(e,a)=>{e.loading.fetchingResults=!1,e.errors.results=a.payload})}}),{initializeSession:Be,startSession:we,pauseSession:Ae,resumeSession:Re,goToQuestion:Te,nextQuestion:Ie,previousQuestion:ze,setAnswer:Se,clearAnswer:qe,flagQuestion:Pe,unflagQuestion:Me,toggleInstructions:_e,setShowConfirmation:ke,setReviewMode:Oe,toggleFullscreen:Le,highlightAnswers:$e,setResults:Fe,clearResults:Qe,updateStatistics:Ue,clearError:He,clearAllErrors:Je,resetSession:Ge,resetTest:We,setCurrentTest:Ye,startTest:Ze,updateTimeRemaining:Xe,answerQuestion:Ke,completeTest:ea,setTestResults:aa,setLoading:ia,setError:ra,resetTestState:sa}=De.actions,ta=N({reducer:{test:De.reducer},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST"]}}),devTools:!0});g(ta.dispatch),ta.getState,ta.dispatch;const oa=f.createContext(),na=({children:e})=>{const[a,i]=f.useState("administrador"),r={user:{id:"dev-user",email:"<EMAIL>",nombre:"Usuario",apellido:"Desarrollo",tipo_usuario:a},loading:!1,error:null,login:()=>Promise.resolve({success:!0}),logout:()=>Promise.resolve({success:!0}),isAuthenticated:!0,userRole:a,isAdmin:"administrador"===a,isPsicologo:"psicologo"===a,isCandidato:"candidato"===a,setUserType:i};return x.jsxDEV(oa.Provider,{value:r,children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/context/NoAuthContext.jsx",lineNumber:38,columnNumber:5},void 0)},la=()=>{const e=f.useContext(oa);if(!e)throw new Error("useNoAuth debe usarse dentro de NoAuthProvider");return e},ca=({children:e})=>x.jsxDEV("div",{className:"page-content opacity-100 transition-opacity duration-200 ease-in-out",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/transitions/PageTransition.jsx",lineNumber:8,columnNumber:5},void 0),ua=({className:e=""})=>x.jsxDEV("div",{className:`title-container ${e}`,children:[x.jsxDEV("div",{className:"inline-flex items-center",children:x.jsxDEV("h1",{className:"simple-title text-2xl font-bold text-blue-900",children:x.jsxDEV("span",{children:"BAT-7 Batería de Aptitudes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/AnimatedTitle.jsx",lineNumber:13,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/AnimatedTitle.jsx",lineNumber:12,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/AnimatedTitle.jsx",lineNumber:10,columnNumber:7},void 0),x.jsxDEV("style",{jsx:"true",children:"\n        .title-container {\n          display: inline-block;\n        }\n        \n        .simple-title {\n          font-weight: 800;\n          letter-spacing: 1px;\n          color: #1d387a;\n        }\n        \n        /* Responsive */\n        @media (max-width: 768px) {\n          .simple-title {\n            font-size: 1.5rem;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .simple-title {\n            font-size: 1.25rem;\n            text-align: center;\n            line-height: 1.2;\n          }\n        }\n\n        @media (max-width: 360px) {\n          .simple-title {\n            font-size: 1rem;\n          }\n        }\n      "},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/AnimatedTitle.jsx",lineNumber:17,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/AnimatedTitle.jsx",lineNumber:9,columnNumber:5},void 0),ma=({isOpen:e,toggleSidebar:a,onLogout:i})=>{const r=E(),[s,t]=f.useState(()=>{const e=localStorage.getItem("sidebarFavorites");return e?JSON.parse(e):{dashboard:!1,home:!1,patients:!1,tests:!1,reports:!1,administration:!1,settings:!1,help:!1}});f.useEffect(()=>{localStorage.setItem("sidebarFavorites",JSON.stringify(s))},[s]);const o=(e,a)=>{a.preventDefault(),a.stopPropagation(),t(a=>l(n({},a),{[e]:!a[e]}))},c=e=>"/"===e?"/"===r.pathname:r.pathname===e||r.pathname.startsWith(e)&&(r.pathname.length===e.length||"/"===r.pathname[e.length]),u=[{title:"Navegación Principal",items:[{name:"Inicio",path:"/home",icon:"home",key:"home"},{name:"Pacientes",path:"/admin/patients",icon:"users",key:"patients"},{name:"Cuestionario",path:"/student/questionnaire",icon:"clipboard-list",key:"tests"},{name:"Resultados",path:"/admin/reports",icon:"chart-bar",key:"reports"}]},{title:"Administración",items:[{name:"Panel Admin",path:"/admin/administration",icon:"shield-alt",key:"administration"},{name:"Configuración",path:"/configuracion",icon:"cog",key:"settings"}]},{title:"Soporte",items:[{name:"Ayuda",path:"/help",icon:"question-circle",key:"help"}]}],m=u.flatMap(e=>e.items).filter(e=>s[e.key]);return x.jsxDEV("div",{className:"sidebar bg-[#121940] text-[#a4b1cd] fixed top-0 left-0 h-full z-50 transition-all duration-300 ease-in-out\n                     "+(e?"w-64":"w-[70px]"),children:[x.jsxDEV("div",{className:"sidebar-header p-5 flex justify-between items-center border-b border-opacity-10 border-white",children:[e&&x.jsxDEV("h1",{className:"sidebar-logo text-3xl font-bold text-white text-center flex-1",children:["Activatu",x.jsxDEV("span",{className:"text-[#ffda0a]",children:"mente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:95,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:94,columnNumber:11},void 0),x.jsxDEV("button",{onClick:a,className:"collapse-button text-[#a4b1cd] cursor-pointer hover:text-white",title:e?"Contraer menú":"Expandir menú","aria-label":e?"Contraer menú":"Expandir menú",children:x.jsxDEV("i",{className:"fas "+(e?"fa-chevron-left":"fa-chevron-right")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:104,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:98,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:92,columnNumber:7},void 0),m.length>0&&x.jsxDEV("div",{className:"sidebar-section py-2 border-b border-opacity-10 border-white",children:[e&&x.jsxDEV("h2",{className:"uppercase text-xs px-5 py-2 tracking-wider font-semibold text-gray-400",children:"FAVORITOS"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:112,columnNumber:13},void 0),x.jsxDEV("ul",{className:"menu-list",children:m.map(a=>x.jsxDEV("li",{className:"py-3 px-5 hover:bg-opacity-10 hover:bg-white transition-all duration-300 relative transform hover:translate-x-1\n                          "+(c(a.path)?"bg-[#ffda0a] bg-opacity-20 border-l-4 border-[#ffda0a] shadow-lg":""),children:x.jsxDEV("div",{className:"flex items-center justify-between w-full",children:[x.jsxDEV(v,{to:a.path,className:"flex items-center flex-grow transition-colors duration-200 "+(c(a.path)?"text-[#ffda0a] font-semibold":"text-[#a4b1cd] hover:text-white"),children:[x.jsxDEV("i",{className:`fas fa-${a.icon} ${e?"mr-3":""} w-5 text-center transition-colors duration-200 ${c(a.path)?"text-[#ffda0a]":""}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:128,columnNumber:21},void 0),e&&x.jsxDEV("span",{children:a.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:129,columnNumber:32},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:124,columnNumber:19},void 0),e&&x.jsxDEV("span",{className:"text-[#ffda0a] cursor-pointer hover:scale-110 transition-transform duration-200",onClick:e=>o(a.key,e),title:"Quitar de favoritos",children:x.jsxDEV("i",{className:"fas fa-star"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:137,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:132,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:123,columnNumber:17},void 0)},`fav-${a.key}`,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:118,columnNumber:15},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:116,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:110,columnNumber:9},void 0),x.jsxDEV("div",{className:"sidebar-content py-2 flex-1 overflow-y-auto",children:u.map((a,i)=>x.jsxDEV("div",{className:"mb-4",children:[e&&x.jsxDEV("div",{className:"px-5 py-2 mb-2",children:x.jsxDEV("h3",{className:"section-title text-xs font-semibold text-gray-400 uppercase tracking-wider",children:a.title},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:154,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:153,columnNumber:15},void 0),!e&&i>0&&x.jsxDEV("div",{className:"section-separator"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:162,columnNumber:15},void 0),x.jsxDEV("ul",{className:"menu-list space-y-1",children:a.items.map(a=>x.jsxDEV("li",{className:"menu-item mx-2 rounded-lg transition-all duration-300 relative transform hover:translate-x-1\n                            "+(c(a.path)?"active bg-[#ffda0a] bg-opacity-20 border-l-4 border-[#ffda0a] shadow-lg":"hover:bg-white hover:bg-opacity-10"),children:x.jsxDEV("div",{className:"flex items-center justify-between w-full px-3 py-3",children:[x.jsxDEV(v,{to:a.path,className:"flex items-center flex-grow transition-colors duration-200 "+(c(a.path)?"text-[#ffda0a] font-semibold":"text-[#a4b1cd] hover:text-white"),children:[x.jsxDEV("i",{className:`menu-icon fas fa-${a.icon} ${e?"mr-4":"text-center"} w-5 transition-colors duration-200 ${c(a.path)?"text-[#ffda0a]":""}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:178,columnNumber:23},void 0),e&&x.jsxDEV("span",{className:"font-medium",children:a.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:179,columnNumber:34},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:174,columnNumber:21},void 0),e&&x.jsxDEV("span",{className:"favorite-star cursor-pointer hover:text-[#ffda0a] transition-all duration-200 ml-2 "+(s[a.key]?"active text-[#ffda0a]":"text-gray-400"),onClick:e=>o(a.key,e),title:s[a.key]?"Quitar de favoritos":"Añadir a favoritos",children:x.jsxDEV("i",{className:(s[a.key]?"fas":"far")+" fa-star text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:187,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:182,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:173,columnNumber:19},void 0)},a.name,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:168,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:166,columnNumber:13},void 0)]},a.title,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:150,columnNumber:11},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:148,columnNumber:7},void 0),x.jsxDEV("div",{className:"mt-auto p-5 border-t border-opacity-10 border-white",children:e?x.jsxDEV("button",{className:"logout-button flex items-center w-full text-gray-400 hover:text-red-400 cursor-pointer transition-all duration-200 hover:bg-red-500 hover:bg-opacity-10 rounded-lg p-3 border border-transparent hover:border-red-500 hover:border-opacity-30",onClick:i,"aria-label":"Cerrar sesión",children:[x.jsxDEV("i",{className:"fas fa-door-open mr-3 transition-transform duration-200"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:206,columnNumber:13},void 0),x.jsxDEV("span",{className:"font-medium",children:"Cerrar Sesión"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:207,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:201,columnNumber:11},void 0):x.jsxDEV("button",{className:"logout-button flex justify-center w-full text-gray-400 hover:text-red-400 cursor-pointer transition-all duration-200 p-3 rounded-lg border border-transparent hover:border-red-500 hover:border-opacity-30 hover:bg-red-500 hover:bg-opacity-10",onClick:i,title:"Cerrar Sesión","aria-label":"Cerrar sesión",children:x.jsxDEV("i",{className:"fas fa-door-open transition-transform duration-200"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:216,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:210,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:199,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:90,columnNumber:5},void 0)},da=()=>{const{user:e,isAdmin:a,isPsicologo:i,isCandidato:r,logout:s}=la(),[t,o]=f.useState(!0),[n,l]=f.useState(!1),c=f.useRef(null),u=h(),d=i,p=e?`${e.nombre||""} ${e.apellido||""}`.trim():"",b=null==e?void 0:e.email,N=()=>m(null,null,function*(){try{yield s(),u("/login")}catch(e){window.location.href="/login"}});return f.useEffect(()=>{const e=e=>{c.current&&!c.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),x.jsxDEV("div",{className:"min-h-screen bg-gray-50 flex",children:[x.jsxDEV(ma,{isOpen:t,toggleSidebar:()=>{o(!t)},onLogout:N},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:274,columnNumber:7},void 0),x.jsxDEV("div",{className:"flex-1 transition-all duration-300 ease-in-out\n                    "+(t?"ml-64":"ml-[70px]"),children:[x.jsxDEV("header",{className:"bg-white shadow-sm",children:x.jsxDEV("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:x.jsxDEV("div",{className:"flex justify-between h-16 items-center",children:[x.jsxDEV("div",{className:"flex items-center",children:x.jsxDEV(ua,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:284,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:283,columnNumber:15},void 0),x.jsxDEV("div",{className:"flex items-center relative",ref:c,children:[x.jsxDEV("button",{className:"flex items-center space-x-3 cursor-pointer hover:bg-gray-50 rounded-lg px-3 py-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",onClick:()=>{l(!n)},id:"user-menu-button","aria-expanded":n,"aria-haspopup":"true","aria-label":"Abrir menú de usuario",children:[x.jsxDEV("div",{className:"flex flex-col items-end",children:[x.jsxDEV("span",{className:"text-sm font-medium text-gray-800",children:p||b||"Usuario"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:298,columnNumber:21},void 0),x.jsxDEV("span",{className:"text-xs text-gray-500",children:x.jsxDEV("span",{className:"inline-flex items-center",children:[x.jsxDEV("span",{className:"w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5 animate-pulse"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:303,columnNumber:25},void 0),x.jsxDEV("span",{className:"text-green-600 font-semibold",children:"Activo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:304,columnNumber:25},void 0),x.jsxDEV("span",{className:"mx-2",children:"•"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:305,columnNumber:25},void 0),x.jsxDEV("span",{className:"text-amber-600 font-medium",children:a?"Administrador":d?"Psicólogo":"Candidato"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:306,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:302,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:301,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:297,columnNumber:19},void 0),x.jsxDEV("div",{className:"h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-md",children:x.jsxDEV("i",{className:"fas fa-user-shield"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:313,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:312,columnNumber:19},void 0),x.jsxDEV("div",{className:"text-gray-400",children:x.jsxDEV("i",{className:`fas fa-chevron-${n?"up":"down"} text-xs transition-transform duration-200 ${n?"rotate-180":""}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:316,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:315,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:289,columnNumber:17},void 0),n&&x.jsxDEV("div",{className:"absolute right-0 top-full mt-2 w-72 bg-white rounded-xl menu-shadow border border-gray-200 z-50 overflow-hidden animate-in user-menu-dropdown",role:"menu","aria-orientation":"vertical","aria-labelledby":"user-menu-button",children:[x.jsxDEV("div",{className:"px-5 py-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-blue-50",children:x.jsxDEV("div",{className:"flex items-start space-x-4",children:[x.jsxDEV("div",{className:"h-14 w-14 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-lg ring-2 ring-blue-100",children:x.jsxDEV("i",{className:"fas fa-user-shield text-lg"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:327,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:326,columnNumber:25},void 0),x.jsxDEV("div",{className:"flex-1 min-w-0",children:[x.jsxDEV("p",{className:"text-base font-semibold text-gray-900 truncate",children:p||b||"Usuario Desarrollo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:330,columnNumber:27},void 0),x.jsxDEV("p",{className:"text-sm text-gray-600 truncate mt-0.5",children:b||"<EMAIL>"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:333,columnNumber:27},void 0),x.jsxDEV("div",{className:"flex items-center mt-2 space-x-2",children:[x.jsxDEV("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800 border border-green-200",children:[x.jsxDEV("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-1.5 animate-pulse"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:338,columnNumber:31},void 0),"Activo"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:337,columnNumber:29},void 0),x.jsxDEV("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 border border-amber-200",children:a?"Administrador":d?"Psicólogo":"Candidato"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:341,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:336,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:329,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:325,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:324,columnNumber:21},void 0),x.jsxDEV("div",{className:"py-2",children:[x.jsxDEV(v,{to:"/profile",className:"flex items-center px-5 py-3 text-sm font-medium text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 focus:outline-none focus:bg-blue-50 focus:text-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-inset",onClick:()=>l(!1),role:"menuitem",tabIndex:0,"aria-label":"Ir a mi perfil",children:[x.jsxDEV("i",{className:"fas fa-user-cog mr-4 text-gray-400 w-4 text-center"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:359,columnNumber:25},void 0),x.jsxDEV("span",{children:"Mi Perfil"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:360,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:351,columnNumber:23},void 0),x.jsxDEV(v,{to:"/configuracion",className:"flex items-center px-5 py-3 text-sm font-medium text-gray-700 hover:bg-amber-50 hover:text-amber-700 transition-all duration-200 focus:outline-none focus:bg-amber-50 focus:text-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-inset",onClick:()=>l(!1),role:"menuitem",tabIndex:0,"aria-label":"Ir a configuración del sistema",children:[x.jsxDEV("i",{className:"fas fa-cog mr-4 text-gray-400 w-4 text-center"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:371,columnNumber:25},void 0),x.jsxDEV("span",{children:"Configuración"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:372,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:363,columnNumber:23},void 0),x.jsxDEV(v,{to:"/help",className:"flex items-center px-5 py-3 text-sm font-medium text-gray-700 hover:bg-green-50 hover:text-green-700 transition-all duration-200 focus:outline-none focus:bg-green-50 focus:text-green-700 focus:ring-2 focus:ring-green-500 focus:ring-inset",onClick:()=>l(!1),role:"menuitem",tabIndex:0,"aria-label":"Obtener ayuda y soporte",children:[x.jsxDEV("i",{className:"fas fa-question-circle mr-4 text-gray-400 w-4 text-center"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:383,columnNumber:25},void 0),x.jsxDEV("span",{children:"Ayuda"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:384,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:375,columnNumber:23},void 0),x.jsxDEV("div",{className:"border-t border-gray-200 my-2 mx-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:387,columnNumber:23},void 0),x.jsxDEV("button",{className:"flex w-full items-center px-5 py-3 text-sm font-medium text-red-700 hover:bg-red-50 hover:text-red-800 transition-all duration-200 focus:outline-none focus:bg-red-50 focus:text-red-800 focus:ring-2 focus:ring-red-500 focus:ring-inset group",onClick:()=>{l(!1),N()},role:"menuitem",tabIndex:0,"aria-label":"Cerrar sesión y salir del sistema",children:[x.jsxDEV("i",{className:"fas fa-door-open mr-4 text-red-500 w-4 text-center group-hover:animate-pulse"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:399,columnNumber:25},void 0),x.jsxDEV("span",{children:"Cerrar Sesión"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:400,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:389,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:350,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:322,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:288,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:282,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:281,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:280,columnNumber:9},void 0),x.jsxDEV("main",{className:"py-10",children:x.jsxDEV("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:x.jsxDEV(ca,{children:x.jsxDEV(j,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:415,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:414,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:412,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:411,columnNumber:9},void 0),x.jsxDEV("footer",{className:"bg-white border-t border-gray-200 py-8",children:x.jsxDEV("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:x.jsxDEV("p",{className:"text-center text-gray-500 text-sm",children:["© ",(new Date).getFullYear()," BAT-7 Evaluaciones. Todos los derechos reservados."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:423,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:422,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:421,columnNumber:9},void 0),x.jsxDEV(V,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!0,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:430,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:277,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:272,columnNumber:5},void 0)},pa=({fullScreen:e=!1,message:a="Cargando..."})=>{const i=e?"fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50":"flex flex-col items-center justify-center py-8";return x.jsxDEV("div",{className:i,children:x.jsxDEV("div",{className:"flex flex-col items-center",children:[x.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/Loading.jsx",lineNumber:11,columnNumber:9},void 0),a&&x.jsxDEV("p",{className:"text-gray-600",children:a},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/Loading.jsx",lineNumber:12,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/Loading.jsx",lineNumber:10,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/Loading.jsx",lineNumber:9,columnNumber:5},void 0)};class ba extends C.Component{constructor(e){super(e),u(this,"handleRetry",()=>{this.setState({hasError:!1,error:null,errorInfo:null})}),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,a){this.setState({error:e,errorInfo:a})}render(){return this.state.hasError?x.jsxDEV("div",{className:"min-h-64 flex items-center justify-center bg-red-50 rounded-lg border border-red-200 p-8",children:x.jsxDEV("div",{className:"text-center",children:[x.jsxDEV(y,{className:"w-12 h-12 text-red-500 mx-auto mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:35,columnNumber:13},this),x.jsxDEV("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:this.props.title||"Something went wrong"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:36,columnNumber:13},this),x.jsxDEV("p",{className:"text-red-600 mb-4",children:this.props.message||"An error occurred while loading this component."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:39,columnNumber:13},this),this.state.error&&x.jsxDEV("details",{className:"text-left bg-red-100 p-4 rounded mb-4",children:[x.jsxDEV("summary",{className:"cursor-pointer font-medium",children:"Error Details"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:45,columnNumber:17},this),x.jsxDEV("pre",{className:"text-sm mt-2 overflow-auto",children:[this.state.error.toString(),this.state.errorInfo.componentStack]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:46,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:44,columnNumber:15},this),x.jsxDEV("button",{onClick:this.handleRetry,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 mx-auto transition-colors",children:[x.jsxDEV(D,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:57,columnNumber:15},this),x.jsxDEV("span",{children:"Try Again"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:58,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:53,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:34,columnNumber:11},this)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:33,columnNumber:9},this):this.props.children}}const Na=({children:e})=>{const{user:a,isAuthenticated:i,isAdmin:r}=la();return i?r?e:x.jsxDEV("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:x.jsxDEV("div",{className:"max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center",children:[x.jsxDEV("div",{className:"mb-6",children:x.jsxDEV("div",{className:"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100",children:x.jsxDEV("svg",{className:"h-8 w-8 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:x.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:24,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:23,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:22,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:21,columnNumber:11},void 0),x.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Acceso Restringido"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:28,columnNumber:11},void 0),x.jsxDEV("p",{className:"text-sm text-gray-600 mb-6",children:"No tienes permisos para acceder a esta sección. Solo los administradores pueden acceder a esta área."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:31,columnNumber:11},void 0),x.jsxDEV("div",{className:"space-y-3",children:[x.jsxDEV("button",{onClick:()=>window.history.back(),className:"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:"Volver Atrás"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:35,columnNumber:13},void 0),x.jsxDEV("button",{onClick:()=>window.location.href="/home",className:"w-full bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors",children:"Ir al Inicio"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:41,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:34,columnNumber:11},void 0),x.jsxDEV("div",{className:"mt-6 pt-4 border-t border-gray-200",children:[x.jsxDEV("p",{className:"text-xs text-gray-500",children:["Usuario actual: ",x.jsxDEV("span",{className:"font-medium",children:(null==a?void 0:a.nombre)||(null==a?void 0:a.email)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:50,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:49,columnNumber:13},void 0),x.jsxDEV("p",{className:"text-xs text-gray-500",children:["Rol: ",x.jsxDEV("span",{className:"font-medium",children:(null==a?void 0:a.rol)||"No definido"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:53,columnNumber:20},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:52,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:48,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:20,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:19,columnNumber:7},void 0):x.jsxDEV(B,{to:"/login",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/auth/AdminRoute.jsx",lineNumber:13,columnNumber:12},void 0)},ga=(e,a,i={})=>{const{serialize:r=JSON.stringify,deserialize:s=JSON.parse,syncAcrossTabs:t=!0,errorOnFailure:o=!1}=i,[n,l]=f.useState(()=>{if("undefined"==typeof window)return a;try{const i=window.localStorage.getItem(e);return i?s(i):a}catch(i){if(o)throw i;return a}}),c=f.useCallback(a=>{try{const i=a instanceof Function?a(n):a;l(i),"undefined"!=typeof window&&(void 0===i?window.localStorage.removeItem(e):window.localStorage.setItem(e,r(i)))}catch(i){if(o)throw i}},[e,r,n,o]),u=f.useCallback(()=>{try{l(void 0),"undefined"!=typeof window&&window.localStorage.removeItem(e)}catch(a){if(o)throw a}},[e,o]);return f.useEffect(()=>{if(!t||"undefined"==typeof window)return;const a=a=>{if(a.key===e&&a.newValue!==r(n))try{const e=a.newValue?s(a.newValue):void 0;l(e)}catch(i){if(o)throw i}};return window.addEventListener("storage",a),()=>window.removeEventListener("storage",a)},[e,n,r,s,t,o]),[n,c,u]},fa=f.createContext(),xa=()=>{const e=f.useContext(fa);if(!e)throw new Error("usePatientSession must be used within a PatientSessionProvider");return e},ha=({children:e})=>{const[a,i]=ga("bat7_selected_patient",null),[r,s]=f.useState(!1),[t,o]=ga("bat7_selected_level","E"),[n,l]=ga("bat7_completed_tests",[]),[c,u]=ga("bat7_session_start",null);f.useEffect(()=>{a&&c&&s(!0)},[a,c]);const m={selectedPatient:a,isSessionActive:r,selectedLevel:t,completedTests:n,sessionStartTime:c,startPatientSession:(e,a="E")=>{i(e),o(a),s(!0),u((new Date).toISOString()),l([])},endPatientSession:()=>{i(null),s(!1),u(null),l([])},markTestCompleted:e=>{l(a=>a.includes(e)?a:[...a,e])},isTestCompleted:e=>n.includes(e),updateSelectedLevel:e=>{o(e)},getSessionInfo:()=>r&&a?{patient:a,level:t,startTime:c,completedTests:n,duration:c?Math.floor((new Date-new Date(c))/1e3/60):0}:null,clearSessionData:()=>{i(null),s(!1),u(null),l([]),o("E")},hasActiveSession:r&&a,sessionDuration:c?Math.floor((new Date-new Date(c))/1e3/60):0};return x.jsxDEV(fa.Provider,{value:m,children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/context/PatientSessionContext.jsx",lineNumber:128,columnNumber:5},void 0)},va=e=>{var a=e,{children:i,className:r=""}=a,s=c(a,["children","className"]);return x.jsxDEV("div",l(n({className:`bg-white rounded-lg shadow-sm border border-gray-200 ${r}`},s),{children:i}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Card.jsx",lineNumber:5,columnNumber:5},void 0)},ja=e=>{var a=e,{children:i,className:r=""}=a,s=c(a,["children","className"]);return x.jsxDEV("div",l(n({className:`px-6 py-4 border-b border-gray-200 ${r}`},s),{children:i}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Card.jsx",lineNumber:16,columnNumber:5},void 0)},Va=e=>{var a=e,{children:i,className:r=""}=a,s=c(a,["children","className"]);return x.jsxDEV("div",l(n({className:`px-6 py-4 ${r}`},s),{children:i}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Card.jsx",lineNumber:27,columnNumber:5},void 0)},Ea=e=>{var a=e,{children:i,className:r=""}=a,s=c(a,["children","className"]);return x.jsxDEV("div",l(n({className:`px-6 py-4 border-t border-gray-200 ${r}`},s),{children:i}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Card.jsx",lineNumber:38,columnNumber:5},void 0)},Ca=e=>{var a=e,{children:i,variant:r="primary",size:s="md",className:t="",disabled:o=!1,as:u="button",to:m}=a,d=c(a,["children","variant","size","className","disabled","as","to"]);const p=`inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm",secondary:"bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-400 shadow-sm",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-400 shadow-sm",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm"}[r]} ${{sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-6 py-3"}[s]} ${o?"opacity-50 cursor-not-allowed":"cursor-pointer"} ${t}`;if(u===v||"Link"===u&&m)return x.jsxDEV(v,l(n({to:m,className:p},d),{children:i}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Button.jsx",lineNumber:37,columnNumber:7},void 0);if("function"==typeof u){const e=u;return x.jsxDEV(e,l(n({className:p},d),{children:i}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Button.jsx",lineNumber:51,columnNumber:7},void 0)}return x.jsxDEV("button",l(n({className:p,disabled:o},d),{children:i}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Button.jsx",lineNumber:62,columnNumber:5},void 0)},ya={verbal:{id:"verbal",name:"Test de Aptitud Verbal",type:"verbal",description:"Test V - Evaluación de analogías verbales. Este test evalúa la capacidad para identificar relaciones entre palabras y conceptos, midiendo el razonamiento verbal y la comprensión de relaciones lógicas.",duration:12,numberOfQuestions:32,instructions:["Lee cada pregunta detenidamente antes de responder.","En cada ejercicio, debes encontrar la palabra que completa la frase dotándola de sentido.","Para las analogías verbales, identifica la relación exacta entre el primer par de palabras.","Entre las cuatro opciones, solo UNA es la correcta.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas.","Trabaja rápidamente, ya que el tiempo es limitado.","Si no estás completamente seguro de una respuesta, elige la opción que creas más correcta; no se penalizan los errores."],additionalInfo:"Este test evalúa tu capacidad para comprender relaciones entre conceptos expresados a través de palabras. Implica el dominio del lenguaje y la habilidad para entender relaciones lógicas entre conceptos verbales.",components:[{name:"Analogías Verbales",description:"Mide tu capacidad para identificar relaciones entre conceptos"},{name:"Razonamiento Verbal",description:"Evalúa tu habilidad para entender relaciones lógicas"},{name:"Comprensión Lingüística",description:"Mide tu dominio del lenguaje y vocabulario"},{name:"Pensamiento Abstracto",description:"Evalúa tu capacidad para identificar patrones conceptuales"}],recommendations:["Fíjate bien en la relación entre el primer par de palabras para identificar el patrón que debes aplicar.","Si no encuentras la respuesta inmediatamente, analiza cada opción eliminando las que claramente no cumplen con la relación buscada.","Recuerda que las relaciones pueden ser de diversos tipos: causa-efecto, parte-todo, función, oposición, etc.","Si terminas antes del tiempo concedido, aprovecha para revisar tus respuestas."]},ortografia:{id:"ortografia",name:"Test de Ortografía",type:"ortografia",description:"Test O - Evaluación de la capacidad para identificar errores ortográficos en palabras.",duration:10,numberOfQuestions:32,instructions:["En cada grupo de cuatro palabras, identificar la única palabra que está mal escrita (intencionadamente).","La falta de ortografía puede ser de cualquier tipo, incluyendo errores en letras o la ausencia/presencia incorrecta de una tilde.","Marcar la letra correspondiente (A, B, C o D) a la palabra mal escrita.","Trabajar rápidamente. Si no se está seguro, elegir la opción que parezca más correcta (no se penaliza el error).","Si se termina antes, repasar las respuestas."],additionalInfo:"Este test evalúa tu dominio de las reglas ortográficas del español, incluyendo acentuación, uso de letras específicas y formación de palabras.",components:[{name:"Ortografía General",description:"Mide tu conocimiento de las reglas básicas de escritura"},{name:"Acentuación",description:"Evalúa tu dominio de las reglas de acentuación"},{name:"Uso de Letras",description:"Mide tu conocimiento del uso correcto de letras que pueden confundirse"},{name:"Atención al Detalle",description:"Evalúa tu capacidad para detectar errores sutiles"}],recommendations:["Revisa visualmente cada palabra con atención.","Recuerda las reglas de acentuación de palabras agudas, llanas y esdrújulas.","Presta especial atención a las letras que suelen causar confusión: b/v, g/j, h, etc.","Observa la presencia o ausencia de tildes en las palabras."],examples:[{question:"A. año, B. berso, C. vuelo, D. campana",answer:"B",explanation:'La grafía correcta es "verso".'},{question:"A. bosque, B. armario, C. telon, D. libro",answer:"C",explanation:'La palabra correcta es "telón", lleva tilde en la "o".'}]},razonamiento:{id:"razonamiento",name:"Test de Razonamiento",type:"razonamiento",description:"Test R - Evaluación de la capacidad para identificar patrones y continuar series lógicas de figuras.",duration:20,numberOfQuestions:32,instructions:["Observar una serie de figuras y determinar qué figura (A, B, C o D) debería ir a continuación, sustituyendo al interrogante, siguiendo la lógica de la serie.","Analiza cuidadosamente cómo evolucionan las figuras en cada serie.","Busca patrones como rotaciones, traslaciones, adiciones o sustracciones de elementos.","Entre las cuatro opciones, solo UNA es la correcta.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas.","Trabaja metódicamente, ya que algunas secuencias pueden tener patrones complejos.","Si no estás completamente seguro de una respuesta, intenta descartar opciones que claramente no siguen el patrón."],additionalInfo:"Este test evalúa tu capacidad para identificar patrones lógicos y aplicarlos para predecir el siguiente elemento en una secuencia. Es una medida del razonamiento inductivo y del pensamiento lógico-abstracto.",components:[{name:"Razonamiento Inductivo",description:"Mide tu capacidad para identificar reglas a partir de ejemplos"},{name:"Pensamiento Lógico",description:"Evalúa tu habilidad para aplicar reglas sistemáticamente"},{name:"Visualización Espacial",description:"Mide tu capacidad para manipular imágenes mentalmente"},{name:"Atención al Detalle",description:"Evalúa tu capacidad para detectar patrones sutiles"}],recommendations:["Intenta identificar más de un patrón en cada serie (puede haber cambios en color, forma, tamaño y posición).","Observa si hay ciclos repetitivos en los patrones.","Analiza cada elemento individualmente si la figura es compleja.","Si encuentras dificultades, intenta verbalizar el patrón para hacerlo más claro."]},atencion:{id:"atencion",name:"Test de Atención",type:"atencion",description:"Test A - Evaluación de la rapidez y precisión en la localización de símbolos.",duration:8,numberOfQuestions:80,instructions:["En cada ejercicio aparece una fila con diferentes símbolos y tu tarea consistirá en localizar cuántas veces aparece uno determinado.","El símbolo que tienes que localizar es siempre el mismo y se presenta en la parte superior de la página.","El símbolo puede aparecer 0, 1, 2 o 3 veces en cada fila, pero nunca más de 3.","Deberás marcar cuántas veces aparece el símbolo en cada fila (0, 1, 2 o 3).","Trabaja con rapidez y precisión, asegurándote de que tu respuesta se corresponda con el número del ejercicio que estás contestando.","Avanza sistemáticamente por cada fila, de izquierda a derecha.","Presta especial atención a símbolos muy similares al modelo pero que no son idénticos."],additionalInfo:"Esta prueba trata de evaluar tu rapidez y tu precisión trabajando con símbolos. Es una medida de la atención selectiva y sostenida, así como de la velocidad y precisión en el procesamiento de información visual.",components:[{name:"Atención Selectiva",description:"Mide tu capacidad para enfocarte en elementos específicos"},{name:"Velocidad Perceptiva",description:"Evalúa tu rapidez para procesar información visual"},{name:"Discriminación Visual",description:"Mide tu habilidad para distinguir detalles visuales"},{name:"Concentración",description:"Evalúa tu capacidad para mantener el foco durante una tarea repetitiva"}],recommendations:["Mantén un ritmo constante, sin detenerte demasiado en ningún elemento.","Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta; no se penalizará el error.","Si terminas antes del tiempo concedido, repasa tus respuestas.","Utiliza el dedo o un marcador para seguir las filas si te ayuda a mantener el enfoque.","Evita distracciones y mantén la concentración en la tarea."]},espacial:{id:"espacial",name:"Test de Aptitud Espacial",type:"espacial",description:"Test E - Evaluación del razonamiento espacial con cubos y redes.",duration:15,numberOfQuestions:28,instructions:["En cada ejercicio encontrarás un cubo junto con su modelo desplegado, al que se le han borrado casi todos los números y letras.","Tu tarea consistirá en averiguar qué número o letra debería aparecer en lugar del interrogante (?) y en qué orientación.","En el cubo se han representado en color gris los números o letras que se encuentran en las caras de atrás (las que no se ven directamente).","Observa cuidadosamente la orientación y posición relativa de las caras en el cubo.","Considera cómo las distintas caras del cubo se conectan entre sí en el modelo desplegado.","Visualiza mentalmente el proceso de plegado del modelo para formar el cubo.","Entre las cuatro opciones, solo UNA es la correcta.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas."],additionalInfo:"Este test evalúa tu capacidad para manipular objetos mentalmente en el espacio tridimensional. Es una medida de la visualización espacial, rotación mental y comprensión de relaciones espaciales.",components:[{name:"Visualización Espacial",description:"Mide tu capacidad para manipular objetos en 3D mentalmente"},{name:"Rotación Mental",description:"Evalúa tu habilidad para rotar figuras en tu mente"},{name:"Relaciones Espaciales",description:"Mide tu comprensión de cómo se conectan las partes de un objeto"},{name:"Razonamiento Geométrico",description:"Evalúa tu entendimiento de principios geométricos básicos"}],recommendations:["Utiliza marcas mentales para orientarte en la ubicación de cada cara del cubo.","Fíjate en detalles específicos de los diseños en cada cara para determinar su orientación correcta.","Si es necesario, utiliza tus manos para ayudarte a visualizar el plegado del modelo.","Si en algún ejercicio no estás completamente seguro de cuál puede ser la respuesta, elige la opción que creas que es más correcta; no se penalizará el error.","Si terminas antes del tiempo concedido, repasa tus respuestas."]},mecanico:{id:"mecanico",name:"Test de Razonamiento Mecánico",type:"mecanico",description:"Test M - Evaluación de la comprensión de principios físicos y mecánicos básicos.",duration:12,numberOfQuestions:28,instructions:["Observar dibujos que representan diversas situaciones físicas o mecánicas y responder a una pregunta sobre cada situación, eligiendo la opción más adecuada.","Analiza los elementos del dibujo y cómo interactúan entre sí.","Aplica principios básicos de física y mecánica como palancas, poleas, engranajes, fuerzas, etc.","Ten en cuenta la dirección de las fuerzas, el movimiento o el equilibrio en cada situación.","Entre las opciones presentadas, selecciona la que mejor explica el fenómeno o predice el resultado.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas.","Si no estás seguro, intenta aplicar el sentido común y los principios básicos que conozcas."],additionalInfo:"Este test evalúa tu comprensión intuitiva de principios físicos y mecánicos, así como tu capacidad para aplicar estos principios a situaciones prácticas. No requiere conocimientos técnicos avanzados, sino una comprensión básica de cómo funcionan los objetos en el mundo físico.",components:[{name:"Comprensión Física",description:"Mide tu entendimiento de principios físicos básicos"},{name:"Razonamiento Mecánico",description:"Evalúa tu capacidad para entender sistemas mecánicos"},{name:"Resolución de Problemas",description:"Mide tu habilidad para aplicar principios a situaciones nuevas"},{name:"Intuición Tecnológica",description:"Evalúa tu comprensión natural de cómo funcionan las máquinas"}],recommendations:["Recuerda principios básicos como la ley de la palanca, la transmisión de fuerzas en poleas y engranajes.","Considera factores como la gravedad, la fricción y la inercia cuando analices cada situación.","Visualiza el movimiento o la acción que ocurriría en la situación presentada.","Si tienes dificultades, intenta simplificar el problema a sus componentes más básicos."]},numerico:{id:"numerico",name:"Test de Aptitud Numérica",type:"numerico",description:"Test N - Resolución de problemas numéricos, series y tablas.",duration:20,numberOfQuestions:32,instructions:["En esta prueba encontrarás distintos ejercicios numéricos que tendrás que resolver.","Para ello tendrás que analizar la información que se presenta y determinar qué debe aparecer en lugar del interrogante.","Cuando lo hayas decidido, deberás marcar la letra de la opción correspondiente en la hoja de respuestas.","Asegúrate de que coincida con el ejercicio que estás contestando.","","El tiempo máximo para su realización es de 20 minutos, por lo que deberás trabajar rápidamente.","Esfuérzate al máximo en encontrar la respuesta correcta.","Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta.","No se penalizará el error.","Si terminas antes del tiempo concedido, repasa tus respuestas, pero NO continúes con las demás pruebas."],additionalInfo:"Este test evalúa tu capacidad para resolver problemas numéricos mediante el análisis de igualdades, series y tablas. Mide el razonamiento matemático, la identificación de patrones numéricos y la habilidad para trabajar con datos organizados.",components:[{name:"Igualdades Numéricas",description:"Resolver ecuaciones con elementos faltantes"},{name:"Series Numéricas",description:"Identificar patrones y continuar secuencias"},{name:"Tablas de Datos",description:"Analizar información organizada y encontrar valores faltantes"},{name:"Cálculo Mental",description:"Realizar operaciones matemáticas con rapidez y precisión"}],recommendations:["Lee cuidadosamente cada problema antes de intentar resolverlo.","En las igualdades, calcula primero el lado conocido de la ecuación.","En las series, busca patrones simples antes de considerar reglas más complejas.","En las tablas, analiza las relaciones entre filas y columnas.","Verifica tus cálculos cuando sea posible.","Si no estás seguro, elige la opción que te parezca más lógica."]},bat7:{id:"bat7",name:"Batería Completa BAT-7",type:"battery",description:"Evaluación completa de aptitudes y habilidades cognitivas.",duration:120,numberOfQuestions:184,instructions:["Lee atentamente cada pregunta antes de responder.","Responde a todas las preguntas, aunque no estés seguro/a de la respuesta.","Administra bien tu tiempo. Si una pregunta te resulta difícil, pasa a la siguiente y vuelve a ella más tarde.","No uses calculadora ni ningún otro dispositivo o material durante el test.","Una vez iniciado el test, no podrás pausarlo. Asegúrate de disponer del tiempo necesario para completarlo.","Responde con honestidad. Este test está diseñado para evaluar tus habilidades actuales.","Cada subtest tiene instrucciones específicas que deberás leer antes de comenzar esa sección."],additionalInfo:"La batería BAT-7 está compuesta por siete pruebas independientes que evalúan diferentes aptitudes: verbal, espacial, numérica, mecánica, razonamiento, atención y ortografía. Cada prueba tiene un tiempo específico de realización y unas instrucciones particulares.",subtests:[{id:"verbal",name:"Test de Aptitud Verbal (V)",duration:12,questions:32,description:"Evaluación de analogías verbales y comprensión de relaciones entre conceptos."},{id:"ortografia",name:"Test de Ortografía (O)",duration:10,questions:32,description:"Identificación de palabras con errores ortográficos."},{id:"razonamiento",name:"Test de Razonamiento (R)",duration:20,questions:32,description:"Continuación de series lógicas de figuras."},{id:"atencion",name:"Test de Atención (A)",duration:8,questions:80,description:"Rapidez y precisión en la localización de símbolos."},{id:"espacial",name:"Test de Visualización Espacial (E)",duration:15,questions:28,description:"Razonamiento espacial con cubos y redes."},{id:"mecanico",name:"Test de Razonamiento Mecánico (M)",duration:12,questions:28,description:"Comprensión de principios físicos y mecánicos básicos."},{id:"numerico",name:"Test de Razonamiento Numérico (N)",duration:20,questions:32,description:"Resolución de problemas numéricos, series y tablas."}],recommendations:["Descansa adecuadamente antes de realizar la batería completa.","Realiza los tests en un ambiente tranquilo y sin distracciones.","Gestiona tu energía a lo largo de toda la batería, ya que algunos tests son más exigentes que otros.","Mantén una actitud positiva y confía en tus capacidades."]}},Da={bat7:{icon:"fas fa-clipboard-list",color:"text-purple-600"},verbal:{icon:"fas fa-comments",color:"text-blue-600"},espacial:{icon:"fas fa-cube",color:"text-indigo-600"},atencion:{icon:"fas fa-eye",color:"text-red-600"},razonamiento:{icon:"fas fa-puzzle-piece",color:"text-amber-600"},numerico:{icon:"fas fa-calculator",color:"text-teal-600"},mecanico:{icon:"fas fa-cogs",color:"text-slate-600"},ortografia:{icon:"fas fa-spell-check",color:"text-green-600"}},Ba=()=>{var e;const{testId:a}=w(),i=h(),r=E(),[s,t]=f.useState(null),[o,n]=f.useState(!0),[l,c]=f.useState(!1),u=null==(e=r.state)?void 0:e.patientId;f.useEffect(()=>{m(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800)),ya[a]?t(ya[a]):d.warning(`No se encontraron instrucciones para el test: ${a}`),n(!1)}catch(e){d.error("Error al cargar la información del test"),n(!1)}})},[a]);const p=Da[a]||{icon:"fas fa-clipboard-list",color:"text-gray-600"};return x.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[x.jsxDEV("div",{className:"mb-6 text-center",children:[x.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[x.jsxDEV("i",{className:`${p.icon} ${p.color} mr-2`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:84,columnNumber:11},void 0),"Instrucciones del Test"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:83,columnNumber:9},void 0),!o&&s&&x.jsxDEV("p",{className:"text-gray-600",children:s.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:88,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:82,columnNumber:7},void 0),o?x.jsxDEV("div",{className:"py-16 text-center",children:x.jsxDEV("div",{className:"flex flex-col items-center justify-center",children:[x.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:95,columnNumber:13},void 0),x.jsxDEV("p",{className:"text-gray-500",children:"Cargando instrucciones del test..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:96,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:94,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:93,columnNumber:9},void 0):s?x.jsxDEV(x.Fragment,{children:[x.jsxDEV(va,{className:"mb-6",children:[x.jsxDEV(ja,{className:"text-center",children:x.jsxDEV("h2",{className:"text-lg font-medium",children:"Información General"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:103,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:102,columnNumber:13},void 0),x.jsxDEV(Va,{children:[x.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-md font-medium mb-2 text-center",children:"Descripción"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:108,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-gray-700",children:s.description},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:109,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:107,columnNumber:17},void 0),x.jsxDEV("div",{className:"grid grid-cols-2 gap-4",children:[x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-md font-medium mb-2 text-center",children:"Duración"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:113,columnNumber:21},void 0),x.jsxDEV("p",{className:"text-gray-700 text-center",children:[s.duration," minutos"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:114,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:112,columnNumber:19},void 0),x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-md font-medium mb-2 text-center",children:"Preguntas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:117,columnNumber:21},void 0),x.jsxDEV("p",{className:"text-gray-700 text-center",children:[s.numberOfQuestions," preguntas"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:118,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:116,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:111,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:106,columnNumber:15},void 0),s.additionalInfo&&x.jsxDEV("div",{className:"bg-blue-50 border-l-4 border-blue-500 p-4 mb-4",children:x.jsxDEV("p",{className:"text-blue-700",children:s.additionalInfo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:125,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:124,columnNumber:17},void 0),"battery"!==s.type&&s.components&&x.jsxDEV("div",{className:"mt-6",children:[x.jsxDEV("h3",{className:"text-md font-medium mb-3 text-center",children:"Componentes Evaluados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:132,columnNumber:19},void 0),x.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:s.components.map((e,a)=>x.jsxDEV("div",{className:"border rounded p-3",children:[x.jsxDEV("p",{className:"font-medium",children:e.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:136,columnNumber:25},void 0),x.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:e.description},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:137,columnNumber:25},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:135,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:133,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:131,columnNumber:17},void 0),"battery"===s.type&&s.subtests&&s.subtests.length>0&&x.jsxDEV("div",{className:"mt-6",children:[x.jsxDEV("h3",{className:"text-md font-medium mb-3 text-center",children:"Subtests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:147,columnNumber:19},void 0),x.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:s.subtests.map((e,a)=>x.jsxDEV("div",{className:"border rounded p-3",children:[x.jsxDEV("p",{className:"font-medium",children:[a+1,". ",e.name]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:151,columnNumber:25},void 0),x.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:e.description},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:152,columnNumber:25},void 0),x.jsxDEV("div",{className:"flex justify-between mt-2 text-sm text-gray-600",children:[x.jsxDEV("span",{children:[e.duration," min"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:154,columnNumber:27},void 0),x.jsxDEV("span",{children:[e.questions," preguntas"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:155,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:153,columnNumber:25},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:150,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:148,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:146,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:105,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:101,columnNumber:11},void 0),x.jsxDEV(va,{className:"mb-6",children:[x.jsxDEV(ja,{className:"text-center",children:x.jsxDEV("h2",{className:"text-lg font-medium",children:"Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:167,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:166,columnNumber:13},void 0),x.jsxDEV(Va,{children:[x.jsxDEV("div",{className:"space-y-3",children:s.instructions.map((e,a)=>{if(""===e)return x.jsxDEV("div",{className:"h-4"},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:174,columnNumber:28},void 0);if(e.startsWith("**")&&e.endsWith("**"))return x.jsxDEV("h4",{className:"text-lg font-semibold text-gray-800 mt-6 mb-3 border-b border-gray-200 pb-2",children:e.replace(/\*\*/g,"")},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:180,columnNumber:23},void 0);const i=s.instructions.slice(0,a+1).filter(e=>""!==e&&!e.startsWith("**")).length;return x.jsxDEV("div",{className:"flex items-start",children:[x.jsxDEV("div",{className:"flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-blue-100 text-blue-600 mr-3 mt-0.5",children:i},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:193,columnNumber:23},void 0),x.jsxDEV("p",{className:"text-gray-700",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:196,columnNumber:23},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:192,columnNumber:21},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:170,columnNumber:15},void 0),s.recommendations&&x.jsxDEV("div",{className:"mt-6 bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[x.jsxDEV("h3",{className:"text-md font-medium text-yellow-800 mb-2",children:"Recomendaciones Adicionales"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:205,columnNumber:19},void 0),x.jsxDEV("ul",{className:"space-y-2 text-yellow-700",children:s.recommendations.map((e,a)=>x.jsxDEV("li",{children:["• ",e]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:208,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:206,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:204,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:169,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:165,columnNumber:11},void 0),x.jsxDEV(va,{children:[x.jsxDEV(Va,{children:x.jsxDEV("div",{className:"flex items-start mb-4",children:[x.jsxDEV("input",{type:"checkbox",id:"accept-conditions",checked:l,onChange:e=>c(e.target.checked),className:"h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500 mt-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:219,columnNumber:17},void 0),x.jsxDEV("label",{htmlFor:"accept-conditions",className:"ml-3 text-gray-700",children:"He leído y acepto las instrucciones. Entiendo que una vez iniciado el test no podré pausarlo y deberé completarlo en su totalidad."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:226,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:218,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:217,columnNumber:13},void 0),x.jsxDEV(Ea,{className:"flex justify-end",children:x.jsxDEV(Ca,{variant:l?"primary":"outline",onClick:()=>{l?(d.info("Iniciando test..."),"battery"===s.type?s.subtests&&s.subtests.length>0?i(`/test/${s.subtests[0].id}`,{state:{patientId:u}}):i("/student/tests"):i(`/test/${s.id}`,{state:{patientId:u}})):d.warning("Debes aceptar las condiciones para continuar")},disabled:!l,children:"Iniciar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:232,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:231,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:216,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:100,columnNumber:9},void 0):x.jsxDEV(va,{children:x.jsxDEV(Va,{children:x.jsxDEV("div",{className:"py-8 text-center",children:x.jsxDEV("p",{className:"text-gray-500",children:"No se encontró información para el test solicitado."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:246,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:245,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:244,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:243,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:81,columnNumber:5},void 0)},wa=Object.freeze(Object.defineProperty({__proto__:null,default:Ba},Symbol.toStringTag,{value:"Module"})),Aa=A("https://ydglduxhgwajqdseqzpy.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c",{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}}),Ra={"12-13":{V:[{pc:99,pd:[30,32]},{pc:97,pd:[29,29]},{pc:95,pd:[28,28]},{pc:90,pd:[27,27]},{pc:85,pd:[26,26]},{pc:80,pd:[25,25]},{pc:70,pd:[24,24]},{pc:65,pd:[23,23]},{pc:55,pd:[22,22]},{pc:50,pd:[21,21]},{pc:40,pd:[20,20]},{pc:35,pd:[19,19]},{pc:25,pd:[18,18]},{pc:20,pd:[17,17]},{pc:15,pd:[16,16]},{pc:10,pd:[15,15]},{pc:5,pd:[13,14]},{pc:4,pd:[12,12]},{pc:2,pd:[11,11]},{pc:1,pd:[0,10]}],E:[{pc:99,pd:[27,28]},{pc:96,pd:[26,26]},{pc:95,pd:[25,25]},{pc:90,pd:[24,24]},{pc:85,pd:[23,23]},{pc:80,pd:[22,22]},{pc:75,pd:[21,21]},{pc:70,pd:[20,20]},{pc:60,pd:[19,19]},{pc:45,pd:[18,18]},{pc:55,pd:[18,18]},{pc:50,pd:[17,17]},{pc:40,pd:[16,16]},{pc:35,pd:[15,15]},{pc:25,pd:[14,14]},{pc:20,pd:[13,13]},{pc:15,pd:[12,12]},{pc:10,pd:[11,11]},{pc:5,pd:[9,10]},{pc:4,pd:[8,8]},{pc:3,pd:[7,7]},{pc:2,pd:[6,6]},{pc:1,pd:[0,5]}],A:[{pc:99,pd:[49,80]},{pc:98,pd:[48,48]},{pc:97,pd:[46,47]},{pc:96,pd:[44,45]},{pc:95,pd:[43,43]},{pc:90,pd:[39,42]},{pc:85,pd:[36,38]},{pc:80,pd:[35,35]},{pc:75,pd:[34,34]},{pc:70,pd:[33,33]},{pc:65,pd:[31,32]},{pc:60,pd:[29,30]},{pc:55,pd:[28,28]},{pc:50,pd:[27,27]},{pc:45,pd:[26,26]},{pc:40,pd:[25,25]},{pc:35,pd:[24,24]},{pc:30,pd:[23,23]},{pc:25,pd:[22,22]},{pc:20,pd:[21,21]},{pc:15,pd:[19,20]},{pc:10,pd:[17,18]},{pc:5,pd:[15,16]},{pc:4,pd:[13,14]},{pc:2,pd:[12,12]},{pc:1,pd:[0,11]}],CON:[{pc:99,pd:[98,100]},{pc:97,pd:[96,97]},{pc:96,pd:[95,95]},{pc:95,pd:[94,94]},{pc:90,pd:[91,93]},{pc:85,pd:[89,90]},{pc:80,pd:[88,88]},{pc:75,pd:[85,87]},{pc:70,pd:[83,84]},{pc:65,pd:[82,82]},{pc:60,pd:[80,81]},{pc:55,pd:[78,79]},{pc:50,pd:[76,77]},{pc:45,pd:[74,75]},{pc:40,pd:[72,73]},{pc:35,pd:[69,71]},{pc:30,pd:[67,68]},{pc:25,pd:[64,66]},{pc:20,pd:[61,63]},{pc:15,pd:[56,60]},{pc:10,pd:[47,55]},{pc:5,pd:[36,46]},{pc:4,pd:[33,35]},{pc:3,pd:[29,32]},{pc:2,pd:[28,28]},{pc:1,pd:[0,27]}],R:[{pc:99,pd:[29,32]},{pc:98,pd:[28,28]},{pc:96,pd:[27,27]},{pc:95,pd:[26,26]},{pc:90,pd:[25,25]},{pc:85,pd:[24,24]},{pc:80,pd:[23,23]},{pc:70,pd:[22,22]},{pc:65,pd:[21,21]},{pc:60,pd:[20,20]},{pc:50,pd:[19,19]},{pc:45,pd:[18,18]},{pc:40,pd:[17,17]},{pc:30,pd:[16,16]},{pc:25,pd:[15,15]},{pc:20,pd:[14,14]},{pc:15,pd:[13,13]},{pc:10,pd:[11,12]},{pc:5,pd:[8,10]},{pc:3,pd:[7,7]},{pc:2,pd:[6,6]},{pc:1,pd:[0,5]}],N:[{pc:99,pd:[28,32]},{pc:98,pd:[27,27]},{pc:97,pd:[26,26]},{pc:96,pd:[25,25]},{pc:95,pd:[24,24]},{pc:90,pd:[22,23]},{pc:85,pd:[22,22]},{pc:85,pd:[20,21]},{pc:80,pd:[19,19]},{pc:75,pd:[18,18]},{pc:70,pd:[17,17]},{pc:65,pd:[16,16]},{pc:60,pd:[15,15]},{pc:55,pd:[14,14]},{pc:50,pd:[13,13]},{pc:45,pd:[12,12]},{pc:40,pd:[11,11]},{pc:35,pd:[10,10]},{pc:25,pd:[9,9]},{pc:20,pd:[8,8]},{pc:15,pd:[7,7]},{pc:10,pd:[6,6]},{pc:5,pd:[5,5]},{pc:3,pd:[4,4]},{pc:1,pd:[0,3]}],M:[{pc:99,pd:[25,28]},{pc:96,pd:[24,24]},{pc:95,pd:[23,23]},{pc:90,pd:[22,22]},{pc:85,pd:[21,21]},{pc:80,pd:[20,20]},{pc:70,pd:[19,19]},{pc:60,pd:[18,18]},{pc:50,pd:[17,17]},{pc:45,pd:[16,16]},{pc:35,pd:[15,15]},{pc:30,pd:[14,14]},{pc:20,pd:[13,13]},{pc:15,pd:[12,12]},{pc:10,pd:[11,11]},{pc:5,pd:[10,10]},{pc:4,pd:[9,9]},{pc:3,pd:[8,8]},{pc:1,pd:[0,7]}],O:[{pc:99,pd:[31,32]},{pc:98,pd:[30,30]},{pc:95,pd:[29,29]},{pc:90,pd:[27,28]},{pc:85,pd:[26,26]},{pc:80,pd:[25,25]},{pc:70,pd:[24,24]},{pc:65,pd:[23,23]},{pc:60,pd:[22,22]},{pc:55,pd:[21,21]},{pc:50,pd:[20,20]},{pc:45,pd:[19,19]},{pc:40,pd:[18,18]},{pc:35,pd:[17,17]},{pc:30,pd:[16,16]},{pc:25,pd:[15,15]},{pc:20,pd:[14,14]},{pc:15,pd:[13,13]},{pc:10,pd:[11,12]},{pc:5,pd:[9,10]},{pc:4,pd:[8,8]},{pc:3,pd:[7,7]},{pc:2,pd:[6,6]},{pc:1,pd:[0,5]}]},"13-14":{V:[{pc:99,pd:[31,32]},{pc:98,pd:[30,30]},{pc:95,pd:[29,29]},{pc:90,pd:[28,28]},{pc:85,pd:[27,27]},{pc:75,pd:[26,26]},{pc:65,pd:[25,25]},{pc:60,pd:[24,24]},{pc:50,pd:[23,23]},{pc:45,pd:[22,22]},{pc:35,pd:[21,21]},{pc:30,pd:[20,20]},{pc:25,pd:[19,19]},{pc:20,pd:[18,18]},{pc:15,pd:[16,17]},{pc:10,pd:[15,15]},{pc:5,pd:[13,14]},{pc:4,pd:[12,12]},{pc:2,pd:[11,11]},{pc:1,pd:[0,10]}],E:[{pc:99,pd:[28,28]},{pc:97,pd:[27,27]},{pc:95,pd:[26,26]},{pc:90,pd:[25,25]},{pc:85,pd:[24,24]},{pc:80,pd:[23,23]},{pc:75,pd:[22,22]},{pc:65,pd:[21,21]},{pc:60,pd:[20,20]},{pc:50,pd:[19,19]},{pc:45,pd:[18,18]},{pc:40,pd:[17,17]},{pc:35,pd:[16,16]},{pc:25,pd:[15,15]},{pc:20,pd:[14,14]},{pc:15,pd:[13,13]},{pc:10,pd:[12,12]},{pc:5,pd:[10,11]},{pc:4,pd:[9,9]},{pc:2,pd:[8,8]},{pc:1,pd:[0,7]}],A:[{pc:99,pd:[60,80]},{pc:98,pd:[55,59]},{pc:97,pd:[51,54]},{pc:96,pd:[49,50]},{pc:95,pd:[48,48]},{pc:90,pd:[42,47]},{pc:85,pd:[39,41]},{pc:80,pd:[37,38]},{pc:75,pd:[35,36]},{pc:70,pd:[34,34]},{pc:65,pd:[33,33]},{pc:60,pd:[31,32]},{pc:55,pd:[30,30]},{pc:50,pd:[29,29]},{pc:45,pd:[28,28]},{pc:40,pd:[26,27]},{pc:35,pd:[25,25]},{pc:30,pd:[24,24]},{pc:25,pd:[23,23]},{pc:20,pd:[22,22]},{pc:15,pd:[20,21]},{pc:10,pd:[18,19]},{pc:5,pd:[14,17]},{pc:3,pd:[13,13]},{pc:2,pd:[10,12]},{pc:1,pd:[0,9]}],CON:[{pc:99,pd:[100,100]},{pc:98,pd:[97,99]},{pc:97,pd:[96,96]},{pc:96,pd:[95,95]},{pc:95,pd:[94,94]},{pc:90,pd:[91,93]},{pc:85,pd:[89,90]},{pc:80,pd:[87,88]},{pc:75,pd:[85,86]},{pc:70,pd:[83,84]},{pc:65,pd:[82,82]},{pc:60,pd:[80,81]},{pc:55,pd:[78,79]},{pc:50,pd:[76,77]},{pc:45,pd:[75,75]},{pc:40,pd:[72,74]},{pc:35,pd:[70,71]},{pc:30,pd:[68,69]},{pc:25,pd:[66,67]},{pc:20,pd:[61,65]},{pc:15,pd:[57,60]},{pc:10,pd:[49,56]},{pc:5,pd:[37,48]},{pc:4,pd:[35,36]},{pc:3,pd:[31,34]},{pc:2,pd:[29,30]},{pc:1,pd:[0,28]}],R:[{pc:99,pd:[30,32]},{pc:98,pd:[29,29]},{pc:95,pd:[28,28]},{pc:90,pd:[26,27]},{pc:85,pd:[25,25]},{pc:80,pd:[24,24]},{pc:70,pd:[23,23]},{pc:65,pd:[22,22]},{pc:55,pd:[21,21]},{pc:50,pd:[20,20]},{pc:45,pd:[19,19]},{pc:40,pd:[18,18]},{pc:30,pd:[17,17]},{pc:25,pd:[16,16]},{pc:20,pd:[15,15]},{pc:15,pd:[13,14]},{pc:10,pd:[11,12]},{pc:5,pd:[9,10]},{pc:3,pd:[8,8]},{pc:2,pd:[7,7]},{pc:1,pd:[0,6]}],N:[{pc:99,pd:[29,32]},{pc:98,pd:[28,28]},{pc:97,pd:[27,27]},{pc:96,pd:[26,26]},{pc:95,pd:[25,25]},{pc:90,pd:[24,24]},{pc:85,pd:[22,23]},{pc:80,pd:[21,21]},{pc:70,pd:[19,20]},{pc:65,pd:[17,18]},{pc:50,pd:[15,15]},{pc:45,pd:[14,14]},{pc:40,pd:[13,13]},{pc:30,pd:[12,12]},{pc:25,pd:[10,11]},{pc:20,pd:[9,9]},{pc:15,pd:[8,8]},{pc:10,pd:[7,7]},{pc:5,pd:[5,6]},{pc:2,pd:[4,4]},{pc:1,pd:[0,3]}],M:[{pc:99,pd:[26,28]},{pc:97,pd:[25,25]},{pc:95,pd:[24,24]},{pc:90,pd:[23,23]},{pc:85,pd:[22,22]},{pc:75,pd:[21,21]},{pc:70,pd:[20,20]},{pc:60,pd:[19,19]},{pc:50,pd:[18,18]},{pc:45,pd:[17,17]},{pc:40,pd:[16,16]},{pc:30,pd:[15,15]},{pc:25,pd:[14,14]},{pc:20,pd:[13,13]},{pc:15,pd:[12,12]},{pc:10,pd:[11,11]},{pc:5,pd:[10,10]},{pc:4,pd:[9,9]},{pc:3,pd:[8,8]},{pc:1,pd:[0,7]}],O:[{pc:99,pd:[32,32]},{pc:98,pd:[31,31]},{pc:95,pd:[30,30]},{pc:90,pd:[29,29]},{pc:85,pd:[27,28]},{pc:80,pd:[26,26]},{pc:70,pd:[25,25]},{pc:65,pd:[24,24]},{pc:60,pd:[23,23]},{pc:50,pd:[22,22]},{pc:45,pd:[21,21]},{pc:40,pd:[20,20]},{pc:35,pd:[19,19]},{pc:30,pd:[18,18]},{pc:25,pd:[17,17]},{pc:20,pd:[16,16]},{pc:15,pd:[14,15]},{pc:10,pd:[13,13]},{pc:5,pd:[10,12]},{pc:3,pd:[9,9]},{pc:2,pd:[8,8]},{pc:1,pd:[0,7]}]}},Ta=(e,a,i)=>{if(null==a)return null;let r;if("string"!=typeof a&&(a=String(a)),12===i)r="12-13";else{if(13!==i&&14!==i)return null;r="13-14"}const s=Ra[r];if(!s)return null;const t=s[a.toUpperCase()];if(!t)return null;for(const o of t)if(e>=o.pd[0]&&e<=o.pd[1])return o.pc;return e<t[t.length-1].pd[0]?t[t.length-1].pc:e>t[0].pd[1]?t[0].pc:null};class Ia{static calcularEdad(e){if(!e)return null;const a=new Date,i=new Date(e);let r=a.getFullYear()-i.getFullYear();const s=a.getMonth()-i.getMonth();return(s<0||0===s&&a.getDate()<i.getDate())&&r--,r}static determinarGrupoEdad(e){return 12===e?"12-13":13===e||14===e?"13-14":null}static convertirPdAPC(e,a,i){return m(this,null,function*(){try{const{data:r,error:s}=yield Aa.from("pacientes").select("fecha_nacimiento").eq("id",i).single();if(s)return null;const t=this.calcularEdad(r.fecha_nacimiento);if(!t)return null;return{pc:Ta(e,a,t),edad:t,grupoEdad:this.determinarGrupoEdad(t),pd:e}}catch(r){return null}})}static actualizarResultadoConPC(e,a){return m(this,null,function*(){try{const{data:i,error:r}=yield Aa.from("resultados").update({percentil:a,updated_at:(new Date).toISOString()}).eq("id",e).select();return r?null:i[0]}catch(i){return null}})}static procesarConversionAutomatica(e,a,i,r){return m(this,null,function*(){try{const s=yield this.convertirPdAPC(a,i,r);if(!s)return null;return yield this.actualizarResultadoConPC(e,s.pc,s.edad,s.grupoEdad)}catch(s){return null}})}static obtenerInterpretacionPC(e){return e>=98?{nivel:"Muy Alto",color:"text-green-700",bg:"bg-green-100"}:e>=85?{nivel:"Alto",color:"text-blue-700",bg:"bg-blue-100"}:e>=70?{nivel:"Medio-Alto",color:"text-indigo-700",bg:"bg-indigo-100"}:e>=31?{nivel:"Medio",color:"text-gray-700",bg:"bg-gray-100"}:e>=16?{nivel:"Medio-Bajo",color:"text-yellow-700",bg:"bg-yellow-100"}:e>=3?{nivel:"Bajo",color:"text-orange-700",bg:"bg-orange-100"}:{nivel:"Muy Bajo",color:"text-red-700",bg:"bg-red-100"}}static recalcularPCPaciente(e){return m(this,null,function*(){var a;try{const{data:i,error:r}=yield Aa.from("resultados").select("\n          id,\n          puntaje_directo,\n          aptitudes:aptitud_id (codigo)\n        ").eq("paciente_id",e).is("percentil",null);if(r)return!1;let s=0;for(const t of i){(yield this.procesarConversionAutomatica(t.id,t.puntaje_directo,null==(a=t.aptitudes)?void 0:a.codigo,e))&&s++}return!0}catch(i){return!1}})}}class za{static _validarParametrosConversion(e,a,i){const r=[];return("number"!=typeof e||e<0||e>100)&&r.push("Puntaje directo debe ser un número entre 0 y 100"),a&&"string"==typeof a&&0!==a.length||r.push("Código de aptitud es requerido"),("number"!=typeof i||i<6||i>18)&&r.push("Edad debe ser un número entre 6 y 18 años"),r}static _limpiarCache(){this._cache={baremos:null,funcionesDisponibles:null,lastCheck:null}}static _esCacheValido(){return this._cache.lastCheck&&Date.now()-this._cache.lastCheck<this.CACHE_DURATION}static recalcularTodosLosPercentiles(){return m(this,null,function*(){try{const{data:e,error:a}=yield Aa.rpc("recalcular_todos_los_percentiles");if(a)return d.error("Error al recalcular percentiles en Supabase"),{success:!1,error:a};const i=e||0;return i>0?d.success(`Se actualizaron ${i} resultados con sus percentiles`):d.info("No hay resultados pendientes de conversión"),{success:!0,count:i}}catch(e){return d.error("Error al ejecutar el recálculo de percentiles"),{success:!1,error:e}}})}static probarConversion(e,a,i){return m(this,null,function*(){try{const r=this._validarParametrosConversion(e,a,i);if(r.length>0){const e=`Parámetros inválidos: ${r.join(", ")}`;return d.error(e),{success:!1,error:{message:e},validationErrors:r}}const{data:s,error:t}=yield Aa.rpc("convertir_pd_a_pc",{p_puntaje_directo:e,p_aptitud_codigo:a.toUpperCase(),p_edad:Math.round(i)});if(t)return d.error(`Error en conversión: ${t.message||"Error desconocido"}`),{success:!1,error:t};if(null==s){const r=`No se pudo calcular percentil para PD=${e}, aptitud=${a}, edad=${i}`;return d.warning(r),{success:!1,error:{message:r}}}if("number"!=typeof s||s<1||s>99){const e=`Percentil fuera de rango válido (1-99): ${s}`;return d.warning(e),{success:!1,error:{message:e}}}return{success:!0,percentil:s}}catch(r){return d.error(`Error inesperado en conversión: ${r.message}`),{success:!1,error:r}}})}static convertirResultadosEnLote(e,a=!0){return m(this,null,function*(){try{if(!Array.isArray(e)||0===e.length)return d.warning("No se proporcionaron resultados para convertir"),{success:!1,error:"Lista de resultados vacía"};const a={exitosos:[],fallidos:[],total:e.length},i=10,r=[];for(let t=0;t<e.length;t+=i)r.push(e.slice(t,t+i));for(let e=0;e<r.length;e++){const i=r[e].map(e=>m(this,null,function*(){try{const i=yield this.forzarConversionResultado(e);i.success?a.exitosos.push({id:e,resultado:i.resultado}):a.fallidos.push({id:e,error:i.error})}catch(i){a.fallidos.push({id:e,error:i})}}));yield Promise.all(i),e<r.length-1&&(yield new Promise(e=>setTimeout(e,100)))}const s=(a.exitosos.length/a.total*100).toFixed(1);return a.exitosos.length>0&&d.success(`Conversión completada: ${a.exitosos.length}/${a.total} resultados convertidos`),a.fallidos.length>0&&d.warning(`${a.fallidos.length} conversiones fallaron`),{success:a.exitosos.length>0,resultados:a,porcentajeExito:parseFloat(s)}}catch(a){return d.error("Error en conversión en lote"),{success:!1,error:a}}})}static configurarConversionAutomatica(){return m(this,null,function*(){try{return(yield this.recalcularTodosLosPercentiles()).success?(d.success("Conversión automática configurada correctamente"),!0):(d.error("Error al configurar la conversión automática"),!1)}catch(e){return d.error("Error al configurar la conversión automática"),!1}})}static obtenerEstadisticasConversion(){return m(this,null,function*(){try{const{data:e,error:a}=yield Aa.from("resultados").select("id",{count:"exact"}).not("percentil","is",null),{data:i,error:r}=yield Aa.from("resultados").select("id",{count:"exact"}).is("percentil",null);if(a||r)return null;return{totalResultados:((null==e?void 0:e.length)||0)+((null==i?void 0:i.length)||0),conPercentil:(null==e?void 0:e.length)||0,sinPercentil:(null==i?void 0:i.length)||0,porcentajeConvertido:(((null==e?void 0:e.length)||0)/(((null==e?void 0:e.length)||0)+((null==i?void 0:i.length)||0))*100).toFixed(1)}}catch(e){return null}})}static forzarConversionResultado(e){return m(this,null,function*(){try{const{data:a,error:i}=yield Aa.from("resultados").select("\n          id,\n          puntaje_directo,\n          aptitudes:aptitud_id (codigo),\n          pacientes:paciente_id (fecha_nacimiento)\n        ").eq("id",e).single();if(i||!a)return{success:!1,error:i};const r=new Date(a.pacientes.fecha_nacimiento),s=new Date;let t=s.getFullYear()-r.getFullYear();const o=s.getMonth()-r.getMonth();(o<0||0===o&&s.getDate()<r.getDate())&&t--;const n=yield this.probarConversion(a.puntaje_directo,a.aptitudes.codigo,t);if(!n.success)return{success:!1,error:"Error en conversión"};const{data:l,error:c}=yield Aa.from("resultados").update({percentil:n.percentil,updated_at:(new Date).toISOString()}).eq("id",e).select().single();return c?{success:!1,error:c}:(d.success(`Conversión completada: PC ${n.percentil}`),{success:!0,resultado:l})}catch(a){return{success:!1,error:a}}})}static verificarBaremos(){return m(this,null,function*(){try{const{data:e,error:a}=yield Aa.from("baremos").select("factor, baremo_grupo, count(*)").group("factor, baremo_grupo");return a?{success:!1,error:a}:{success:!0,baremos:e}}catch(e){return{success:!1,error:e}}})}}u(za,"_cache",{baremos:null,funcionesDisponibles:null,lastCheck:null}),u(za,"CACHE_DURATION",3e5);class Sa{static getAptitudeId(e){return m(this,null,function*(){try{const a=this.TEST_APTITUDE_MAP[e];if(!a)throw new Error(`Tipo de test no reconocido: ${e}`);const{data:i,error:r}=yield Aa.from("aptitudes").select("id").eq("codigo",a).single();if(r)throw r;return i.id}catch(a){throw a}})}static calculateConcentration(e,a=0){return e&&0!==e?e/(e+a)*100:0}static saveTestResult(e){return m(this,arguments,function*({patientId:e,testType:a,correctCount:i,incorrectCount:r,unansweredCount:s,timeUsed:t,totalQuestions:o,answers:n={},errores:l=0}){try{if(!e)throw new Error("ID del paciente es requerido");if(!a)throw new Error("Tipo de test es requerido");const p=yield this.getAptitudeId(a),b=i||0;let N=null;"atencion"===a&&(N=this.calculateConcentration(b,l));const g={paciente_id:e,aptitud_id:p,puntaje_directo:b,tiempo_segundos:t||0,respuestas:n,errores:l,concentracion:N,respuestas_correctas:i||0,respuestas_incorrectas:r||0,respuestas_sin_contestar:s||0,total_preguntas:o||0,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},{data:f,error:x}=yield Aa.from("resultados").insert([g]).select().single();if(x)throw x;try{const a=(yield R(()=>m(this,null,function*(){const{default:e}=yield import("./InformesService-3ys5QOjE.js");return{default:e}}),__vite__mapDeps([0,1,2,3]))).default;(yield a.generarInformeAutomatico(e,f.id))&&d.success("Resultado guardado e informe generado automáticamente")}catch(c){}try{if(f.percentil)return d.success(`Resultado guardado y convertido automáticamente (PC: ${f.percentil})`),f;const i=this.TEST_APTITUDE_MAP[a];if(i&&f.id){const a=yield za.forzarConversionResultado(f.id);if(a.success)return a.resultado;{const a=yield Ia.procesarConversionAutomatica(f.id,b,i,e);if(a)return d.success(`Resultado guardado y convertido (PC: ${a.percentil})`),a}}}catch(u){d.warning("Resultado guardado, pero falló la conversión automática a PC")}return d.success("Resultado guardado correctamente en la base de datos"),f}catch(p){throw d.error(`Error al guardar resultado: ${p.message}`),p}})}static getPatientResults(e){return m(this,null,function*(){try{const{data:a,error:i}=yield Aa.from("resultados").select("\n          *,\n          aptitudes:aptitud_id (\n            codigo,\n            nombre,\n            descripcion\n          )\n        ").eq("paciente_id",e).order("created_at",{ascending:!1});if(i)throw i;return a||[]}catch(a){throw a}})}static hasTestResult(e,a){return m(this,null,function*(){try{const i=yield this.getAptitudeId(a),{data:r,error:s}=yield Aa.from("resultados").select("id").eq("paciente_id",e).eq("aptitud_id",i).limit(1);if(s)throw s;return r&&r.length>0}catch(i){return!1}})}static updateTestResult(e,a){return m(this,null,function*(){try{const{data:i,error:r}=yield Aa.from("resultados").update(l(n({},a),{updated_at:(new Date).toISOString()})).eq("id",e).select().single();if(r)throw r;return d.success("Resultado actualizado correctamente"),i}catch(i){throw d.error(`Error al actualizar resultado: ${i.message}`),i}})}static deleteTestResult(e){return m(this,null,function*(){try{const{error:a}=yield Aa.from("resultados").delete().eq("id",e);if(a)throw a;return d.success("Resultado eliminado correctamente"),!0}catch(a){throw d.error(`Error al eliminar resultado: ${a.message}`),a}})}static getPatientStats(e){return m(this,null,function*(){try{const a=yield this.getPatientResults(e),i={totalTests:a.length,averageScore:0,completedTests:a.map(e=>{var a;return null==(a=e.aptitudes)?void 0:a.codigo}).filter(Boolean),lastTestDate:a.length>0?a[0].created_at:null};if(a.length>0){const e=a.reduce((e,a)=>e+(a.puntaje_directo||0),0);i.averageScore=Math.round(e/a.length)}return i}catch(a){throw a}})}}u(Sa,"TEST_APTITUDE_MAP",{verbal:"V",espacial:"E",atencion:"A",razonamiento:"R",numerico:"N",mecanico:"M",ortografia:"O"});const qa=()=>{var e;const a=h(),i=E(),[r,s]=f.useState(!0),[t,o]=f.useState([]),[c,u]=f.useState(0),[p,b]=f.useState({}),[N,g]=f.useState(720),[v,j]=f.useState(!1),V=null==(e=i.state)?void 0:e.patientId,C={1:"4",2:"1",3:"4",4:"2",5:"2",6:"1",7:"3",8:"2",9:"3",10:"4",11:"3",12:"4",13:"3",14:"4",15:"2",16:"3",17:"3",18:"2",19:"3",20:"2",21:"3",22:"3",23:"4",24:"3",25:"2",26:"1",27:"3",28:"1",29:"2",30:"2",31:"1",32:"1"},y={a:"1",b:"2",c:"3",d:"4"};f.useEffect(()=>{m(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800)),o([{id:1,type:"analogies",text:"Ciudad es a hombre como colmena es a ...",options:[{id:"a",text:"Hormiga"},{id:"b",text:"Mosquito"},{id:"c",text:"Araña"},{id:"d",text:"Abeja"}],correctAnswer:"d"},{id:2,type:"analogies",text:"Batido es a batir como zumo es a ...",options:[{id:"a",text:"Exprimir"},{id:"b",text:"Aplastar"},{id:"c",text:"Machacar"},{id:"d",text:"Succionar"}],correctAnswer:"a"},{id:3,type:"analogies",text:"Consejero es a consejo como cantante es a ...",options:[{id:"a",text:"Fama"},{id:"b",text:"Éxito"},{id:"c",text:"Composición"},{id:"d",text:"Canción"}],correctAnswer:"d"},{id:4,type:"analogies",text:"Estufa es a calor como nevera es a ...",options:[{id:"a",text:"Temperatura"},{id:"b",text:"Frío"},{id:"c",text:"Conservación"},{id:"d",text:"Congelación"}],correctAnswer:"b"},{id:5,type:"analogies",text:"Martillo es a clavo como destornillador es a ...",options:[{id:"a",text:"Hierro"},{id:"b",text:"Tornillo"},{id:"c",text:"Remache"},{id:"d",text:"Herramienta"}],correctAnswer:"b"},{id:6,type:"analogies",text:"Asa es a cesta como pomo es a ...",options:[{id:"a",text:"Puerta"},{id:"b",text:"Tirador"},{id:"c",text:"Envase"},{id:"d",text:"Manillar"}],correctAnswer:"a"},{id:7,type:"analogies",text:"Líquido es a sopa como sólido es a ...",options:[{id:"a",text:"Comer"},{id:"b",text:"Bebida"},{id:"c",text:"Plátano"},{id:"d",text:"Gaseoso"}],correctAnswer:"c"},{id:8,type:"analogies",text:"Ballena es a acuático como león es a ...",options:[{id:"a",text:"Carnívoro"},{id:"b",text:"Terrestre"},{id:"c",text:"Depredador"},{id:"d",text:"Devorador"}],correctAnswer:"b"},{id:9,type:"analogies",text:"Restar es a sumar como arreglar es a ...",options:[{id:"a",text:"Incluir"},{id:"b",text:"Corregir"},{id:"c",text:"Estropear"},{id:"d",text:"Resarcir"}],correctAnswer:"c"},{id:10,type:"analogies",text:"Más es a menos como después es a ...",options:[{id:"a",text:"Tiempo"},{id:"b",text:"Siguiente"},{id:"c",text:"Pronto"},{id:"d",text:"Antes"}],correctAnswer:"d"},{id:11,type:"analogies",text:"Fémur es a hueso como corazón es a ...",options:[{id:"a",text:"Glándula"},{id:"b",text:"Vena"},{id:"c",text:"Músculo"},{id:"d",text:"Arteria"}],correctAnswer:"c"},{id:12,type:"analogies",text:"Cuatro es a cinco como cuadrado es a ...",options:[{id:"a",text:"Triángulo"},{id:"b",text:"Heptágono"},{id:"c",text:"Hexágono"},{id:"d",text:"Pentágono"}],correctAnswer:"d"},{id:13,type:"analogies",text:"Harina es a trigo como cerveza es a ...",options:[{id:"a",text:"Manzana"},{id:"b",text:"Patata"},{id:"c",text:"Cebada"},{id:"d",text:"Alfalfa"}],correctAnswer:"c"},{id:14,type:"analogies",text:"Pie es a cuerpo como bombilla es a ...",options:[{id:"a",text:"Ojos"},{id:"b",text:"Luz"},{id:"c",text:"Vela"},{id:"d",text:"Lámpara"}],correctAnswer:"d"},{id:15,type:"analogies",text:"Excavar es a cavidad como alinear es a ...",options:[{id:"a",text:"Seguido"},{id:"b",text:"Recta"},{id:"c",text:"Acodo"},{id:"d",text:"Ensamblar"}],correctAnswer:"b"},{id:16,type:"analogies",text:"Harina es a pan como leche es a ...",options:[{id:"a",text:"Vaca"},{id:"b",text:"Trigo"},{id:"c",text:"Yogur"},{id:"d",text:"Agua"}],correctAnswer:"c"},{id:17,type:"analogies",text:"Círculo es a cuadrado como esfera es a ...",options:[{id:"a",text:"Cuadrilátero"},{id:"b",text:"Rombo"},{id:"c",text:"Cubo"},{id:"d",text:"Circunferencia"}],correctAnswer:"c"},{id:18,type:"analogies",text:"Bicicleta es a avión como metal es a ...",options:[{id:"a",text:"Solidez"},{id:"b",text:"Madera"},{id:"c",text:"Velocidad"},{id:"d",text:"Fragmento"}],correctAnswer:"b"},{id:19,type:"analogies",text:"Doctora es a doctor como amazona es a ...",options:[{id:"a",text:"Piloto"},{id:"b",text:"Modisto"},{id:"c",text:"Jinete"},{id:"d",text:"Bailarín"}],correctAnswer:"c"},{id:20,type:"analogies",text:"Escultor es a estudio como actor es a ...",options:[{id:"a",text:"Arte"},{id:"b",text:"Escenario"},{id:"c",text:"Drama"},{id:"d",text:"Literatura"}],correctAnswer:"b"},{id:21,type:"analogies",text:"Perder es a ganar como reposo es a ...",options:[{id:"a",text:"Ganancia"},{id:"b",text:"Descanso"},{id:"c",text:"Actividad"},{id:"d",text:"Calma"}],correctAnswer:"c"},{id:22,type:"analogies",text:"Encubierto es a clandestino como endeble es a ...",options:[{id:"a",text:"Doblado"},{id:"b",text:"Simple"},{id:"c",text:"Delicado"},{id:"d",text:"Comprimido"}],correctAnswer:"c"},{id:23,type:"analogies",text:"Apocado es a tímido como arrogante es a ...",options:[{id:"a",text:"Listo"},{id:"b",text:"Humilde"},{id:"c",text:"Virtuoso"},{id:"d",text:"Soberbio"}],correctAnswer:"d"},{id:24,type:"analogies",text:"Rodillo es a masa como torno es a ...",options:[{id:"a",text:"Escayola"},{id:"b",text:"Goma"},{id:"c",text:"Arcilla"},{id:"d",text:"Pintura"}],correctAnswer:"c"},{id:25,type:"analogies",text:"Hora es a tiempo como litro es a ...",options:[{id:"a",text:"Peso"},{id:"b",text:"Capacidad"},{id:"c",text:"Balanza"},{id:"d",text:"Cantidad"}],correctAnswer:"b"},{id:26,type:"analogies",text:"Indefenso es a desvalido como enlazado es a ...",options:[{id:"a",text:"Conexo"},{id:"b",text:"Recorrido"},{id:"c",text:"Torcido"},{id:"d",text:"Explorado"}],correctAnswer:"a"},{id:27,type:"analogies",text:"Reparar es a enmendar como mantener es a ...",options:[{id:"a",text:"Moderar"},{id:"b",text:"Presumir"},{id:"c",text:"Proseguir"},{id:"d",text:"Ayunar"}],correctAnswer:"c"},{id:28,type:"analogies",text:"Adelantar es a demorar como anticipar es a ...",options:[{id:"a",text:"Aplazar"},{id:"b",text:"Desistir"},{id:"c",text:"Proveer"},{id:"d",text:"Achacar"}],correctAnswer:"a"},{id:29,type:"analogies",text:"Infinito es a inagotable como vasto es a ...",options:[{id:"a",text:"Expedito"},{id:"b",text:"Colosal"},{id:"c",text:"Demorado"},{id:"d",text:"Confuso"}],correctAnswer:"b"},{id:30,type:"analogies",text:"Amenazar es a intimidar como articular es a ...",options:[{id:"a",text:"Legislar"},{id:"b",text:"Pronunciar"},{id:"c",text:"Afirmar"},{id:"d",text:"Arquear"}],correctAnswer:"b"},{id:31,type:"analogies",text:"Agua es a embudo como tierra es a ...",options:[{id:"a",text:"Criba"},{id:"b",text:"Fresadora"},{id:"c",text:"Cincel"},{id:"d",text:"Escariador"}],correctAnswer:"a"},{id:32,type:"analogies",text:"Prender es a extinguir como juntar es a ...",options:[{id:"a",text:"Separar"},{id:"b",text:"Unir"},{id:"c",text:"Apagar"},{id:"d",text:"Reducir"}],correctAnswer:"a"}]),s(!1)}catch(e){d.error("Error al cargar las preguntas del test"),s(!1)}})},[]);const D=f.useCallback(()=>m(null,null,function*(){try{const i=Object.keys(p).length,r=t.length,s=r-i;let o=0;Object.entries(p).forEach(([e,a])=>{C[e]===y[a]&&o++});const n=i-o,l=720-N,c={correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,testType:"verbal"};if(V)try{yield Sa.saveTestResult({patientId:V,testType:"verbal",correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,answers:p,errores:n}),d.success("Resultado guardado correctamente")}catch(e){d.error(`Error al guardar: ${e.message}`)}d.success(`Test completado. Has respondido ${i} de ${r} preguntas. Respuestas correctas: ${o}`),a("/test/results/verbal",{state:c})}catch(e){d.error("Error al procesar los resultados del test")}}),[p,t.length,N,a,V]);f.useEffect(()=>{if(!v||N<=0)return;const e=setInterval(()=>{g(a=>a<=1?(clearInterval(e),setTimeout(()=>D(),0),0):a-1)},1e3);return()=>clearInterval(e)},[v,N,D]);const B=e=>{const a=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},w=t[c],A=!!w&&p[w.id];return x.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("div",{className:"text-center mb-4",children:[x.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[x.jsxDEV("i",{className:"fas fa-comments mr-2 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:592,columnNumber:13},void 0),"Test de Aptitud Verbal"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:591,columnNumber:11},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Evaluación de analogías verbales y comprensión de relaciones entre conceptos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:595,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:590,columnNumber:9},void 0),v&&x.jsxDEV("div",{className:"text-center",children:x.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:B(N)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:599,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:598,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:589,columnNumber:7},void 0),r?x.jsxDEV("div",{className:"py-16 text-center",children:x.jsxDEV("div",{className:"flex flex-col items-center justify-center",children:[x.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:609,columnNumber:13},void 0),x.jsxDEV("p",{className:"text-gray-500",children:"Cargando test de razonamiento verbal..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:610,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:608,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:607,columnNumber:9},void 0):v?x.jsxDEV(x.Fragment,{children:x.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[x.jsxDEV("div",{className:"md:col-span-3",children:x.jsxDEV(va,{className:"mb-6",children:[x.jsxDEV(ja,{className:"flex justify-between items-center",children:[x.jsxDEV("div",{children:[x.jsxDEV("h2",{className:"text-lg font-medium",children:["Pregunta ",c+1," de ",t.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:715,columnNumber:21},void 0),x.jsxDEV("p",{className:"text-sm text-gray-500",children:w?(R=w.type,"analogies"===R?"Analogías":R):""},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:718,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:714,columnNumber:19},void 0),x.jsxDEV("div",{className:"text-sm text-gray-500",children:A?"Respondida":"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:722,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:713,columnNumber:17},void 0),x.jsxDEV(Va,{children:w&&x.jsxDEV(x.Fragment,{children:[x.jsxDEV("div",{className:"text-gray-800 mb-6 whitespace-pre-line font-medium",children:w.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:729,columnNumber:23},void 0),x.jsxDEV("div",{className:"space-y-3",children:w.options.map(e=>x.jsxDEV("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(p[w.id]===e.id&&e.id===w.correctAnswer?"bg-green-100 border-green-500":p[w.id]===e.id?"bg-blue-50 border-blue-500":"hover:bg-gray-50"),onClick:()=>{return a=w.id,i=e.id,void b(l(n({},p),{[a]:i}));var a,i},children:x.jsxDEV("div",{className:"flex items-center",children:[x.jsxDEV("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(p[w.id]===e.id?"bg-blue-500 text-white":"bg-gray-200 text-gray-700"),children:e.id.toUpperCase()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:744,columnNumber:31},void 0),x.jsxDEV("div",{className:"text-gray-700",children:e.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:751,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:743,columnNumber:29},void 0)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:732,columnNumber:27},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:730,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:728,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:726,columnNumber:17},void 0),x.jsxDEV(Ea,{className:"flex justify-between",children:[x.jsxDEV(Ca,{variant:"outline",onClick:()=>{c>0&&u(c-1)},disabled:0===c,children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:760,columnNumber:19},void 0),c<t.length-1?x.jsxDEV(Ca,{variant:"primary",onClick:()=>{c<t.length-1&&u(c+1)},children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:768,columnNumber:21},void 0):x.jsxDEV(Ca,{variant:"primary",onClick:D,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:775,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:759,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:712,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:711,columnNumber:13},void 0),x.jsxDEV("div",{children:x.jsxDEV(va,{className:"sticky top-6",children:[x.jsxDEV(ja,{children:x.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:789,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:788,columnNumber:17},void 0),x.jsxDEV(Va,{children:[x.jsxDEV("div",{className:"grid grid-cols-4 gap-2",children:t.map((e,a)=>x.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(c===a?"bg-blue-500 text-white":p[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>u(a),title:`Pregunta ${a+1}`,children:a+1},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:794,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:792,columnNumber:19},void 0),x.jsxDEV("div",{className:"mt-6",children:[x.jsxDEV("div",{className:"flex items-center justify-between mb-2 text-sm",children:[x.jsxDEV("span",{children:"Progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:813,columnNumber:23},void 0),x.jsxDEV("span",{children:[Object.keys(p).length," de ",t.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:814,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:812,columnNumber:21},void 0),x.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:x.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(p).length/t.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:817,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:816,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:811,columnNumber:19},void 0),x.jsxDEV("div",{className:"mt-6",children:x.jsxDEV("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[x.jsxDEV("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:826,columnNumber:23},void 0),x.jsxDEV("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",x.jsxDEV("span",{className:"font-medium",children:B(N)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:828,columnNumber:42},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:827,columnNumber:23},void 0),x.jsxDEV("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:830,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:825,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:824,columnNumber:19},void 0),x.jsxDEV(Ca,{variant:"primary",className:"w-full mt-2",onClick:D,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:836,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:791,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:787,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:786,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:710,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:709,columnNumber:9},void 0):x.jsxDEV(va,{children:[x.jsxDEV(ja,{children:x.jsxDEV("h2",{className:"text-xl font-semibold text-gray-800",children:"Razonamiento Verbal: Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:616,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:615,columnNumber:11},void 0),x.jsxDEV(Va,{children:x.jsxDEV("div",{className:"space-y-6",children:[x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Razonamiento Verbal?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:621,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-2",children:"El razonamiento verbal es la capacidad para comprender y establecer relaciones lógicas entre conceptos expresados mediante palabras. Implica entender analogías, relaciones semánticas y encontrar patrones en expresiones verbales."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:622,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Esta habilidad es fundamental en el ámbito académico y profesional, siendo especialmente relevante para carreras que requieren pensamiento lógico y analítico."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:625,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:620,columnNumber:15},void 0),x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:631,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-3",children:"A continuación encontrarás frases a las que les falta una palabra que ha sido sustituida por puntos suspensivos. Tu tarea consistirá en descubrir qué palabra falta para que la frase resulte verdadera y con sentido."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:632,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-3",children:"En cada ejercicio se proponen cuatro palabras u opciones de respuesta posibles. Entre las cuatro palabras solamente UNA es la opción correcta, la que completa mejor la frase dotándola de sentido."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:635,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600",children:'Las frases tienen la siguiente estructura: "A es a B como C es a D". Deberás identificar la relación entre A y B, y aplicar la misma relación entre C y la palabra que falta (D).'},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:638,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:630,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-orange-700 mb-2",children:"Ejemplos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:644,columnNumber:17},void 0),x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("p",{className:"text-gray-600 mb-3",children:[x.jsxDEV("strong",{className:"text-blue-600",children:"Ejemplo 1:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:648,columnNumber:21},void 0)," ",x.jsxDEV("strong",{children:"Alto es a bajo como grande es a ..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:648,columnNumber:75},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:647,columnNumber:19},void 0),x.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[x.jsxDEV("div",{className:"bg-white p-3 rounded border border-gray-200",children:"A. Visible"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:651,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-white p-3 rounded border border-gray-200",children:"B. Gordo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:652,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-lime-100 p-3 rounded border border-lime-300 font-medium",children:"C. Pequeño"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:653,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-white p-3 rounded border border-gray-200",children:"D. Poco"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:654,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:650,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",x.jsxDEV("strong",{children:"C. Pequeño"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:657,columnNumber:46},void 0),", porque grande y pequeño se relacionan de la misma forma que alto y bajo: son opuestos."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:656,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:646,columnNumber:17},void 0),x.jsxDEV("div",{children:[x.jsxDEV("p",{className:"text-gray-600 mb-3",children:[x.jsxDEV("strong",{className:"text-blue-600",children:"Ejemplo 2:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:663,columnNumber:21},void 0)," ",x.jsxDEV("strong",{children:"...?... es a estrella como tierra es a planeta."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:663,columnNumber:75},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:662,columnNumber:19},void 0),x.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[x.jsxDEV("div",{className:"bg-white p-3 rounded border border-gray-200",children:"A. Luz"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:666,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-white p-3 rounded border border-gray-200",children:"B. Calor"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:667,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-white p-3 rounded border border-gray-200",children:"C. Noche"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:668,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-lime-100 p-3 rounded border border-lime-300 font-medium",children:"D. Sol"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:669,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:665,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",x.jsxDEV("strong",{children:"D. Sol"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:672,columnNumber:46},void 0),', porque sol y estrella guardan entre sí la misma relación que tierra y planeta: el Sol es una estrella y la Tierra es un planeta. Fíjate igualmente en que cualquiera de las otras opciones no sería correcta; por ejemplo, en la opción B, es cierto que las estrellas producen calor, pero no tiene sentido la misma relación en las dos últimas palabras ("planeta" no produce "tierra").']},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:671,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:661,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:643,columnNumber:15},void 0),x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:678,columnNumber:17},void 0),x.jsxDEV("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[x.jsxDEV("li",{children:"El test consta de 32 preguntas de analogías verbales."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:680,columnNumber:19},void 0),x.jsxDEV("li",{children:["Dispondrás de ",x.jsxDEV("span",{className:"font-medium",children:"12 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:681,columnNumber:37},void 0)," para completar todas las preguntas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:681,columnNumber:19},void 0),x.jsxDEV("li",{children:"Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:682,columnNumber:19},void 0),x.jsxDEV("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:683,columnNumber:19},void 0),x.jsxDEV("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:684,columnNumber:19},void 0),x.jsxDEV("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:685,columnNumber:19},void 0),x.jsxDEV("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:686,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:679,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:677,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:691,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:692,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:690,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:619,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:618,columnNumber:11},void 0),x.jsxDEV(Ea,{className:"flex justify-end",children:x.jsxDEV(Ca,{variant:"primary",onClick:()=>{j(!0),d.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2",children:"Comenzar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:699,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:698,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:614,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:588,columnNumber:5},void 0);var R},Pa=Object.freeze(Object.defineProperty({__proto__:null,default:qa},Symbol.toStringTag,{value:"Module"}));T.number.isRequired,T.number.isRequired,T.object.isRequired,T.func.isRequired,T.number,T.number.isRequired,T.func.isRequired;const Ma=()=>{var e;const a=h(),i=E(),[r,s]=f.useState(0),[t,o]=f.useState({}),[c,u]=f.useState(!1),{timeRemaining:p,startTimer:b,stopTimer:N,formatTime:g}=(e=>{const[a,i]=f.useState(e),[r,s]=f.useState(!1),t=f.useCallback(()=>{s(!0)},[]),o=f.useCallback(()=>{s(!1)},[]),n=f.useCallback(()=>{i(e),s(!1)},[e]),l=f.useCallback(e=>{const a=e%60;return`${Math.floor(e/60)}:${a<10?"0":""}${a}`},[]);return f.useEffect(()=>{let e;return r&&a>0?e=setInterval(()=>{i(e=>e-1)},1e3):0===a&&s(!1),()=>{e&&clearInterval(e)}},[r,a]),{timeRemaining:a,isRunning:r,startTimer:t,stopTimer:o,resetTimer:n,formatTime:l}})(600),v=null==(e=i.state)?void 0:e.patientId;f.useEffect(()=>(b(),()=>{N()}),[b,N]),f.useEffect(()=>{0===p&&V()},[p]);const j=[{id:1,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"reloj"},{id:"B",text:"reciclaje"},{id:"C",text:"reyna"},{id:"D",text:"nube"}],correctAnswer:"C"},{id:2,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hola"},{id:"B",text:"Zoo"},{id:"C",text:"ambos"},{id:"D",text:"vallena"}],correctAnswer:"D"},{id:3,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"adibinar"},{id:"B",text:"inmediato"},{id:"C",text:"gestar"},{id:"D",text:"anchoa"}],correctAnswer:"A"},{id:4,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"herrero"},{id:"B",text:"saver"},{id:"C",text:"cerrar"},{id:"D",text:"honrado"}],correctAnswer:"B"},{id:5,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"éxtasis"},{id:"B",text:"cesta"},{id:"C",text:"ademas"},{id:"D",text:"llevar"}],correctAnswer:"C"},{id:6,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"avión"},{id:"B",text:"abrir"},{id:"C",text:"favor"},{id:"D",text:"espionage"}],correctAnswer:"D"},{id:7,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"insecto"},{id:"B",text:"jota"},{id:"C",text:"habrigo"},{id:"D",text:"extraño"}],correctAnswer:"C"},{id:8,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hacha"},{id:"B",text:"oler"},{id:"C",text:"polbo"},{id:"D",text:"abril"}],correctAnswer:"C"},{id:9,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"amartillar"},{id:"B",text:"desacer"},{id:"C",text:"exageración"},{id:"D",text:"humildad"}],correctAnswer:"B"},{id:10,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"bendige"},{id:"B",text:"bifurcación"},{id:"C",text:"amarrar"},{id:"D",text:"país"}],correctAnswer:"A"},{id:11,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"horrible"},{id:"B",text:"llacimiento"},{id:"C",text:"inmóvil"},{id:"D",text:"enredar"}],correctAnswer:"B"},{id:12,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"zebra"},{id:"B",text:"impaciente"},{id:"C",text:"alrededor"},{id:"D",text:"mayor"}],correctAnswer:"A"},{id:13,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hormona"},{id:"B",text:"jirafa"},{id:"C",text:"desván"},{id:"D",text:"enpañar"}],correctAnswer:"D"},{id:14,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"abdicar"},{id:"B",text:"area"},{id:"C",text:"ombligo"},{id:"D",text:"extinguir"}],correctAnswer:"B"},{id:15,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"júbilo"},{id:"B",text:"lúz"},{id:"C",text:"quince"},{id:"D",text:"hilera"}],correctAnswer:"B"},{id:16,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"inexorable"},{id:"B",text:"coraje"},{id:"C",text:"ingerir"},{id:"D",text:"hunir"}],correctAnswer:"D"},{id:17,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"aereo"},{id:"B",text:"conserje"},{id:"C",text:"drástico"},{id:"D",text:"ataviar"}],correctAnswer:"A"},{id:18,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"grave"},{id:"B",text:"abrumar"},{id:"C",text:"contración"},{id:"D",text:"enmienda"}],correctAnswer:"C"},{id:19,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hay"},{id:"B",text:"gemido"},{id:"C",text:"carácter"},{id:"D",text:"harpón"}],correctAnswer:"D"},{id:20,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"embarcar"},{id:"B",text:"ambiguo"},{id:"C",text:"arroyo"},{id:"D",text:"esotérico"}],correctAnswer:"D"},{id:21,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"léntamente"},{id:"B",text:"utopía"},{id:"C",text:"aprensivo"},{id:"D",text:"irascible"}],correctAnswer:"A"},{id:22,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"desahogar"},{id:"B",text:"córnea"},{id:"C",text:"convenido"},{id:"D",text:"azúl"}],correctAnswer:"D"},{id:23,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"próspero"},{id:"B",text:"fué"},{id:"C",text:"regencia"},{id:"D",text:"pelaje"}],correctAnswer:"B"},{id:24,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"savia"},{id:"B",text:"ciénaga"},{id:"C",text:"andamiage"},{id:"D",text:"inmediatamente"}],correctAnswer:"C"},{id:25,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"traspié"},{id:"B",text:"urón"},{id:"C",text:"embellecer"},{id:"D",text:"vasija"}],correctAnswer:"B"},{id:26,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"río"},{id:"B",text:"barar"},{id:"C",text:"hiena"},{id:"D",text:"buhardilla"}],correctAnswer:"B"},{id:27,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"sátira"},{id:"B",text:"crujir"},{id:"C",text:"subrayar"},{id:"D",text:"extrategia"}],correctAnswer:"D"},{id:28,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"dátil"},{id:"B",text:"imágen"},{id:"C",text:"geranio"},{id:"D",text:"anteojo"}],correctAnswer:"B"},{id:29,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"incisivo"},{id:"B",text:"baya"},{id:"C",text:"impío"},{id:"D",text:"arnes"}],correctAnswer:"D"},{id:30,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"jersey"},{id:"B",text:"berengena"},{id:"C",text:"exhibir"},{id:"D",text:"atestar"}],correctAnswer:"B"},{id:31,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"versátil"},{id:"B",text:"hogaza"},{id:"C",text:"vadear"},{id:"D",text:"hurraca"}],correctAnswer:"D"},{id:32,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"exacerbar"},{id:"B",text:"leído"},{id:"C",text:"hayar"},{id:"D",text:"hostil"}],correctAnswer:"C"}],V=()=>m(null,null,function*(){try{N(),u(!0);const i=Object.keys(t).length,r=j.length,s=r-i;let o=0;Object.entries(t).forEach(([e,a])=>{const i=j.find(a=>a.id===parseInt(e));i&&i.correctAnswer===a&&o++});const n=i-o,l=600-p,c={correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,testType:"ortografia"};if(v)try{yield Sa.saveTestResult({patientId:v,testType:"ortografia",correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,answers:t,errores:n})}catch(e){}d.success(`Test completado. Has respondido ${i} de ${r} preguntas. Respuestas correctas: ${o}`),a("/test/results/ortografia",{state:c})}catch(e){d.error("Error al procesar los resultados del test")}});Object.keys(t).length,j.length;const C=j[r],y=void 0!==t[C.id];return x.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("div",{className:"text-center mb-4",children:[x.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[x.jsxDEV("i",{className:"fas fa-spell-check mr-2 text-green-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:508,columnNumber:13},void 0),"Test de Ortografía"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:507,columnNumber:11},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Identificación de palabras con errores ortográficos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:511,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:506,columnNumber:9},void 0),x.jsxDEV("div",{className:"text-center",children:x.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:g(p)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:514,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:513,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:505,columnNumber:7},void 0),x.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[x.jsxDEV("div",{className:"md:col-span-3",children:x.jsxDEV("div",{className:"bg-white rounded-lg shadow border border-gray-200",children:[x.jsxDEV("div",{className:"p-4 border-b border-gray-200",children:x.jsxDEV("div",{className:"flex justify-between items-center",children:[x.jsxDEV("div",{children:[x.jsxDEV("h2",{className:"text-lg font-semibold text-gray-800",children:["Pregunta ",r+1," de ",j.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:526,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-sm text-gray-600",children:"Ortografía"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:527,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:525,columnNumber:15},void 0),x.jsxDEV("div",{className:"text-sm font-medium text-gray-500",children:y?"Respondida":"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:529,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:524,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:523,columnNumber:11},void 0),x.jsxDEV("div",{className:"p-6",children:[x.jsxDEV("p",{className:"text-lg font-medium text-gray-800 mb-6",children:C.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:536,columnNumber:13},void 0),x.jsxDEV("div",{className:"space-y-3",children:C.options.map(e=>x.jsxDEV("button",{className:"w-full text-left p-4 rounded-lg border "+(t[C.id]===e.id?"bg-blue-50 border-blue-500":"bg-white border-gray-200 hover:bg-gray-50"),onClick:()=>{return a=C.id,i=e.id,void o(e=>l(n({},e),{[a]:i}));var a,i},children:x.jsxDEV("div",{className:"flex items-center",children:[x.jsxDEV("div",{className:"w-8 h-8 flex items-center justify-center rounded-full border mr-3 "+(t[C.id]===e.id?"bg-blue-500 text-white border-blue-500":"text-gray-500 border-gray-300"),children:e.id},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:552,columnNumber:21},void 0),x.jsxDEV("span",{className:"text-gray-800 font-medium",children:e.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:559,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:551,columnNumber:19},void 0)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:542,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:540,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:535,columnNumber:11},void 0),x.jsxDEV("div",{className:"px-6 py-4 border-t border-gray-200 flex justify-between",children:[x.jsxDEV("button",{onClick:()=>{r>0&&s(r-1)},disabled:0===r,className:"px-4 py-2 rounded-md "+(0===r?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"),children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:567,columnNumber:13},void 0),x.jsxDEV("button",{onClick:r<j.length-1?()=>{r<j.length-1&&s(r+1)}:V,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:r<j.length-1?"Siguiente":"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:578,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:566,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:522,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:521,columnNumber:9},void 0),x.jsxDEV("div",{children:x.jsxDEV("div",{className:"bg-white rounded-lg shadow border border-gray-200 sticky top-6",children:[x.jsxDEV("div",{className:"p-4 border-b border-gray-200",children:x.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:592,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:591,columnNumber:13},void 0),x.jsxDEV("div",{className:"p-4",children:[x.jsxDEV("div",{className:"grid grid-cols-4 gap-2",children:j.map((e,a)=>x.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(r===a?"bg-blue-500 text-white":void 0!==t[j[a].id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>s(a),title:`Pregunta ${a+1}`,children:a+1},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:597,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:595,columnNumber:15},void 0),x.jsxDEV("div",{className:"mt-6",children:[x.jsxDEV("div",{className:"flex items-center justify-between mb-2 text-sm",children:[x.jsxDEV("span",{children:"Progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:616,columnNumber:19},void 0),x.jsxDEV("span",{children:[Object.keys(t).length," de ",j.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:617,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:615,columnNumber:17},void 0),x.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:x.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(t).length/j.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:620,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:619,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:614,columnNumber:15},void 0),x.jsxDEV("div",{className:"mt-6",children:x.jsxDEV("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[x.jsxDEV("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:629,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",x.jsxDEV("span",{className:"font-medium",children:g(p)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:631,columnNumber:38},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:630,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:633,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:628,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:627,columnNumber:15},void 0),x.jsxDEV("button",{onClick:V,className:"w-full mt-2 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium",children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:639,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:594,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:590,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:589,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:520,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:504,columnNumber:5},void 0)},_a=Object.freeze(Object.defineProperty({__proto__:null,default:Ma},Symbol.toStringTag,{value:"Module"})),ka=e=>{let a=e.startsWith("/")?e.slice(1):e;return a.startsWith("assets/images/")?a=a.replace("assets/images/",""):a.startsWith("images/")&&(a=a.replace("images/","")),(e=>{const a=e.startsWith("/")?e.slice(1):e,i="/Bat-7/";return a.startsWith("assets/")?`${i}${a}`:`${i}assets/${a}`})(`images/${a}`)},Oa=(e,a)=>ka(`${e}/${a}`),La=()=>{var e;const a=h(),i=E(),[r,s]=f.useState(!0),[t,o]=f.useState([]),[c,u]=f.useState(0),[p,b]=f.useState({}),[N,g]=f.useState(1200),[v,j]=f.useState(!1),V=null==(e=i.state)?void 0:e.patientId,C={1:"4",2:"4",3:"4",4:"3",5:"2",6:"4",7:"3",8:"3",9:"1",10:"4",11:"3",12:"1",13:"2",14:"2",15:"3",16:"2",17:"1",18:"3",19:"3",20:"4",21:"3",22:"2",23:"2",24:"1",25:"3",26:"1",27:"1",28:"1",29:"3",30:"4",31:"3",32:"2"},y={1:"a",2:"b",3:"c",4:"d"},D={a:"1",b:"2",c:"3",d:"4"};f.useEffect(()=>{m(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800));const e=Array.from({length:32},(e,a)=>({id:a+1,type:"series",imagePath:Oa("razonamiento",`Racionamiento${a+1}.png`),options:[{id:"a",text:"Opción A"},{id:"b",text:"Opción B"},{id:"c",text:"Opción C"},{id:"d",text:"Opción D"}],correctAnswer:y[C[a+1]]}));o(e),s(!1)}catch(e){d.error("Error al cargar las preguntas del test"),s(!1)}})},[]),f.useEffect(()=>{if(!v||N<=0)return;const e=setInterval(()=>{g(a=>a<=1?(clearInterval(e),B(),0):a-1)},1e3);return()=>clearInterval(e)},[v,N]);const B=()=>m(null,null,function*(){try{const i=Object.keys(p).length,r=t.length,s=r-i;let o=0;Object.entries(p).forEach(([e,a])=>{C[e]===D[a]&&o++});const n=i-o,l=1200-N,c={correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,testType:"razonamiento"};if(V)try{yield Sa.saveTestResult({patientId:V,testType:"razonamiento",correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,answers:p,errores:n})}catch(e){}d.success(`Test completado. Has respondido ${i} de ${r} preguntas. Respuestas correctas: ${o}`),a("/test/results/razonamiento",{state:c})}catch(e){d.error("Error al procesar los resultados del test")}}),w=e=>{const a=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},A=t[c],R=!!A&&p[A.id];return x.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("div",{className:"text-center mb-4",children:[x.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[x.jsxDEV("i",{className:"fas fa-puzzle-piece mr-2 text-amber-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:208,columnNumber:13},void 0),"Test de Razonamiento"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:207,columnNumber:11},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Continuar series lógicas de figuras"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:211,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:206,columnNumber:9},void 0),v&&x.jsxDEV("div",{className:"text-center",children:x.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:w(N)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:215,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:214,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:205,columnNumber:7},void 0),r?x.jsxDEV("div",{className:"py-16 text-center",children:x.jsxDEV("div",{className:"flex flex-col items-center justify-center",children:[x.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:225,columnNumber:13},void 0),x.jsxDEV("p",{className:"text-gray-500",children:"Cargando test de razonamiento..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:226,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:224,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:223,columnNumber:9},void 0):v?x.jsxDEV(x.Fragment,{children:x.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[x.jsxDEV("div",{className:"md:col-span-3",children:x.jsxDEV(va,{className:"mb-6",children:[x.jsxDEV(ja,{className:"flex justify-between items-center",children:[x.jsxDEV("div",{children:[x.jsxDEV("h2",{className:"text-lg font-medium",children:["Pregunta ",c+1," de ",t.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:319,columnNumber:21},void 0),x.jsxDEV("p",{className:"text-sm text-gray-500",children:A?(T=A.type,"series"===T?"Series":T):""},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:322,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:318,columnNumber:19},void 0),x.jsxDEV("div",{className:"text-sm text-gray-500",children:R?"Respondida":"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:326,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:317,columnNumber:17},void 0),x.jsxDEV(Va,{children:A&&x.jsxDEV(x.Fragment,{children:[x.jsxDEV("div",{className:"flex justify-center mb-6",children:x.jsxDEV("img",{src:A.imagePath,alt:`Pregunta ${A.id}`,className:"max-w-full h-auto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:334,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:333,columnNumber:23},void 0),x.jsxDEV("div",{className:"space-y-3",children:A.options.map(e=>x.jsxDEV("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(p[A.id]===e.id?"bg-amber-50 border-amber-500":"hover:bg-gray-50"),onClick:()=>{return a=A.id,i=e.id,void b(l(n({},p),{[a]:i}));var a,i},children:x.jsxDEV("div",{className:"flex items-center",children:[x.jsxDEV("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(p[A.id]===e.id?"bg-amber-500 text-white":"bg-gray-200 text-gray-700"),children:e.id.toUpperCase()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:352,columnNumber:31},void 0),x.jsxDEV("div",{className:"text-gray-700",children:e.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:359,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:351,columnNumber:29},void 0)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:342,columnNumber:27},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:340,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:332,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:330,columnNumber:17},void 0),x.jsxDEV(Ea,{className:"flex justify-between",children:[x.jsxDEV(Ca,{variant:"outline",onClick:()=>{c>0&&u(c-1)},disabled:0===c,children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:368,columnNumber:19},void 0),c<t.length-1?x.jsxDEV(Ca,{variant:"primary",onClick:()=>{c<t.length-1&&u(c+1)},className:"bg-amber-600 hover:bg-amber-700",children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:376,columnNumber:21},void 0):x.jsxDEV(Ca,{variant:"primary",onClick:B,className:"bg-amber-600 hover:bg-amber-700",children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:384,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:367,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:316,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:315,columnNumber:13},void 0),x.jsxDEV("div",{children:x.jsxDEV(va,{className:"sticky top-6",children:[x.jsxDEV(ja,{children:x.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:399,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:398,columnNumber:17},void 0),x.jsxDEV(Va,{children:[x.jsxDEV("div",{className:"grid grid-cols-4 gap-2",children:t.map((e,a)=>x.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(c===a?"bg-amber-500 text-white":p[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>u(a),title:`Pregunta ${a+1}`,children:a+1},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:404,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:402,columnNumber:19},void 0),x.jsxDEV("div",{className:"mt-6",children:[x.jsxDEV("div",{className:"flex items-center justify-between mb-2 text-sm",children:[x.jsxDEV("span",{children:"Progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:423,columnNumber:23},void 0),x.jsxDEV("span",{children:[Object.keys(p).length," de ",t.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:424,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:422,columnNumber:21},void 0),x.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:x.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(p).length/t.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:427,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:426,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:421,columnNumber:19},void 0),x.jsxDEV("div",{className:"mt-6",children:x.jsxDEV("div",{className:"bg-amber-50 p-3 rounded-lg border border-amber-100 mb-4",children:[x.jsxDEV("h3",{className:"text-sm font-medium text-amber-700 mb-1",children:"Información"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:436,columnNumber:23},void 0),x.jsxDEV("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",x.jsxDEV("span",{className:"font-medium",children:w(N)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:438,columnNumber:42},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:437,columnNumber:23},void 0),x.jsxDEV("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:440,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:435,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:434,columnNumber:19},void 0),x.jsxDEV(Ca,{variant:"primary",className:"w-full mt-2 bg-amber-600 hover:bg-amber-700",onClick:B,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:446,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:401,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:397,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:396,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:314,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:313,columnNumber:9},void 0):x.jsxDEV(va,{children:[x.jsxDEV(ja,{children:x.jsxDEV("h2",{className:"text-xl font-semibold text-gray-800",children:"Razonamiento: Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:232,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:231,columnNumber:11},void 0),x.jsxDEV(Va,{children:x.jsxDEV("div",{className:"space-y-6",children:[x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Razonamiento?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:237,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-2",children:"El razonamiento es la capacidad para identificar patrones, relaciones y reglas lógicas en series de figuras o dibujos. Esta habilidad es fundamental para resolver problemas, tomar decisiones y aprender nuevos conceptos."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:238,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:236,columnNumber:15},void 0),x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:244,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-3",children:"En esta prueba se trabaja con series de figuras o dibujos, ordenados de acuerdo con una ley. Tu tarea consistirá en averiguar la ley que ordena las figuras y elegir entre las opciones de respuesta la que continúa la serie."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:245,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-3",children:"En todos los ejercicios se presenta la serie en la parte superior y las opciones de respuesta en la parte inferior. Cuando hayas decidido qué opción es la única correcta, selecciona la letra correspondiente (A, B, C o D)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:248,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:243,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-orange-700 mb-2",children:"Ejemplos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:254,columnNumber:17},void 0),x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("p",{className:"text-gray-600 mb-3",children:x.jsxDEV("strong",{className:"text-blue-600",children:"Ejemplo R1:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:258,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:257,columnNumber:19},void 0),x.jsxDEV("div",{className:"flex justify-center mb-4",children:x.jsxDEV("img",{src:"/assets/images/razonamiento/R1.png",alt:"Ejemplo R1",className:"max-w-full h-auto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:261,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:260,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-gray-600 mt-3",children:"En este ejemplo se presenta una figura que va girando 90 grados hacia la derecha de una casilla a otra. ¿Cuál debería ser la próxima figura de la serie?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:263,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es la ",x.jsxDEV("strong",{children:"D"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:267,columnNumber:49},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:266,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:256,columnNumber:17},void 0),x.jsxDEV("div",{children:[x.jsxDEV("p",{className:"text-gray-600 mb-3",children:x.jsxDEV("strong",{className:"text-blue-600",children:"Ejemplo R2:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:273,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:272,columnNumber:19},void 0),x.jsxDEV("div",{className:"flex justify-center mb-4",children:x.jsxDEV("img",{src:"/assets/images/razonamiento/R2.png",alt:"Ejemplo R2",className:"max-w-full h-auto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:276,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:275,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:271,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:253,columnNumber:15},void 0),x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:282,columnNumber:17},void 0),x.jsxDEV("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[x.jsxDEV("li",{children:"El test consta de 32 preguntas de series lógicas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:284,columnNumber:19},void 0),x.jsxDEV("li",{children:["Dispondrás de ",x.jsxDEV("span",{className:"font-medium",children:"20 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:285,columnNumber:37},void 0)," para completar todas las preguntas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:285,columnNumber:19},void 0),x.jsxDEV("li",{children:"Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:286,columnNumber:19},void 0),x.jsxDEV("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:287,columnNumber:19},void 0),x.jsxDEV("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:288,columnNumber:19},void 0),x.jsxDEV("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:289,columnNumber:19},void 0),x.jsxDEV("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:290,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:283,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:281,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:295,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:296,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:294,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:235,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:234,columnNumber:11},void 0),x.jsxDEV(Ea,{className:"flex justify-end",children:x.jsxDEV(Ca,{variant:"primary",onClick:()=>{j(!0),d.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2 bg-amber-600 hover:bg-amber-700",children:"Comenzar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:303,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:302,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:230,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:204,columnNumber:5},void 0);var T},$a=Object.freeze(Object.defineProperty({__proto__:null,default:La},Symbol.toStringTag,{value:"Module"})),Fa=()=>{var e;const a=h(),i=E(),[r,s]=f.useState(!0),[t,o]=f.useState([]),[c,u]=f.useState(0),[p,b]=f.useState({}),[N,g]=f.useState(480),[v,j]=f.useState(!1),V=null==(e=i.state)?void 0:e.patientId,C=10,y={1:3,2:3,3:2,4:1,5:2,6:3,7:2,8:2,9:4,10:2,11:4,12:1,13:4,14:2,15:4,16:2,17:2,18:3,19:2,20:3,21:4,22:2,23:3,24:2,25:3,26:3,27:1,28:2,29:1,30:2,31:3,32:3,33:4,34:1,35:4,36:3,37:1,38:2,39:4,40:1,41:1,42:4,43:2,44:3,45:2,46:1,47:2,48:3,49:1,50:3,51:1,52:4,53:1,54:1,55:1,56:3,57:3,58:2,59:1,60:4,61:4,62:3,63:2,64:3,65:2,66:4,67:3,68:1,69:2,70:4,71:3,72:3,73:3,74:1,75:1,76:2,77:2,78:4,79:1,80:1};f.useEffect(()=>{m(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800));const e=Array.from({length:80},(e,a)=>({id:a+1,type:"attention",imageUrl:Oa("atencion",`Atencion${a+1}.png`),options:[{id:"0",text:"0 veces"},{id:"1",text:"1 vez"},{id:"2",text:"2 veces"},{id:"3",text:"3 veces"},{id:"4",text:"4 veces"}],correctAnswer:y[a+1]?y[a+1].toString():"0"}));o(e),s(!1)}catch(e){d.error("Error al cargar las preguntas del test"),s(!1)}})},[]),f.useEffect(()=>{if(!v||N<=0)return;const e=setInterval(()=>{g(a=>a<=1?(clearInterval(e),D(),0):a-1)},1e3);return()=>clearInterval(e)},[v,N]);const D=()=>m(null,null,function*(){try{const i=Object.keys(p).length,r=t.length,s=r-i;let o=0;Object.entries(p).forEach(([e,a])=>{const i=t.find(a=>a.id.toString()===e);i&&a===i.correctAnswer&&o++});const n=i-o,l=480-N,c={correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,testType:"atencion"};if(V)try{yield Sa.saveTestResult({patientId:V,testType:"atencion",correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,answers:p,errores:n})}catch(e){}d.success(`Test completado. Has respondido ${i} de ${r} preguntas. Respuestas correctas: ${o}`),a("/test/results/atencion",{state:c})}catch(e){d.error("Error al procesar los resultados del test")}}),B=e=>{const a=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},w=Math.ceil(t.length/C);return x.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("div",{className:"text-center mb-4",children:[x.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[x.jsxDEV("i",{className:"fas fa-eye mr-2 text-purple-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:204,columnNumber:13},void 0),"Test de Atención y Concentración"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:203,columnNumber:11},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Localización de símbolos específicos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:207,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:202,columnNumber:9},void 0),v&&x.jsxDEV("div",{className:"text-center",children:x.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:B(N)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:211,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:210,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:201,columnNumber:7},void 0),r?x.jsxDEV("div",{className:"py-16 text-center",children:x.jsxDEV("div",{className:"flex flex-col items-center justify-center",children:[x.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:221,columnNumber:13},void 0),x.jsxDEV("p",{className:"text-gray-500",children:"Cargando test de atención..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:222,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:220,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:219,columnNumber:9},void 0):v?t.length>0?x.jsxDEV(x.Fragment,{children:x.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[x.jsxDEV("div",{className:"md:col-span-3",children:[x.jsxDEV(va,{className:"mb-6",children:[x.jsxDEV(ja,{className:"flex justify-between items-center",children:[x.jsxDEV("div",{children:[x.jsxDEV("h2",{className:"text-lg font-medium",children:["Página ",c+1," de ",w]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:320,columnNumber:21},void 0),x.jsxDEV("p",{className:"text-sm text-gray-500",children:["Preguntas ",c*C+1," - ",Math.min((c+1)*C,t.length)," de ",t.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:323,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:319,columnNumber:19},void 0),x.jsxDEV("div",{className:"text-sm text-gray-500",children:["Tiempo restante: ",B(N)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:327,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:318,columnNumber:17},void 0),x.jsxDEV(Va,{children:x.jsxDEV("div",{className:"space-y-8",children:(()=>{const e=c*C,a=e+C;return t.slice(e,a)})().map(e=>x.jsxDEV("div",{className:"border-b pb-6 mb-6 last:border-b-0 last:pb-0 last:mb-0",children:x.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-start gap-4",children:[x.jsxDEV("div",{className:"md:w-3/4",children:[x.jsxDEV("h3",{className:"text-md font-medium mb-3",children:["Pregunta ",e.id]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:337,columnNumber:25},void 0),x.jsxDEV("div",{className:"mb-4",children:x.jsxDEV("img",{src:e.imageUrl,alt:`Pregunta ${e.id}`,className:"w-full max-w-2xl h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/600x100?text=Imagen+no+disponible"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:339,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:338,columnNumber:25},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-3",children:"¿Cuántas veces aparece el símbolo modelo en esta fila?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:349,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:336,columnNumber:23},void 0),x.jsxDEV("div",{className:"md:w-1/4",children:x.jsxDEV("div",{className:"space-y-2",children:e.options.map(a=>x.jsxDEV("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(p[e.id]===a.id?"bg-indigo-50 border-indigo-500":"hover:bg-gray-50"),onClick:()=>{return i=e.id,r=a.id,void b(l(n({},p),{[i]:r}));var i,r},children:x.jsxDEV("div",{className:"flex items-center",children:[x.jsxDEV("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(p[e.id]===a.id?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"),children:a.id},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:364,columnNumber:33},void 0),x.jsxDEV("div",{className:"text-gray-700",children:a.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:371,columnNumber:33},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:363,columnNumber:31},void 0)},a.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:354,columnNumber:29},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:352,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:351,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:335,columnNumber:21},void 0)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:334,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:332,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:331,columnNumber:13},void 0),x.jsxDEV(Ea,{className:"flex justify-between",children:[x.jsxDEV(Ca,{variant:"outline",onClick:()=>{c>0&&(u(c-1),window.scrollTo(0,0))},disabled:0===c,children:"Página Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:383,columnNumber:15},void 0),x.jsxDEV("div",{className:"flex space-x-2",children:[Array.from({length:w},(e,a)=>x.jsxDEV("button",{className:"w-8 h-8 rounded-full text-sm "+(c===a?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),onClick:()=>u(a),children:a+1},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:392,columnNumber:19},void 0)).slice(Math.max(0,c-2),Math.min(w,c+3)),c+3<w&&x.jsxDEV("span",{className:"self-center",children:"..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:404,columnNumber:50},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:390,columnNumber:15},void 0),c<w-1?x.jsxDEV(Ca,{variant:"primary",onClick:()=>{(c+1)*C<t.length&&(u(c+1),window.scrollTo(0,0))},children:"Página Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:407,columnNumber:17},void 0):x.jsxDEV(Ca,{variant:"primary",onClick:D,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:414,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:382,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:317,columnNumber:15},void 0),x.jsxDEV("div",{className:"flex justify-between items-center",children:[x.jsxDEV("div",{children:[x.jsxDEV("p",{className:"text-sm text-gray-600",children:["Has respondido ",Object.keys(p).length," de ",t.length," preguntas (",Math.round(Object.keys(p).length/t.length*100),"%)"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:426,columnNumber:15},void 0),x.jsxDEV("div",{className:"w-64 bg-gray-200 rounded-full h-2 mt-1",children:x.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(p).length/t.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:430,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:429,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:425,columnNumber:13},void 0),x.jsxDEV(Ca,{variant:"primary",onClick:D,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:436,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:424,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:316,columnNumber:13},void 0),x.jsxDEV("div",{children:x.jsxDEV(va,{className:"sticky top-6",children:[x.jsxDEV(ja,{children:x.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:449,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:448,columnNumber:13},void 0),x.jsxDEV(Va,{children:[x.jsxDEV("div",{className:"grid grid-cols-4 gap-2 max-h-64 overflow-y-auto p-1",children:t.map((e,a)=>{const i=Math.floor(a/C),r=i===c;return x.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(p[e.id]?"bg-green-100 text-green-800 border border-green-300":r?"bg-blue-100 text-blue-800 border border-blue-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>{const e=Math.floor(a/C);u(e)},title:`Pregunta ${a+1} - Página ${i+1}`,children:a+1},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:457,columnNumber:21},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:452,columnNumber:15},void 0),x.jsxDEV("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[x.jsxDEV("div",{className:"text-sm text-gray-600 mb-2",children:["Progreso: ",Object.keys(p).length,"/",t.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:479,columnNumber:17},void 0),x.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-3",children:x.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-300",style:{width:Object.keys(p).length/t.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:483,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:482,columnNumber:17},void 0),x.jsxDEV(Ca,{variant:"primary",className:"w-full",onClick:D,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:489,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:478,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:451,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:447,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:446,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:315,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:314,columnNumber:9},void 0):x.jsxDEV(va,{children:x.jsxDEV(Va,{children:x.jsxDEV("div",{className:"py-8 text-center",children:[x.jsxDEV("p",{className:"text-gray-500",children:"No se encontraron preguntas para este test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:506,columnNumber:15},void 0),x.jsxDEV(Ca,{variant:"outline",className:"mt-4",onClick:()=>a("/student/tests"),children:"Volver a Tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:507,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:505,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:504,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:503,columnNumber:9},void 0):x.jsxDEV(va,{children:[x.jsxDEV(ja,{children:x.jsxDEV("h2",{className:"text-xl font-semibold text-gray-800",children:"Test de Atención: Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:228,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:227,columnNumber:11},void 0),x.jsxDEV(Va,{children:x.jsxDEV("div",{className:"space-y-6",children:[x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Test de Atención?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:233,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-2",children:"El test de atención evalúa tu capacidad para mantener la concentración y detectar estímulos específicos entre un conjunto de elementos similares. Esta habilidad es fundamental para el aprendizaje, el trabajo y muchas actividades cotidianas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:234,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Esta prueba mide específicamente tu atención selectiva, velocidad perceptiva, discriminación visual y capacidad de concentración sostenida."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:237,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:232,columnNumber:15},void 0),x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:243,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-3",children:"Esta prueba trata de evaluar tu rapidez y tu precisión trabajando con símbolos. En cada ejercicio aparece una fila con diferentes símbolos y tu tarea consistirá en localizar cuántas veces aparece uno determinado."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:244,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-3",children:"El símbolo que tienes que localizar es siempre el mismo y se presenta en la parte superior de la página; en cada ejercicio puede aparecer 0, 1, 2, 3 o 4 veces."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:247,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Deberás seleccionar cuántas veces aparece el símbolo en cada fila (0, 1, 2, 3 o 4) asegurándote de que tu respuesta se corresponda con el número del ejercicio que estás contestando."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:250,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:242,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-indigo-700 mb-2",children:"Ejemplos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:256,columnNumber:17},void 0),x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("div",{className:"flex justify-center mb-3",children:x.jsxDEV("img",{src:Oa("atencion","Atencion.png"),alt:"Ejemplos de atención",className:"max-w-md h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/300x150?text=Imagen+no+disponible"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:260,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:259,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-gray-600 mt-3",children:[x.jsxDEV("strong",{className:"text-indigo-600",children:"Ejemplo A1:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:271,columnNumber:21},void 0)," El símbolo del óvalo aparece una única vez, y es el tercer símbolo de la fila. Por eso la respuesta correcta es ",x.jsxDEV("strong",{children:"1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:271,columnNumber:190},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:270,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-gray-600 mt-2",children:[x.jsxDEV("strong",{className:"text-indigo-600",children:"Ejemplo A2:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:274,columnNumber:21},void 0)," En esta ocasión no hay ningún símbolo que coincida exactamente con el modelo; por tanto la respuesta correcta es ",x.jsxDEV("strong",{children:"0"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:274,columnNumber:191},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:273,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-gray-600 mt-2",children:[x.jsxDEV("strong",{className:"text-indigo-600",children:"Ejemplo A3:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:277,columnNumber:21},void 0)," El símbolo del óvalo aparece en dos ocasiones, en primera y quinta posición. Por eso, la respuesta correcta es ",x.jsxDEV("strong",{children:"2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:277,columnNumber:189},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:276,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:258,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:255,columnNumber:15},void 0),x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:283,columnNumber:17},void 0),x.jsxDEV("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[x.jsxDEV("li",{children:"El test consta de 80 preguntas de atención."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:285,columnNumber:19},void 0),x.jsxDEV("li",{children:["Dispondrás de ",x.jsxDEV("span",{className:"font-medium",children:"8 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:286,columnNumber:37},void 0)," para completar todas las preguntas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:286,columnNumber:19},void 0),x.jsxDEV("li",{children:"Las preguntas se presentan en páginas de 10 preguntas cada una."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:287,columnNumber:19},void 0),x.jsxDEV("li",{children:"Puedes navegar libremente entre las páginas y modificar tus respuestas durante el tiempo disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:288,columnNumber:19},void 0),x.jsxDEV("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:289,columnNumber:19},void 0),x.jsxDEV("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:290,columnNumber:19},void 0),x.jsxDEV("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:291,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:284,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:282,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:296,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:297,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:295,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:231,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:230,columnNumber:11},void 0),x.jsxDEV(Ea,{className:"flex justify-end",children:x.jsxDEV(Ca,{variant:"primary",onClick:()=>{j(!0),d.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2",children:"Comenzar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:304,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:303,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:226,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:200,columnNumber:5},void 0)},Qa=Object.freeze(Object.defineProperty({__proto__:null,default:Fa},Symbol.toStringTag,{value:"Module"})),Ua=()=>{var e;const a=h(),i=E(),[r,s]=f.useState(!0),[t,o]=f.useState([]),[c,u]=f.useState(0),[p,b]=f.useState({}),[N,g]=f.useState(900),[v,j]=f.useState(!1),V=null==(e=i.state)?void 0:e.patientId,C={1:"C",2:"D",3:"B",4:"A",5:"A",6:"A",7:"D",8:"B",9:"D",10:"D",11:"C",12:"A",13:"D",14:"A",15:"A",16:"B",17:"C",18:"A",19:"C",20:"D",21:"D",22:"C",23:"D",24:"B",25:"C",26:"C",27:"D",28:"C"};f.useEffect(()=>{m(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800));const e=Array.from({length:28},(e,a)=>({id:a+1,type:"spatial",imageUrl:Oa("espacial",`Espacial${a+1}.png`),options:[{id:"A",text:"Opción A"},{id:"B",text:"Opción B"},{id:"C",text:"Opción C"},{id:"D",text:"Opción D"}],correctAnswer:C[a+1]}));o(e),s(!1)}catch(e){d.error("Error al cargar las preguntas del test"),s(!1)}})},[]),f.useEffect(()=>{if(!v||N<=0)return;const e=setInterval(()=>{g(a=>a<=1?(clearInterval(e),y(),0):a-1)},1e3);return()=>clearInterval(e)},[v,N]);const y=()=>m(null,null,function*(){try{const i=Object.keys(p).length,r=t.length,s=r-i;let o=0;Object.entries(p).forEach(([e,a])=>{const i=t.find(a=>a.id.toString()===e);i&&a===i.correctAnswer&&o++});const n=i-o,l=900-N,c={correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,testType:"espacial"};if(V)try{yield Sa.saveTestResult({patientId:V,testType:"espacial",correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,answers:p,errores:n})}catch(e){}d.success(`Test completado. Has respondido ${i} de ${r} preguntas. Respuestas correctas: ${o}`),a("/test/results/espacial",{state:c})}catch(e){d.error("Error al procesar los resultados del test")}}),D=e=>{const a=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},B=t[c],w=!!B&&p[B.id];return x.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("div",{className:"text-center mb-4",children:[x.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[x.jsxDEV("i",{className:"fas fa-cube mr-2 text-indigo-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:189,columnNumber:13},void 0),"Test de Aptitud Espacial"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:188,columnNumber:11},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Razonamiento espacial con cubos y redes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:192,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:187,columnNumber:9},void 0),v&&x.jsxDEV("div",{className:"text-center",children:x.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:D(N)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:196,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:195,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:186,columnNumber:7},void 0),r?x.jsxDEV("div",{className:"py-16 text-center",children:x.jsxDEV("div",{className:"flex flex-col items-center justify-center",children:[x.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:206,columnNumber:13},void 0),x.jsxDEV("p",{className:"text-gray-500",children:"Cargando test de razonamiento espacial..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:207,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:205,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:204,columnNumber:9},void 0):v?t.length>0?x.jsxDEV(x.Fragment,{children:x.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[x.jsxDEV("div",{className:"md:col-span-3",children:x.jsxDEV(va,{className:"mb-6",children:[x.jsxDEV(ja,{className:"flex justify-between items-center",children:[x.jsxDEV("div",{children:[x.jsxDEV("h2",{className:"text-lg font-medium",children:["Pregunta ",c+1," de ",t.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:322,columnNumber:21},void 0),x.jsxDEV("p",{className:"text-sm text-gray-500",children:B?(A=B.type,"spatial"===A?"Razonamiento Espacial":A):""},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:325,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:321,columnNumber:19},void 0),x.jsxDEV("div",{className:"text-sm text-gray-500",children:w?"Respondida":"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:329,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:320,columnNumber:17},void 0),x.jsxDEV(Va,{children:B&&x.jsxDEV(x.Fragment,{children:[x.jsxDEV("div",{className:"flex justify-center mb-6",children:x.jsxDEV("img",{src:B.imageUrl,alt:`Pregunta ${c+1}`,className:"max-w-full h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/600x300?text=Imagen+no+disponible"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:337,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:336,columnNumber:23},void 0),x.jsxDEV("div",{className:"space-y-3",children:B.options.map(e=>x.jsxDEV("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(p[B.id]===e.id?"bg-indigo-50 border-indigo-500":"hover:bg-gray-50"),onClick:()=>{return a=B.id,i=e.id,void b(l(n({},p),{[a]:i}));var a,i},children:x.jsxDEV("div",{className:"flex items-center",children:[x.jsxDEV("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(p[B.id]===e.id?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"),children:e.id.toUpperCase()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:359,columnNumber:31},void 0),x.jsxDEV("div",{className:"text-gray-700",children:e.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:366,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:358,columnNumber:29},void 0)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:349,columnNumber:27},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:347,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:335,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:333,columnNumber:17},void 0),x.jsxDEV(Ea,{className:"flex justify-between",children:[x.jsxDEV(Ca,{variant:"outline",onClick:()=>{c>0&&u(c-1)},disabled:0===c,children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:375,columnNumber:19},void 0),c<t.length-1?x.jsxDEV(Ca,{variant:"primary",onClick:()=>{c<t.length-1&&u(c+1)},children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:383,columnNumber:21},void 0):x.jsxDEV(Ca,{variant:"primary",onClick:y,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:390,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:374,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:319,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:318,columnNumber:13},void 0),x.jsxDEV("div",{children:x.jsxDEV(va,{className:"sticky top-6",children:[x.jsxDEV(ja,{children:x.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:404,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:403,columnNumber:17},void 0),x.jsxDEV(Va,{children:[x.jsxDEV("div",{className:"grid grid-cols-4 gap-2 max-h-64 overflow-y-auto p-1",children:t.map((e,a)=>x.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(c===a?"bg-indigo-500 text-white":p[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>u(a),title:`Pregunta ${a+1}`,children:a+1},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:409,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:407,columnNumber:19},void 0),x.jsxDEV("div",{className:"mt-6",children:[x.jsxDEV("div",{className:"flex items-center justify-between mb-2 text-sm",children:[x.jsxDEV("span",{children:"Progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:428,columnNumber:23},void 0),x.jsxDEV("span",{children:[Object.keys(p).length," de ",t.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:429,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:427,columnNumber:21},void 0),x.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:x.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(p).length/t.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:432,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:431,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:426,columnNumber:19},void 0),x.jsxDEV("div",{className:"mt-6",children:x.jsxDEV("div",{className:"bg-indigo-50 p-3 rounded-lg border border-indigo-100 mb-4",children:[x.jsxDEV("h3",{className:"text-sm font-medium text-indigo-700 mb-1",children:"Información"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:441,columnNumber:23},void 0),x.jsxDEV("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",x.jsxDEV("span",{className:"font-medium",children:D(N)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:443,columnNumber:42},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:442,columnNumber:23},void 0),x.jsxDEV("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:445,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:440,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:439,columnNumber:19},void 0),x.jsxDEV(Ca,{variant:"primary",className:"w-full mt-2",onClick:y,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:451,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:406,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:402,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:401,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:317,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:316,columnNumber:9},void 0):x.jsxDEV(va,{children:x.jsxDEV(Va,{children:x.jsxDEV("div",{className:"py-8 text-center",children:[x.jsxDEV("p",{className:"text-gray-500",children:"No se encontraron preguntas para este test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:467,columnNumber:15},void 0),x.jsxDEV(Ca,{variant:"outline",className:"mt-4",onClick:()=>a("/student/tests"),children:"Volver a Tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:468,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:466,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:465,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:464,columnNumber:9},void 0):x.jsxDEV(va,{children:[x.jsxDEV(ja,{children:x.jsxDEV("h2",{className:"text-xl font-semibold text-gray-800",children:"Razonamiento Espacial: Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:213,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:212,columnNumber:11},void 0),x.jsxDEV(Va,{children:x.jsxDEV("div",{className:"space-y-6",children:[x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Razonamiento Espacial?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:218,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-2",children:"El razonamiento espacial es la capacidad para visualizar y manipular objetos mentalmente en el espacio tridimensional. Implica entender cómo se relacionan las formas y los objetos entre sí, y cómo se transforman cuando cambian de posición o perspectiva."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:219,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Esta habilidad es fundamental en campos como la arquitectura, ingeniería, diseño, matemáticas y ciencias, siendo especialmente relevante para carreras que requieren visualización y manipulación de objetos en el espacio."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:222,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:217,columnNumber:15},void 0),x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:228,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-3",children:"En este test encontrarás un cubo junto con un modelo desplegado del mismo cubo. En el modelo desplegado falta una cara, marcada con un signo de interrogación (?)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:229,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-3",children:"Tu tarea consistirá en averiguar qué opción (A, B, C o D) debería aparecer en lugar del interrogante para que el modelo desplegado corresponda al cubo cuando se pliega."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:232,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Para facilitar tu tarea, en el cubo se han representado en color gris los números o letras que se encuentran en las caras de atrás (las que no se ven directamente)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:235,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:227,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-indigo-700 mb-2",children:"Ejemplos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:241,columnNumber:17},void 0),x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("p",{className:"text-gray-600 mb-3",children:x.jsxDEV("strong",{className:"text-indigo-600",children:"Ejemplo 1:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:245,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:244,columnNumber:19},void 0),x.jsxDEV("div",{className:"flex justify-center mb-3",children:x.jsxDEV("img",{src:Oa("espacial","Modelo Espacial.png"),alt:"Ejemplo 1",className:"max-w-full h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/400x200?text=Ejemplo+1"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:248,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:247,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",x.jsxDEV("strong",{children:"B"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:259,columnNumber:46},void 0),". Si se sustituye el interrogante por la letra «h» y se pliegan las caras del modelo hasta formar el cubo, este se corresponde con el que aparece a la izquierda."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:258,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:243,columnNumber:17},void 0),x.jsxDEV("div",{children:[x.jsxDEV("p",{className:"text-gray-600 mb-3",children:x.jsxDEV("strong",{className:"text-indigo-600",children:"Ejemplo 2:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:265,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:264,columnNumber:19},void 0),x.jsxDEV("div",{className:"flex justify-center mb-3",children:x.jsxDEV("img",{src:Oa("espacial","Espacial1.png"),alt:"Ejemplo 2",className:"max-w-full h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/400x200?text=Ejemplo+2"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:268,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:267,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",x.jsxDEV("strong",{children:"A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:279,columnNumber:46},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:278,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:263,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:240,columnNumber:15},void 0),x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:285,columnNumber:17},void 0),x.jsxDEV("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[x.jsxDEV("li",{children:"El test consta de 28 preguntas de razonamiento espacial."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:287,columnNumber:19},void 0),x.jsxDEV("li",{children:["Dispondrás de ",x.jsxDEV("span",{className:"font-medium",children:"15 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:288,columnNumber:37},void 0)," para completar todas las preguntas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:288,columnNumber:19},void 0),x.jsxDEV("li",{children:"Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:289,columnNumber:19},void 0),x.jsxDEV("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:290,columnNumber:19},void 0),x.jsxDEV("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:291,columnNumber:19},void 0),x.jsxDEV("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:292,columnNumber:19},void 0),x.jsxDEV("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:293,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:286,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:284,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:298,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:299,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:297,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:216,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:215,columnNumber:11},void 0),x.jsxDEV(Ea,{className:"flex justify-end",children:x.jsxDEV(Ca,{variant:"primary",onClick:()=>{j(!0),d.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2",children:"Comenzar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:306,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:305,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:211,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:185,columnNumber:5},void 0);var A},Ha=Object.freeze(Object.defineProperty({__proto__:null,default:Ua},Symbol.toStringTag,{value:"Module"})),Ja=()=>{var e;const a=h(),i=E(),[r,s]=f.useState(!1),t=null==(e=i.state)?void 0:e.patientId,[o,c]=f.useState(0),[u,p]=f.useState({}),[b,N]=f.useState(720),[g,v]=f.useState(!1),j=[{id:1,question:"¿Qué tipo de polea podrá subir MÁS peso sin vencerse?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico1.png",options:["A","B","C","D"],correctAnswer:0},{id:2,question:"¿Qué estante es MENOS resistente?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico2.png",options:["A","B","C","D"],correctAnswer:2},{id:3,question:"¿Qué tipo de listones permite mover la carga con MENOS esfuerzo?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico3.png",options:["A","B","C","D"],correctAnswer:0},{id:4,question:"Si el viento sopla en la dirección indicada, ¿hacia dónde tendríamos que golpear la bola para acercarla MÁS al hoyo?",subtitle:"",image:"/assets/images/mecanico/mecanico4.png",options:["A","B","C","D"],correctAnswer:1},{id:5,question:"¿En qué zona (A, B o C) es MÁS probable que se rompan las cuerdas al colocar la carga?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico5.png",options:["A","B","C","D"],correctAnswer:1},{id:6,question:"¿De qué recipiente saldrá el líquido con MÁS fuerza?",subtitle:"",image:"/assets/images/mecanico/mecanico6.png",options:["A","B","C","D"],correctAnswer:2},{id:7,question:"¿Cuál de estos tres recipientes llenos de agua pesa MENOS?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico7.png",options:["A","B","C","D"],correctAnswer:3},{id:8,question:"¿Qué torno deberá dar MÁS vueltas para enrollar los mismos metros de cuerda?",subtitle:"",image:"/assets/images/mecanico/mecanico8.png",options:["A","B","C","D"],correctAnswer:3},{id:9,question:"¿Hacia qué dirección (A, B, C o D) está soplando el viento?",subtitle:"",image:"/assets/images/mecanico/mecanico9.png",options:["A","B","C","D"],correctAnswer:1},{id:10,question:"¿Cuál de estos tres tejados es MÁS probable que se rompa en caso de nevada?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico10.png",options:["A","B","C","D"],correctAnswer:0},{id:11,question:"¿A cuál de estas personas le costará MÁS esfuerzo trasladar la carga?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico11.png",options:["A","B","C","D"],correctAnswer:2},{id:12,question:"¿Con qué bomba se inflará MÁS lentamente un colchón flotador?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico12.png",options:["A","B","C","D"],correctAnswer:2},{id:13,question:"¿En qué caso se debe ejercer MENOS fuerza en el punto indicado por la flecha para sujetar el mismo peso?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico13.png",options:["A","B","C","D"],correctAnswer:0},{id:14,question:"Si al frenar la bicicleta solo se usan los frenos delanteros, ¿hacia qué dirección será impulsado el ciclista?",subtitle:"",image:"/assets/images/mecanico/mecanico14.png",options:["A","B","C","D"],correctAnswer:3},{id:15,question:"¿Cuál de estos tres pesos (A, B o C) pesa MENOS?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico15.png",options:["A","B","C","D"],correctAnswer:3},{id:16,question:"¿Qué columna será MÁS resistente en caso de terremoto?",subtitle:"",image:"/assets/images/mecanico/mecanico16.png",options:["A","B","C","D"],correctAnswer:1},{id:17,question:"¿Qué micrófono tiene MENOS probabilidad de caerse ante un golpe?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico17.png",options:["A","B","C","D"],correctAnswer:0},{id:18,question:"¿Qué trayectoria (A, B o C) debe seguir el nadador para cruzar el río con MENOS esfuerzo?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico18.png",options:["A","B","C","D"],correctAnswer:1},{id:19,question:"¿En qué punto es necesario ejercer MÁS fuerza para cerrar la puerta?",subtitle:"",image:"/assets/images/mecanico/mecanico19.png",options:["A","B","C","D"],correctAnswer:0},{id:20,question:"¿En qué caso habrá que ejercer MENOS fuerza para levantar las ruedas delanteras del carro?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico20.png",options:["A","B","C","D"],correctAnswer:2},{id:21,question:"¿Qué coche ofrece MENOS resistencia al aire?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico21.png",options:["A","B","C","D"],correctAnswer:2},{id:22,question:"¿Cómo debe agarrarse la persona a la roca para que no la arrastre la corriente?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico22.png",options:["A","B","C","D"],correctAnswer:2},{id:23,question:"Si tenemos estas tres linternas, ¿cuál iluminará un área MAYOR?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico23.png",options:["A","B","C","D"],correctAnswer:2},{id:24,question:"¿Qué coche es MENOS probable que vuelque?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico24.png",options:["A","B","C","D"],correctAnswer:2},{id:25,question:"¿En qué punto alcanzará MÁS velocidad el paracaidista?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico25.png",options:["A","B","C","D"],correctAnswer:2},{id:26,question:"Si dejáramos tan solo UNO de los bloques (A, B, C o D), ¿con cuál se mantendría la estructura en equilibrio?",subtitle:"",image:"/assets/images/mecanico/mecanico26.png",options:["A","B","C","D"],correctAnswer:2},{id:27,question:"¿Hacia qué zona de la cápsula será impulsado el astronauta cuando la máquina gire en el sentido indicado por la flecha?",subtitle:"",image:"/assets/images/mecanico/mecanico27.png",options:["A","B","C","D"],correctAnswer:1},{id:28,question:"Si colgamos el peso de esta forma, ¿por cuál de los puntos (A, B o C) es MENOS probable que se rompa la madera?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico28.png",options:["A","B","C","D"],correctAnswer:2}];f.useEffect(()=>{let e;return r&&!g&&b>0&&(e=setInterval(()=>{N(e=>e<=1?(y(),0):e-1)},1e3)),()=>clearInterval(e)},[r,g,b]);const V=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`,C=e=>{c(e)},y=()=>m(null,null,function*(){try{v(!0);const i=Object.keys(u).length,r=j.length,s=r-i;let o=0;j.forEach(e=>{u[e.id]===e.correctAnswer&&o++});const n=i-o,l=720-b,c={correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,testType:"mecanico"};if(t)try{yield Sa.saveTestResult({patientId:t,testType:"mecanico",correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,answers:u,errores:n})}catch(e){}d.success(`Test completado. Has respondido ${i} de ${r} preguntas. Respuestas correctas: ${o}`),a("/test/results/mecanico",{state:c})}catch(e){d.error("Error al procesar los resultados del test")}}),D=j[o];return x.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("div",{className:"text-center mb-4",children:[x.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[x.jsxDEV("i",{className:"fas fa-cogs mr-2 text-orange-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:361,columnNumber:13},void 0),"Test de Aptitud Mecánica"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:360,columnNumber:11},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Evalúa tu comprensión de principios mecánicos y físicos básicos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:364,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:359,columnNumber:9},void 0),r&&x.jsxDEV("div",{className:"text-center",children:x.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:V(b)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:368,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:367,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:358,columnNumber:7},void 0),r?x.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[x.jsxDEV("div",{className:"md:col-span-3",children:x.jsxDEV(va,{className:"mb-6",children:[x.jsxDEV(ja,{className:"flex justify-between items-center",children:[x.jsxDEV("div",{children:[x.jsxDEV("h2",{className:"text-lg font-medium",children:["Pregunta ",o+1," de ",j.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:514,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-sm text-gray-500",children:"Aptitud Mecánica"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:517,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:513,columnNumber:15},void 0),x.jsxDEV("div",{className:"text-sm text-gray-500",children:void 0!==u[D.id]?"Respondida":"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:521,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:512,columnNumber:13},void 0),x.jsxDEV(Va,{children:[x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("h4",{className:"text-lg font-medium text-gray-800 mb-2",children:D.question},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:528,columnNumber:17},void 0),D.subtitle&&x.jsxDEV("p",{className:"text-sm text-gray-600 mb-4",children:D.subtitle},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:532,columnNumber:19},void 0),x.jsxDEV("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6 text-center",children:[x.jsxDEV("img",{src:D.image,alt:`Pregunta ${D.id}`,className:"max-w-full h-auto mx-auto",style:{maxHeight:"400px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:537,columnNumber:19},void 0),x.jsxDEV("div",{className:"text-gray-500 text-sm",style:{display:"none"},children:["[Imagen no disponible: ",D.image,"]"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:547,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:536,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:527,columnNumber:15},void 0),x.jsxDEV("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:D.options.map((e,a)=>x.jsxDEV("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors text-center "+(u[D.id]===a?"bg-blue-50 border-blue-500":"hover:bg-gray-50"),onClick:()=>{return e=D.id,i=a,void p(a=>l(n({},a),{[e]:i}));var e,i},children:[x.jsxDEV("div",{className:"w-8 h-8 flex items-center justify-center rounded-full mx-auto mb-2 "+(u[D.id]===a?"bg-blue-500 text-white":"bg-gray-200 text-gray-700"),children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:565,columnNumber:21},void 0),x.jsxDEV("div",{className:"text-sm text-gray-600",children:["Opción ",e]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:572,columnNumber:21},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:556,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:554,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:525,columnNumber:13},void 0),x.jsxDEV(Ea,{className:"flex justify-between",children:[x.jsxDEV(Ca,{variant:"outline",onClick:()=>C(Math.max(0,o-1)),disabled:0===o,children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:579,columnNumber:15},void 0),o<j.length-1?x.jsxDEV(Ca,{variant:"primary",onClick:()=>C(Math.min(j.length-1,o+1)),children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:587,columnNumber:17},void 0):x.jsxDEV(Ca,{variant:"primary",onClick:y,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:594,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:578,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:511,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:510,columnNumber:9},void 0),x.jsxDEV("div",{children:x.jsxDEV(va,{className:"sticky top-6",children:[x.jsxDEV(ja,{children:x.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:609,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:608,columnNumber:13},void 0),x.jsxDEV(Va,{children:[x.jsxDEV("div",{className:"grid grid-cols-4 gap-2",children:j.map((e,a)=>x.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(o===a?"bg-blue-500 text-white":void 0!==u[j[a].id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>C(a),title:`Pregunta ${a+1}`,children:a+1},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:614,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:612,columnNumber:15},void 0),x.jsxDEV("div",{className:"mt-6",children:[x.jsxDEV("div",{className:"flex items-center justify-between mb-2 text-sm",children:[x.jsxDEV("span",{children:"Progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:633,columnNumber:19},void 0),x.jsxDEV("span",{children:[Object.keys(u).length," de ",j.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:634,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:632,columnNumber:17},void 0),x.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:x.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(u).length/j.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:637,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:636,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:631,columnNumber:15},void 0),x.jsxDEV("div",{className:"mt-6",children:x.jsxDEV("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[x.jsxDEV("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:646,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",x.jsxDEV("span",{className:"font-medium",children:V(b)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:648,columnNumber:38},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:647,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-xs text-gray-600",children:"Observa cuidadosamente cada imagen antes de responder. Puedes cambiar tu respuesta antes de finalizar el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:650,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:645,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:644,columnNumber:15},void 0),x.jsxDEV(Ca,{variant:"primary",className:"w-full mt-2",onClick:y,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:656,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:611,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:607,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:606,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:509,columnNumber:7},void 0):x.jsxDEV(va,{children:[x.jsxDEV(ja,{children:x.jsxDEV("h2",{className:"text-xl font-semibold text-gray-800",children:"Aptitud Mecánica: Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:378,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:377,columnNumber:11},void 0),x.jsxDEV(Va,{children:x.jsxDEV("div",{className:"space-y-6",children:[x.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-blue-800 mb-3",children:"Confirmación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:384,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-700 mb-4",children:"He leído y acepto las instrucciones. Entiendo que una vez iniciado el test no podré pausarlo y deberé completarlo en su totalidad."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:385,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:383,columnNumber:15},void 0),x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-3",children:"Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:391,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-4",children:"En esta prueba aparecen varios tipos de situaciones sobre las cuales se te harán algunas preguntas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:392,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-4",children:"Lee atentamente cada pregunta, observa el dibujo y elige cuál de las opciones es la más adecuada."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:395,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-4",children:["Recuerda que solo existe ",x.jsxDEV("strong",{children:"UNA opción correcta"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:399,columnNumber:44},void 0),". Cuando hayas decidido qué opción es, marca la letra correspondiente (A, B, C o D), asegurándote de que coincida con el número del ejercicio que estás contestando."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:398,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:390,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[x.jsxDEV("h4",{className:"text-md font-medium text-blue-800 mb-3",children:"Ejemplo M1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:405,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-700 mb-4 font-medium",children:["¿Cuál de las tres botellas podría quitarse sin que se cayera la bandeja?",x.jsxDEV("br",{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:407,columnNumber:91},void 0),x.jsxDEV("span",{className:"text-sm text-gray-600",children:"(Si no hay diferencia, marca D)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:408,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:406,columnNumber:17},void 0),x.jsxDEV("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[x.jsxDEV("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-3 text-center",children:[x.jsxDEV("img",{src:"/assets/images/mecanico/m1.png",alt:"Ejemplo M1 - Bandeja en equilibrio",className:"max-w-full h-auto mx-auto",style:{maxHeight:"300px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:413,columnNumber:21},void 0),x.jsxDEV("div",{className:"text-gray-500 text-sm",style:{display:"none"},children:"[Imagen no disponible: Bandeja en equilibrio con 3 botellas]"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:423,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:412,columnNumber:19},void 0),x.jsxDEV("div",{className:"grid grid-cols-4 gap-2 mb-3",children:[x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:428,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"B"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:429,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"C"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:430,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:431,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:427,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-sm text-gray-600",children:[x.jsxDEV("strong",{children:"Solución:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:434,columnNumber:21},void 0)," En este ejemplo se presenta una bandeja en equilibrio sobre una mesa y encima de ella 3 botellas. Si se quitase la botella A o la botella B, la bandeja perdería el equilibrio y caería al suelo; si quitáramos la botella C la bandeja se mantendría en equilibrio. Por lo tanto, la solución al ejemplo M1 es ",x.jsxDEV("strong",{children:"C"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:434,columnNumber:352},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:433,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:411,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:404,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[x.jsxDEV("h4",{className:"text-md font-medium text-green-800 mb-3",children:"Ejemplo M2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:441,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-700 mb-4 font-medium",children:["Si los tres vehículos se están desplazando a 70 km/h, ¿cuál va MÁS rápido?",x.jsxDEV("br",{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:443,columnNumber:93},void 0),x.jsxDEV("span",{className:"text-sm text-gray-600",children:"(Si no hay diferencia, marca D)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:444,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:442,columnNumber:17},void 0),x.jsxDEV("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[x.jsxDEV("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-3 text-center",children:[x.jsxDEV("img",{src:"/assets/images/mecanico/m2.png",alt:"Ejemplo M2 - Tres vehículos a 70 km/h",className:"max-w-full h-auto mx-auto",style:{maxHeight:"300px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:449,columnNumber:21},void 0),x.jsxDEV("div",{className:"text-gray-500 text-sm",style:{display:"none"},children:"[Imagen no disponible: Tres vehículos a 70 km/h]"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:459,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:448,columnNumber:19},void 0),x.jsxDEV("div",{className:"grid grid-cols-4 gap-2 mb-3",children:[x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:464,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"B"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:465,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:466,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"D"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:467,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:463,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-sm text-gray-600",children:[x.jsxDEV("strong",{children:"Solución:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:470,columnNumber:21},void 0)," Al desplazarse los tres vehículos a la misma velocidad (70 km/h), los tres van igual de rápido. Por lo tanto, la solución a este ejemplo es la opción ",x.jsxDEV("strong",{children:"D"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:470,columnNumber:198},void 0)," (no hay diferencia)."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:469,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:447,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:440,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[x.jsxDEV("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Instrucciones para el Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:477,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-700 mb-4",children:["El tiempo máximo para la realización de esta prueba es de ",x.jsxDEV("strong",{children:"12 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:479,columnNumber:77},void 0),", por lo que deberás trabajar rápidamente, esforzándote al máximo en encontrar la respuesta correcta."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:478,columnNumber:17},void 0),x.jsxDEV("ul",{className:"list-disc pl-5 space-y-2 text-gray-700 mb-4",children:[x.jsxDEV("li",{children:"Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta de las que aparecen."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:482,columnNumber:19},void 0),x.jsxDEV("li",{children:[x.jsxDEV("strong",{children:"No se penalizará el error"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:483,columnNumber:23},void 0),", así que intenta responder todas las preguntas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:483,columnNumber:19},void 0),x.jsxDEV("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas, pero NO continúes con las demás pruebas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:484,columnNumber:19},void 0),x.jsxDEV("li",{children:"Puedes navegar libremente entre las preguntas durante el tiempo disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:485,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:481,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:476,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:490,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:491,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:489,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:381,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:380,columnNumber:11},void 0),x.jsxDEV(Ea,{className:"flex justify-end",children:x.jsxDEV(Ca,{variant:"primary",onClick:()=>{s(!0)},className:"px-6 py-2",children:"Comenzar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:498,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:497,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:376,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:357,columnNumber:5},void 0)},Ga=Object.freeze(Object.defineProperty({__proto__:null,default:Ja},Symbol.toStringTag,{value:"Module"})),Wa=[{id:1,type:"equality",question:"6 + 22 = 30 - ?",options:["2","8","10","12","28"],correct:0},{id:2,type:"equality",question:"18 - 6 = 16 - ?",options:["2","3","4","6","10"],correct:2},{id:3,type:"equality",question:"7² - 9 = ? x 2",options:["2","7","10","20","40"],correct:3},{id:4,type:"equality",question:"(6 + 8) x ? = 4 x 7",options:["2","3","4","7","10"],correct:0},{id:5,type:"equality",question:"(3 + 9) x 3 = (? x 2) x 6",options:["1","2","3","4","6"],correct:2},{id:6,type:"series",question:"23 • 18 • 14 • 11 • ?",options:["5","6","7","8","9"],correct:4},{id:7,type:"series",question:"9 • 11 • 10 • 12 • 11 • 13 • ?",options:["11","12","13","14","15"],correct:1},{id:8,type:"series",question:"2 • 6 • 11 • 17 • 24 • 32 • ?",options:["36","37","40","41","42"],correct:3},{id:9,type:"series",question:"21 • 23 • 20 • 24 • 19 • 25 • 18 • ?",options:["16","20","21","22","26"],correct:4},{id:10,type:"series",question:"16 • 8 • 16 • 20 • 10 • 20 • 24 • 12 • ?",options:["4","6","14","24","25"],correct:3},{id:11,type:"table",question:'Tabla: "Puntos obtenidos en la compra"',tableData:{headers:["Artículo","Unidades","Puntos/Unidad","Total puntos"],rows:[["Café","55","15","825"],["Galletas","?","6","240"],["Sal","20","5","100"],["","","","1.165"]]},questionText:"El interrogante (?) está en las Unidades de Galletas.",options:["4","40","60","75","234"],correct:1},{id:12,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Televisión","Altavoces","Auriculares","Total"],rows:[["Enero","?","(dato borrado)","35","85"],["Febrero","45","(dato borrado)","80","175"],["Marzo","60","45","(dato borrado)","(dato borrado)"],["Total","125","(dato borrado)","155","(dato borrado)"]]},questionText:"El interrogante (?) está en Televisión de Enero.",options:["10","20","25","30","50"],correct:1},{id:13,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Secadoras","Lavadoras","Frigoríficos","Total"],rows:[["Enero","(dato borrado)","(dato borrado)","30","90"],["Febrero","5","40","25","70"],["Marzo","(dato borrado)","30","35","105"],["Abril","50","45","?","(dato borrado)"],["Total","(dato borrado)","155","145","(dato borrado)"]]},questionText:"El interrogante (?) está en Frigoríficos de Abril.",options:["30","45","55","65","90"],correct:2},{id:14,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Televisión","Altavoces","Auriculares","Total"],rows:[["Abril","5","8","(dato borrado)","33"],["Mayo","(dato borrado)","15","5","30"],["Junio","10","(dato borrado)","(dato borrado)","(dato borrado)"],["Total","?","38","32","(dato borrado)"]]},questionText:"El interrogante (?) está en el Total de Televisión.",options:["10","15","20","25","30"],correct:3},{id:15,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Televisión","Altavoces","Auriculares","Total"],rows:[["Enero","20","(dato borrado)","15","(dato borrado)"],["Febrero","?","(dato borrado)","30","70"],["Marzo","20","(dato borrado)","30","(dato borrado)"],["Abril","(dato borrado)","15","10","55"],["Total","85","(dato borrado)","80","(dato borrado)"]]},questionText:"El interrogante (?) está en Televisión de Febrero.",options:["10","15","25","40","45"],correct:1},{id:16,type:"equality",question:"(30 : 5) : (14 : 7) = [(? x 5) + 3] : 11",options:["1","2","3","4","6"],correct:4},{id:17,type:"equality",question:"[(23 - 9) - 4] x 2 = [(? : 6) - 3] x 5",options:["7","20","24","30","42"],correct:4},{id:18,type:"equality",question:"20 + 35 - 14 = (? x 2) - 19",options:["11","25","30","35","60"],correct:2},{id:19,type:"equality",question:"(9 x 7) : (? - 2) = 9 + 7 + 5",options:["3","4","5","6","12"],correct:2},{id:20,type:"equality",question:"[(? : 7) - 3] : 2 = 21 : 7",options:["2","9","42","49","63"],correct:4},{id:21,type:"series",question:"14 • 11 • 15 • 12 • 17 • 14 • 20 • 17 • 24 • 21 • ?",options:["18","25","26","27","29"],correct:4},{id:22,type:"series",question:"2 • 8 • 4 • 16 • 8 • ?",options:["4","14","24","26","32"],correct:4},{id:23,type:"series",question:"5 • 6 • 8 • 7 • 10 • 14 • 13 • 18 • 24 • 23 • ?",options:["22","24","26","28","30"],correct:4},{id:24,type:"series",question:"11 • 13 • 16 • 15 • 19 • 24 • 22 • ?",options:["23","24","25","26","28"],correct:4},{id:25,type:"series",question:"3 • 6 • 4 • 8 • 6 • ?",options:["4","9","10","11","12"],correct:4},{id:26,type:"series",question:"3 • 2 • 6 • 4 • 12 • 8 • 24 • 16 • 48 • 32 • 96 • ?",options:["64","80","89","92","95"],correct:0},{id:27,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Plancha","Depiladora","Afeitadora","Total"],rows:[["Mayo","20","5","(dato borrado)","40"],["Junio","(dato borrado)","(dato borrado)","10","(dato borrado)"],["Abril","(dato borrado)","5","(dato borrado)","25"],["Total","40","20","?","(dato borrado)"]]},questionText:"El interrogante (?) está en el Total de Afeitadora.",options:["60","65","75","90","95"],correct:3},{id:28,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Hornos","Microondas","Vitrocerámica","Total"],rows:[["Septiembre","25","40","5","70"],["Octubre","(dato borrado)","45","50","(dato borrado)"],["Noviembre","30","(dato borrado)","?","90"],["Diciembre","35","30","(dato borrado)","105"],["Total","145","155","(dato borrado)","(dato borrado)"]]},questionText:"El interrogante (?) está en Vitrocerámica de Noviembre.",options:["10","15","20","30","60"],correct:2},{id:29,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Cafetera","Tostadora","Freidora","Total"],rows:[["Enero","(dato borrado)","5","20","35"],["Febrero","(dato borrado)","(dato borrado)","5","30"],["Marzo","15","30","?","(dato borrado)"],["Total","(dato borrado)","55","40","(dato borrado)"]]},questionText:"El interrogante (?) está en Freidora de Marzo.",options:["5","10","15","20","25"],correct:2},{id:30,type:"table",question:'Tabla: "Puntos obtenidos en la compra"',tableData:{headers:["Artículo","Unidades","Puntos/Unidad","Total puntos"],rows:[["Chocolate","5","225","1.125"],["Harina","6","?","(dato borrado)"],["Nueces","8","140","(dato borrado)"],["","","","3.925"]]},questionText:"El interrogante (?) está en Puntos/Unidad de Harina.",options:["26","265","270","280","1.680"],correct:3},{id:31,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Hornos","Microondas","Vitrocerámica","Total"],rows:[["Mayo","(dato borrado)","15","20","45"],["Junio","15","10","(dato borrado)","55"],["Julio","(dato borrado)","5","20","(dato borrado)"],["Agosto","10","(dato borrado)","10","25"],["Total","?","(dato borrado)","80","155"]]},questionText:"El interrogante (?) está en el Total de Hornos.",options:["25","35","40","45","50"],correct:2},{id:32,type:"table",question:'Tabla: "Puntos obtenidos en la compra"',tableData:{headers:["Artículo","Unidades","Puntos/Unidad","Total puntos"],rows:[["Grapa","2.500","0,05","125"],["Chincheta","3.000","?","(dato borrado)"],["Tornillo","1.200","0,1","(dato borrado)"],["","","","845"]]},questionText:"El interrogante (?) está en Puntos/Unidad de Chincheta.",options:["0,03","0,1","0,2","0,5","5"],correct:2}];function Ya({children:e="dato borrado"}){return x.jsxDEV("span",{className:"line-through text-red-600 font-medium",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/test/TextoTachado.jsx",lineNumber:9,columnNumber:5},this)}const Za=()=>{var e;const a=h(),i=E(),[r,s]=f.useState(0),[t,o]=f.useState({}),[c,u]=f.useState(1200),[p,b]=f.useState(!1),[N,g]=f.useState(!1),v=null==(e=i.state)?void 0:e.patientId,j=Wa,V=f.useCallback(()=>m(null,null,function*(){try{const i=Object.keys(t).length,r=j.length,s=r-i;let o=0;Object.entries(t).forEach(([e,a])=>{const i=j.find(a=>a.id.toString()===e);i&&parseInt(a)===i.correct&&o++});const n=i-o,l=1200-c,u={correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,testType:"numerico"};if(v)try{yield Sa.saveTestResult({patientId:v,testType:"numerico",correctCount:o,incorrectCount:n,unansweredCount:s,timeUsed:l,totalQuestions:r,answers:t,errores:n})}catch(e){}d.success(`Test completado. Has respondido ${i} de ${r} preguntas. Respuestas correctas: ${o}`),a("/test/results/numerico",{state:u})}catch(e){d.error("Error al procesar los resultados del test")}}),[t,j,c,a,v]);f.useEffect(()=>{if(N&&c>0&&!p){const e=setTimeout(()=>u(c-1),1e3);return()=>clearTimeout(e)}0===c&&setTimeout(()=>V(),0)},[c,p,N,V]);const C=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`,y=e=>{s(e)},D=j[r];return x.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("div",{className:"text-center mb-4",children:[x.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[x.jsxDEV("i",{className:"fas fa-calculator mr-2 text-teal-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:140,columnNumber:13},void 0),"Test de Aptitud Numérica"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:139,columnNumber:11},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Resolución de igualdades, series numéricas y análisis de tablas de datos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:143,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:138,columnNumber:9},void 0),N&&x.jsxDEV("div",{className:"text-center",children:x.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:C(c)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:147,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:146,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:137,columnNumber:7},void 0),N?x.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[x.jsxDEV("div",{className:"md:col-span-3",children:x.jsxDEV(va,{className:"mb-6",children:[x.jsxDEV(ja,{className:"flex justify-between items-center",children:[x.jsxDEV("div",{children:[x.jsxDEV("h2",{className:"text-lg font-medium",children:["Pregunta ",r+1," de ",j.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:320,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-sm text-gray-500",children:"equality"===D.type?"Igualdades Numéricas":"series"===D.type?"Series Numéricas":"Tablas de Datos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:323,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:319,columnNumber:15},void 0),x.jsxDEV("div",{className:"text-sm text-gray-500",children:void 0!==t[D.id]?"Respondida":"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:328,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:318,columnNumber:13},void 0),x.jsxDEV(Va,{children:[x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("h4",{className:"text-lg font-medium text-gray-800 mb-4",children:["equality"===D.type&&"¿Qué número debe aparecer en lugar del interrogante (?) para que se cumpla la igualdad?","series"===D.type&&"¿Qué número debe aparecer en lugar del interrogante (?) de modo que continúe la serie?","table"===D.type&&"¿Qué número debe aparecer en lugar del interrogante (?) a partir de los datos de la tabla?"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:335,columnNumber:17},void 0),"table"===D.type?x.jsxDEV("div",{children:[x.jsxDEV("h5",{className:"font-medium mb-3",children:D.question},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:343,columnNumber:21},void 0),x.jsxDEV("div",{className:"overflow-x-auto mb-4",children:x.jsxDEV("table",{className:"min-w-full border border-gray-300",children:[x.jsxDEV("thead",{children:x.jsxDEV("tr",{className:"bg-gray-50",children:D.tableData.headers.map((e,a)=>x.jsxDEV("th",{className:"border border-gray-300 px-3 py-2 text-center font-medium",children:e},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:349,columnNumber:31},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:347,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:346,columnNumber:25},void 0),x.jsxDEV("tbody",{children:D.tableData.rows.map((e,a)=>x.jsxDEV("tr",{className:a%2==0?"bg-white":"bg-gray-50",children:e.map((e,a)=>x.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"(dato borrado)"===e?x.jsxDEV(Ya,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:360,columnNumber:64},void 0):e},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:359,columnNumber:33},void 0))},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:357,columnNumber:29},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:355,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:345,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:344,columnNumber:21},void 0),x.jsxDEV("p",{className:"text-gray-700 mb-4",children:D.questionText},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:368,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:342,columnNumber:19},void 0):x.jsxDEV("div",{className:"bg-gray-50 p-4 rounded-lg text-center",children:x.jsxDEV("span",{className:"text-xl font-mono",children:D.question},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:372,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:371,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:334,columnNumber:15},void 0),x.jsxDEV("div",{className:"space-y-3",children:D.options.map((e,a)=>x.jsxDEV("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(t[D.id]===a?"bg-blue-50 border-blue-500":"hover:bg-gray-50"),onClick:()=>{return e=D.id,i=a,void o(a=>l(n({},a),{[e]:i}));var e,i},children:x.jsxDEV("div",{className:"flex items-center",children:[x.jsxDEV("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(t[D.id]===a?"bg-blue-500 text-white":"bg-gray-200 text-gray-700"),children:String.fromCharCode(65+a)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:390,columnNumber:23},void 0),x.jsxDEV("div",{className:"text-gray-700",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:397,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:389,columnNumber:21},void 0)},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:380,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:378,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:332,columnNumber:13},void 0),x.jsxDEV(Ea,{className:"flex justify-between",children:[x.jsxDEV(Ca,{variant:"outline",onClick:()=>y(Math.max(0,r-1)),disabled:0===r,children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:405,columnNumber:15},void 0),r<j.length-1?x.jsxDEV(Ca,{variant:"primary",onClick:()=>y(Math.min(j.length-1,r+1)),children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:413,columnNumber:17},void 0):x.jsxDEV(Ca,{variant:"primary",onClick:V,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:420,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:404,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:317,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:316,columnNumber:9},void 0),x.jsxDEV("div",{children:x.jsxDEV(va,{className:"sticky top-6",children:[x.jsxDEV(ja,{children:x.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:435,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:434,columnNumber:13},void 0),x.jsxDEV(Va,{children:[x.jsxDEV("div",{className:"grid grid-cols-4 gap-2",children:j.map((e,a)=>x.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(r===a?"bg-blue-500 text-white":void 0!==t[j[a].id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>y(a),title:`Pregunta ${a+1}`,children:a+1},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:440,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:438,columnNumber:15},void 0),x.jsxDEV("div",{className:"mt-6",children:[x.jsxDEV("div",{className:"flex items-center justify-between mb-2 text-sm",children:[x.jsxDEV("span",{children:"Progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:459,columnNumber:19},void 0),x.jsxDEV("span",{children:[Object.keys(t).length," de ",j.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:460,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:458,columnNumber:17},void 0),x.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:x.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(t).length/j.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:463,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:462,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:457,columnNumber:15},void 0),x.jsxDEV("div",{className:"mt-6",children:x.jsxDEV("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[x.jsxDEV("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:472,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",x.jsxDEV("span",{className:"font-medium",children:C(c)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:474,columnNumber:38},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:473,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:476,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:471,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:470,columnNumber:15},void 0),x.jsxDEV(Ca,{variant:"primary",className:"w-full mt-2",onClick:V,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:482,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:437,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:433,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:432,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:315,columnNumber:7},void 0):x.jsxDEV(va,{children:[x.jsxDEV(ja,{children:x.jsxDEV("h2",{className:"text-xl font-semibold text-gray-800",children:"Aptitud Numérica: Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:157,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:156,columnNumber:11},void 0),x.jsxDEV(Va,{children:x.jsxDEV("div",{className:"space-y-6",children:[x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-3",children:"Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:162,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-600 mb-4",children:"En esta prueba encontrarás distintos ejercicios numéricos que tendrás que resolver. Para ello tendrás que analizar la información que se presenta y determinar qué debe aparecer en lugar del interrogante. Cuando lo hayas decidido, deberás marcar la letra de la opción correspondiente (A, B, C, D o E), asegurándote de que coincida con el ejercicio que estás contestando. Ten en cuenta que en este ejercicio hay 5 posibles opciones de respuesta."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:163,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:161,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[x.jsxDEV("h4",{className:"text-md font-medium text-blue-800 mb-3",children:"Tipo 1: Igualdades Numéricas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:170,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-700 mb-4",children:"En un primer tipo de ejercicios aparecerá una igualdad numérica en la que se ha sustituido uno de los elementos por un interrogante (?). Tu tarea consistirá en averiguar qué valor numérico debe aparecer en lugar del interrogante para que se cumpla la igualdad."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:171,columnNumber:17},void 0),x.jsxDEV("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[x.jsxDEV("h5",{className:"font-medium text-gray-800 mb-2",children:"Ejemplo N1: ¿Qué número debe aparecer en lugar del interrogante (?) para que se cumpla la igualdad?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:176,columnNumber:19},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-3 rounded text-center mb-3",children:x.jsxDEV("span",{className:"text-xl font-mono",children:"16 - 4 = ? + 2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:178,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:177,columnNumber:19},void 0),x.jsxDEV("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A. 8"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:181,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"B. 10"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:182,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C. 12"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:183,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D. 14"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:184,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"E. 16"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:185,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:180,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-sm text-gray-600",children:[x.jsxDEV("strong",{children:"Solución:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:188,columnNumber:21},void 0)," La primera parte de la igualdad, 16 – 4, da lugar a 12. Para que en la segunda parte se obtenga el mismo resultado sería necesario sustituir el interrogante por 10, quedando la igualdad como 16 – 4 = 10 + 2. Por tanto, la respuesta correcta es ",x.jsxDEV("strong",{children:"B"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:188,columnNumber:292},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:187,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:175,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:169,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[x.jsxDEV("h4",{className:"text-md font-medium text-green-800 mb-3",children:"Tipo 2: Series Numéricas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:195,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-700 mb-4",children:"En otros ejercicios tendrás que observar una serie de números ordenados de acuerdo con una ley y determinar cuál debe continuar la serie ocupando el lugar del interrogante."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:196,columnNumber:17},void 0),x.jsxDEV("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[x.jsxDEV("h5",{className:"font-medium text-gray-800 mb-2",children:"Ejemplo N2: ¿Qué número debe aparecer en lugar del interrogante (?) de modo que continúe la serie?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:201,columnNumber:19},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-3 rounded text-center mb-3",children:x.jsxDEV("span",{className:"text-xl font-mono",children:"3 • 5 • 6 • 8 • 9 • 11 • 12 • 14 • ?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:203,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:202,columnNumber:19},void 0),x.jsxDEV("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A. 13"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:206,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"B. 15"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:207,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C. 16"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:208,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D. 18"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:209,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"E. 20"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:210,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:205,columnNumber:19},void 0),x.jsxDEV("div",{className:"bg-gray-50 p-3 rounded mb-3",children:x.jsxDEV("div",{className:"text-center text-sm font-mono",children:["3 → 5 → 6 → 8 → 9 → 11 → 12 → 14 → ?",x.jsxDEV("br",{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:214,columnNumber:59},void 0),x.jsxDEV("span",{className:"text-blue-600",children:"+2 +1 +2 +1 +2 +1 +2 +1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:215,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:213,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:212,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-sm text-gray-600",children:[x.jsxDEV("strong",{children:"Solución:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:219,columnNumber:21},void 0)," En este ejemplo la serie combina aumentos de 2 unidades y de 1 unidad (+2, +1, +2, +1...). Como puede observarse, en el lugar del interrogante debe aumentarse 1 unidad con respecto al número anterior, por lo que el número que continuaría la serie sería el 15. Por tanto, la respuesta correcta es ",x.jsxDEV("strong",{children:"B"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:219,columnNumber:344},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:218,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:200,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:194,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[x.jsxDEV("h4",{className:"text-md font-medium text-purple-800 mb-3",children:"Tipo 3: Tablas de Datos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:226,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-700 mb-4",children:"Finalmente, en un tercer tipo de ejercicios, aparecen tablas en las que un valor se ha sustituido intencionadamente por un interrogante (?) y otros valores han sido borrados (<<>>). Tu tarea consistirá en averiguar el número que debería aparecer en lugar del interrogante."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:227,columnNumber:17},void 0),x.jsxDEV("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[x.jsxDEV("h5",{className:"font-medium text-gray-800 mb-2",children:"Ejemplo N3: De acuerdo con los datos de la tabla, ¿qué número debe aparecer en lugar del interrogante (?)?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:232,columnNumber:19},void 0),x.jsxDEV("div",{className:"mb-4",children:[x.jsxDEV("h6",{className:"text-center font-medium mb-3",children:"Puntos obtenidos en la compra"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:235,columnNumber:21},void 0),x.jsxDEV("div",{className:"overflow-x-auto",children:x.jsxDEV("table",{className:"w-full border border-gray-300",children:[x.jsxDEV("thead",{children:x.jsxDEV("tr",{className:"bg-gray-100",children:[x.jsxDEV("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Artículo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:240,columnNumber:29},void 0),x.jsxDEV("th",{className:"border border-gray-300 px-3 py-2 text-center",children:"Unidades"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:241,columnNumber:29},void 0),x.jsxDEV("th",{className:"border border-gray-300 px-3 py-2 text-center",children:"Puntos/Unidad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:242,columnNumber:29},void 0),x.jsxDEV("th",{className:"border border-gray-300 px-3 py-2 text-center",children:"Total puntos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:243,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:239,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:238,columnNumber:25},void 0),x.jsxDEV("tbody",{children:[x.jsxDEV("tr",{children:[x.jsxDEV("td",{className:"border border-gray-300 px-3 py-2",children:"Jabón"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:248,columnNumber:29},void 0),x.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"10"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:249,columnNumber:29},void 0),x.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center font-bold text-red-600",children:"?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:250,columnNumber:29},void 0),x.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"30"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:251,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:247,columnNumber:27},void 0),x.jsxDEV("tr",{className:"bg-gray-50",children:[x.jsxDEV("td",{className:"border border-gray-300 px-3 py-2",children:"Aceite"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:254,columnNumber:29},void 0),x.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"20"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:255,columnNumber:29},void 0),x.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:256,columnNumber:29},void 0),x.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center",children:x.jsxDEV("span",{className:"line-through",children:"40"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:257,columnNumber:90},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:257,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:253,columnNumber:27},void 0),x.jsxDEV("tr",{children:[x.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 font-bold",colSpan:"3",children:"Total"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:260,columnNumber:29},void 0),x.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center font-bold",children:"70"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:261,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:259,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:246,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:237,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:236,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:234,columnNumber:19},void 0),x.jsxDEV("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[x.jsxDEV("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"A. 3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:269,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"B. 5"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:270,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C. 10"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:271,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D. 40"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:272,columnNumber:21},void 0),x.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"E. 60"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:273,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:268,columnNumber:19},void 0),x.jsxDEV("p",{className:"text-sm text-gray-600",children:[x.jsxDEV("strong",{children:"Solución:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:276,columnNumber:21},void 0)," A partir de los datos de la tabla sabemos que se han comprado 10 unidades de jabón y que se han obtenido 30 puntos, por lo que se puede deducir que el valor del interrogante es igual a 3 (10 unidades × 3 puntos/unidad = 30 puntos). Por tanto, la respuesta correcta es ",x.jsxDEV("strong",{children:"A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:276,columnNumber:316},void 0),". Fíjate que en este ejemplo no es necesario calcular el valor que ha sido borrado para obtener el valor del interrogante, pero en otros ejercicios sí será necesario calcular todos o algunos de estos valores para alcanzar la solución."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:275,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:231,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:225,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[x.jsxDEV("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Instrucciones para el Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:283,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-gray-700 mb-4",children:["Cuando comience la prueba encontrarás más ejercicios como estos. El tiempo máximo para su realización es de ",x.jsxDEV("strong",{children:"20 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:285,columnNumber:127},void 0),", por lo que deberás trabajar rápidamente, esforzándote al máximo en encontrar la respuesta correcta."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:284,columnNumber:17},void 0),x.jsxDEV("ul",{className:"list-disc pl-5 space-y-2 text-gray-700 mb-4",children:[x.jsxDEV("li",{children:"Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta de las cinco que aparecen."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:288,columnNumber:19},void 0),x.jsxDEV("li",{children:[x.jsxDEV("strong",{children:"No se penalizará el error"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:289,columnNumber:23},void 0),", así que intenta responder todas las preguntas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:289,columnNumber:19},void 0),x.jsxDEV("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas, pero NO continúes con las demás pruebas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:290,columnNumber:19},void 0),x.jsxDEV("li",{children:"Puedes navegar libremente entre las preguntas durante el tiempo disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:291,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:287,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:282,columnNumber:15},void 0),x.jsxDEV("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:296,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:297,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:295,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:160,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:159,columnNumber:11},void 0),x.jsxDEV(Ea,{className:"flex justify-end",children:x.jsxDEV(Ca,{variant:"primary",onClick:()=>{g(!0)},className:"px-6 py-2",children:"Comenzar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:304,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:303,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:155,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:136,columnNumber:5},void 0)},Xa=Object.freeze(Object.defineProperty({__proto__:null,default:Za},Symbol.toStringTag,{value:"Module"})),Ka=({data:e})=>{var a;const i=e.reduce((e,a)=>e+a.value,0);if(!e.length||0===i)return x.jsxDEV("div",{className:"flex items-center justify-center h-full",children:x.jsxDEV("p",{className:"text-gray-500",children:"No hay datos disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:15,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:14,columnNumber:7},void 0);let r=0;const s=e.map(e=>{const a=e.value/i*100,s=a/100*360,t=l(n({},e),{percentage:a,startAngle:r,endAngle:r+s});return r+=s,t}),t=(e,a=50)=>{const i=(e-90)*Math.PI/180;return{x:50+a*Math.cos(i),y:50+a*Math.sin(i)}},o=e=>{const a=t(e.startAngle),i=t(e.endAngle),r=e.endAngle-e.startAngle<=180?"0":"1";return`M 50 50 L ${a.x} ${a.y} A 50 50 0 ${r} 1 ${i.x} ${i.y} Z`},c=1===s.length||s.some(e=>100===e.percentage);return x.jsxDEV("div",{className:"flex flex-col items-center h-full",children:[x.jsxDEV("div",{className:"w-full max-w-xs",children:x.jsxDEV("svg",{viewBox:"0 0 100 100",className:"w-48 h-48 mx-auto mb-4",children:c?x.jsxDEV("circle",{cx:"50",cy:"50",r:"50",fill:(null==(a=s.find(e=>100===e.percentage))?void 0:a.color)||s[0].color},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:62,columnNumber:13},void 0):s.map((e,a)=>x.jsxDEV("path",{d:o(e),fill:e.color,stroke:"#fff",strokeWidth:"0.5"},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:71,columnNumber:15},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:59,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:58,columnNumber:7},void 0),x.jsxDEV("div",{className:"flex flex-col items-center w-full",children:s.map((e,a)=>x.jsxDEV("div",{className:"flex items-center mb-2 w-full justify-between max-w-xs",children:[x.jsxDEV("div",{className:"flex items-center",children:[x.jsxDEV("div",{className:"w-4 h-4 mr-2",style:{backgroundColor:e.color}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:87,columnNumber:15},void 0),x.jsxDEV("span",{className:"text-sm text-gray-700",children:e.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:91,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:86,columnNumber:13},void 0),x.jsxDEV("div",{className:"flex items-center",children:[x.jsxDEV("span",{className:"font-medium text-sm mr-2",children:e.value},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:94,columnNumber:15},void 0),x.jsxDEV("span",{className:"text-xs text-gray-500",children:["(",e.percentage.toFixed(1),"%)"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:95,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:93,columnNumber:13},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:85,columnNumber:11},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:83,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:57,columnNumber:5},void 0)},ei=()=>{const e=E().state||{correctCount:0,incorrectCount:0,unansweredCount:0,timeUsed:0,totalQuestions:32,testType:"unknown"},{correctCount:a,incorrectCount:i,unansweredCount:r,timeUsed:s,totalQuestions:t,testType:o}=e,n=[{name:"Correctas",value:a,color:"#10B981"},{name:"Incorrectas",value:i,color:"#EF4444"},{name:"Sin responder",value:r,color:"#9CA3AF"}].filter(e=>e.value>0);0===n.length&&n.push({name:"Correctas",value:a,color:"#10B981"});const l=Math.round(a/t*100),c=((e,a)=>{let i=[];return i="ortografia"===e?a<60?["Repasa las reglas básicas de ortografía, especialmente las reglas de acentuación","Practica la identificación de palabras correctas e incorrectas con ejercicios diarios","Presta especial atención a las letras que suelen causar confusión (b/v, g/j, h)"]:a<80?["Refuerza tu conocimiento en acentuación, especialmente en palabras agudas, llanas y esdrújulas","Practica con palabras que contengan h, b/v, g/j para mejorar tu precisión","Dedica tiempo a la lectura para familiarizarte con la escritura correcta de las palabras"]:["Continúa practicando con palabras poco comunes para expandir tu dominio ortográfico","Profundiza en las excepciones a las reglas de acentuación","Mantén el hábito de lectura para reforzar tu ortografía"]:"espacial"===e?100===a?["¡Felicidades! Has demostrado una capacidad excepcional de razonamiento espacial","Considera explorar campos profesionales como arquitectura, ingeniería, diseño 3D o ciencias que requieran esta habilidad","Tu capacidad para visualizar y manipular objetos mentalmente es extraordinaria","Podrías compartir tus técnicas y estrategias con otros para ayudarles a mejorar sus habilidades espaciales"]:a<60?["Practica con rompecabezas tridimensionales y juegos de construcción para mejorar tu visualización espacial","Realiza ejercicios de rotación mental, como imaginar objetos desde diferentes ángulos","Intenta dibujar objetos tridimensionales desde diferentes perspectivas","Utiliza aplicaciones o juegos que ejerciten el razonamiento espacial"]:a<80?["Continúa practicando con ejercicios de visualización espacial más complejos","Intenta resolver problemas de plegado de papel (origami) para mejorar tu comprensión de transformaciones espaciales","Practica con juegos de construcción y ensamblaje que requieran visualización tridimensional","Analiza las preguntas que te resultaron más difíciles para identificar patrones específicos"]:["Desafíate con problemas de visualización espacial más avanzados","Explora campos como la geometría tridimensional, el diseño 3D o la arquitectura","Comparte tus conocimientos y estrategias con otros para reforzar tu comprensión","Considera carreras o actividades que aprovechen tu excelente capacidad de razonamiento espacial"]:"mecanico"===e?100===a?["¡Excelente! Has demostrado una comprensión excepcional de principios mecánicos y físicos","Considera carreras en ingeniería mecánica, física aplicada, o diseño industrial","Tu capacidad para analizar sistemas mecánicos y predecir comportamientos es sobresaliente","Podrías explorar campos como robótica, automatización o diseño de maquinaria"]:a<60?["Repasa los principios básicos de física: fuerzas, palancas, poleas y equilibrio","Practica con ejercicios de mecánica básica y análisis de sistemas simples","Observa cómo funcionan las máquinas simples en la vida cotidiana","Dedica tiempo a entender conceptos como centro de gravedad, resistencia y fricción"]:a<80?["Profundiza en el estudio de máquinas simples y compuestas","Practica con problemas de equilibrio de fuerzas y análisis de estructuras","Estudia casos prácticos de aplicaciones mecánicas en la industria","Refuerza tu comprensión de principios como ventaja mecánica y eficiencia"]:["Explora conceptos avanzados de mecánica y termodinámica","Considera estudiar ingeniería mecánica o campos relacionados","Practica con simulaciones y modelado de sistemas mecánicos complejos","Mantén tu conocimiento actualizado con las últimas tecnologías mecánicas"]:"numerico"===e?100===a?["¡Excelente! Has demostrado una capacidad excepcional en razonamiento numérico","Considera carreras en matemáticas, estadística, ingeniería, economía o ciencias actuariales","Tu habilidad para resolver problemas numéricos complejos es sobresaliente","Podrías explorar campos como análisis de datos, investigación operativa o finanzas cuantitativas"]:a<60?["Repasa las operaciones básicas: suma, resta, multiplicación y división","Practica con ejercicios de igualdades numéricas y resolución de ecuaciones simples","Dedica tiempo a entender patrones en series numéricas","Refuerza tu comprensión de fracciones, decimales y porcentajes"]:a<80?["Practica con series numéricas más complejas para identificar patrones","Mejora tu velocidad en cálculo mental y operaciones aritméticas","Estudia problemas de proporcionalidad y regla de tres","Analiza tablas de datos y practica la interpretación de información numérica"]:["Desafíate con problemas de matemáticas más avanzados","Explora áreas como álgebra, estadística y análisis de datos","Considera estudiar carreras que requieran fuerte razonamiento cuantitativo","Mantén tu agilidad mental practicando regularmente con ejercicios numéricos"]:["Continúa practicando ejercicios similares para mejorar tu desempeño","Revisa los conceptos básicos relacionados con este tipo de prueba","Analiza las preguntas que te resultaron más difíciles para identificar áreas de mejora"],i})(o,l);return x.jsxDEV("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:(e=>{switch(e){case"verbal":return"Test de Aptitud Verbal";case"ortografia":return"Test de Ortografía";case"razonamiento":return"Test de Razonamiento";case"atencion":return"Test de Atención";case"espacial":return"Test de Visualización Espacial";case"mecanico":return"Test de Razonamiento Mecánico";case"numerico":return"Test de Razonamiento Numérico";default:return"Resultados del Test"}})(o)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:220,columnNumber:9},void 0),x.jsxDEV("p",{className:"text-gray-600",children:"Resumen de tu desempeño en el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:221,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:219,columnNumber:7},void 0),x.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[x.jsxDEV("div",{className:"bg-white shadow-md rounded-lg p-6",children:[x.jsxDEV("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Resultados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:227,columnNumber:11},void 0),x.jsxDEV("div",{className:"h-64",children:x.jsxDEV(Ka,{data:n},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:229,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:228,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:226,columnNumber:9},void 0),x.jsxDEV("div",{className:"bg-white shadow-md rounded-lg p-6",children:[x.jsxDEV("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Estadísticas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:235,columnNumber:11},void 0),x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("div",{className:"text-3xl font-bold mb-1 "+(u=l,100===u?"text-green-600 animate-pulse":u>=90?"text-green-600":u>=75?"text-green-500":u>=60?"text-blue-500":u>=50?"text-yellow-500":"text-red-500"),children:[l,"%"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:238,columnNumber:13},void 0),x.jsxDEV("p",{className:"text-gray-700 font-medium",children:(e=>100===e||e>=90?"Excelente desempeño":e>=75?"Muy buen desempeño":e>=60?"Buen desempeño":e>=50?"Desempeño aceptable":"Necesita mejorar")(l)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:241,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:237,columnNumber:11},void 0),x.jsxDEV("div",{className:"space-y-3",children:[x.jsxDEV("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[x.jsxDEV("span",{className:"text-gray-600",children:"Respuestas correctas:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:246,columnNumber:15},void 0),x.jsxDEV("span",{className:"font-medium text-gray-800",children:[a," de ",t]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:247,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:245,columnNumber:13},void 0),x.jsxDEV("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[x.jsxDEV("span",{className:"text-gray-600",children:"Respuestas incorrectas:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:250,columnNumber:15},void 0),x.jsxDEV("span",{className:"font-medium text-gray-800",children:i},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:251,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:249,columnNumber:13},void 0),x.jsxDEV("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[x.jsxDEV("span",{className:"text-gray-600",children:"Sin responder:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:254,columnNumber:15},void 0),x.jsxDEV("span",{className:"font-medium text-gray-800",children:r},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:255,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:253,columnNumber:13},void 0),x.jsxDEV("div",{className:"flex justify-between py-2",children:[x.jsxDEV("span",{className:"text-gray-600",children:"Tiempo utilizado:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:258,columnNumber:15},void 0),x.jsxDEV("span",{className:"font-medium text-gray-800",children:(e=>{const a=e%60;return`${Math.floor(e/60)}:${a<10?"0":""}${a}`})(s)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:259,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:257,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:244,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:234,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:224,columnNumber:7},void 0),x.jsxDEV("div",{className:"bg-white shadow-md rounded-lg p-6 mb-6",children:[x.jsxDEV("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Recomendaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:267,columnNumber:9},void 0),x.jsxDEV("ul",{className:"space-y-2",children:c.map((e,a)=>x.jsxDEV("li",{className:"flex items-start",children:[x.jsxDEV("div",{className:"flex-shrink-0 h-5 w-5 mt-0.5",children:x.jsxDEV("svg",{className:"h-5 w-5 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:x.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:273,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:272,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:271,columnNumber:15},void 0),x.jsxDEV("span",{className:"ml-2 text-gray-700",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:276,columnNumber:15},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:270,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:268,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:266,columnNumber:7},void 0),x.jsxDEV("div",{className:"flex flex-col sm:flex-row gap-4",children:[x.jsxDEV(v,{to:`/test/instructions/${o}`,className:"flex-1 bg-blue-600 text-white text-center py-3 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Realizar el Test Nuevamente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:284,columnNumber:9},void 0),x.jsxDEV(v,{to:"/student/questionnaire",className:"flex-1 bg-gray-100 text-gray-800 text-center py-3 px-4 rounded-md hover:bg-gray-200 transition-colors",children:"Volver a la Lista de Tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:290,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:283,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:218,columnNumber:5},void 0);var u},ai=Object.freeze(Object.defineProperty({__proto__:null,default:ei},Symbol.toStringTag,{value:"Module"})),ii=f.createContext(),ri=e=>{if(!e)return null;const a=new Date,i=new Date(e);let r=a.getFullYear()-i.getFullYear();const s=a.getMonth()-i.getMonth();return(s<0||0===s&&a.getDate()<i.getDate())&&r--,r},si=e=>{if(!e)return"";return new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"2-digit",day:"2-digit"})},ti=e=>{if(!e)return"";return new Date(e).toLocaleDateString("es-ES",{weekday:"long",year:"numeric",month:"long",day:"numeric"})},oi=e=>{if("number"==typeof e){if(e<0)return"N/A";const a=Math.floor(e/3600),i=Math.floor(e%3600/60),r=Math.floor(e%60);return a>0?`${a}:${i.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`:`${i}:${r.toString().padStart(2,"0")}`}if(!e)return"";return new Date(e).toLocaleTimeString("es-ES",{hour:"2-digit",minute:"2-digit"})},ni=f.forwardRef((e,a)=>{var i=e,{checked:r=!1,indeterminate:s=!1,onChange:t,disabled:o=!1,className:l="",children:u,id:m,"aria-label":d,"aria-describedby":p}=i,b=c(i,["checked","indeterminate","onChange","disabled","className","children","id","aria-label","aria-describedby"]);const N=f.useRef(null),g=a||N;f.useEffect(()=>{g.current&&(g.current.indeterminate=s)},[s,g]);const h=`\n    relative inline-flex items-center justify-center w-5 h-5 border-2 rounded transition-all duration-200\n    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\n    ${o?"cursor-not-allowed opacity-50":"cursor-pointer"}\n  `,v="border-gray-300 bg-white hover:border-gray-400",j="border-blue-600 bg-blue-600 hover:border-blue-700 hover:bg-blue-700",V="border-blue-600 bg-blue-600 hover:border-blue-700 hover:bg-blue-700";return x.jsxDEV("label",{className:`inline-flex items-center ${o?"cursor-not-allowed":"cursor-pointer"} ${l}`,htmlFor:m,children:[x.jsxDEV("div",{className:"relative",children:[x.jsxDEV("input",n({ref:g,type:"checkbox",id:m,checked:r,onChange:e=>{t&&!o&&t(e)},disabled:o,className:"sr-only","aria-label":d,"aria-describedby":p},b),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:71,columnNumber:9},void 0),x.jsxDEV("div",{className:`${h} ${s?V:r?j:v}`,children:s?x.jsxDEV(I,{className:"w-3 h-3 text-white","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:57,columnNumber:14},void 0):r?x.jsxDEV(z,{className:"w-3 h-3 text-white","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:60,columnNumber:14},void 0):null},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:83,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:70,columnNumber:7},void 0),u&&x.jsxDEV("span",{className:"ml-2 text-sm "+(o?"text-gray-400":"text-gray-700"),children:u},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:88,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:66,columnNumber:5},void 0)});ni.displayName="Checkbox";const li=e=>{var a=e,{children:i,content:r,position:s="top",delay:t=300,disabled:o=!1,className:l="",contentClassName:u="",arrow:m=!0}=a,d=c(a,["children","content","position","delay","disabled","className","contentClassName","arrow"]);const[p,b]=f.useState(!1),[N,g]=f.useState({top:0,left:0}),h=f.useRef(null),v=f.useRef(null),j=f.useRef(null),V=()=>{if(!h.current||!v.current)return;const e=h.current.getBoundingClientRect(),a=v.current.getBoundingClientRect(),i=window.pageYOffset||document.documentElement.scrollTop,r=window.pageXOffset||document.documentElement.scrollLeft;let t,o;switch(s){case"top":default:t=e.top+i-a.height-8,o=e.left+r+(e.width-a.width)/2;break;case"bottom":t=e.bottom+i+8,o=e.left+r+(e.width-a.width)/2;break;case"left":t=e.top+i+(e.height-a.height)/2,o=e.left+r-a.width-8;break;case"right":t=e.top+i+(e.height-a.height)/2,o=e.right+r+8}const n=window.innerWidth-a.width-8,l=window.innerHeight-a.height-8;o=Math.max(8,Math.min(o,n)),t=Math.max(8,Math.min(t,l)),g({top:t,left:o})},E=()=>{!o&&r&&(j.current&&clearTimeout(j.current),j.current=setTimeout(()=>{b(!0)},t))},C=()=>{j.current&&clearTimeout(j.current),b(!1)};f.useEffect(()=>{if(p){V();const e=()=>V();return window.addEventListener("resize",e),window.addEventListener("scroll",e,!0),()=>{window.removeEventListener("resize",e),window.removeEventListener("scroll",e,!0)}}},[p,s]),f.useEffect(()=>()=>{j.current&&clearTimeout(j.current)},[]);const y=f.cloneElement(i,n({ref:h,onMouseEnter:e=>{E(),i.props.onMouseEnter&&i.props.onMouseEnter(e)},onMouseLeave:e=>{C(),i.props.onMouseLeave&&i.props.onMouseLeave(e)},onFocus:e=>{E(),i.props.onFocus&&i.props.onFocus(e)},onBlur:e=>{C(),i.props.onBlur&&i.props.onBlur(e)},className:`${i.props.className||""} ${l}`.trim()},d)),D=p&&r&&x.jsxDEV("div",{ref:v,className:`\n        fixed z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg\n        pointer-events-none transition-opacity duration-200\n        ${p?"opacity-100":"opacity-0"}\n        ${u}\n      `,style:{top:N.top,left:N.left},role:"tooltip","aria-hidden":!p,children:[r,m&&x.jsxDEV("div",{className:(()=>{const e="absolute w-2 h-2 bg-gray-900 transform rotate-45";switch(s){case"top":default:return`${e} -bottom-1 left-1/2 -translate-x-1/2`;case"bottom":return`${e} -top-1 left-1/2 -translate-x-1/2`;case"left":return`${e} -right-1 top-1/2 -translate-y-1/2`;case"right":return`${e} -left-1 top-1/2 -translate-y-1/2`}})()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Tooltip.jsx",lineNumber:182,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Tooltip.jsx",lineNumber:166,columnNumber:5},void 0);return x.jsxDEV(x.Fragment,{children:[y,"undefined"!=typeof document&&S.createPortal(D,document.body)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Tooltip.jsx",lineNumber:187,columnNumber:5},void 0)},ci=e=>{var a=e,{className:i="",width:r,height:s,variant:t="rectangular",animation:o="pulse",lines:u=1}=a,m=c(a,["className","width","height","variant","animation","lines"]);const d={pulse:"animate-pulse",wave:"animate-wave",none:""},p={rectangular:"rounded",circular:"rounded-full",text:"rounded h-4",avatar:"rounded-full w-10 h-10"},b=()=>`\n      bg-gray-200\n      ${d[o]||d.pulse}\n      ${p[t]||p.rectangular}\n      ${i}\n    `.trim(),N=()=>{const e={};return r&&(e.width="number"==typeof r?`${r}px`:r),s&&(e.height="number"==typeof s?`${s}px`:s),e};return"text"===t&&u>1?x.jsxDEV("div",l(n({className:`space-y-2 ${i}`},m),{children:Array.from({length:u},(e,a)=>x.jsxDEV("div",{className:b(),style:l(n({},N()),{width:a===u-1?"75%":"100%"})},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/LoadingSkeleton.jsx",lineNumber:53,columnNumber:11},void 0))}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/LoadingSkeleton.jsx",lineNumber:51,columnNumber:7},void 0):x.jsxDEV("div",n({className:b(),style:N()},m),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/LoadingSkeleton.jsx",lineNumber:67,columnNumber:5},void 0)},ui=f.memo(({index:e,style:a,data:i})=>{var r,s,t,o,n;const{informes:l,selectedInformes:c,onToggleSelection:u,onViewInforme:m,onDeleteInforme:d,onViewChart:p,isLoading:b}=i,N=l[e],g=c.has(null==N?void 0:N.id),h=e%2==0,v=f.useCallback(()=>{u(N.id)},[null==N?void 0:N.id,u]),j=f.useCallback(()=>{m(N)},[N,m]),V=f.useCallback(()=>{d(N.id)},[null==N?void 0:N.id,d]),E=f.useCallback(()=>{p(N)},[N,p]);return b||!N?x.jsxDEV("div",{style:a,className:"flex items-center px-4 py-3",children:x.jsxDEV(ci,{className:"w-full h-16"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:53,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:52,columnNumber:7},void 0):x.jsxDEV("div",{style:a,className:`\n        flex items-center px-4 py-3 border-b border-gray-200 hover:bg-gray-50 transition-colors\n        ${h?"bg-white":"bg-gray-25"}\n        ${g?"bg-blue-50 border-blue-200":""}\n      `,role:"row","aria-selected":g,children:[x.jsxDEV("div",{className:"flex-shrink-0 w-12",children:x.jsxDEV(ni,{checked:g,onChange:v,"aria-label":`Seleccionar informe ${N.titulo}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:71,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:70,columnNumber:7},void 0),x.jsxDEV("div",{className:"flex-1 min-w-0 px-3",children:[x.jsxDEV("div",{className:"flex items-center space-x-2",children:[x.jsxDEV(q,{className:"w-4 h-4 text-gray-400 flex-shrink-0","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:81,columnNumber:11},void 0),x.jsxDEV("span",{className:"font-medium text-gray-900 truncate",children:(null==(r=N.resultados)?void 0:r.nombre_paciente)||"Paciente no especificado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:82,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:80,columnNumber:9},void 0),x.jsxDEV("div",{className:"flex items-center space-x-2 mt-1",children:[x.jsxDEV(P,{className:"w-3 h-3 text-gray-400 flex-shrink-0","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:87,columnNumber:11},void 0),x.jsxDEV("span",{className:"text-sm text-gray-600 truncate",children:N.titulo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:88,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:86,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:79,columnNumber:7},void 0),x.jsxDEV("div",{className:"flex-shrink-0 w-40 px-3",children:x.jsxDEV("div",{className:"flex items-center space-x-2",children:[x.jsxDEV(M,{className:"w-4 h-4 text-gray-400","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:97,columnNumber:11},void 0),x.jsxDEV("div",{className:"text-sm",children:[x.jsxDEV("div",{className:"text-gray-900",children:si(N.fecha_generacion)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:99,columnNumber:13},void 0),x.jsxDEV("div",{className:"text-gray-500",children:oi(N.fecha_generacion)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:102,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:98,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:96,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:95,columnNumber:7},void 0),x.jsxDEV("div",{className:"flex-shrink-0 w-40 px-3",children:x.jsxDEV("div",{className:"flex items-center space-x-2",children:[x.jsxDEV(M,{className:"w-4 h-4 text-gray-400","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:112,columnNumber:11},void 0),x.jsxDEV("div",{className:"text-sm",children:x.jsxDEV("div",{className:"text-gray-900",children:(null==(s=N.resultados)?void 0:s.fecha_evaluacion)?si(N.resultados.fecha_evaluacion):"No especificada"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:114,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:113,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:111,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:110,columnNumber:7},void 0),x.jsxDEV("div",{className:"flex-shrink-0 w-32 px-3",children:x.jsxDEV("div",{className:"flex items-center space-x-1",children:[x.jsxDEV(li,{content:"Ver informe",children:x.jsxDEV(Ca,{variant:"ghost",size:"sm",onClick:j,className:"p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100","aria-label":`Ver informe de ${null==(t=N.resultados)?void 0:t.nombre_paciente}`,children:x.jsxDEV(_,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:135,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:128,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:127,columnNumber:11},void 0),x.jsxDEV(li,{content:"Ver gráfico",children:x.jsxDEV(Ca,{variant:"ghost",size:"sm",onClick:E,className:"p-2 text-green-600 hover:text-green-800 hover:bg-green-100","aria-label":`Ver gráfico de ${null==(o=N.resultados)?void 0:o.nombre_paciente}`,children:x.jsxDEV(k,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:147,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:140,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:139,columnNumber:11},void 0),x.jsxDEV(li,{content:"Eliminar informe",children:x.jsxDEV(Ca,{variant:"ghost",size:"sm",onClick:V,className:"p-2 text-red-600 hover:text-red-800 hover:bg-red-100","aria-label":`Eliminar informe de ${null==(n=N.resultados)?void 0:n.nombre_paciente}`,children:x.jsxDEV(O,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:159,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:152,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:151,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:126,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:125,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:59,columnNumber:5},void 0)});ui.displayName="InformeRow";const mi=f.memo(({selectedCount:e,totalCount:a,onSelectAll:i,onDeselectAll:r,allSelected:s,someSelected:t})=>{const o=f.useCallback(()=>{s||t?r():i()},[s,t,i,r]);return x.jsxDEV("div",{className:"flex items-center px-4 py-3 bg-gray-50 border-b border-gray-200 font-medium text-gray-700",role:"row",children:[x.jsxDEV("div",{className:"flex-shrink-0 w-12",children:x.jsxDEV(ni,{checked:s,indeterminate:t&&!s,onChange:o,"aria-label":"Seleccionar todos los informes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:196,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:195,columnNumber:7},void 0),x.jsxDEV("div",{className:"flex-1 px-3",children:[x.jsxDEV("span",{children:"Paciente / Título"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:206,columnNumber:9},void 0),e>0&&x.jsxDEV("span",{className:"ml-2 text-sm text-blue-600",children:["(",e," seleccionados)"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:208,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:205,columnNumber:7},void 0),x.jsxDEV("div",{className:"flex-shrink-0 w-40 px-3",children:x.jsxDEV("span",{children:"Fecha Generación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:215,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:214,columnNumber:7},void 0),x.jsxDEV("div",{className:"flex-shrink-0 w-40 px-3",children:x.jsxDEV("span",{children:"Fecha Evaluación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:219,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:218,columnNumber:7},void 0),x.jsxDEV("div",{className:"flex-shrink-0 w-32 px-3",children:x.jsxDEV("span",{children:"Acciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:223,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:222,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:190,columnNumber:5},void 0)});mi.displayName="TableHeader";const di=f.memo(({searchTerm:e,onClearSearch:a})=>x.jsxDEV("div",{className:"flex flex-col items-center justify-center py-12 px-4",children:[x.jsxDEV(P,{className:"w-16 h-16 text-gray-300 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:236,columnNumber:5},void 0),x.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:e?"No se encontraron informes":"No hay informes generados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:237,columnNumber:5},void 0),x.jsxDEV("p",{className:"text-gray-600 text-center max-w-md",children:e?`No se encontraron informes que coincidan con "${e}"`:"Aún no se han generado informes. Los informes aparecerán aquí una vez que se generen."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:240,columnNumber:5},void 0),e&&a&&x.jsxDEV(Ca,{onClick:a,variant:"outline",className:"mt-4",children:"Limpiar búsqueda"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:247,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:235,columnNumber:3},void 0));di.displayName="EmptyState";const pi=f.memo(({informes:e=[],selectedInformes:a=new Set,onToggleSelection:i,onSelectAll:r,onDeselectAll:s,onViewInforme:t,onDeleteInforme:o,onViewChart:n,isLoading:l=!1,searchTerm:c="",onClearSearch:u,height:m=600})=>{const{allSelected:d,someSelected:p,selectedCount:b}=f.useMemo(()=>{const i=a.size,r=e.length;return{allSelected:r>0&&i===r,someSelected:i>0&&i<r,selectedCount:i}},[a.size,e.length]),N=f.useMemo(()=>({informes:e,selectedInformes:a,onToggleSelection:i,onViewInforme:t,onDeleteInforme:o,onViewChart:n,isLoading:l}),[e,a,i,t,o,n,l]);return l||0!==e.length?x.jsxDEV("div",{className:"bg-white rounded-lg border border-gray-200 overflow-hidden",role:"table","aria-label":"Tabla de informes generados",children:[x.jsxDEV(mi,{selectedCount:b,totalCount:e.length,onSelectAll:r,onDeselectAll:s,allSelected:d,someSelected:p},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:334,columnNumber:7},void 0),x.jsxDEV("div",{className:"relative",children:[x.jsxDEV(L,{height:m-60,itemCount:l?10:e.length,itemSize:80,itemData:N,overscanCount:5,children:ui},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:345,columnNumber:9},void 0),l&&x.jsxDEV("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center",children:x.jsxDEV("div",{className:"flex items-center space-x-2",children:[x.jsxDEV("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:359,columnNumber:15},void 0),x.jsxDEV("span",{className:"text-gray-600",children:"Cargando informes..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:360,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:358,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:357,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:344,columnNumber:7},void 0),x.jsxDEV("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-200 text-sm text-gray-600",children:l?x.jsxDEV("span",{children:"Cargando..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:369,columnNumber:11},void 0):x.jsxDEV("span",{children:["Mostrando ",e.length," informe",1!==e.length?"s":"",b>0&&x.jsxDEV("span",{className:"ml-2 text-blue-600",children:["• ",b," seleccionado",1!==b?"s":""]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:374,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:371,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:367,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:329,columnNumber:5},void 0):x.jsxDEV("div",{className:"bg-white rounded-lg border border-gray-200",children:[x.jsxDEV(mi,{selectedCount:b,totalCount:e.length,onSelectAll:r,onDeselectAll:s,allSelected:d,someSelected:p},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:312,columnNumber:9},void 0),x.jsxDEV(di,{searchTerm:c,onClearSearch:u},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:320,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:311,columnNumber:7},void 0)});pi.displayName="InformesTable";const bi=({searchTerm:e,onSearchChange:a,onClearSearch:i,totalCount:r,selectedCount:s})=>x.jsxDEV("div",{className:"bg-white p-4 rounded-lg border border-gray-200 mb-6",children:x.jsxDEV("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[x.jsxDEV("div",{className:"flex-1 max-w-md",children:x.jsxDEV("div",{className:"relative",children:[x.jsxDEV(Q,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:32,columnNumber:13},void 0),x.jsxDEV("input",{type:"text",placeholder:"Buscar por paciente o título...",value:e,onChange:e=>a(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500","aria-label":"Buscar informes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:33,columnNumber:13},void 0),e&&x.jsxDEV("button",{onClick:i,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600","aria-label":"Limpiar búsqueda",children:"×"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:42,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:31,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:30,columnNumber:9},void 0),x.jsxDEV("div",{className:"flex items-center gap-4",children:[x.jsxDEV("div",{className:"text-sm text-gray-600",children:[r," informe",1!==r?"s":"",s>0&&x.jsxDEV("span",{className:"ml-2 text-blue-600 font-medium",children:["• ",s," seleccionado",1!==s?"s":""]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:58,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:55,columnNumber:11},void 0),x.jsxDEV(Ca,{variant:"outline",size:"sm",className:"flex items-center gap-2",children:[x.jsxDEV(U,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:69,columnNumber:13},void 0),"Filtros"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:64,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:54,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:28,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:27,columnNumber:5},void 0),Ni=({selectedCount:e,onBulkDelete:a,onBulkExport:i,disabled:r})=>0===e?null:x.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:x.jsxDEV("div",{className:"flex items-center justify-between",children:[x.jsxDEV("div",{className:"flex items-center gap-2",children:x.jsxDEV("span",{className:"text-sm font-medium text-blue-900",children:[e," informe",1!==e?"s":""," seleccionado",1!==e?"s":""]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:88,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:87,columnNumber:9},void 0),x.jsxDEV("div",{className:"flex items-center gap-2",children:[x.jsxDEV(Ca,{variant:"outline",size:"sm",onClick:i,disabled:r,className:"flex items-center gap-2",children:[x.jsxDEV(H,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:101,columnNumber:13},void 0),"Exportar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:94,columnNumber:11},void 0),x.jsxDEV(Ca,{variant:"destructive",size:"sm",onClick:a,disabled:r,className:"flex items-center gap-2",children:[x.jsxDEV(O,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:112,columnNumber:13},void 0),"Eliminar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:105,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:93,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:86,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:85,columnNumber:5},void 0),gi=()=>{const[e,a]=f.useState(""),[i,r]=f.useState(new Set),[s,t]=f.useState(1),o=((e,a)=>{const[i,r]=f.useState(e);return f.useEffect(()=>{const i=setTimeout(()=>{r(e)},a);return()=>{clearTimeout(i)}},[e,a]),i})(e,300),{informes:c,loading:u,error:d,totalCount:p,totalPages:b,refetch:N,deleteInforme:g,deleteBulkInformes:h}=(({itemsPerPage:e=10,autoRefresh:a=!1,refreshInterval:i=3e4}={})=>{const[r,s]=f.useState({informes:[],loading:!1,error:null,totalCount:0,currentPage:1,hasNextPage:!1,hasPrevPage:!1}),t=f.useRef(null),o=f.useRef(null),c=f.useRef(new Map),u=f.useCallback(e=>{s(a=>n(n({},a),e))},[]),p=f.useCallback((...a)=>m(null,[...a],function*(a=1,i={}){t.current&&t.current.abort(),t.current=new AbortController;const{signal:r}=t.current,s=`page-${a}-${e}`;if(c.current.has(s)&&!i.forceRefresh){const e=c.current.get(s);return void u(l(n({},e),{currentPage:a,loading:!1}))}try{u({loading:!0,error:null});const i=(a-1)*e,{data:t,error:o,count:p}=yield Aa.from("informes_generados").select("\n          id,\n          titulo,\n          descripcion,\n          fecha_generacion,\n          metadatos,\n          contenido,\n          pacientes:paciente_id (\n            id,\n            nombre,\n            apellido,\n            documento,\n            genero\n          )\n        ",{count:"exact"}).eq("tipo_informe","completo").eq("estado","generado").range(i,i+e-1).order("fecha_generacion",{ascending:!1}).abortSignal(r);if(o)throw o;const b={informes:yield Promise.all((t||[]).map(e=>m(null,null,function*(){var a;try{const{data:i,error:s}=yield Aa.from("resultados").select("\n                id,\n                puntaje_directo,\n                percentil,\n                errores,\n                tiempo_segundos,\n                concentracion,\n                created_at,\n                aptitudes:aptitud_id (\n                  codigo,\n                  nombre,\n                  descripcion\n                )\n              ").eq("paciente_id",null==(a=e.pacientes)?void 0:a.id).not("puntaje_directo","is",null).not("percentil","is",null).order("created_at",{ascending:!1}).limit(20).abortSignal(r);return l(n({},e),s?{resultados:[]}:{resultados:i||[]})}catch(d){if("AbortError"===d.name)throw d;return l(n({},e),{resultados:[]})}}))),totalCount:p||0,hasNextPage:i+e<(p||0),hasPrevPage:a>1,loading:!1,error:null};if(c.current.set(s,b),c.current.size>10){const e=c.current.keys().next().value;c.current.delete(e)}u(l(n({},b),{currentPage:a}))}catch(d){if("AbortError"===d.name)return;u({loading:!1,error:d.message||"Error al cargar los informes"})}}),[e,u]),b=f.useCallback(a=>{a>=1&&a<=Math.ceil(r.totalCount/e)&&p(a)},[p,r.totalCount,e]),N=f.useCallback(()=>{r.hasNextPage&&b(r.currentPage+1)},[b,r.hasNextPage,r.currentPage]),g=f.useCallback(()=>{r.hasPrevPage&&b(r.currentPage-1)},[b,r.hasPrevPage,r.currentPage]),x=f.useCallback(()=>{const a=`page-${r.currentPage}-${e}`;c.current.delete(a),p(r.currentPage,{forceRefresh:!0})},[p,r.currentPage,e]),h=f.useCallback(()=>{c.current.clear()},[]),v=f.useCallback(e=>m(null,null,function*(){try{u({loading:!0});const{error:a}=yield Aa.from("informes_generados").delete().eq("id",e);if(a)throw a;return h(),yield p(r.currentPage,{forceRefresh:!0}),{success:!0}}catch(d){return u({loading:!1,error:d.message||"Error al eliminar el informe"}),{success:!1,error:d.message}}}),[p,r.currentPage,h,u]),j=f.useCallback(e=>m(null,null,function*(){try{u({loading:!0});const{error:a}=yield Aa.from("informes_generados").delete().in("id",e);if(a)throw a;return h(),yield p(r.currentPage,{forceRefresh:!0}),{success:!0,deletedCount:e.length}}catch(d){return u({loading:!1,error:d.message||"Error al eliminar los informes"}),{success:!1,error:d.message}}}),[p,r.currentPage,h,u]);return f.useEffect(()=>{if(a&&i>0)return o.current=setInterval(()=>{x()},i),()=>{o.current&&clearInterval(o.current)}},[a,i,x]),f.useEffect(()=>()=>{t.current&&t.current.abort(),o.current&&clearInterval(o.current)},[]),f.useEffect(()=>{p(1)},[p]),{informes:r.informes,loading:r.loading,error:r.error,currentPage:r.currentPage,totalCount:r.totalCount,totalPages:Math.ceil(r.totalCount/e),hasNextPage:r.hasNextPage,hasPrevPage:r.hasPrevPage,fetchInformes:p,goToPage:b,nextPage:N,prevPage:g,refresh:x,clearCache:h,deleteInforme:v,bulkDeleteInformes:j}})({}),v=i.size;c.length>0&&c.length,v>0&&c.length;const j=f.useCallback(e=>{a(e),t(1)},[]),V=f.useCallback(()=>{a(""),t(1)},[]),E=f.useCallback(e=>{r(a=>{const i=new Set(a);return i.has(e)?i.delete(e):i.add(e),i})},[]),C=f.useCallback(()=>{r(new Set(c.map(e=>e.id)))},[c]),y=f.useCallback(()=>{r(new Set)},[]),D=f.useCallback(e=>{$.success(`Abriendo informe: ${e.titulo}`)},[]),B=f.useCallback(e=>m(null,null,function*(){if(window.confirm("¿Está seguro de que desea eliminar este informe?"))try{yield g(e),r(a=>{const i=new Set(a);return i.delete(e),i}),$.success("Informe eliminado correctamente")}catch(a){$.error("Error al eliminar el informe")}}),[g]),w=f.useCallback(e=>{$.success(`Abriendo gráfico: ${e.titulo}`)},[]),A=f.useCallback(()=>m(null,null,function*(){const e=v;if(window.confirm(`¿Está seguro de que desea eliminar ${e} informe${1!==e?"s":""}?`))try{yield h(Array.from(i)),r(new Set),$.success(`${e} informe${1!==e?"s":""} eliminado${1!==e?"s":""} correctamente`)}catch(a){$.error("Error al eliminar los informes")}}),[v,i,h]),R=f.useCallback(()=>{$.success(`Exportando ${v} informe${1!==v?"s":""}...`)},[i,v]);return d?x.jsxDEV("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 text-center",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-red-900 mb-2",children:"Error al cargar informes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:236,columnNumber:9},void 0),x.jsxDEV("p",{className:"text-red-700 mb-4",children:d.message},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:237,columnNumber:9},void 0),x.jsxDEV(Ca,{onClick:N,variant:"outline",children:"Reintentar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:238,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:235,columnNumber:7},void 0):x.jsxDEV(ba,{children:x.jsxDEV("div",{className:"space-y-6",children:[x.jsxDEV("div",{className:"flex items-center justify-between",children:[x.jsxDEV("div",{children:[x.jsxDEV("h1",{className:"text-2xl font-bold text-gray-900",children:"Informes Generados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:251,columnNumber:13},void 0),x.jsxDEV("p",{className:"text-gray-600 mt-1",children:"Gestiona y visualiza los informes de evaluación generados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:252,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:250,columnNumber:11},void 0),x.jsxDEV(Ca,{className:"flex items-center gap-2",children:[x.jsxDEV(F,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:258,columnNumber:13},void 0),"Nuevo Informe"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:257,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:249,columnNumber:9},void 0),x.jsxDEV(bi,{searchTerm:e,onSearchChange:j,onClearSearch:V,totalCount:p,selectedCount:v},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:264,columnNumber:9},void 0),x.jsxDEV(Ni,{selectedCount:v,onBulkDelete:A,onBulkExport:R,disabled:u},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:273,columnNumber:9},void 0),x.jsxDEV(pi,{informes:c,selectedInformes:i,onToggleSelection:E,onSelectAll:C,onDeselectAll:y,onViewInforme:D,onDeleteInforme:B,onViewChart:w,isLoading:u,searchTerm:o,onClearSearch:V,height:600},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:281,columnNumber:9},void 0),b>1&&x.jsxDEV("div",{className:"flex items-center justify-between bg-white p-4 rounded-lg border border-gray-200",children:[x.jsxDEV("div",{className:"text-sm text-gray-600",children:["Página ",s," de ",b]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:299,columnNumber:13},void 0),x.jsxDEV("div",{className:"flex items-center gap-2",children:[x.jsxDEV(Ca,{variant:"outline",size:"sm",onClick:()=>t(e=>Math.max(1,e-1)),disabled:1===s||u,children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:304,columnNumber:15},void 0),x.jsxDEV(Ca,{variant:"outline",size:"sm",onClick:()=>t(e=>Math.min(b,e+1)),disabled:s===b||u,children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:313,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:303,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:298,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:247,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:246,columnNumber:5},void 0)},fi=()=>{const e=h(),{resultados:a,aplicacionId:i,testCompletado:r,cargarResultados:s,cargando:t}=f.useContext(ii),[o,n]=f.useState(!0),[l,c]=f.useState(null),[u,d]=f.useState({fortalezas:[],areas_mejora:[],recomendaciones:[]});f.useEffect(()=>{m(null,null,function*(){try{if(n(!0),!a&&i&&(yield s(i)),i){const e={fecha_evaluacion:"2025-06-12T10:00:00Z",candidatos:{id_candidato:"367894512",nombre:"Camila",apellido:"Vargas Vargas",sexo:"Femenino"}};e&&e.candidatos&&c({nombreCompleto:`${e.candidatos.nombre} ${e.candidatos.apellido}`,id_paciente:e.candidatos.id_candidato,sexo:e.candidatos.sexo,fecha_evaluacion:new Date(e.fecha_evaluacion).toLocaleDateString("es-ES",{day:"numeric",month:"long",year:"numeric"})})}a&&p(a)}catch(e){}finally{n(!1)}})},[i,a,s]);const p=e=>{const a=[],i=[],r=[];Object.entries(e).forEach(([e,r])=>{const{codigo:s,nombre:t,puntuacionCentil:o,interpretacion:n}=r;o>=70?a.push({codigo:s,nombre:t,interpretacion:`${n} (PC: ${o})`,descripcion:b(s,!0)}):o<=30&&i.push({codigo:s,nombre:t,interpretacion:`${n} (PC: ${o})`,descripcion:b(s,!1)})}),i.forEach(e=>{r.push({codigo:e.codigo,recomendacion:N(e.codigo)})}),d({fortalezas:a,areas_mejora:i,recomendaciones:r})},b=(e,a)=>{const i={V:{fortaleza:"Alta capacidad para comprender, utilizar y analizar el lenguaje escrito y hablado.",debilidad:"Dificultades para comprender conceptos expresados a través de palabras."},E:{fortaleza:"Excelente capacidad para visualizar y manipular mentalmente formas y patrones espaciales.",debilidad:"Dificultades para comprender relaciones espaciales y visualizar objetos en diferentes dimensiones."},A:{fortaleza:"Gran capacidad para mantener el foco en tareas específicas, detectando detalles con precisión.",debilidad:"Dificultad para mantener la concentración y detectar detalles específicos en tareas que requieren atención sostenida."},R:{fortaleza:"Destacada habilidad para identificar patrones lógicos y resolver problemas mediante el razonamiento.",debilidad:"Dificultades para identificar reglas lógicas y establecer inferencias en situaciones nuevas."},N:{fortaleza:"Excelente capacidad para comprender y manipular conceptos numéricos y resolver problemas matemáticos.",debilidad:"Dificultades en el manejo de conceptos numéricos y operaciones matemáticas básicas."},M:{fortaleza:"Buena comprensión de principios físicos y mecánicos básicos aplicados a situaciones cotidianas.",debilidad:"Dificultades para comprender el funcionamiento de dispositivos mecánicos y principios físicos básicos."},O:{fortaleza:"Excelente dominio de las reglas ortográficas y alta precisión en la escritura.",debilidad:"Dificultades con las reglas ortográficas y tendencia a cometer errores en la escritura."}};return i[e]?a?i[e].fortaleza:i[e].debilidad:"No hay descripción disponible."},N=e=>({V:"Fomentar la lectura diaria y realizar actividades que enriquezcan el vocabulario como juegos de palabras, debates y redacción.",E:"Practicar con rompecabezas, ejercicios de rotación mental, dibujo técnico y actividades que involucren navegación espacial.",A:"Realizar ejercicios de mindfulness, practicar tareas que requieran concentración por períodos cortos e ir aumentando gradualmente el tiempo.",R:"Resolver acertijos lógicos, participar en juegos de estrategia y analizar problemas complejos dividiéndolos en partes más sencillas.",N:"Practicar operaciones matemáticas diariamente, resolver problemas aplicados a la vida real y utilizar juegos que involucren cálculos.",M:"Construir modelos, experimentar con el funcionamiento de objetos cotidianos y estudiar los principios básicos de la física.",O:"Realizar ejercicios de dictado, revisión de textos y practicar la escritura consciente prestando atención a las reglas ortográficas."}[e]||"No hay recomendaciones específicas disponibles.");return o||t?x.jsxDEV("div",{className:"flex items-center justify-center h-screen",children:x.jsxDEV(pa,{fullScreen:!0,message:"Cargando resultados..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:219,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:218,columnNumber:7},void 0):a&&0!==Object.keys(a).length?x.jsxDEV("div",{className:"min-h-screen bg-gray-100 py-8 px-4 sm:px-6 lg:px-8",children:x.jsxDEV("div",{className:"max-w-5xl mx-auto",children:[x.jsxDEV("h1",{className:"text-3xl font-bold text-gray-800 mb-6",children:"Resultados Detallados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:255,columnNumber:9},void 0),l&&x.jsxDEV("div",{className:"bg-white shadow-lg rounded-lg p-6 mb-8 flex items-center justify-between",children:[x.jsxDEV("div",{className:"flex items-center",children:[x.jsxDEV("div",{className:"mr-4",children:"Femenino"===l.sexo?x.jsxDEV(J,{className:"text-pink-500 text-5xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:263,columnNumber:19},void 0):"Masculino"===l.sexo?x.jsxDEV(G,{className:"text-blue-500 text-5xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:265,columnNumber:19},void 0):x.jsxDEV(q,{className:"text-gray-500 text-5xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:266,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:261,columnNumber:15},void 0),x.jsxDEV("div",{children:[x.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900",children:l.nombreCompleto},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:270,columnNumber:17},void 0),x.jsxDEV("p",{className:"text-sm text-gray-600",children:["ID: ",l.id_paciente]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:271,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:269,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:260,columnNumber:13},void 0),x.jsxDEV("div",{className:"text-right",children:[x.jsxDEV("p",{className:"text-sm text-gray-700",children:"Fecha de Evaluación:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:275,columnNumber:15},void 0),x.jsxDEV("p",{className:"text-md font-semibold text-gray-800",children:l.fecha_evaluacion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:276,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:274,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:259,columnNumber:11},void 0),a&&Object.keys(a).length>0?x.jsxDEV("div",{className:"bg-white shadow-lg rounded-lg overflow-hidden mb-8",children:[x.jsxDEV("div",{className:"overflow-x-auto",children:x.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[x.jsxDEV("thead",{className:"bg-gray-50",children:x.jsxDEV("tr",{children:[x.jsxDEV("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:292,columnNumber:21},void 0),x.jsxDEV("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Puntaje PD"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:295,columnNumber:21},void 0),x.jsxDEV("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Puntuación T"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:298,columnNumber:21},void 0),x.jsxDEV("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Errores"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:301,columnNumber:21},void 0),x.jsxDEV("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tiempo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:304,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:291,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:290,columnNumber:17},void 0),x.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:Object.entries(a).map(([e,a])=>x.jsxDEV("tr",{children:[x.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:x.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:a.nombre||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:317,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:316,columnNumber:23},void 0),x.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:x.jsxDEV("div",{className:"text-sm text-gray-900",children:void 0!==a.puntuacionDirecta?a.puntuacionDirecta:"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:320,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:319,columnNumber:23},void 0),x.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:x.jsxDEV("div",{className:"text-sm text-gray-900",children:void 0!==a.puntuacionCentil?a.puntuacionCentil:"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:324,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:322,columnNumber:23},void 0),x.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:x.jsxDEV("div",{className:"text-sm text-gray-900",children:void 0!==a.errores?a.errores:"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:328,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:326,columnNumber:23},void 0),x.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:x.jsxDEV("div",{className:"text-sm text-gray-900",children:a.tiempo||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:332,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:330,columnNumber:23},void 0)]},e,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:315,columnNumber:21},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:313,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:289,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:288,columnNumber:13},void 0),x.jsxDEV("div",{className:"p-4 text-xs text-gray-500 bg-gray-50 border-t",children:[x.jsxDEV("p",{children:[x.jsxDEV("span",{className:"font-medium",children:"Puntaje PD:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:349,columnNumber:18},void 0)," Puntuación Directa - Número de respuestas correctas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:349,columnNumber:15},void 0),x.jsxDEV("p",{children:[x.jsxDEV("span",{className:"font-medium",children:"Puntuación T:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:350,columnNumber:18},void 0)," Puntuación Transformada (ej. Percentil) - Posición relativa respecto a la población de referencia."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:350,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:348,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:287,columnNumber:11},void 0):x.jsxDEV("div",{className:"bg-white shadow-lg rounded-lg p-6 mb-8 text-center",children:x.jsxDEV("p",{className:"text-gray-600",children:"No hay resultados de pruebas para mostrar para este paciente."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:355,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:354,columnNumber:11},void 0),(u.fortalezas.length>0||u.areas_mejora.length>0||u.recomendaciones.length>0)&&x.jsxDEV("div",{className:"bg-white shadow-lg rounded-lg mb-8",children:[x.jsxDEV("h2",{className:"text-xl font-semibold p-4 bg-gray-50 border-b text-gray-800",children:"Informe Cualitativo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:362,columnNumber:13},void 0),x.jsxDEV("div",{className:"p-6",children:[u.fortalezas.length>0&&x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-green-700 mb-2",children:"Fortalezas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:367,columnNumber:19},void 0),x.jsxDEV("div",{className:"space-y-3",children:u.fortalezas.map((e,a)=>x.jsxDEV("div",{className:"bg-green-50 p-3 rounded-md",children:[x.jsxDEV("div",{className:"font-semibold text-green-800",children:[e.nombre,": ",e.interpretacion]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:371,columnNumber:25},void 0),x.jsxDEV("p",{className:"text-sm text-green-700 mt-1",children:e.descripcion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:374,columnNumber:25},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:370,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:368,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:366,columnNumber:17},void 0),u.areas_mejora.length>0&&x.jsxDEV("div",{className:"mb-6",children:[x.jsxDEV("h3",{className:"text-lg font-medium text-red-700 mb-2",children:"Áreas de Mejora"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:383,columnNumber:19},void 0),x.jsxDEV("div",{className:"space-y-3",children:u.areas_mejora.map((e,a)=>x.jsxDEV("div",{className:"bg-red-50 p-3 rounded-md",children:[x.jsxDEV("div",{className:"font-semibold text-red-800",children:[e.nombre,": ",e.interpretacion]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:387,columnNumber:25},void 0),x.jsxDEV("p",{className:"text-sm text-red-700 mt-1",children:e.descripcion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:390,columnNumber:25},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:386,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:384,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:382,columnNumber:17},void 0),u.recomendaciones.length>0&&x.jsxDEV("div",{children:[x.jsxDEV("h3",{className:"text-lg font-medium text-blue-700 mb-2",children:"Recomendaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:399,columnNumber:19},void 0),x.jsxDEV("div",{className:"space-y-3",children:u.recomendaciones.map((e,i)=>{var r;return x.jsxDEV("div",{className:"bg-blue-50 p-3 rounded-md",children:[x.jsxDEV("div",{className:"font-semibold text-blue-800",children:[e.codigo," - ",null==(r=a[Object.keys(a).find(i=>a[i].codigo===e.codigo)])?void 0:r.nombre]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:403,columnNumber:25},void 0),x.jsxDEV("p",{className:"text-sm text-blue-700 mt-1",children:e.recomendacion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:404,columnNumber:25},void 0)]},i,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:402,columnNumber:23},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:400,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:398,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:363,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:361,columnNumber:12},void 0),x.jsxDEV("div",{className:"bg-white shadow-lg rounded-lg mb-8",children:[x.jsxDEV("h2",{className:"text-xl font-semibold p-4 bg-gray-50 border-b text-gray-800",children:"Test de Conexión"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:416,columnNumber:11},void 0),x.jsxDEV("div",{className:"p-6"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:417,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:415,columnNumber:9},void 0),x.jsxDEV("div",{className:"bg-white shadow-lg rounded-lg mb-8",children:[x.jsxDEV("h2",{className:"text-xl font-semibold p-4 bg-gray-50 border-b text-gray-800",children:"Informes Generados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:424,columnNumber:11},void 0),x.jsxDEV("div",{className:"p-6",children:x.jsxDEV(gi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:426,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:425,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:423,columnNumber:9},void 0),x.jsxDEV("div",{className:"flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 mt-8",children:[x.jsxDEV("button",{className:"px-6 py-3 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition duration-150",onClick:()=>e("/test"),children:"Volver"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:432,columnNumber:11},void 0),x.jsxDEV("div",{className:"flex space-x-3",children:[x.jsxDEV("button",{className:"px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition duration-150",onClick:()=>window.print(),children:"Imprimir Resultados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:439,columnNumber:13},void 0),x.jsxDEV("button",{className:"px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition duration-150",onClick:()=>alert("Función de exportar a PDF en desarrollo."),children:"Exportar a PDF"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:445,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:438,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:431,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:254,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:253,columnNumber:5},void 0):x.jsxDEV("div",{className:"flex items-center justify-center h-screen bg-blue-50",children:x.jsxDEV("div",{className:"bg-white p-8 rounded-lg shadow-md max-w-md",children:x.jsxDEV("div",{className:"text-center",children:[x.jsxDEV("svg",{className:"mx-auto h-16 w-16 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:x.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:231,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:230,columnNumber:13},void 0),x.jsxDEV("h2",{className:"mt-4 text-xl font-bold text-gray-800",children:"No hay resultados disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:233,columnNumber:13},void 0),x.jsxDEV("p",{className:"mt-2 text-gray-600",children:"No se han encontrado resultados para mostrar. Es posible que aún no hayas completado el test o que haya ocurrido un error."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:234,columnNumber:13},void 0),x.jsxDEV("div",{className:"mt-6",children:x.jsxDEV("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",onClick:()=>e("/test"),children:"Volver a Tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:238,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:237,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:229,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:228,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:227,columnNumber:7},void 0)},xi=()=>x.jsxDEV(W,{children:[x.jsxDEV(Y,{path:"/instructions/:testId",element:x.jsxDEV(Ba,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:24,columnNumber:52},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:24,columnNumber:7},void 0),x.jsxDEV(Y,{path:"/verbal",element:x.jsxDEV(qa,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:27,columnNumber:38},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:27,columnNumber:7},void 0),x.jsxDEV(Y,{path:"/ortografia",element:x.jsxDEV(Ma,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:28,columnNumber:42},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:28,columnNumber:7},void 0),x.jsxDEV(Y,{path:"/razonamiento",element:x.jsxDEV(La,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:29,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:29,columnNumber:7},void 0),x.jsxDEV(Y,{path:"/atencion",element:x.jsxDEV(Fa,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:30,columnNumber:40},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:30,columnNumber:7},void 0),x.jsxDEV(Y,{path:"/espacial",element:x.jsxDEV(Ua,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:31,columnNumber:40},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:31,columnNumber:7},void 0),x.jsxDEV(Y,{path:"/mecanico",element:x.jsxDEV(Ja,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:32,columnNumber:40},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:32,columnNumber:7},void 0),x.jsxDEV(Y,{path:"/numerico",element:x.jsxDEV(Za,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:33,columnNumber:40},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:33,columnNumber:7},void 0),x.jsxDEV(Y,{path:"/results/:resultId",element:x.jsxDEV(ei,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:36,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:36,columnNumber:7},void 0),x.jsxDEV(Y,{path:"/resultados/:resultId",element:x.jsxDEV(fi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:37,columnNumber:52},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:37,columnNumber:7},void 0),x.jsxDEV(Y,{path:"/",element:x.jsxDEV(B,{to:"/student/tests",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:40,columnNumber:32},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:40,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:22,columnNumber:5},void 0),hi=()=>x.jsxDEV("div",{className:"flex justify-center items-center min-h-screen",children:x.jsxDEV("div",{className:"text-center",children:[x.jsxDEV(Z,{className:"animate-spin text-blue-600 mx-auto mb-4 text-4xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:18,columnNumber:7},void 0),x.jsxDEV("p",{className:"text-gray-600 font-medium",children:"Cargando..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:19,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:17,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:16,columnNumber:3},void 0),vi=e=>a=>x.jsxDEV(ba,{children:x.jsxDEV(e,n({},a),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:27,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:26,columnNumber:3},void 0),ji=e=>a=>x.jsxDEV(ba,{children:x.jsxDEV(f.Suspense,{fallback:x.jsxDEV(hi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:34,columnNumber:25},void 0),children:x.jsxDEV(e,n({},a),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:35,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:34,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:33,columnNumber:3},void 0),Vi=ji(f.lazy(()=>R(()=>import("./Dashboard-By-A3w0U.js"),__vite__mapDeps([4,1,2,5,3,6])))),Ei=ji(f.lazy(()=>R(()=>import("./Profile-CmhsO56d.js"),__vite__mapDeps([7,1,2])))),Ci=ji(f.lazy(()=>R(()=>import("./Settings-D29HDXcH.js"),__vite__mapDeps([8,1,2,6])))),yi=ji(f.lazy(()=>R(()=>import("./Home-C8IC7kLs.js"),__vite__mapDeps([9,1,2])))),Di=ji(f.lazy(()=>R(()=>import("./Help-C1P9aIaX.js"),__vite__mapDeps([10,1,2,11])))),Bi=ji(f.lazy(()=>R(()=>import("./Configuracion-DDoyuVvc.js"),__vite__mapDeps([12,1,2,11,13,3])))),wi=vi(f.lazy(()=>R(()=>import("./Candidates-CMjiwpbT.js"),__vite__mapDeps([14,1,2,15])))),Ai=vi(f.lazy(()=>R(()=>import("./VerbalInfo-BUE3FkkE.js"),__vite__mapDeps([16,1,2])))),Ri=ji(f.lazy(()=>R(()=>import("./Users-DqDIbwFi.js"),__vite__mapDeps([17,1,2])))),Ti=ji(f.lazy(()=>R(()=>import("./Institutions-CS_6dbxn.js"),__vite__mapDeps([18,1,2])))),Ii=ji(f.lazy(()=>R(()=>import("./Reports-OAjgqATU.js"),__vite__mapDeps([19,1,2,0,3,11,20])))),zi=ji(f.lazy(()=>R(()=>import("./Patients-CI8AwP-m.js"),__vite__mapDeps([21,1,2,11])))),Si=ji(f.lazy(()=>R(()=>import("./Administration-ouykJIdO.js"),__vite__mapDeps([22,1,2,11])))),qi=ji(f.lazy(()=>R(()=>import("./TestPage-Dzn6P88L.js"),__vite__mapDeps([23,1,2,5])))),Pi=ji(f.lazy(()=>R(()=>import("./CompleteReport-BYdz7KY5.js"),__vite__mapDeps([24,1,2,25])))),Mi=ji(f.lazy(()=>R(()=>import("./SavedReports-CALKB9OU.js"),__vite__mapDeps([26,1,2])))),_i=ji(f.lazy(()=>R(()=>import("./ViewSavedReport-1RHPfgYI.js"),__vite__mapDeps([27,1,2,25])))),ki=ji(f.lazy(()=>R(()=>import("./PinAssignmentPanel-GP7D23rK.js"),__vite__mapDeps([28,1,2,13])))),Oi=ji(f.lazy(()=>R(()=>import("./Students-CBhBRc-M.js"),__vite__mapDeps([29,1,2])))),Li=ji(f.lazy(()=>R(()=>import("./Tests-BvRoNzkc.js"),__vite__mapDeps([30,1,2])))),$i=ji(f.lazy(()=>R(()=>import("./Reports-CYHl12v3.js"),__vite__mapDeps([31,1,2])))),Fi=ji(f.lazy(()=>R(()=>Promise.resolve().then(()=>lr),void 0))),Qi=ji(f.lazy(()=>R(()=>import("./Tests-TiOHsE2T.js"),__vite__mapDeps([32,1,2,33,34])))),Ui=ji(f.lazy(()=>R(()=>import("./Results-Cb2ZYrtP.js"),__vite__mapDeps([35,1,2,15,11])))),Hi=ji(f.lazy(()=>R(()=>import("./Patients-DFxJmHQx.js"),__vite__mapDeps([36,1,2,37])))),Ji=ji(f.lazy(()=>R(()=>import("./Questionnaire-Bv8A40Ij.js"),__vite__mapDeps([38,1,2,33,34,11])))),Gi=ji(f.lazy(()=>R(()=>import("./InformePaciente-DJgp1Ajm.js"),__vite__mapDeps([39,1,2])))),Wi=ji(f.lazy(()=>R(()=>Promise.resolve().then(()=>wa),void 0))),Yi=ji(f.lazy(()=>R(()=>Promise.resolve().then(()=>Pa),void 0))),Zi=ji(f.lazy(()=>R(()=>Promise.resolve().then(()=>Ha),void 0))),Xi=ji(f.lazy(()=>R(()=>Promise.resolve().then(()=>Qa),void 0))),Ki=ji(f.lazy(()=>R(()=>Promise.resolve().then(()=>$a),void 0))),er=ji(f.lazy(()=>R(()=>Promise.resolve().then(()=>Xa),void 0))),ar=ji(f.lazy(()=>R(()=>Promise.resolve().then(()=>Ga),void 0))),ir=ji(f.lazy(()=>R(()=>Promise.resolve().then(()=>_a),void 0))),rr=ji(f.lazy(()=>R(()=>Promise.resolve().then(()=>ai),void 0))),sr=ji(f.lazy(()=>R(()=>import("./BasicLogin-Dj7A0Hte.js"),__vite__mapDeps([40,1,2])))),tr=()=>{const e=E();return f.useEffect(()=>{},[e.pathname]),x.jsxDEV(ba,{children:x.jsxDEV(f.Suspense,{fallback:x.jsxDEV(hi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:107,columnNumber:27},void 0),children:x.jsxDEV(W,{children:[x.jsxDEV(Y,{path:"/",element:x.jsxDEV(B,{to:"/home",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:110,columnNumber:36},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:110,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/login",element:x.jsxDEV(sr,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:113,columnNumber:41},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:113,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/register",element:x.jsxDEV(B,{to:"/login",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:114,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:114,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/auth/troubleshooting",element:x.jsxDEV(B,{to:"/login",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:115,columnNumber:56},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:115,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/auth",element:x.jsxDEV(B,{to:"/login",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:116,columnNumber:40},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:116,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/force-admin",element:x.jsxDEV(B,{to:"/admin/administration",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:118,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:118,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/info/verbal",element:x.jsxDEV(Ai,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:119,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:119,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/test/instructions/:testId",element:x.jsxDEV(Wi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:122,columnNumber:61},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:122,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/test/verbal",element:x.jsxDEV(Yi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:123,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:123,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/test/espacial",element:x.jsxDEV(Zi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:124,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:124,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/test/atencion",element:x.jsxDEV(Xi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:125,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:125,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/test/razonamiento",element:x.jsxDEV(Ki,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:126,columnNumber:53},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:126,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/test/numerico",element:x.jsxDEV(er,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:127,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:127,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/test/mecanico",element:x.jsxDEV(ar,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:128,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:128,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/test/ortografia",element:x.jsxDEV(ir,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:129,columnNumber:51},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:129,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/test/results/:applicationId",element:x.jsxDEV(rr,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:130,columnNumber:63},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:130,columnNumber:11},void 0),x.jsxDEV(Y,{path:"/test/*",element:x.jsxDEV(xi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:131,columnNumber:42},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:131,columnNumber:11},void 0),x.jsxDEV(Y,{element:x.jsxDEV(da,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:134,columnNumber:27},void 0),children:[x.jsxDEV(Y,{path:"/dashboard",element:x.jsxDEV(B,{to:"/home",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:136,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:136,columnNumber:13},void 0),x.jsxDEV(Y,{path:"/profile",element:x.jsxDEV(Ei,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:137,columnNumber:45},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:137,columnNumber:13},void 0),x.jsxDEV(Y,{path:"/settings",element:x.jsxDEV(Ci,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:138,columnNumber:46},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:138,columnNumber:13},void 0),x.jsxDEV(Y,{path:"/home",element:x.jsxDEV(yi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:139,columnNumber:42},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:139,columnNumber:13},void 0),x.jsxDEV(Y,{path:"/help",element:x.jsxDEV(Di,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:140,columnNumber:42},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:140,columnNumber:13},void 0),x.jsxDEV(Y,{path:"/configuracion",element:x.jsxDEV(Na,{children:x.jsxDEV(Bi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:141,columnNumber:63},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:141,columnNumber:51},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:141,columnNumber:13},void 0),x.jsxDEV(Y,{path:"/admin",children:[x.jsxDEV(Y,{index:!0,element:x.jsxDEV(Na,{children:x.jsxDEV(Si,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:145,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:145,columnNumber:37},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:145,columnNumber:15},void 0),x.jsxDEV(Y,{path:"dashboard",element:x.jsxDEV(Na,{children:x.jsxDEV(Vi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:146,columnNumber:60},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:146,columnNumber:48},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:146,columnNumber:15},void 0),x.jsxDEV(Y,{path:"users",element:x.jsxDEV(Na,{children:x.jsxDEV(Ri,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:147,columnNumber:56},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:147,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:147,columnNumber:15},void 0),x.jsxDEV(Y,{path:"institutions",element:x.jsxDEV(Na,{children:x.jsxDEV(Ti,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:148,columnNumber:63},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:148,columnNumber:51},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:148,columnNumber:15},void 0),x.jsxDEV(Y,{path:"reports",element:x.jsxDEV(Na,{children:x.jsxDEV(Ii,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:149,columnNumber:58},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:149,columnNumber:46},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:149,columnNumber:15},void 0),x.jsxDEV(Y,{path:"patients",element:x.jsxDEV(Na,{children:x.jsxDEV(zi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:150,columnNumber:59},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:150,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:150,columnNumber:15},void 0),x.jsxDEV(Y,{path:"administration",element:x.jsxDEV(Na,{children:x.jsxDEV(Si,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:151,columnNumber:65},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:151,columnNumber:53},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:151,columnNumber:15},void 0),x.jsxDEV(Y,{path:"configuracion",element:x.jsxDEV(Na,{children:x.jsxDEV(Bi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:152,columnNumber:64},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:152,columnNumber:52},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:152,columnNumber:15},void 0),x.jsxDEV(Y,{path:"tests",element:x.jsxDEV(Na,{children:x.jsxDEV(qi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:153,columnNumber:56},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:153,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:153,columnNumber:15},void 0),x.jsxDEV(Y,{path:"pines",element:x.jsxDEV(Na,{children:x.jsxDEV(ki,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:154,columnNumber:56},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:154,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:154,columnNumber:15},void 0),x.jsxDEV(Y,{path:"informe-completo/:patientId",element:x.jsxDEV(Na,{children:x.jsxDEV(Pi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:155,columnNumber:78},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:155,columnNumber:66},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:155,columnNumber:15},void 0),x.jsxDEV(Y,{path:"informes-guardados",element:x.jsxDEV(Na,{children:x.jsxDEV(Mi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:156,columnNumber:69},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:156,columnNumber:57},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:156,columnNumber:15},void 0),x.jsxDEV(Y,{path:"informe-guardado/:reportId",element:x.jsxDEV(Na,{children:x.jsxDEV(_i,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:157,columnNumber:77},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:157,columnNumber:65},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:157,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:144,columnNumber:13},void 0),x.jsxDEV(Y,{path:"/professional",children:[x.jsxDEV(Y,{index:!0,element:x.jsxDEV(Vi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:162,columnNumber:37},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:162,columnNumber:15},void 0),x.jsxDEV(Y,{path:"dashboard",element:x.jsxDEV(Vi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:163,columnNumber:48},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:163,columnNumber:15},void 0),x.jsxDEV(Y,{path:"students",element:x.jsxDEV(Oi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:164,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:164,columnNumber:15},void 0),x.jsxDEV(Y,{path:"tests",element:x.jsxDEV(Li,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:165,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:165,columnNumber:15},void 0),x.jsxDEV(Y,{path:"reports",element:x.jsxDEV($i,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:166,columnNumber:46},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:166,columnNumber:15},void 0),x.jsxDEV(Y,{path:"candidates",element:x.jsxDEV(wi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:167,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:167,columnNumber:15},void 0),x.jsxDEV(Y,{path:"patients",element:x.jsxDEV(Fi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:168,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:168,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:161,columnNumber:13},void 0),x.jsxDEV(Y,{path:"/student",children:[x.jsxDEV(Y,{index:!0,element:x.jsxDEV(Vi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:173,columnNumber:37},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:173,columnNumber:15},void 0),x.jsxDEV(Y,{path:"dashboard",element:x.jsxDEV(Vi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:174,columnNumber:48},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:174,columnNumber:15},void 0),x.jsxDEV(Y,{path:"tests",element:x.jsxDEV(Qi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:175,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:175,columnNumber:15},void 0),x.jsxDEV(Y,{path:"questionnaire",element:x.jsxDEV(Ji,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:176,columnNumber:52},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:176,columnNumber:15},void 0),x.jsxDEV(Y,{path:"results",element:x.jsxDEV(Ui,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:177,columnNumber:46},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:177,columnNumber:15},void 0),x.jsxDEV(Y,{path:"informe/:pacienteId",element:x.jsxDEV(Gi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:178,columnNumber:58},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:178,columnNumber:15},void 0),x.jsxDEV(Y,{path:"patients",element:x.jsxDEV(Hi,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:179,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:179,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:172,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:134,columnNumber:11},void 0),x.jsxDEV(Y,{path:"*",element:x.jsxDEV(B,{to:"/home",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:184,columnNumber:36},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:184,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:108,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:107,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:106,columnNumber:5},void 0)},or=()=>x.jsxDEV(X,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:x.jsxDEV(ha,{children:x.jsxDEV(tr,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:203,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:202,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:196,columnNumber:5},void 0);function nr(){return f.useEffect(()=>{const e=localStorage.getItem("userTheme");("dark"===e||"system"===e&&window.matchMedia("(prefers-color-scheme: dark)").matches)&&document.body.classList.add("dark-mode");const a=window.matchMedia("(prefers-color-scheme: dark)"),i=e=>{"system"===localStorage.getItem("userTheme")&&(e.matches?document.body.classList.add("dark-mode"):document.body.classList.remove("dark-mode"))};return a.addEventListener("change",i),()=>{a.removeEventListener("change",i)}},[]),x.jsxDEV("div",{className:"app",children:[x.jsxDEV(V,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!0,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/App.jsx",lineNumber:48,columnNumber:7},this),x.jsxDEV(or,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/App.jsx",lineNumber:61,columnNumber:7},this)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/App.jsx",lineNumber:46,columnNumber:5},this)}K.createRoot(document.getElementById("root")).render(x.jsxDEV(C.StrictMode,{children:x.jsxDEV(ee,{store:ta,children:x.jsxDEV(na,{children:x.jsxDEV(nr,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/main.jsx",lineNumber:17,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/main.jsx",lineNumber:16,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/main.jsx",lineNumber:15,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/main.jsx",lineNumber:14,columnNumber:3},void 0));const lr=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));export{Ca as B,va as C,Ka as P,za as S,Va as a,ja as b,Ia as c,ri as d,Ea as e,Ta as f,ka as g,xa as h,ti as i,si as j,Aa as s,la as u};
