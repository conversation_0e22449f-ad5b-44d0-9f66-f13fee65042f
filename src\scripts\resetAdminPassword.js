import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjMxMjg0MSwiZXhwIjoyMDYxODg4ODQxfQ.zwk3Wiay5jjeYOrg8B1M6T98B2esCqRFI43At-AFV3A';

// Crear cliente con service key para operaciones administrativas
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function resetAdminPassword() {
  console.log('🔑 Reseteando contraseña del administrador...');

  try {
    // Buscar el usuario administrador
    const { data: adminUser, error: findError } = await supabase
      .from('usuarios')
      .select('id, email, nombre, apellido')
      .eq('documento', '13716261')
      .single();

    if (findError || !adminUser) {
      console.error('❌ No se encontró el usuario administrador:', findError);
      return;
    }

    console.log('👤 Usuario encontrado:', adminUser);

    // Resetear contraseña usando el Admin API
    const { data, error } = await supabase.auth.admin.updateUserById(
      adminUser.id,
      { 
        password: 'admin123',
        email_confirm: true
      }
    );

    if (error) {
      console.error('❌ Error reseteando contraseña:', error);
      return;
    }

    console.log('✅ Contraseña reseteada exitosamente!');
    console.log('\n🎉 Credenciales de acceso:');
    console.log('📧 Email:', adminUser.email);
    console.log('🔑 Password: admin123');
    console.log('👑 Rol: administrador');
    console.log('\n🌐 Puedes iniciar sesión en: http://localhost:5173');

  } catch (error) {
    console.error('❌ Error inesperado:', error);
  }
}

resetAdminPassword();
