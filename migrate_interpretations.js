/**
 * Script para migrar interpretaciones oficiales a Supabase
 * Ejecutar con: node migrate_interpretations.js
 */

import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMjU3NzE5NywiZXhwIjoyMDQ4MTUzMTk3fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Interpretaciones hardcodeadas basadas en el archivo oficial
function getInterpretations() {
  return {
    'V': {
      1: {
        rendimiento: 'El evaluado presenta dificultades significativas para establecer relaciones entre términos lingüísticos y para resolver problemas expresados verbalmente. Su habilidad para comprender y expresar ideas de manera fluida es muy limitada, y su nivel de vocabulario y conocimiento cultural es marcadamente inferior a lo esperado para su edad.',
        academico: 'Se espera un rendimiento académico muy bajo en asignaturas que requieran una alta comprensión lectora, redacción, análisis de textos o expresión oral. Necesitará un apoyo intensivo y estrategias de enseñanza muy estructuradas.',
        vocacional: 'La capacidad de comunicación puede estar seriamente comprometida, lo que podría afectar la resolución de problemas que dependan del lenguaje. Se anticipan dificultades en áreas profesionales que demanden una comunicación verbal o escrita precisa y compleja.'
      },
      2: {
        rendimiento: 'Se observa una capacidad por debajo del promedio para trabajar con información verbal, lo que puede manifestarse en una comprensión de ideas y conceptos menos profunda que la de sus pares. La fluidez verbal y la precisión en la expresión escrita son limitadas, y el nivel de vocabulario es inferior a lo esperado.',
        academico: 'Puede presentar dificultades en asignaturas teóricas con un alto componente de lectura y escritura. Podría requerir estrategias de estudio que se centren en la repetición y el apoyo visual para compensar las limitaciones verbales.',
        vocacional: 'Es posible que tenga dificultades para resolver problemas cotidianos que impliquen razonamiento verbal. En el ámbito vocacional, podría ser más exitoso en campos que se basen menos en habilidades lingüísticas y más en habilidades prácticas o no verbales.'
      },
      3: {
        rendimiento: 'La habilidad para establecer relaciones entre términos lingüísticos y resolver problemas verbales es ligeramente inferior a la media de su grupo de referencia. El evaluado podría mostrar algunas dificultades para establecer conexiones semánticas entre conceptos y para comprender y expresar ideas con la claridad y fluidez esperadas.',
        academico: 'Es probable que su rendimiento en asignaturas como Lengua y Literatura sea inferior al de sus compañeros. Su nivel de vocabulario y su habilidad para trabajar con información cultural son ligeramente inferiores a lo que cabría esperar para su nivel de estudios. Se beneficiaría de un refuerzo en la lectura comprensiva y la ampliación de vocabulario.',
        vocacional: 'Aunque es capaz de comunicarse adecuadamente, puede tener un ritmo más lento al procesar y verbalizar información compleja. Se recomienda la exploración de profesiones que no dependan exclusivamente de una aptitud verbal superior, pero que sí permitan el desarrollo de esta habilidad.'
      },
      4: {
        rendimiento: 'El evaluado muestra un nivel promedio de aptitud verbal. Posee una habilidad similar a la de sus pares para captar relaciones entre conceptos verbales, comprender y expresar el lenguaje de forma oral o escrita, y posee un nivel promedio de vocabulario.',
        academico: 'Su rendimiento académico en asignaturas de lenguaje, lectura y escritura es adecuado y se encuentra dentro de lo esperado. Generalmente, es capaz de recuperar la información verbal de la memoria a largo plazo y segmentar las palabras en componentes.',
        vocacional: 'No se observan dificultades notables en la comunicación o la resolución de problemas verbales. El evaluado tiene el potencial para desempeñarse en una amplia gama de campos profesionales, con un desarrollo normal de sus habilidades lingüísticas.'
      },
      5: {
        rendimiento: 'La habilidad para operar con conceptos verbales es superior a la media. El evaluado muestra una notable facilidad para establecer relaciones complejas entre términos lingüísticos, posee un buen nivel de vocabulario y cultura general, y es capaz de expresar ideas de manera clara y precisa.',
        academico: 'El rendimiento en asignaturas que demandan lectura, análisis crítico y redacción es sólido. Tiene una mayor facilidad para el aprendizaje de idiomas y para la comprensión de textos complejos.',
        vocacional: 'Muestra una gran capacidad para la comunicación efectiva y para la resolución de problemas que requieren la verbalización de conceptos abstractos. Puede destacar en áreas profesionales como la docencia, el derecho, el periodismo o la escritura.'
      },
      6: {
        rendimiento: 'El evaluado exhibe una excelente aptitud verbal, superando a la gran mayoría de sus pares. Muestra una gran destreza para establecer y trabajar con relaciones semánticas, un vocabulario muy amplio y una alta capacidad para recuperar información verbal de la memoria a largo plazo. Su expresión oral y escrita es notablemente fluida y precisa.',
        academico: 'Su rendimiento en todas las asignaturas con componente verbal es superior, lo que le permite procesar y asimilar información académica de manera muy eficiente. Puede dominar rápidamente nuevos conceptos y lenguajes.',
        vocacional: 'La facilidad para la comunicación y la expresión de ideas es una fortaleza evidente. Destacará en cualquier campo profesional que requiera el uso del lenguaje como herramienta principal, como la investigación, la diplomacia, la literatura o la psicología.'
      },
      7: {
        rendimiento: 'El evaluado posee una aptitud verbal sobresaliente y excepcional. Su habilidad para comprender, analizar y manipular conceptos verbales está muy por encima del promedio, demostrando una maestría en el uso del lenguaje.',
        academico: 'Su capacidad para el aprendizaje académico es extraordinariamente alta, especialmente en las áreas de humanidades y ciencias sociales. La asimilación de nuevos conocimientos verbales es rápida y profunda.',
        vocacional: 'Se caracteriza por una agilidad mental y una gran destreza para el pensamiento abstracto y la resolución de problemas a través del lenguaje. Su potencial es óptimo para áreas de liderazgo, investigación avanzada, y profesiones que requieran una comunicación estratégica y persuasiva.'
      }
    },
    'R': {
      1: {
        rendimiento: 'El evaluado presenta dificultades muy significativas para resolver problemas abstractos y novedosos. Muestra una incapacidad para identificar y deducir las leyes lógicas que rigen las secuencias, lo que limita su capacidad para formular y comprobar hipótesis.',
        academico: 'Se esperan grandes dificultades en asignaturas como Matemáticas, Lógica, Física o cualquier materia que requiera el uso de razonamiento abstracto. El aprendizaje de conceptos nuevos será un proceso muy lento y arduo.',
        vocacional: 'La capacidad de resolver problemas complejos y de adaptarse a situaciones no familiares es muy limitada. El ámbito vocacional debe enfocarse en áreas que requieran un razonamiento concreto y práctico, evitando campos que demanden una alta capacidad de abstracción.'
      },
      2: {
        rendimiento: 'La habilidad para aplicar el razonamiento deductivo e inductivo a problemas novedosos está por debajo del promedio. Presenta dificultades para identificar las reglas lógicas que subyacen a las secuencias y para formular hipótesis de diverso tipo.',
        academico: 'Puede tener problemas en asignaturas que requieren el pensamiento lógico y la resolución de problemas abstractos, como las ciencias exactas. El aprendizaje de nuevos conocimientos se realiza de forma lenta y con apoyo.',
        vocacional: 'La resolución de problemas complejos y la adaptación a situaciones novedosas no son sus puntos fuertes. Las áreas profesionales que requieran un alto nivel de razonamiento abstracto pueden ser una fuente de frustración.'
      },
      3: {
        rendimiento: 'La capacidad para el razonamiento no verbal es ligeramente inferior a la media. El evaluado muestra algunas dificultades para solucionar problemas abstractos y para razonar con situaciones novedosas.',
        academico: 'Podría tener un rendimiento medio-bajo en matemáticas y ciencias, necesitando un enfoque más estructurado para la resolución de problemas. El aprendizaje de conceptos abstractos requiere de un esfuerzo adicional y de un apoyo pedagógico constante.',
        vocacional: 'Se adapta a situaciones nuevas con cierta dificultad. En el ámbito vocacional, se sugiere la exploración de carreras que se basen más en la aplicación de reglas conocidas que en la formulación de nuevas hipótesis.'
      },
      4: {
        rendimiento: 'El evaluado demuestra un nivel promedio de razonamiento no verbal. Es capaz de solucionar problemas abstractos y razonar con situaciones novedosas de manera adecuada. Tiene una habilidad satisfactoria para establecer y trabajar con secuencias y para identificar las leyes lógicas que las rigen.',
        academico: 'Su rendimiento académico en asignaturas que requieren razonamiento abstracto es adecuado. Es capaz de aprender nuevos conceptos con un ritmo normal y de aplicarlos de manera efectiva.',
        vocacional: 'La capacidad para resolver problemas lógicos y adaptarse a situaciones no familiares es funcional. El potencial para la mayoría de las profesiones es adecuado, con un desarrollo normal de su capacidad de razonamiento.'
      },
      5: {
        rendimiento: 'El evaluado presenta una capacidad superior a la media para el razonamiento abstracto y la resolución de problemas novedosos. Muestra una notable habilidad para formular y contrastar hipótesis, y para identificar las reglas que subyacen a las secuencias.',
        academico: 'Su rendimiento es destacado en asignaturas como Matemáticas, Física, Química o Lógica. Tiene una mayor facilidad para el aprendizaje de conceptos complejos y para la aplicación de conocimientos a situaciones nuevas.',
        vocacional: 'La resolución de problemas complejos y la adaptabilidad son puntos fuertes. Tiene un potencial elevado para profesiones que requieran un pensamiento analítico y lógico, como la ingeniería, la investigación o la programación.'
      },
      6: {
        rendimiento: 'El evaluado demuestra una habilidad sobresaliente para el razonamiento no verbal de tipo deductivo, superando a la gran mayoría de sus pares. Destaca por su capacidad para solucionar problemas abstractos y razonar con situaciones novedosas y complejas. Su capacidad para establecer y trabajar con secuencias y para identificar las leyes lógicas es excelente.',
        academico: 'Su rendimiento en asignaturas que demandan razonamiento lógico y abstracto es superior, lo que le permite destacar en ciencias y matemáticas.',
        vocacional: 'Esta aptitud es una gran fortaleza, lo que le confiere una ventaja en la resolución de problemas de gran complejidad. Su potencial es óptimo para profesiones como la investigación, la ciencia de datos o la filosofía.'
      },
      7: {
        rendimiento: 'El evaluado posee una capacidad de razonamiento no verbal excepcional y sobresaliente. Su habilidad para aplicar el razonamiento inductivo y deductivo a problemas novedosos con contenido no verbal es extraordinaria. Es capaz de formular y poner a prueba hipótesis de diverso tipo con una agilidad y precisión únicas.',
        academico: 'Su rendimiento académico es extraordinario, especialmente en las áreas de ciencias exactas, donde puede sobresalir con gran facilidad. La asimilación de nuevos conocimientos abstractos es excepcionalmente rápida.',
        vocacional: 'Su potencial para la innovación, la investigación y la resolución de problemas complejos es óptimo. Puede destacarse en cualquier campo que requiera un pensamiento analítico y una capacidad de razonamiento superior, como la física teórica, la neurociencia o la inteligencia artificial.'
      }
    }
  };
}

// Función para migrar interpretaciones
async function migrateInterpretations() {
  console.log('🚀 Iniciando migración de interpretaciones...');

  try {
    const interpretations = getInterpretations();
    console.log('📊 Interpretaciones disponibles:', Object.keys(interpretations));
    
    let totalInserted = 0;
    let errors = 0;
    
    for (const [aptitudCodigo, niveles] of Object.entries(interpretations)) {
      console.log(`\n📝 Procesando aptitud: ${aptitudCodigo}`);
      
      for (const [nivelId, interpretacion] of Object.entries(niveles)) {
        try {
          const { data, error } = await supabase
            .from('interpretaciones_oficiales')
            .upsert({
              aptitud_codigo: aptitudCodigo,
              nivel_id: parseInt(nivelId),
              rendimiento: interpretacion.rendimiento || 'Sin interpretación disponible',
              academico: interpretacion.academico || 'Sin interpretación disponible',
              vocacional: interpretacion.vocacional || 'Sin interpretación disponible',
              es_oficial: true,
              fuente: 'Documento oficial BAT-7 - Interpretacion de aptitudes y Generalidaes.txt'
            }, {
              onConflict: 'aptitud_codigo,nivel_id'
            });
          
          if (error) {
            console.error(`❌ Error insertando ${aptitudCodigo}-${nivelId}:`, error);
            errors++;
          } else {
            console.log(`✅ Insertada interpretación ${aptitudCodigo}-${nivelId}`);
            totalInserted++;
          }
          
        } catch (err) {
          console.error(`❌ Error procesando ${aptitudCodigo}-${nivelId}:`, err);
          errors++;
        }
      }
    }
    
    console.log('\n📊 Resumen de migración:');
    console.log(`✅ Interpretaciones insertadas: ${totalInserted}`);
    console.log(`❌ Errores: ${errors}`);
    
    // Verificar migración
    await verifyMigration();
    
  } catch (error) {
    console.error('💥 Error durante la migración:', error);
  }
}

// Función para verificar migración
async function verifyMigration() {
  console.log('\n🔍 Verificando migración...');
  
  try {
    const { data: conteos, error } = await supabase
      .from('interpretaciones_oficiales')
      .select('aptitud_codigo, nivel_id')
      .eq('es_oficial', true);
    
    if (error) {
      console.error('❌ Error verificando migración:', error);
      return;
    }
    
    const conteoPorAptitud = {};
    conteos.forEach(item => {
      if (!conteoPorAptitud[item.aptitud_codigo]) {
        conteoPorAptitud[item.aptitud_codigo] = 0;
      }
      conteoPorAptitud[item.aptitud_codigo]++;
    });
    
    console.log('\n📈 Interpretaciones por aptitud en Supabase:');
    Object.keys(conteoPorAptitud).sort().forEach(aptitud => {
      const count = conteoPorAptitud[aptitud];
      const status = count === 7 ? '✅' : '⚠️';
      console.log(`${status} ${aptitud}: ${count}/7 interpretaciones`);
    });
    
  } catch (error) {
    console.error('💥 Error durante verificación:', error);
  }
}

// Ejecutar migración
migrateInterpretations();
