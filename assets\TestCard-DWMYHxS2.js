import{j as e,L as s,P as a}from"./vendor-CIyllXGj.js";const i=({test:a,iconClass:i,bgClass:t,textClass:r,buttonColor:n,abbreviation:o,showButton:l=!0,disabled:d=!1,patientId:m=null,level:u="E",isCompleted:c=!1,onRepeatTest:b=null})=>e.jsxDEV("div",{className:"test-card-container",children:e.jsxDEV("div",{className:"test-card "+(c?"test-card-completed":"test-card-pending"),children:[o&&e.jsxDEV("div",{className:`abbreviation-circle ${{blue:"bg-blue-600",green:"bg-green-600",red:"bg-red-600",amber:"bg-amber-600",indigo:"bg-indigo-600",gray:"bg-gray-700",slate:"bg-slate-600",teal:"bg-teal-600",purple:"bg-purple-600",pink:"bg-pink-600"}[n]}`,children:o},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:54,columnNumber:11},void 0),c&&e.jsxDEV("div",{className:"completion-check",children:e.jsxDEV("i",{className:"fas fa-check-circle"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:62,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:61,columnNumber:11},void 0),e.jsxDEV("div",{className:"test-card-status-badge",children:e.jsxDEV("span",{className:"status-badge "+(c?"status-badge-completed":"status-badge-pending"),children:[e.jsxDEV("i",{className:`fas ${c?"fa-check":"fa-clock"} mr-1`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:69,columnNumber:13},void 0),c?"Completado":"Pendiente"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:68,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:67,columnNumber:9},void 0),e.jsxDEV("div",{className:"test-card-level-badge",children:e.jsxDEV("span",{className:"level-badge "+("E"===u?"level-badge-green":"M"===u?"level-badge-blue":"S"===u?"level-badge-purple":"level-badge-gray"),children:["📗 Nivel ",u]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:76,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:75,columnNumber:9},void 0),e.jsxDEV("div",{className:"test-card-header",children:[e.jsxDEV("div",{className:`test-card-icon ${t}`,children:e.jsxDEV("i",{className:`${i} ${r}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:88,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:87,columnNumber:11},void 0),e.jsxDEV("h3",{className:"test-card-title",children:a.title},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:90,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:86,columnNumber:9},void 0),e.jsxDEV("div",{className:"test-card-description",children:e.jsxDEV("p",{children:a.description},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:94,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:93,columnNumber:9},void 0),e.jsxDEV("div",{className:"test-card-info-container",children:[e.jsxDEV("div",{className:"test-card-info",children:[e.jsxDEV("span",{className:"info-label",children:"Tiempo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:99,columnNumber:13},void 0),e.jsxDEV("span",{className:"info-value",children:a.time},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:100,columnNumber:13},void 0),e.jsxDEV("span",{className:"info-unit",children:"minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:101,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:98,columnNumber:11},void 0),e.jsxDEV("div",{className:"test-card-info",children:[e.jsxDEV("span",{className:"info-label",children:"Preguntas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:104,columnNumber:13},void 0),e.jsxDEV("span",{className:"info-value",children:a.questions},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:105,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:103,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:97,columnNumber:9},void 0),e.jsxDEV("div",{className:"test-card-button-container",children:d?e.jsxDEV("button",{disabled:!0,className:"test-card-button bg-gray-400 cursor-not-allowed opacity-50",children:[e.jsxDEV("i",{className:"fas fa-lock mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:116,columnNumber:15},void 0),"Selecciona Paciente"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:112,columnNumber:13},void 0):c?e.jsxDEV("button",{onClick:b,className:"test-card-button bg-orange-600 hover:bg-orange-700",children:[e.jsxDEV("i",{className:"fas fa-redo mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:124,columnNumber:15},void 0),"Repetir Test"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:120,columnNumber:13},void 0):e.jsxDEV(s,{to:a.path||`/test/instructions/${a.id}`,state:{patientId:m},className:`test-card-button ${{blue:"bg-blue-600 hover:bg-blue-700",green:"bg-green-600 hover:bg-green-700",red:"bg-red-600 hover:bg-red-700",amber:"bg-amber-600 hover:bg-amber-700",indigo:"bg-indigo-600 hover:bg-indigo-700",gray:"bg-gray-700 hover:bg-gray-800",slate:"bg-slate-600 hover:bg-slate-700",teal:"bg-teal-600 hover:bg-teal-700",purple:"bg-purple-600 hover:bg-purple-700",pink:"bg-pink-600 hover:bg-pink-700"}[n]}`,children:[e.jsxDEV("i",{className:"fas fa-play-circle mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:133,columnNumber:15},void 0),"Iniciar Test"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:128,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:110,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:51,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/components/TestCard.jsx",lineNumber:50,columnNumber:5},void 0);i.propTypes={test:a.shape({id:a.string.isRequired,title:a.string.isRequired,description:a.string.isRequired,time:a.number.isRequired,questions:a.number.isRequired,path:a.string}).isRequired,iconClass:a.string.isRequired,bgClass:a.string.isRequired,textClass:a.string.isRequired,buttonColor:a.string.isRequired,abbreviation:a.string,showButton:a.bool,disabled:a.bool,patientId:a.string,level:a.string,isCompleted:a.bool,onRepeatTest:a.func};export{i as T};
