import { useState, useEffect, useCallback, useRef } from 'react';
import EnhancedNotificationService from '../services/pin/EnhancedNotificationService';
import { toast } from 'react-toastify';

/**
 * Hook para gestionar notificaciones de pines
 * Proporciona funcionalidades completas de notificación
 */
export const usePinNotifications = (psychologistId, options = {}) => {
  const {
    enableAutoRefresh = true,
    refreshInterval = 30000, // 30 segundos
    enableToastNotifications = true,
    maxToastNotifications = 3
  } = options;

  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastRefresh, setLastRefresh] = useState(null);

  const intervalRef = useRef(null);
  const toastCountRef = useRef(0);

  // Cargar notificaciones
  const loadNotifications = useCallback(async (showLoading = true) => {
    if (!psychologistId) return;

    try {
      if (showLoading) setLoading(true);
      setError(null);

      const unreadNotifications = await EnhancedNotificationService.getUnreadNotifications(psychologistId);
      
      // Detectar nuevas notificaciones para mostrar toast
      if (enableToastNotifications && notifications.length > 0) {
        const newNotifications = unreadNotifications.filter(
          newNotif => !notifications.some(existingNotif => existingNotif.id === newNotif.id)
        );

        // Mostrar toast para nuevas notificaciones (limitado)
        newNotifications.slice(0, maxToastNotifications - toastCountRef.current).forEach(notification => {
          if (toastCountRef.current < maxToastNotifications) {
            showNotificationToast(notification);
            toastCountRef.current++;
          }
        });

        // Resetear contador después de un tiempo
        setTimeout(() => {
          toastCountRef.current = 0;
        }, 60000); // 1 minuto
      }
      
      setNotifications(unreadNotifications);
      setUnreadCount(unreadNotifications.length);
      setLastRefresh(new Date());
      
    } catch (err) {
      console.error('Error cargando notificaciones:', err);
      setError(err.message);
    } finally {
      if (showLoading) setLoading(false);
    }
  }, [psychologistId, notifications, enableToastNotifications, maxToastNotifications]);

  // Mostrar toast de notificación
  const showNotificationToast = useCallback((notification) => {
    const { title, message, severity } = notification;
    const toastMessage = `${title}: ${message}`;

    switch (severity) {
      case 'error':
      case 'critical':
        toast.error(toastMessage, { 
          autoClose: 8000,
          toastId: notification.id // Evitar duplicados
        });
        break;
      case 'warning':
        toast.warning(toastMessage, { 
          autoClose: 6000,
          toastId: notification.id
        });
        break;
      case 'success':
        toast.success(toastMessage, {
          toastId: notification.id
        });
        break;
      default:
        toast.info(toastMessage, {
          toastId: notification.id
        });
    }
  }, []);

  // Marcar notificación como leída
  const markAsRead = useCallback(async (notificationId) => {
    try {
      await EnhancedNotificationService.markAsRead(notificationId);
      
      // Actualizar estado local
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      setUnreadCount(prev => Math.max(0, prev - 1));
      
      return true;
    } catch (err) {
      console.error('Error marcando notificación como leída:', err);
      setError(err.message);
      return false;
    }
  }, []);

  // Marcar todas como leídas
  const markAllAsRead = useCallback(async () => {
    try {
      const promises = notifications.map(n => EnhancedNotificationService.markAsRead(n.id));
      await Promise.all(promises);
      
      setNotifications([]);
      setUnreadCount(0);
      
      return true;
    } catch (err) {
      console.error('Error marcando todas las notificaciones como leídas:', err);
      setError(err.message);
      return false;
    }
  }, [notifications]);

  // Crear notificación de pines bajos
  const createLowPinNotification = useCallback(async (remainingPins, options = {}) => {
    try {
      const result = await EnhancedNotificationService.createLowPinNotification(
        psychologistId,
        remainingPins,
        options
      );
      
      // Recargar notificaciones después de crear una nueva
      setTimeout(() => loadNotifications(false), 1000);
      
      return result;
    } catch (err) {
      console.error('Error creando notificación de pines bajos:', err);
      setError(err.message);
      return null;
    }
  }, [psychologistId, loadNotifications]);

  // Crear notificación de consumo de pin
  const createConsumptionNotification = useCallback(async (remainingPins, consumptionData = {}) => {
    try {
      const result = await EnhancedNotificationService.createPinConsumptionNotification(
        psychologistId,
        remainingPins,
        consumptionData
      );
      
      if (result) {
        setTimeout(() => loadNotifications(false), 1000);
      }
      
      return result;
    } catch (err) {
      console.error('Error creando notificación de consumo:', err);
      setError(err.message);
      return null;
    }
  }, [psychologistId, loadNotifications]);

  // Crear notificación de asignación de pines
  const createAssignmentNotification = useCallback(async (assignedPins, totalPins) => {
    try {
      const result = await EnhancedNotificationService.createPinAssignmentNotification(
        psychologistId,
        assignedPins,
        totalPins
      );
      
      setTimeout(() => loadNotifications(false), 1000);
      
      return result;
    } catch (err) {
      console.error('Error creando notificación de asignación:', err);
      setError(err.message);
      return null;
    }
  }, [psychologistId, loadNotifications]);

  // Refrescar manualmente
  const refresh = useCallback(() => {
    return loadNotifications(true);
  }, [loadNotifications]);

  // Configurar auto-refresh
  useEffect(() => {
    if (psychologistId) {
      // Carga inicial
      loadNotifications(true);

      // Configurar intervalo si está habilitado
      if (enableAutoRefresh && refreshInterval > 0) {
        intervalRef.current = setInterval(() => {
          loadNotifications(false); // Sin loading spinner para refresh automático
        }, refreshInterval);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [psychologistId, enableAutoRefresh, refreshInterval, loadNotifications]);

  // Limpiar al desmontar
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    // Estado
    notifications,
    unreadCount,
    loading,
    error,
    lastRefresh,

    // Acciones
    markAsRead,
    markAllAsRead,
    refresh,

    // Creación de notificaciones
    createLowPinNotification,
    createConsumptionNotification,
    createAssignmentNotification,

    // Utilidades
    hasUnread: unreadCount > 0,
    isEmpty: notifications.length === 0,
    
    // Configuración
    isAutoRefreshEnabled: enableAutoRefresh,
    refreshInterval
  };
};

/**
 * Hook simplificado para notificaciones básicas
 */
export const useBasicPinNotifications = (psychologistId) => {
  const {
    unreadCount,
    hasUnread,
    createLowPinNotification,
    createConsumptionNotification
  } = usePinNotifications(psychologistId, {
    enableAutoRefresh: true,
    enableToastNotifications: true,
    maxToastNotifications: 1
  });

  return {
    unreadCount,
    hasUnread,
    notifyLowPins: createLowPinNotification,
    notifyConsumption: createConsumptionNotification
  };
};

export default usePinNotifications;
