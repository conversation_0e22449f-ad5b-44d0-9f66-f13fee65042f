/**
 * @file testInterpretacionesRapido.js
 * @description Prueba rápida para verificar que las interpretaciones oficiales funcionan
 */

import { InterpretacionCualitativaService } from '../services/interpretacionCualitativaService.js';

// Datos de prueba
const resultadosPrueba = [
  {
    aptitud: { codigo: 'V', nombre: 'Aptitud Verbal' },
    percentil: 5  // Muy Bajo - debería mostrar texto oficial largo
  },
  {
    aptitud: { codigo: 'R', nombre: 'Razonamiento' },
    percentil: 90  // Muy Alto - debería mostrar texto oficial largo
  }
];

const pacientePrueba = {
  nombre: 'Paciente de Prueba',
  edad: 16
};

console.log('🧪 Prueba rápida de interpretaciones oficiales...\n');

try {
  const interpretacion = await InterpretacionCualitativaService.generarInterpretacionPersonalizada(
    resultadosPrueba, 
    pacientePrueba
  );

  console.log('✅ Interpretación generada exitosamente\n');

  interpretacion.aptitudesEspecificas.forEach((apt, index) => {
    console.log(`📋 ${apt.codigo} - ${apt.nombre} (Percentil: ${apt.percentil})`);
    console.log(`   Nivel: ${apt.nivel}`);
    console.log(`   Rendimiento (${apt.interpretacion.rendimiento.length} chars):`);
    console.log(`   "${apt.interpretacion.rendimiento.substring(0, 150)}..."`);
    
    if (apt.interpretacion.rendimiento.length > 100) {
      console.log('   ✅ Texto oficial detectado (largo)');
    } else {
      console.log('   ⚠️ Texto parece corto - posible problema');
    }
    
    // Verificar palabras clave oficiales
    const textoCompleto = apt.interpretacion.rendimiento.toLowerCase();
    const palabrasOficiales = ['evaluado', 'rendimiento', 'capacidad'];
    const palabrasEncontradas = palabrasOficiales.filter(palabra => 
      textoCompleto.includes(palabra)
    );
    
    if (palabrasEncontradas.length > 0) {
      console.log(`   ✅ Palabras oficiales encontradas: ${palabrasEncontradas.join(', ')}`);
    } else {
      console.log('   ⚠️ No se encontraron palabras clave oficiales');
    }
    
    console.log('');
  });

} catch (error) {
  console.error('❌ Error en la prueba:', error);
}

console.log('🎯 Prueba completada');

export default {};
