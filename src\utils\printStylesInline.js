/**
 * ========================================
 * SISTEMA ULTRA-ROBUSTO DE ESTILOS INLINE
 * Fidelidad Visual 100% - Cross-Browser
 * ========================================
 *
 * Estos estilos se aplican directamente como atributos style
 * para garantizar máxima compatibilidad y preservación en PDF
 * Compatible con Chrome, Firefox, Safari, Edge
 */

// Estilos base para contenedores
export const baseContainerStyle = {
  fontFamily: 'Arial, sans-serif',
  fontSize: '12pt',
  lineHeight: '1.4',
  color: 'black',
  backgroundColor: 'white',
  WebkitPrintColorAdjust: 'exact',
  printColorAdjust: 'exact',
  colorAdjust: 'exact'
};

// Estilos para headers azules
export const blueHeaderStyle = {
  background: '#2563eb',
  backgroundColor: '#2563eb',
  color: 'white',
  WebkitPrintColorAdjust: 'exact',
  printColorAdjust: 'exact',
  colorAdjust: 'exact'
};

// Estilos para texto blanco
export const whiteTextStyle = {
  color: 'white'
};

// Estilos para iconos de aptitudes
export const aptitudeIconStyles = {
  razonamiento: { backgroundColor: '#f97316', color: 'white' }, // orange
  verbal: { backgroundColor: '#3b82f6', color: 'white' }, // blue
  numerico: { backgroundColor: '#22c55e', color: 'white' }, // green
  ortografia: { backgroundColor: '#22c55e', color: 'white' }, // green
  atencion: { backgroundColor: '#ef4444', color: 'white' }, // red
  concentracion: { backgroundColor: '#eab308', color: 'white' }, // yellow
  mecanico: { backgroundColor: '#6b7280', color: 'white' } // gray
};

// Estilos para niveles de rendimiento
export const performanceLevelStyles = {
  'Muy Bajo': { backgroundColor: '#ef4444', color: 'white' }, // red
  'Bajo': { backgroundColor: '#f97316', color: 'white' }, // orange
  'Medio': { backgroundColor: '#6b7280', color: 'white' }, // gray
  'Alto': { backgroundColor: '#3b82f6', color: 'white' }, // blue
  'Superior': { backgroundColor: '#22c55e', color: 'white' } // green
};

// Estilos de tipografía
export const typographyStyles = {
  'text-3xl': { fontSize: '1.875rem', fontWeight: '700' },
  'text-2xl': { fontSize: '1.5rem', fontWeight: '700' },
  'text-xl': { fontSize: '1.25rem', fontWeight: '600' },
  'text-lg': { fontSize: '1.125rem', fontWeight: '500' },
  'text-base': { fontSize: '1rem' },
  'text-sm': { fontSize: '0.875rem' },
  'text-xs': { fontSize: '0.75rem' }
};

// Estilos de peso de fuente
export const fontWeightStyles = {
  'font-bold': { fontWeight: '700' },
  'font-semibold': { fontWeight: '600' },
  'font-medium': { fontWeight: '500' }
};

// Estilos de espaciado
export const spacingStyles = {
  'p-8': { padding: '2rem' },
  'p-6': { padding: '1.5rem' },
  'p-4': { padding: '1rem' },
  'p-3': { padding: '0.75rem' },
  'p-2': { padding: '0.5rem' },
  'mb-8': { marginBottom: '2rem' },
  'mb-6': { marginBottom: '1.5rem' },
  'mb-4': { marginBottom: '1rem' },
  'mb-3': { marginBottom: '0.75rem' },
  'mb-2': { marginBottom: '0.5rem' },
  'mb-1': { marginBottom: '0.25rem' },
  'mr-3': { marginRight: '0.75rem' },
  'mr-4': { marginRight: '1rem' }
};

// Estilos de layout
export const layoutStyles = {
  'flex': { display: 'flex' },
  'grid': { display: 'grid' },
  'items-center': { alignItems: 'center' },
  'justify-between': { justifyContent: 'space-between' },
  'text-center': { textAlign: 'center' },
  'text-right': { textAlign: 'right' }
};

// Estilos de bordes y esquinas
export const borderStyles = {
  'border': { border: '1px solid #e5e7eb' },
  'border-gray-200': { borderColor: '#e5e7eb' },
  'rounded-lg': { borderRadius: '0.5rem' },
  'rounded-full': { borderRadius: '9999px' }
};

/**
 * Aplica estilos inline a un elemento basado en sus clases CSS
 * @param {HTMLElement} element - Elemento al que aplicar estilos
 * @param {Object} additionalStyles - Estilos adicionales a aplicar
 */
export const applyInlineStyles = (element, additionalStyles = {}) => {
  if (!element) return;

  const classList = Array.from(element.classList);
  
  // Aplicar estilos de tipografía
  classList.forEach(className => {
    if (typographyStyles[className]) {
      Object.assign(element.style, typographyStyles[className]);
    }
    if (fontWeightStyles[className]) {
      Object.assign(element.style, fontWeightStyles[className]);
    }
    if (spacingStyles[className]) {
      Object.assign(element.style, spacingStyles[className]);
    }
    if (layoutStyles[className]) {
      Object.assign(element.style, layoutStyles[className]);
    }
    if (borderStyles[className]) {
      Object.assign(element.style, borderStyles[className]);
    }
  });

  // Aplicar estilos adicionales
  Object.assign(element.style, additionalStyles);

  // Aplicar preservación de colores
  element.style.WebkitPrintColorAdjust = 'exact';
  element.style.printColorAdjust = 'exact';
  element.style.colorAdjust = 'exact';
};

/**
 * ========================================
 * FUNCIÓN ULTRA-ROBUSTA DE APLICACIÓN DE ESTILOS
 * Aplica estilos inline con máxima compatibilidad
 * ========================================
 */
export const applyAllInlineStyles = (container) => {
  if (!container) {
    console.warn('⚠️ [printStylesInline] No se proporcionó contenedor');
    return;
  }

  console.log('🎨 [printStylesInline] Iniciando aplicación ultra-robusta de estilos...');

  try {
    // PASO 1: Aplicar estilos base al contenedor
    applyContainerBaseStyles(container);

    // PASO 2: Aplicar estilos a headers azules
    applyBlueHeaderStyles(container);

    // PASO 3: Aplicar estilos a iconos de aptitudes
    applyAptitudeIconStyles(container);

    // PASO 4: Aplicar estilos de tipografía
    applyTypographyStyles(container);

    // PASO 5: Aplicar estilos de layout
    applyLayoutStyles(container);

    // PASO 6: Aplicar estilos específicos a gráficos de resultados
    applyResultsGraphicsStyles(container);

    // PASO 7: Forzar texto negro en contenido normal
    applyNormalTextStyles(container);

    // PASO 8: Aplicar preservación de colores global
    applyColorPreservation(container);

    console.log('✅ [printStylesInline] Estilos ultra-robustos aplicados exitosamente');

  } catch (error) {
    console.error('❌ [printStylesInline] Error aplicando estilos:', error);
  }
};

/**
 * Aplica estilos base al contenedor principal
 */
const applyContainerBaseStyles = (container) => {
  const baseStyles = {
    fontFamily: 'Arial, sans-serif',
    fontSize: '12pt',
    lineHeight: '1.4',
    color: 'black',
    backgroundColor: 'white',
    WebkitPrintColorAdjust: 'exact',
    printColorAdjust: 'exact'
  };

  Object.assign(container.style, baseStyles);
  console.log('📦 [printStylesInline] Estilos base aplicados al contenedor');
};

/**
 * Aplica estilos a headers azules con máxima especificidad
 */
const applyBlueHeaderStyles = (container) => {
  const headerSelectors = [
    '.print-header',
    '.bg-blue-600',
    '.bg-blue-500',
    '.bg-gradient-to-r',
    '[class*="bg-gradient"]',
    '[class*="from-blue"]',
    '[class*="to-blue"]'
  ];

  headerSelectors.forEach(selector => {
    const headers = container.querySelectorAll(selector);
    headers.forEach(header => {
      // Estilos de fondo azul
      header.style.background = '#2563eb';
      header.style.backgroundColor = '#2563eb';
      header.style.backgroundImage = 'none';
      header.style.color = 'white';
      header.style.WebkitPrintColorAdjust = 'exact';
      header.style.printColorAdjust = 'exact';
      header.style.display = 'block';
      header.style.visibility = 'visible';
      header.style.width = '100%';
      header.style.boxSizing = 'border-box';
      header.style.padding = '1.5rem';
      header.style.marginBottom = '1rem';
      header.style.borderRadius = '0.5rem';

      // Aplicar texto blanco a todos los elementos hijos
      const children = header.querySelectorAll('*');
      children.forEach(child => {
        child.style.color = 'white';
        child.style.visibility = 'visible';
      });
    });
  });

  console.log('🔵 [printStylesInline] Estilos de headers azules aplicados');
};

/**
 * Aplica estilos a iconos de aptitudes con colores específicos
 */
const applyAptitudeIconStyles = (container) => {
  const aptitudeColors = {
    'bg-orange-500': '#f97316',
    'bg-orange-600': '#f97316',
    'bg-blue-500': '#3b82f6',
    'bg-green-500': '#22c55e',
    'bg-green-600': '#22c55e',
    'bg-red-500': '#ef4444',
    'bg-red-600': '#ef4444',
    'bg-yellow-500': '#eab308',
    'bg-yellow-600': '#eab308',
    'bg-gray-500': '#6b7280',
    'bg-gray-600': '#6b7280'
  };

  Object.entries(aptitudeColors).forEach(([className, color]) => {
    const elements = container.querySelectorAll(`.${className}`);
    elements.forEach(element => {
      element.style.backgroundColor = color;
      element.style.background = color;
      element.style.color = 'white';
      element.style.WebkitPrintColorAdjust = 'exact';
      element.style.printColorAdjust = 'exact';

      // Texto blanco en elementos hijos
      const children = element.querySelectorAll('*');
      children.forEach(child => {
        child.style.color = 'white';
      });
    });
  });

  console.log('🎨 [printStylesInline] Estilos de iconos de aptitudes aplicados');
};

/**
 * Aplica estilos de tipografía
 */
const applyTypographyStyles = (container) => {
  // Pesos de fuente
  const fontWeights = {
    'font-bold': '700',
    'font-semibold': '600',
    'font-medium': '500'
  };

  Object.entries(fontWeights).forEach(([className, weight]) => {
    const elements = container.querySelectorAll(`.${className}`);
    elements.forEach(element => {
      element.style.fontWeight = weight;
    });
  });

  // Tamaños de texto
  const textSizes = {
    'text-3xl': '1.875rem',
    'text-2xl': '1.5rem',
    'text-xl': '1.25rem',
    'text-lg': '1.125rem',
    'text-base': '1rem',
    'text-sm': '0.875rem',
    'text-xs': '0.75rem'
  };

  Object.entries(textSizes).forEach(([className, size]) => {
    const elements = container.querySelectorAll(`.${className}`);
    elements.forEach(element => {
      element.style.fontSize = size;
    });
  });

  console.log('📝 [printStylesInline] Estilos de tipografía aplicados');
};

/**
 * Aplica estilos de layout
 */
const applyLayoutStyles = (container) => {
  // Flexbox
  const flexElements = container.querySelectorAll('.flex');
  flexElements.forEach(element => {
    element.style.display = 'flex';
  });

  const itemsCenterElements = container.querySelectorAll('.items-center');
  itemsCenterElements.forEach(element => {
    element.style.alignItems = 'center';
  });

  const justifyBetweenElements = container.querySelectorAll('.justify-between');
  justifyBetweenElements.forEach(element => {
    element.style.justifyContent = 'space-between';
  });

  // Grid
  const gridElements = container.querySelectorAll('.grid');
  gridElements.forEach(element => {
    element.style.display = 'grid';
  });

  // Alineación de texto
  const textAlignments = {
    'text-center': 'center',
    'text-right': 'right',
    'text-left': 'left'
  };

  Object.entries(textAlignments).forEach(([className, alignment]) => {
    const elements = container.querySelectorAll(`.${className}`);
    elements.forEach(element => {
      element.style.textAlign = alignment;
    });
  });

  console.log('📐 [printStylesInline] Estilos de layout aplicados');
};

/**
 * Aplica estilos específicos a la sección de Resultados Gráficos por Aptitud
 */
const applyResultsGraphicsStyles = (container) => {
  console.log('📊 [printStylesInline] Aplicando estilos específicos a gráficos de resultados...');

  // Forzar estilos en tablas
  const tables = container.querySelectorAll('table');
  tables.forEach(table => {
    table.style.borderCollapse = 'collapse';
    table.style.width = '100%';
    table.style.visibility = 'visible';

    // Headers de tabla
    const headers = table.querySelectorAll('thead th');
    headers.forEach(header => {
      header.style.backgroundColor = '#1f2937';
      header.style.color = 'white';
      header.style.WebkitPrintColorAdjust = 'exact';
      header.style.printColorAdjust = 'exact';
      header.style.fontWeight = '700';
      header.style.textAlign = 'center';
      header.style.padding = '0.75rem 1rem';
    });

    // Celdas de tabla
    const cells = table.querySelectorAll('tbody td');
    cells.forEach(cell => {
      cell.style.padding = '1rem';
      cell.style.borderBottom = '1px solid #e5e7eb';
      cell.style.verticalAlign = 'middle';
    });
  });

  // Forzar estilos en barras de percentiles
  const percentileBars = container.querySelectorAll('.h-6[style*="backgroundColor"]');
  percentileBars.forEach(bar => {
    const currentBgColor = bar.style.backgroundColor;
    if (currentBgColor) {
      bar.style.backgroundColor = currentBgColor;
      bar.style.background = currentBgColor;
      bar.style.color = 'white';
      bar.style.fontWeight = '700';
      bar.style.WebkitPrintColorAdjust = 'exact';
      bar.style.printColorAdjust = 'exact';
      bar.style.display = 'flex';
      bar.style.alignItems = 'center';
      bar.style.justifyContent = 'center';
      bar.style.borderRadius = '9999px';
      bar.style.height = '1.5rem';

      // Texto dentro de la barra
      const textSpans = bar.querySelectorAll('span');
      textSpans.forEach(span => {
        span.style.color = 'white';
        span.style.fontWeight = '700';
        span.style.fontSize = '0.875rem';
      });
    }
  });

  // Forzar estilos en contenedores de barras
  const barContainers = container.querySelectorAll('.bg-gray-200');
  barContainers.forEach(container => {
    container.style.backgroundColor = '#e5e7eb';
    container.style.background = '#e5e7eb';
    container.style.borderRadius = '9999px';
    container.style.height = '1.5rem';
    container.style.width = '100%';
    container.style.position = 'relative';
    container.style.overflow = 'hidden';
    container.style.WebkitPrintColorAdjust = 'exact';
    container.style.printColorAdjust = 'exact';
  });

  // Forzar estilos en leyenda de niveles
  const legendItems = container.querySelectorAll('.w-4.h-4');
  legendItems.forEach(item => {
    item.style.width = '1rem';
    item.style.height = '1rem';
    item.style.borderRadius = '0.25rem';
    item.style.WebkitPrintColorAdjust = 'exact';
    item.style.printColorAdjust = 'exact';
    item.style.display = 'inline-block';
    item.style.marginRight = '0.5rem';

    // Preservar colores específicos
    const classList = Array.from(item.classList);
    if (classList.includes('bg-red-500')) {
      item.style.backgroundColor = '#ef4444';
      item.style.background = '#ef4444';
    } else if (classList.includes('bg-orange-500')) {
      item.style.backgroundColor = '#f97316';
      item.style.background = '#f97316';
    } else if (classList.includes('bg-yellow-500')) {
      item.style.backgroundColor = '#eab308';
      item.style.background = '#eab308';
    } else if (classList.includes('bg-green-500')) {
      item.style.backgroundColor = '#22c55e';
      item.style.background = '#22c55e';
    } else if (classList.includes('bg-blue-500')) {
      item.style.backgroundColor = '#3b82f6';
      item.style.background = '#3b82f6';
    } else if (classList.includes('bg-purple-500')) {
      item.style.backgroundColor = '#8b5cf6';
      item.style.background = '#8b5cf6';
    }
  });

  // Forzar estilos en iconos de aptitudes circulares
  const aptitudeIcons = container.querySelectorAll('.w-12.h-12');
  aptitudeIcons.forEach(icon => {
    icon.style.width = '3rem';
    icon.style.height = '3rem';
    icon.style.borderRadius = '50%';
    icon.style.display = 'flex';
    icon.style.alignItems = 'center';
    icon.style.justifyContent = 'center';
    icon.style.WebkitPrintColorAdjust = 'exact';
    icon.style.printColorAdjust = 'exact';

    // Preservar color de fondo si existe
    const currentBgColor = window.getComputedStyle(icon).backgroundColor;
    if (currentBgColor && currentBgColor !== 'rgba(0, 0, 0, 0)') {
      icon.style.backgroundColor = currentBgColor;
      icon.style.background = currentBgColor;
    }
  });

  // Forzar estilos en números de percentiles grandes
  const percentilNumbers = container.querySelectorAll('.text-4xl');
  percentilNumbers.forEach(number => {
    number.style.fontSize = '2.25rem';
    number.style.fontWeight = '700';
    number.style.color = 'black';
  });

  console.log('📊 [printStylesInline] Estilos de gráficos de resultados aplicados');
};

/**
 * Fuerza texto negro en contenido normal (no headers)
 */
const applyNormalTextStyles = (container) => {
  const normalTextSelectors = [
    'p:not(.print-header p):not(.bg-blue-600 p):not(.bg-gradient-to-r p)',
    'span:not(.print-header span):not(.bg-blue-600 span):not(.bg-gradient-to-r span)',
    'div:not(.print-header):not(.bg-blue-600):not(.bg-gradient-to-r)',
    'h3', 'h4', 'h5', 'h6',
    '.text-gray-900', '.text-gray-800', '.text-gray-700', '.text-black'
  ];

  normalTextSelectors.forEach(selector => {
    try {
      const elements = container.querySelectorAll(selector);
      elements.forEach(element => {
        // Solo aplicar si no está dentro de un header azul
        const isInBlueHeader = element.closest('.print-header, .bg-blue-600, .bg-gradient-to-r');
        if (!isInBlueHeader) {
          element.style.color = 'black';
          element.style.visibility = 'visible';
        }
      });
    } catch (error) {
      console.warn(`⚠️ [printStylesInline] Error con selector ${selector}:`, error);
    }
  });

  console.log('⚫ [printStylesInline] Estilos de texto negro aplicados');
};

/**
 * Aplica preservación de colores a todos los elementos
 */
const applyColorPreservation = (container) => {
  const allElements = container.querySelectorAll('*');
  allElements.forEach(element => {
    element.style.WebkitPrintColorAdjust = 'exact';
    element.style.printColorAdjust = 'exact';
  });

  console.log('🎨 [printStylesInline] Preservación de colores aplicada globalmente');
};

/**
 * Crea un objeto de estilo para un header azul
 * @param {string} padding - Padding personalizado
 * @returns {Object} Objeto de estilo
 */
export const createBlueHeaderStyle = (padding = '1rem 1.5rem') => ({
  ...blueHeaderStyle,
  padding
});

/**
 * Crea un objeto de estilo para un icono de aptitud
 * @param {string} aptitude - Nombre de la aptitud
 * @returns {Object} Objeto de estilo
 */
export const createAptitudeIconStyle = (aptitude) => {
  const aptitudeLower = aptitude.toLowerCase();
  return aptitudeIconStyles[aptitudeLower] || aptitudeIconStyles.mecanico;
};

/**
 * Crea un objeto de estilo para un nivel de rendimiento
 * @param {string} level - Nivel de rendimiento
 * @returns {Object} Objeto de estilo
 */
export const createPerformanceLevelStyle = (level) => {
  return performanceLevelStyles[level] || performanceLevelStyles['Medio'];
};
