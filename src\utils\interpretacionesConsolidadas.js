/**
 * @file interpretacionesConsolidadas.js
 * @description Interpretaciones profesionales consolidadas para todas las aptitudes del BAT-7
 * Reemplaza las interpretaciones hardcodeadas con textos profesionales y completos
 */

import { INTERPRETACIONES_MEJORADAS } from './interpretacionesMejoradas.js';
import { INTERPRETACIONES_ATENCION_MECANICA_ORTOGRAFIA } from './interpretacionesMejoradas_parte2.js';

/**
 * Configuración consolidada de todas las interpretaciones
 */
export const INTERPRETACIONES_CONSOLIDADAS = {
  // Mapeo de percentiles a niveles
  niveles: INTERPRETACIONES_MEJORADAS.niveles,

  // Función para obtener nivel por percentil
  obtenerNivelPorPercentil: INTERPRETACIONES_MEJORADAS.obtenerNivelPorPercentil,

  // Interpretaciones completas por aptitud
  interpretaciones: {
    // Aptitudes del primer archivo
    'V': INTERPRETACIONES_MEJORADAS.interpretaciones.V,
    'E': INTERPRETACIONES_MEJORADAS.interpretaciones.E,
    'R': INTERPRETACIONES_MEJORADAS.interpretaciones.R,
    'N': INTERPRETACIONES_MEJORADAS.interpretaciones.N,
    
    // Aptitudes del segundo archivo
    'A': INTERPRETACIONES_ATENCION_MECANICA_ORTOGRAFIA.A,
    'M': INTERPRETACIONES_ATENCION_MECANICA_ORTOGRAFIA.M,
    'O': INTERPRETACIONES_ATENCION_MECANICA_ORTOGRAFIA.O
  },

  /**
   * Función principal para obtener interpretación de una aptitud
   * @param {string} aptitudCodigo - Código de la aptitud (V, E, A, R, N, M, O)
   * @param {number} percentil - Percentil obtenido (0-100)
   * @returns {Object} Objeto con interpretaciones de rendimiento, académico y vocacional
   */
  obtenerInterpretacionAptitud: (aptitudCodigo, percentil) => {
    const nivel = INTERPRETACIONES_CONSOLIDADAS.obtenerNivelPorPercentil(percentil);
    const interpretacion = INTERPRETACIONES_CONSOLIDADAS.interpretaciones[aptitudCodigo]?.[nivel.id];
    
    if (!interpretacion) {
      return {
        nivel_nombre: nivel.nombre,
        percentil_rango: nivel.rango,
        rendimiento: 'Interpretación específica en desarrollo para este nivel de rendimiento.',
        academico: 'Se requiere evaluación complementaria para determinar implicaciones académicas específicas.',
        vocacional: 'Se recomienda consulta con profesional especializado para orientación vocacional personalizada.'
      };
    }
    
    return {
      nivel_nombre: nivel.nombre,
      percentil_rango: nivel.rango,
      percentil_valor: percentil,
      rendimiento: interpretacion.rendimiento,
      academico: interpretacion.academico,
      vocacional: interpretacion.vocacional
    };
  },

  /**
   * Función para obtener todas las interpretaciones de una aptitud
   * @param {string} aptitudCodigo - Código de la aptitud
   * @returns {Object} Todas las interpretaciones por nivel
   */
  obtenerTodasInterpretacionesAptitud: (aptitudCodigo) => {
    return INTERPRETACIONES_CONSOLIDADAS.interpretaciones[aptitudCodigo] || {};
  },

  /**
   * Función para validar si existe interpretación para una aptitud y nivel
   * @param {string} aptitudCodigo - Código de la aptitud
   * @param {number} nivel - Nivel (1-7)
   * @returns {boolean} True si existe la interpretación
   */
  existeInterpretacion: (aptitudCodigo, nivel) => {
    return !!(INTERPRETACIONES_CONSOLIDADAS.interpretaciones[aptitudCodigo]?.[nivel]);
  },

  /**
   * Función para obtener resumen de completitud de interpretaciones
   * @returns {Object} Resumen de qué interpretaciones están disponibles
   */
  obtenerResumenCompletitud: () => {
    const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
    const niveles = [1, 2, 3, 4, 5, 6, 7];
    const resumen = {};

    aptitudes.forEach(aptitud => {
      resumen[aptitud] = {
        total: niveles.length,
        disponibles: 0,
        faltantes: []
      };

      niveles.forEach(nivel => {
        if (INTERPRETACIONES_CONSOLIDADAS.existeInterpretacion(aptitud, nivel)) {
          resumen[aptitud].disponibles++;
        } else {
          resumen[aptitud].faltantes.push(nivel);
        }
      });

      resumen[aptitud].porcentaje = Math.round((resumen[aptitud].disponibles / resumen[aptitud].total) * 100);
    });

    return resumen;
  },

  /**
   * Función para obtener interpretaciones múltiples (para informes completos)
   * @param {Array} resultados - Array de objetos con {aptitud, percentil}
   * @returns {Array} Array de interpretaciones
   */
  obtenerInterpretacionesMultiples: (resultados) => {
    return resultados.map(resultado => ({
      aptitud: resultado.aptitud,
      percentil: resultado.percentil,
      interpretacion: INTERPRETACIONES_CONSOLIDADAS.obtenerInterpretacionAptitud(
        resultado.aptitud, 
        resultado.percentil
      )
    }));
  }
};

/**
 * Función de compatibilidad con el sistema existente
 * Mantiene la interfaz original pero usa las interpretaciones mejoradas
 */
export const obtenerInterpretacionCualitativa = (aptitudCodigo, percentil) => {
  return INTERPRETACIONES_CONSOLIDADAS.obtenerInterpretacionAptitud(aptitudCodigo, percentil);
};

/**
 * Función para migrar desde interpretaciones hardcodeadas
 * Convierte el formato antiguo al nuevo formato mejorado
 */
export const migrarInterpretacionesAnteriores = (interpretacionesAnteriores) => {
  const interpretacionesMigradas = {};
  
  Object.keys(interpretacionesAnteriores).forEach(aptitud => {
    interpretacionesMigradas[aptitud] = {};
    
    Object.keys(interpretacionesAnteriores[aptitud]).forEach(nivel => {
      const anterior = interpretacionesAnteriores[aptitud][nivel];
      interpretacionesMigradas[aptitud][nivel] = {
        rendimiento: anterior.rendimiento || 'Interpretación de rendimiento no disponible.',
        academico: anterior.academico || 'Implicaciones académicas no especificadas.',
        vocacional: anterior.vocacional || 'Implicaciones vocacionales no especificadas.'
      };
    });
  });
  
  return interpretacionesMigradas;
};

// Exportar como default para compatibilidad
export default INTERPRETACIONES_CONSOLIDADAS;
