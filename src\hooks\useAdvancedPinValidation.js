import { useState, useCallback, useEffect } from 'react';
import PinValidationService from '../services/pin/PinValidationService';
import AdminPinService from '../services/pin/AdminPinService';
import { toast } from 'react-toastify';

/**
 * Hook avanzado para validación de pines con funcionalidades mejoradas
 * Proporciona validación robusta antes de generar informes
 */
export const useAdvancedPinValidation = (psychologistId, options = {}) => {
  const {
    showToastAlerts = true,
    autoValidate = false,
    onValidationChange = null
  } = options;

  const [validationResult, setValidationResult] = useState(null);
  const [isValidating, setIsValidating] = useState(false);
  const [lastValidation, setLastValidation] = useState(null);

  // Validar generación de informe individual
  const validateSingleReport = useCallback(async (requiredPins = 1) => {
    if (!psychologistId) {
      const errorResult = {
        isValid: false,
        canProceed: false,
        reason: 'NO_PSYCHOLOGIST_ID',
        userMessage: 'ID de psicólogo no proporcionado',
        severity: 'error'
      };
      setValidationResult(errorResult);
      return errorResult;
    }

    try {
      setIsValidating(true);
      
      // Usar validación inteligente que detecta automáticamente si es admin
      const result = await AdminPinService.smartValidation(
        psychologistId,
        requiredPins
      );
      
      setValidationResult(result);
      setLastValidation(new Date());
      
      // Mostrar alertas si está habilitado
      if (showToastAlerts) {
        PinValidationService.displayValidationAlerts(result);
      }
      
      // Callback de cambio de validación
      if (onValidationChange) {
        onValidationChange(result);
      }
      
      return result;
    } catch (error) {
      console.error('Error en validación de pin:', error);
      const errorResult = {
        isValid: false,
        canProceed: false,
        reason: 'VALIDATION_ERROR',
        userMessage: 'Error al validar permisos',
        severity: 'error',
        error: error.message
      };
      
      setValidationResult(errorResult);
      
      if (showToastAlerts) {
        toast.error('Error al validar permisos de generación');
      }
      
      return errorResult;
    } finally {
      setIsValidating(false);
    }
  }, [psychologistId, showToastAlerts, onValidationChange]);

  // Validar generación de informes en lote
  const validateBatchReports = useCallback(async (patientIds) => {
    if (!psychologistId) {
      const errorResult = {
        isValid: false,
        canProceed: false,
        reason: 'NO_PSYCHOLOGIST_ID',
        userMessage: 'ID de psicólogo no proporcionado',
        severity: 'error'
      };
      setValidationResult(errorResult);
      return errorResult;
    }

    if (!Array.isArray(patientIds) || patientIds.length === 0) {
      const errorResult = {
        isValid: false,
        canProceed: false,
        reason: 'INVALID_PATIENT_LIST',
        userMessage: 'Lista de pacientes inválida',
        severity: 'error'
      };
      setValidationResult(errorResult);
      return errorResult;
    }

    try {
      setIsValidating(true);
      
      const result = await PinValidationService.validateBatchReportGeneration(
        psychologistId, 
        patientIds
      );
      
      setValidationResult(result);
      setLastValidation(new Date());
      
      // Mostrar alertas si está habilitado
      if (showToastAlerts) {
        PinValidationService.displayValidationAlerts(result);
      }
      
      // Callback de cambio de validación
      if (onValidationChange) {
        onValidationChange(result);
      }
      
      return result;
    } catch (error) {
      console.error('Error en validación de lote:', error);
      const errorResult = {
        isValid: false,
        canProceed: false,
        reason: 'BATCH_VALIDATION_ERROR',
        userMessage: 'Error al validar permisos para generación en lote',
        severity: 'error',
        error: error.message
      };
      
      setValidationResult(errorResult);
      
      if (showToastAlerts) {
        toast.error('Error al validar permisos para generación en lote');
      }
      
      return errorResult;
    } finally {
      setIsValidating(false);
    }
  }, [psychologistId, showToastAlerts, onValidationChange]);

  // Función para limpiar la validación
  const clearValidation = useCallback(() => {
    setValidationResult(null);
    setLastValidation(null);
  }, []);

  // Función para revalidar
  const revalidate = useCallback(async (requiredPins = 1) => {
    return await validateSingleReport(requiredPins);
  }, [validateSingleReport]);

  // Crear resumen de validación para UI
  const getValidationSummary = useCallback(() => {
    if (!validationResult) return null;
    return PinValidationService.createValidationSummary(validationResult);
  }, [validationResult]);

  // Verificar si puede proceder con la operación
  const canProceed = validationResult?.canProceed || false;
  const hasWarning = validationResult?.hasWarning || false;
  const isBlocked = validationResult && !validationResult.canProceed;

  // Auto-validación si está habilitada
  useEffect(() => {
    if (autoValidate && psychologistId && !validationResult && !isValidating) {
      validateSingleReport(1);
    }
  }, [autoValidate, psychologistId, validationResult, isValidating, validateSingleReport]);

  return {
    // Estado de validación
    validationResult,
    isValidating,
    lastValidation,
    
    // Estados derivados
    canProceed,
    hasWarning,
    isBlocked,
    
    // Funciones de validación
    validateSingleReport,
    validateBatchReports,
    revalidate,
    clearValidation,
    
    // Utilidades
    getValidationSummary,
    
    // Funciones de conveniencia
    isValid: validationResult?.isValid || false,
    severity: validationResult?.severity || 'info',
    message: validationResult?.userMessage || '',
    suggestions: validationResult?.suggestions || []
  };
};

/**
 * Hook simplificado para validación rápida
 */
export const useQuickPinValidation = (psychologistId) => {
  const [canGenerate, setCanGenerate] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [pinCount, setPinCount] = useState(0);

  const quickCheck = useCallback(async () => {
    if (!psychologistId) return false;

    try {
      setIsChecking(true);
      const result = await PinValidationService.validateReportGeneration(psychologistId, 1);
      
      setCanGenerate(result.canProceed);
      setPinCount(result.remainingPins || 0);
      
      return result.canProceed;
    } catch (error) {
      console.error('Error en verificación rápida:', error);
      setCanGenerate(false);
      setPinCount(0);
      return false;
    } finally {
      setIsChecking(false);
    }
  }, [psychologistId]);

  useEffect(() => {
    if (psychologistId) {
      quickCheck();
    }
  }, [psychologistId, quickCheck]);

  return {
    canGenerate,
    isChecking,
    pinCount,
    refresh: quickCheck
  };
};

export default useAdvancedPinValidation;
