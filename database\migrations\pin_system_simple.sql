-- =====================================================
-- SISTEMA DE PINES - TABLAS DE PERSISTENCIA (VERSIÓN SIMPLIFICADA)
-- =====================================================

-- 1. TABLA DE SOLICITUDES DE RECARGA
-- =====================================================
CREATE TABLE IF NOT EXISTS pin_recharge_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    psychologist_id UUID NOT NULL,
    requested_pins INTEGER NOT NULL CHECK (requested_pins > 0),
    urgency VARCHAR(20) NOT NULL CHECK (urgency IN ('normal', 'high', 'urgent')),
    reason TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
    
    -- Información del administrador que procesa
    processed_by UUID NULL,
    processed_at TIMESTAMP WITH TIME ZONE NULL,
    admin_notes TEXT NULL,
    approved_pins INTEGER NULL,
    
    -- Metadatos de la solicitud
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. TABLA DE NOTIFICACIONES
-- =====================================================
CREATE TABLE IF NOT EXISTS pin_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL DEFAULT 'info' CHECK (severity IN ('info', 'success', 'warning', 'error', 'critical')),
    
    -- Estado de la notificación
    read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP WITH TIME ZONE NULL,
    
    -- Canales de notificación
    channels JSONB DEFAULT '["toast"]',
    sent_channels JSONB DEFAULT '[]',
    
    -- Metadatos adicionales
    metadata JSONB DEFAULT '{}',
    related_entity_type VARCHAR(50) NULL,
    related_entity_id UUID NULL,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NULL
);

-- 3. TABLA DE HISTORIAL DE CONSUMO DE PINES
-- =====================================================
CREATE TABLE IF NOT EXISTS pin_consumption_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    psychologist_id UUID NOT NULL,
    session_id UUID NULL,
    patient_id UUID NULL,
    report_id UUID NULL,
    
    -- Información del consumo
    pins_consumed INTEGER NOT NULL DEFAULT 1,
    consumption_type VARCHAR(50) NOT NULL DEFAULT 'report_generation',
    consumption_reason TEXT NULL,
    
    -- Timestamps del proceso
    session_completed_at TIMESTAMP WITH TIME ZONE NULL,
    pin_consumed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    report_generated_at TIMESTAMP WITH TIME ZONE NULL,
    
    -- Metadatos del consumo
    metadata JSONB DEFAULT '{}',
    
    -- Información adicional
    patient_name VARCHAR(255) NULL,
    report_title VARCHAR(255) NULL,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. TABLA DE ESTADÍSTICAS DE PINES (CACHE)
-- =====================================================
CREATE TABLE IF NOT EXISTS pin_statistics_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    psychologist_id UUID NOT NULL UNIQUE,
    
    -- Estadísticas actuales
    total_assigned INTEGER DEFAULT 0,
    total_consumed INTEGER DEFAULT 0,
    remaining_pins INTEGER DEFAULT 0,
    
    -- Estadísticas por período
    consumed_today INTEGER DEFAULT 0,
    consumed_this_week INTEGER DEFAULT 0,
    consumed_this_month INTEGER DEFAULT 0,
    
    -- Información del plan
    is_unlimited BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    last_assignment_at TIMESTAMP WITH TIME ZONE NULL,
    last_consumption_at TIMESTAMP WITH TIME ZONE NULL,
    
    -- Metadatos
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ÍNDICES PARA OPTIMIZAR CONSULTAS
-- =====================================================

-- Índices para solicitudes de recarga
CREATE INDEX IF NOT EXISTS idx_pin_recharge_requests_psychologist ON pin_recharge_requests(psychologist_id);
CREATE INDEX IF NOT EXISTS idx_pin_recharge_requests_status ON pin_recharge_requests(status);
CREATE INDEX IF NOT EXISTS idx_pin_recharge_requests_urgency ON pin_recharge_requests(urgency);
CREATE INDEX IF NOT EXISTS idx_pin_recharge_requests_created ON pin_recharge_requests(created_at DESC);

-- Índices para notificaciones
CREATE INDEX IF NOT EXISTS idx_pin_notifications_user ON pin_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_pin_notifications_read ON pin_notifications(read);
CREATE INDEX IF NOT EXISTS idx_pin_notifications_type ON pin_notifications(type);
CREATE INDEX IF NOT EXISTS idx_pin_notifications_created ON pin_notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_pin_notifications_expires ON pin_notifications(expires_at) WHERE expires_at IS NOT NULL;

-- Índices para historial de consumo
CREATE INDEX IF NOT EXISTS idx_pin_consumption_psychologist ON pin_consumption_history(psychologist_id);
CREATE INDEX IF NOT EXISTS idx_pin_consumption_session ON pin_consumption_history(session_id);
CREATE INDEX IF NOT EXISTS idx_pin_consumption_patient ON pin_consumption_history(patient_id);
CREATE INDEX IF NOT EXISTS idx_pin_consumption_date ON pin_consumption_history(pin_consumed_at DESC);
CREATE INDEX IF NOT EXISTS idx_pin_consumption_type ON pin_consumption_history(consumption_type);

-- Índices para estadísticas
CREATE INDEX IF NOT EXISTS idx_pin_stats_psychologist ON pin_statistics_cache(psychologist_id);
CREATE INDEX IF NOT EXISTS idx_pin_stats_active ON pin_statistics_cache(is_active);
CREATE INDEX IF NOT EXISTS idx_pin_stats_unlimited ON pin_statistics_cache(is_unlimited);

-- =====================================================
-- TRIGGER PARA ACTUALIZACIÓN AUTOMÁTICA
-- =====================================================

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Aplicar trigger a todas las tablas
DROP TRIGGER IF EXISTS update_pin_recharge_requests_updated_at ON pin_recharge_requests;
CREATE TRIGGER update_pin_recharge_requests_updated_at 
    BEFORE UPDATE ON pin_recharge_requests 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_pin_notifications_updated_at ON pin_notifications;
CREATE TRIGGER update_pin_notifications_updated_at 
    BEFORE UPDATE ON pin_notifications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_pin_consumption_history_updated_at ON pin_consumption_history;
CREATE TRIGGER update_pin_consumption_history_updated_at 
    BEFORE UPDATE ON pin_consumption_history 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_pin_statistics_cache_updated_at ON pin_statistics_cache;
CREATE TRIGGER update_pin_statistics_cache_updated_at 
    BEFORE UPDATE ON pin_statistics_cache 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- DATOS DE PRUEBA (OPCIONAL)
-- =====================================================

-- Insertar algunas solicitudes de prueba
INSERT INTO pin_recharge_requests (psychologist_id, requested_pins, urgency, reason, metadata) VALUES
('74c8230e-6f01-4b5d-ae72-cf5ac61db33e', 50, 'normal', 'Necesito más pines para completar los informes de mis pacientes de esta semana', '{"usage_stats": {"current_pins": 2, "total_assigned": 100, "total_consumed": 98}}'),
('74c8230e-6f01-4b5d-ae72-cf5ac61db33e', 25, 'high', 'Tengo una evaluación urgente que requiere generar el informe hoy', '{"usage_stats": {"current_pins": 0, "total_assigned": 50, "total_consumed": 50}}'),
('74c8230e-6f01-4b5d-ae72-cf5ac61db33e', 100, 'urgent', 'Sin pines disponibles y tengo 15 pacientes esperando sus informes para mañana', '{"usage_stats": {"current_pins": 0, "total_assigned": 75, "total_consumed": 75}}')
ON CONFLICT DO NOTHING;

-- Insertar algunas notificaciones de prueba
INSERT INTO pin_notifications (user_id, type, title, message, severity, metadata) VALUES
('74c8230e-6f01-4b5d-ae72-cf5ac61db33e', 'low_pins', 'Pines bajos', 'Te quedan solo 3 pines disponibles', 'warning', '{"remaining_pins": 3}'),
('74c8230e-6f01-4b5d-ae72-cf5ac61db33e', 'pin_assignment', 'Pines asignados', 'Se han asignado 50 pines a tu cuenta', 'success', '{"assigned_pins": 50}'),
('74c8230e-6f01-4b5d-ae72-cf5ac61db33e', 'recharge_request_approved', 'Solicitud aprobada', 'Tu solicitud de recarga ha sido aprobada', 'success', '{"approved_pins": 25}')
ON CONFLICT DO NOTHING;
