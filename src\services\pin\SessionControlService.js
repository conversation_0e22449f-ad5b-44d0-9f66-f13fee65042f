import { supabase } from '../../api/supabaseClient.js';
import { PinLogger } from './PinLogger.js';
import { PIN_CONSTANTS } from './PinConstants.js';

/**
 * Servicio para controlar el consumo de pines por sesión de paciente
 * Evita duplicados y asegura que cada sesión consuma exactamente 1 pin
 */
export class SessionControlService {
  
  /**
   * Estados de sesión para control de pines
   */
  static SESSION_STATES = {
    PENDING: 'pending',           // Sesión completada, pin pendiente
    PIN_CONSUMED: 'pin_consumed', // Pin ya consumido
    REPORT_GENERATED: 'report_generated', // Informe generado
    DUPLICATE_BLOCKED: 'duplicate_blocked' // Duplicado bloqueado
  };

  /**
   * Verificar si una sesión ya ha consumido un pin
   * @param {string} sessionId - ID de la sesión
   * @returns {Promise<Object>} Estado de la sesión
   */
  static async checkSessionPinStatus(sessionId) {
    try {
      PinLogger.logInfo('Checking session pin status', { sessionId });

      const { data: session, error } = await supabase
        .from('test_sessions')
        .select(`
          id,
          paciente_id,
          estado,
          fecha_fin,
          pin_consumed_at,
          pin_consumption_id,
          pacientes!inner(psicologo_id, nombre, apellido)
        `)
        .eq('id', sessionId)
        .single();

      if (error) {
        PinLogger.logError('Error checking session pin status', error);
        throw error;
      }

      if (!session) {
        return {
          exists: false,
          canConsume: false,
          reason: 'SESSION_NOT_FOUND',
          message: 'Sesión no encontrada'
        };
      }

      // Verificar si la sesión está finalizada
      if (session.estado !== 'finalizado') {
        return {
          exists: true,
          canConsume: false,
          reason: 'SESSION_NOT_COMPLETED',
          message: 'La sesión no está finalizada',
          session
        };
      }

      // Verificar si ya se consumió un pin
      if (session.pin_consumed_at) {
        return {
          exists: true,
          canConsume: false,
          reason: 'PIN_ALREADY_CONSUMED',
          message: 'Ya se consumió un pin para esta sesión',
          session,
          consumedAt: session.pin_consumed_at
        };
      }

      // Verificar si ya existe un informe para este paciente
      const reportExists = await this.checkExistingReport(session.paciente_id);
      
      return {
        exists: true,
        canConsume: !reportExists.exists,
        reason: reportExists.exists ? 'REPORT_ALREADY_EXISTS' : 'CAN_CONSUME',
        message: reportExists.exists 
          ? 'Ya existe un informe para este paciente' 
          : 'Puede consumir pin para esta sesión',
        session,
        existingReport: reportExists.exists ? reportExists.report : null
      };

    } catch (error) {
      PinLogger.logError('Error in checkSessionPinStatus', error);
      throw error;
    }
  }

  /**
   * Verificar si ya existe un informe para un paciente
   * @param {string} patientId - ID del paciente
   * @returns {Promise<Object>} Estado del informe
   */
  static async checkExistingReport(patientId) {
    try {
      const { data: reports, error } = await supabase
        .from('informes')
        .select('id, titulo, created_at, tipo_informe')
        .eq('paciente_id', patientId)
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) {
        PinLogger.logError('Error checking existing report', error);
        throw error;
      }

      return {
        exists: reports && reports.length > 0,
        report: reports && reports.length > 0 ? reports[0] : null,
        count: reports ? reports.length : 0
      };

    } catch (error) {
      PinLogger.logError('Error in checkExistingReport', error);
      throw error;
    }
  }

  /**
   * Marcar una sesión como que ya consumió un pin
   * @param {string} sessionId - ID de la sesión
   * @param {string} consumptionId - ID del registro de consumo
   * @param {string} reportId - ID del informe generado (opcional)
   * @returns {Promise<boolean>} Éxito de la operación
   */
  static async markSessionPinConsumed(sessionId, consumptionId, reportId = null) {
    try {
      PinLogger.logInfo('Marking session pin as consumed', { sessionId, consumptionId, reportId });

      const updateData = {
        pin_consumed_at: new Date().toISOString(),
        pin_consumption_id: consumptionId
      };

      if (reportId) {
        updateData.report_id = reportId;
      }

      const { error } = await supabase
        .from('test_sessions')
        .update(updateData)
        .eq('id', sessionId);

      if (error) {
        PinLogger.logError('Error marking session pin as consumed', error);
        throw error;
      }

      PinLogger.logSuccess(`Session ${sessionId} marked as pin consumed`);
      return true;

    } catch (error) {
      PinLogger.logError('Error in markSessionPinConsumed', error);
      throw error;
    }
  }

  /**
   * Obtener sesiones pendientes de consumo de pin para un psicólogo
   * @param {string} psychologistId - ID del psicólogo
   * @returns {Promise<Array>} Lista de sesiones pendientes
   */
  static async getPendingSessions(psychologistId) {
    try {
      PinLogger.logInfo('Getting pending sessions', { psychologistId });

      const { data: sessions, error } = await supabase
        .from('test_sessions')
        .select(`
          id,
          paciente_id,
          fecha_fin,
          estado,
          pin_consumed_at,
          pacientes!inner(
            id,
            nombre,
            apellido,
            psicologo_id
          )
        `)
        .eq('pacientes.psicologo_id', psychologistId)
        .eq('estado', 'finalizado')
        .is('pin_consumed_at', null)
        .order('fecha_fin', { ascending: false });

      if (error) {
        PinLogger.logError('Error getting pending sessions', error);
        throw error;
      }

      return sessions || [];

    } catch (error) {
      PinLogger.logError('Error in getPendingSessions', error);
      throw error;
    }
  }

  /**
   * Obtener historial de consumo de pines por sesión
   * @param {string} psychologistId - ID del psicólogo
   * @param {Object} options - Opciones de consulta
   * @returns {Promise<Array>} Historial de consumo
   */
  static async getConsumptionHistory(psychologistId, options = {}) {
    const {
      limit = 50,
      offset = 0,
      startDate = null,
      endDate = null
    } = options;

    try {
      PinLogger.logInfo('Getting consumption history', { psychologistId, options });

      let query = supabase
        .from('test_sessions')
        .select(`
          id,
          paciente_id,
          fecha_fin,
          pin_consumed_at,
          pin_consumption_id,
          report_id,
          pacientes!inner(
            id,
            nombre,
            apellido,
            psicologo_id
          ),
          informes(
            id,
            titulo,
            created_at
          )
        `)
        .eq('pacientes.psicologo_id', psychologistId)
        .eq('estado', 'finalizado')
        .not('pin_consumed_at', 'is', null)
        .order('pin_consumed_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (startDate) {
        query = query.gte('pin_consumed_at', startDate);
      }

      if (endDate) {
        query = query.lte('pin_consumed_at', endDate);
      }

      // Datos de prueba realistas para el historial de consumo
      const mockHistory = [
        {
          id: 'session-001',
          paciente_id: 'patient-001',
          fecha_fin: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          pin_consumed_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000).toISOString(),
          pin_consumption_id: 'consumption-001',
          report_id: 'report-001',
          pacientes: {
            id: 'patient-001',
            nombre: 'Juan',
            apellido: 'Pérez',
            psicologo_id: psychologistId
          },
          informes: {
            id: 'report-001',
            titulo: 'Informe BAT-7 - Juan Pérez',
            created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 45 * 60 * 1000).toISOString()
          }
        },
        {
          id: 'session-002',
          paciente_id: 'patient-002',
          fecha_fin: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          pin_consumed_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 45 * 60 * 1000).toISOString(),
          pin_consumption_id: 'consumption-002',
          report_id: 'report-002',
          pacientes: {
            id: 'patient-002',
            nombre: 'María',
            apellido: 'González',
            psicologo_id: psychologistId
          },
          informes: {
            id: 'report-002',
            titulo: 'Informe BAT-7 - María González',
            created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString()
          }
        },
        {
          id: 'session-003',
          paciente_id: 'patient-003',
          fecha_fin: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          pin_consumed_at: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
          pin_consumption_id: 'consumption-003',
          report_id: 'report-003',
          pacientes: {
            id: 'patient-003',
            nombre: 'Carlos',
            apellido: 'Rodríguez',
            psicologo_id: psychologistId
          },
          informes: {
            id: 'report-003',
            titulo: 'Informe BAT-7 - Carlos Rodríguez',
            created_at: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000).toISOString()
          }
        }
      ];

      // Aplicar filtros de fecha si se proporcionan
      let filteredHistory = mockHistory;

      if (startDate) {
        filteredHistory = filteredHistory.filter(item =>
          new Date(item.pin_consumed_at) >= new Date(startDate)
        );
      }

      if (endDate) {
        filteredHistory = filteredHistory.filter(item =>
          new Date(item.pin_consumed_at) <= new Date(endDate)
        );
      }

      // Aplicar paginación
      const paginatedHistory = filteredHistory.slice(offset, offset + limit);

      PinLogger.logSuccess('Mock consumption history retrieved', {
        psychologistId,
        total: mockHistory.length,
        filtered: filteredHistory.length,
        returned: paginatedHistory.length
      });

      return paginatedHistory;

      /*
      // Implementación real cuando esté lista
      const { data: history, error } = await query;

      if (error) {
        PinLogger.logError('Error getting consumption history', error);
        throw error;
      }

      return history || [];
      */

    } catch (error) {
      PinLogger.logError('Error in getConsumptionHistory', error);
      throw error;
    }
  }

  /**
   * Validar que una sesión puede consumir un pin de forma segura
   * @param {string} sessionId - ID de la sesión
   * @param {string} psychologistId - ID del psicólogo
   * @returns {Promise<Object>} Resultado de la validación
   */
  static async validateSessionForPinConsumption(sessionId, psychologistId) {
    try {
      PinLogger.logInfo('Validating session for pin consumption', { sessionId, psychologistId });

      // Verificar estado de la sesión
      const sessionStatus = await this.checkSessionPinStatus(sessionId);
      
      if (!sessionStatus.exists) {
        return {
          isValid: false,
          canProceed: false,
          reason: 'SESSION_NOT_FOUND',
          message: 'La sesión no existe',
          sessionStatus
        };
      }

      if (!sessionStatus.canConsume) {
        return {
          isValid: false,
          canProceed: false,
          reason: sessionStatus.reason,
          message: sessionStatus.message,
          sessionStatus
        };
      }

      // Verificar que el psicólogo es el propietario
      if (sessionStatus.session.pacientes.psicologo_id !== psychologistId) {
        return {
          isValid: false,
          canProceed: false,
          reason: 'UNAUTHORIZED_PSYCHOLOGIST',
          message: 'No tienes permisos para esta sesión',
          sessionStatus
        };
      }

      return {
        isValid: true,
        canProceed: true,
        reason: 'VALIDATION_PASSED',
        message: 'La sesión puede consumir un pin',
        sessionStatus
      };

    } catch (error) {
      PinLogger.logError('Error in validateSessionForPinConsumption', error);
      return {
        isValid: false,
        canProceed: false,
        reason: 'VALIDATION_ERROR',
        message: `Error en la validación: ${error.message}`,
        error: error.message
      };
    }
  }

  /**
   * Obtener estadísticas de sesiones y consumo de pines
   * @param {string} psychologistId - ID del psicólogo
   * @returns {Promise<Object>} Estadísticas
   */
  static async getSessionStatistics(psychologistId) {
    try {
      PinLogger.logInfo('Getting session statistics', { psychologistId });

      // Obtener conteos de sesiones
      const [completedSessions, pendingSessions, consumedSessions] = await Promise.all([
        // Sesiones completadas
        supabase
          .from('test_sessions')
          .select('id', { count: 'exact' })
          .eq('pacientes.psicologo_id', psychologistId)
          .eq('estado', 'finalizado'),
        
        // Sesiones pendientes de consumo
        supabase
          .from('test_sessions')
          .select('id', { count: 'exact' })
          .eq('pacientes.psicologo_id', psychologistId)
          .eq('estado', 'finalizado')
          .is('pin_consumed_at', null),
        
        // Sesiones que ya consumieron pin
        supabase
          .from('test_sessions')
          .select('id', { count: 'exact' })
          .eq('pacientes.psicologo_id', psychologistId)
          .eq('estado', 'finalizado')
          .not('pin_consumed_at', 'is', null)
      ]);

      return {
        totalCompleted: completedSessions.count || 0,
        pendingConsumption: pendingSessions.count || 0,
        alreadyConsumed: consumedSessions.count || 0,
        consumptionRate: completedSessions.count > 0 
          ? ((consumedSessions.count || 0) / completedSessions.count * 100).toFixed(1)
          : 0
      };

    } catch (error) {
      PinLogger.logError('Error getting session statistics', error);
      throw error;
    }
  }
}

export default SessionControlService;
