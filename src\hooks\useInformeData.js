/**
 * @file useInformeData.js
 * @description Custom hook para procesar y normalizar datos de informes psicológicos
 * Extrae toda la lógica de procesamiento de datos del componente InformeModalProfessional
 */

import { useMemo } from 'react';
import { getAptitudeConfig, getPercentilLevel as getPercentilLevelFromConstants } from '../constants/aptitudeConstants';

/**
 * Custom hook para procesar datos de informes psicológicos
 * @param {Array} results - Array de resultados de evaluaciones
 * @returns {Object} Objeto con datos normalizados e índices calculados
 */
export const useInformeData = (results = []) => {
  
  // Normalizar los datos para asegurar consistencia
  const normalizedResultsData = useMemo(() => {
    if (!Array.isArray(results)) return [];
    
    return results.map(result => {
      // Mapear los diferentes formatos de datos a una estructura consistente
      const normalizedResult = {
        id: result.id,
        // Aptitud información
        aptitudes: result.aptitudes || {
          codigo: result.aptitud_codigo || result.codigo || 'N/A',
          nombre: result.aptitud_nombre || result.aptitud || result.testName || result.test || 'N/A'
        },
        // Puntajes
        puntaje_directo: result.puntaje_directo || result.puntuacion_directa || result.puntajes?.directo || 0,
        percentil: result.percentil || result.puntaje_pc || result.percentiles?.general || 0,
        // Datos adicionales
        respuestas_correctas: result.respuestas_correctas || result.aciertos || 0,
        respuestas_incorrectas: result.respuestas_incorrectas || result.errores || 0,
        errores: result.errores || result.respuestas_incorrectas || 0,
        tiempo_total: result.tiempo_total || result.tiempo || result.tiempo_segundos || 0,
        tiempo_segundos: result.tiempo_segundos || result.tiempo || result.tiempo_total || 0, // Campo directo para tiempo
        created_at: result.created_at || result.fecha_evaluacion
      };

      console.log(`📊 [useInformeData] ${normalizedResult.aptitudes.codigo}: PD=${normalizedResult.puntaje_directo}, PC=${normalizedResult.percentil}, Tiempo=${normalizedResult.tiempo_segundos} segundos (original: ${result.tiempo_segundos || 'N/A'})`);

      return normalizedResult;
    });
  }, [results]);

  // Calcular índices de inteligencia basados en datos reales
  const intelligenceIndices = useMemo(() => {
    if (normalizedResultsData.length === 0) {
      return {
        capacidadGeneral: 0,
        inteligenciaFluida: 0,
        inteligenciaCristalizada: 0
      };
    }

    // Calculate general capacity (g) as average of all percentiles
    const totalPercentil = normalizedResultsData.reduce((sum, result) => {
      return sum + result.percentil;
    }, 0);
    const capacidadGeneral = Math.round(totalPercentil / normalizedResultsData.length);

    // Find specific aptitudes by code for more accurate mapping
    const verbalResult = normalizedResultsData.find(r => r.aptitudes?.codigo === 'V');
    const espacialResult = normalizedResultsData.find(r => r.aptitudes?.codigo === 'E');
    const razonamientoResult = normalizedResultsData.find(r => r.aptitudes?.codigo === 'R');
    const numericaResult = normalizedResultsData.find(r => r.aptitudes?.codigo === 'N');
    const ortografiaResult = normalizedResultsData.find(r => r.aptitudes?.codigo === 'O');

    // Fluid intelligence (spatial, reasoning, numeric)
    const fluidResults = [espacialResult, razonamientoResult, numericaResult].filter(Boolean);
    const inteligenciaFluida = fluidResults.length > 0
      ? Math.round(fluidResults.reduce((sum, result) => {
        return sum + result.percentil;
      }, 0) / fluidResults.length)
      : capacidadGeneral;

    // Crystallized intelligence (verbal, orthography - knowledge-based)
    const crystallizedResults = [verbalResult, ortografiaResult].filter(Boolean);
    const inteligenciaCristalizada = crystallizedResults.length > 0
      ? Math.round(crystallizedResults.reduce((sum, result) => {
        return sum + result.percentil;
      }, 0) / crystallizedResults.length)
      : capacidadGeneral;

    console.log('🧠 [useInformeData] Intelligence Indices Calculated:', {
      capacidadGeneral,
      inteligenciaFluida,
      inteligenciaCristalizada,
      fluidResults: fluidResults.map(r => `${r.aptitudes.codigo}:${r.percentil}`),
      crystallizedResults: crystallizedResults.map(r => `${r.aptitudes.codigo}:${r.percentil}`)
    });

    return {
      'g': capacidadGeneral,        // Capacidad General
      'Gf': inteligenciaFluida,     // Inteligencia Fluida
      'Gc': inteligenciaCristalizada // Inteligencia Cristalizada
    };
  }, [normalizedResultsData]);

  // Calcular estadísticas generales
  const generalStats = useMemo(() => {
    if (normalizedResultsData.length === 0) {
      return {
        testsCompletados: 0,
        percentilPromedio: 0,
        aptitudesAltas: 0,
        aptitudesAReforzar: 0
      };
    }

    const totalPercentil = normalizedResultsData.reduce((sum, result) => {
      return sum + result.percentil;
    }, 0);

    const percentilPromedio = normalizedResultsData.length > 0 
      ? (totalPercentil / normalizedResultsData.length).toFixed(1) 
      : '0';

    const aptitudesAltas = normalizedResultsData.filter(result => {
      return result.percentil >= 75;
    }).length;

    const aptitudesAReforzar = normalizedResultsData.filter(result => {
      return result.percentil < 50;
    }).length;

    return {
      testsCompletados: normalizedResultsData.length,
      percentilPromedio,
      aptitudesAltas,
      aptitudesAReforzar
    };
  }, [normalizedResultsData]);

  // Usar la configuración centralizada de aptitudes
  const getAptitudeConfigLocal = (letter, name) => {
    return getAptitudeConfig(letter);
  };

  // Usar la configuración centralizada de percentiles
  const getPercentilLevel = (percentil) => {
    return getPercentilLevelFromConstants(percentil);
  };

  return {
    normalizedResultsData,
    intelligenceIndices,
    generalStats,
    getAptitudeConfig: getAptitudeConfigLocal,
    getPercentilLevel,
    isLoading: false // Podríamos agregar lógica de loading si fuera necesario
  };
};

export default useInformeData;
