/**
 * Script para asignar más pines al psicólogo que se quedó sin pines
 */

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://ydglduxhgwajqdseqzpy.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjMxMjg0MSwiZXhwIjoyMDYxODg4ODQxfQ.zwk3Wiay5jjeYOrg8B1M6T98B2esCqRFI43At-AFV3A'
);

async function assignMorePins() {
  try {
    const psychologistId = '5e02c38d-47af-40ec-abce-6c7bd1c1e7ae';
    const additionalPins = 5; // Asignar 5 pines más
    
    console.log(`Asignando ${additionalPins} pines adicionales al psicólogo ${psychologistId}...`);
    
    // Obtener datos actuales
    const { data: currentData, error: fetchError } = await supabase
      .from('psychologist_usage_control')
      .select('*')
      .eq('psychologist_id', psychologistId)
      .eq('is_active', true)
      .single();
    
    if (fetchError) {
      console.error('Error obteniendo datos actuales:', fetchError);
      return;
    }
    
    if (!currentData) {
      console.log('No se encontraron datos para el psicólogo');
      return;
    }
    
    console.log('Datos actuales:');
    console.log('  Total Uses:', currentData.total_uses);
    console.log('  Used Uses:', currentData.used_uses);
    console.log('  Remaining:', currentData.total_uses - currentData.used_uses);
    
    // Actualizar con más pines (agregamos a total_uses, mantenemos used_uses)
    const newTotalUses = currentData.total_uses + additionalPins;
    
    const { data: updatedData, error: updateError } = await supabase
      .from('psychologist_usage_control')
      .update({
        total_uses: newTotalUses,
        updated_at: new Date().toISOString()
      })
      .eq('psychologist_id', psychologistId)
      .eq('is_active', true)
      .select()
      .single();
    
    if (updateError) {
      console.error('Error actualizando pines:', updateError);
      return;
    }
    
    console.log('✅ Pines asignados exitosamente!');
    console.log('Datos actualizados:');
    console.log('  Total Uses:', updatedData.total_uses);
    console.log('  Used Uses:', updatedData.used_uses);
    console.log('  Remaining:', updatedData.total_uses - updatedData.used_uses);
    console.log(`  Pines agregados: ${additionalPins}`);
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

assignMorePins();
