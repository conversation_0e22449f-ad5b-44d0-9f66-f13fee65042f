/* Mejoras para el menú lateral */

/* Animaciones suaves para el sidebar */
.sidebar {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

/* Mejoras para los títulos de sección */
.section-title {
  position: relative;
  overflow: hidden;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30%;
  height: 1px;
  background: linear-gradient(to right, #ffda0a, transparent);
  transition: width 0.3s ease;
}

.section-title:hover::after {
  width: 60%;
}

/* Mejoras para los items del menú */
.menu-item {
  position: relative;
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 218, 10, 0.1), transparent);
  transition: left 0.6s ease;
}

.menu-item:hover::before {
  left: 100%;
}

/* Efecto de glow para el item activo */
.menu-item.active {
  position: relative;
}

.menu-item.active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(255, 218, 10, 0.1), transparent);
  border-radius: 8px;
  pointer-events: none;
}

/* Mejoras para los iconos */
.menu-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
}

.menu-item:hover .menu-icon {
  transform: scale(1.1);
}

.menu-item.active .menu-icon {
  transform: scale(1.05);
  filter: drop-shadow(0 0 8px rgba(255, 218, 10, 0.5));
}

/* Mejoras para las estrellas de favoritos */
.favorite-star {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.7;
}

.favorite-star:hover {
  opacity: 1;
  transform: scale(1.2) rotate(15deg);
  filter: drop-shadow(0 0 4px rgba(255, 218, 10, 0.8));
}

.favorite-star.active {
  opacity: 1;
  animation: starPulse 2s infinite;
}

@keyframes starPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Separadores mejorados */
.section-separator {
  background: linear-gradient(to right, transparent, rgba(164, 177, 205, 0.3), transparent);
  height: 1px;
  margin: 12px 16px;
  position: relative;
}

.section-separator::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background: rgba(164, 177, 205, 0.5);
  border-radius: 50%;
}

/* Mejoras para el botón de colapsar */
.collapse-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-button:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

/* Mejoras para el header del sidebar */
.sidebar-header {
  background: linear-gradient(135deg, rgba(18, 25, 64, 0.9), rgba(18, 25, 64, 1));
  backdrop-filter: blur(10px);
}

/* Mejoras para el logo */
.sidebar-logo {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-logo:hover {
  transform: scale(1.02);
  filter: drop-shadow(0 0 10px rgba(255, 218, 10, 0.3));
}

/* Scroll personalizado para el contenido del sidebar */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(255, 218, 10, 0.3);
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 218, 10, 0.5);
}

/* Responsive improvements */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
}

/* Mejoras para el botón de cerrar sesión */
.logout-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.logout-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(239, 68, 68, 0.1), transparent);
  transition: left 0.5s ease;
}

.logout-button:hover::before {
  left: 100%;
}

.logout-button:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

/* Mejoras de accesibilidad */
@media (prefers-reduced-motion: reduce) {
  .sidebar,
  .menu-item,
  .menu-icon,
  .favorite-star,
  .collapse-button,
  .sidebar-logo,
  .logout-button {
    transition: none !important;
    animation: none !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .sidebar {
    border-right: 2px solid #ffffff;
  }
  
  .menu-item.active {
    border: 2px solid #ffda0a;
  }
  
  .section-separator {
    background: #ffffff;
    height: 2px;
  }
}
