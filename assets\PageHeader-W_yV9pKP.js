import{j as e}from"./vendor-CIyllXGj.js";const i=({title:i,subtitle:a,icon:s,className:r="",showTransitions:l=!0})=>e.jsxDEV("div",{className:`bg-gradient-to-r from-blue-900 to-blue-800 text-white ${r}`,children:e.jsxDEV("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"flex items-center justify-center mb-3",children:[s&&e.jsxDEV("div",{className:"w-12 h-12 bg-[#f59e0b] rounded-full flex items-center justify-center mr-4 shadow-lg",children:e.jsxDEV(s,{className:"text-white text-xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:22,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:21,columnNumber:15},void 0),e.jsxDEV("h1",{className:"text-3xl font-bold",children:i},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:25,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:19,columnNumber:11},void 0),a&&e.jsxDEV("p",{className:"text-blue-100 text-lg",children:a},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:30,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:18,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:17,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:15,columnNumber:5},void 0);export{i as P};
