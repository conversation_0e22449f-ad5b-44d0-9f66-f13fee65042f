// Script de prueba para verificar que los imports funcionan correctamente
console.log('🔍 Verificando imports...');

try {
  // Verificar import de UnifiedAuthService
  import('./services/UnifiedAuthService.js').then(() => {
    console.log('✅ UnifiedAuthService importado correctamente');
  }).catch(error => {
    console.error('❌ Error al importar UnifiedAuthService:', error);
  });

  // Verificar import de BasicLogin
  import('./pages/auth/BasicLogin.jsx').then(() => {
    console.log('✅ BasicLogin importado correctamente');
  }).catch(error => {
    console.error('❌ Error al importar BasicLogin:', error);
  });

  // Verificar import de Configuracion
  import('./pages/Configuracion/Configuracion.jsx').then(() => {
    console.log('✅ Configuracion importado correctamente');
  }).catch(error => {
    console.error('❌ Error al importar Configuracion:', error);
  });

} catch (error) {
  console.error('❌ Error general:', error);
}
