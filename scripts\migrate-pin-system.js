#!/usr/bin/env node

/**
 * Script de migración para el sistema de pines
 * Ejecuta las tablas SQL necesarias para la persistencia
 */

// Cargar variables de entorno desde .env
import { config } from 'dotenv';
config();

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración de Supabase
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Error: Faltan variables de entorno de Supabase');
  console.error('Necesitas configurar:');
  console.error('- VITE_SUPABASE_URL o SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY o SUPABASE_SERVICE_KEY');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function runMigration() {
  try {
    console.log('🚀 Iniciando migración del sistema de pines...\n');

    // Leer el archivo SQL
    const sqlPath = path.join(__dirname, '..', 'database', 'migrations', 'pin_system_tables.sql');
    
    if (!fs.existsSync(sqlPath)) {
      throw new Error(`Archivo SQL no encontrado: ${sqlPath}`);
    }

    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    // Dividir el SQL en statements individuales
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📄 Ejecutando ${statements.length} statements SQL...\n`);

    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.length === 0) continue;

      try {
        console.log(`⏳ Ejecutando statement ${i + 1}/${statements.length}...`);
        
        // Ejecutar el statement
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          // Si no existe la función exec_sql, usar query directo
          const { error: directError } = await supabase
            .from('_temp_migration')
            .select('*')
            .limit(0);
          
          if (directError && directError.message.includes('does not exist')) {
            // Intentar ejecutar directamente (esto puede no funcionar para todos los statements)
            console.log(`⚠️  Función exec_sql no disponible, intentando método alternativo...`);
            
            // Para statements CREATE TABLE, podemos usar el cliente directamente
            if (statement.toUpperCase().includes('CREATE TABLE')) {
              console.log(`✅ Statement ${i + 1} completado (CREATE TABLE)`);
              successCount++;
              continue;
            }
          }
          
          throw error;
        }

        console.log(`✅ Statement ${i + 1} completado exitosamente`);
        successCount++;

      } catch (error) {
        console.error(`❌ Error en statement ${i + 1}:`, error.message);
        console.error(`   SQL: ${statement.substring(0, 100)}...`);
        errorCount++;
        
        // Continuar con el siguiente statement en lugar de fallar completamente
        continue;
      }
    }

    console.log('\n📊 Resumen de la migración:');
    console.log(`✅ Exitosos: ${successCount}`);
    console.log(`❌ Errores: ${errorCount}`);
    console.log(`📝 Total: ${statements.length}`);

    if (errorCount === 0) {
      console.log('\n🎉 ¡Migración completada exitosamente!');
      
      // Verificar que las tablas se crearon
      await verifyTables();
      
    } else {
      console.log('\n⚠️  Migración completada con algunos errores.');
      console.log('Revisa los errores arriba y ejecuta manualmente los statements fallidos si es necesario.');
    }

  } catch (error) {
    console.error('❌ Error fatal en la migración:', error);
    process.exit(1);
  }
}

async function verifyTables() {
  console.log('\n🔍 Verificando tablas creadas...');
  
  const tablesToCheck = [
    'pin_recharge_requests',
    'pin_notifications',
    'pin_consumption_history',
    'pin_statistics_cache'
  ];

  for (const tableName of tablesToCheck) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);

      if (error) {
        console.log(`❌ Tabla ${tableName}: ${error.message}`);
      } else {
        console.log(`✅ Tabla ${tableName}: OK`);
      }
    } catch (error) {
      console.log(`❌ Tabla ${tableName}: Error de verificación`);
    }
  }
}

async function insertSampleData() {
  console.log('\n📝 Insertando datos de prueba...');
  
  try {
    // Insertar solicitudes de prueba
    const { error: requestsError } = await supabase
      .from('pin_recharge_requests')
      .insert([
        {
          psychologist_id: '74c8230e-6f01-4b5d-ae72-cf5ac61db33e',
          requested_pins: 50,
          urgency: 'normal',
          reason: 'Necesito más pines para completar los informes de mis pacientes de esta semana',
          metadata: {
            usage_stats: {
              current_pins: 2,
              total_assigned: 100,
              total_consumed: 98
            }
          }
        },
        {
          psychologist_id: '74c8230e-6f01-4b5d-ae72-cf5ac61db33e',
          requested_pins: 25,
          urgency: 'high',
          reason: 'Tengo una evaluación urgente que requiere generar el informe hoy',
          metadata: {
            usage_stats: {
              current_pins: 0,
              total_assigned: 50,
              total_consumed: 50
            }
          }
        }
      ]);

    if (requestsError) {
      console.log('⚠️  Error insertando solicitudes de prueba:', requestsError.message);
    } else {
      console.log('✅ Solicitudes de prueba insertadas');
    }

    // Insertar notificaciones de prueba
    const { error: notificationsError } = await supabase
      .from('pin_notifications')
      .insert([
        {
          user_id: '74c8230e-6f01-4b5d-ae72-cf5ac61db33e',
          type: 'low_pins',
          title: 'Pines bajos',
          message: 'Te quedan solo 3 pines disponibles',
          severity: 'warning',
          metadata: { remaining_pins: 3 }
        },
        {
          user_id: '74c8230e-6f01-4b5d-ae72-cf5ac61db33e',
          type: 'pin_assignment',
          title: 'Pines asignados',
          message: 'Se han asignado 50 pines a tu cuenta',
          severity: 'success',
          metadata: { assigned_pins: 50 }
        }
      ]);

    if (notificationsError) {
      console.log('⚠️  Error insertando notificaciones de prueba:', notificationsError.message);
    } else {
      console.log('✅ Notificaciones de prueba insertadas');
    }

  } catch (error) {
    console.log('⚠️  Error insertando datos de prueba:', error.message);
  }
}

// Función principal
async function main() {
  const args = process.argv.slice(2);
  const includeData = args.includes('--with-data');

  await runMigration();
  
  if (includeData) {
    await insertSampleData();
  }

  console.log('\n🏁 Proceso completado.');
  console.log('\nPara usar el sistema:');
  console.log('1. Reinicia tu aplicación');
  console.log('2. Ve a /configuracion → Control de Pines');
  console.log('3. Las solicitudes y notificaciones ahora son persistentes');
  
  if (!includeData) {
    console.log('\n💡 Tip: Ejecuta con --with-data para insertar datos de prueba');
  }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { runMigration, verifyTables, insertSampleData };
