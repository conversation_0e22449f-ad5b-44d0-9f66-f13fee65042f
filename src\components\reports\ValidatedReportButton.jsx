import React, { useState, useEffect } from 'react';
import { FaFileAlt, FaS<PERSON>ner, FaExclamationTriangle, FaLock, FaCrown } from 'react-icons/fa';
import { useAdvancedPinValidation } from '../../hooks/useAdvancedPinValidation';
import PinValidationAlert, { PinValidationConfirmation } from '../pin/PinValidationAlert';
import PinRechargePrompt from '../pin/PinRechargePrompt';
import AdminPinService from '../../services/pin/AdminPinService';
import { toast } from 'react-toastify';

/**
 * Botón inteligente para generar informes con validación automática de pines
 * Incluye todas las validaciones y confirmaciones necesarias
 */
const ValidatedReportButton = ({
  psychologistId,
  patientId,
  patientIds = null, // Para generación en lote
  onGenerateReport,
  requiredPins = 1,
  buttonText = "Generar Informe",
  buttonClass = "bg-blue-600 hover:bg-blue-700 text-white",
  disabled = false,
  showValidationDetails = true,
  size = "md",
  variant = "primary"
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showRechargePrompt, setShowRechargePrompt] = useState(false);
  const [validationError, setValidationError] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [adminValidation, setAdminValidation] = useState(null);

  // Determinar si es generación en lote
  const isBatch = Array.isArray(patientIds) && patientIds.length > 0;
  const actualRequiredPins = isBatch ? patientIds.length : requiredPins;

  // Hook de validación avanzada
  const {
    validationResult,
    isValidating,
    canProceed,
    hasWarning,
    isBlocked,
    validateSingleReport,
    validateBatchReports,
    getValidationSummary
  } = useAdvancedPinValidation(psychologistId, {
    showToastAlerts: false, // Manejamos las alertas manualmente
    autoValidate: true,
    onValidationChange: (result) => {
      if (!result.canProceed) {
        setValidationError(result);
        // Mostrar prompt de recarga si no hay pines
        if (result.reason === 'INSUFFICIENT_PINS' || result.reason === 'NO_PINS_AVAILABLE') {
          setShowRechargePrompt(true);
        }
      } else {
        setValidationError(null);
        setShowRechargePrompt(false);
      }
    }
  });

  // Verificar si es administrador y validar automáticamente
  useEffect(() => {
    const checkAdminAndValidate = async () => {
      if (!psychologistId) return;

      try {
        // Verificar si es administrador
        const adminCheck = await AdminPinService.validateAdminAccess(psychologistId, actualRequiredPins);

        if (adminCheck.isAdmin) {
          setIsAdmin(true);
          setAdminValidation(adminCheck);
          return; // Los admins no necesitan validación de pines
        }

        setIsAdmin(false);
        setAdminValidation(null);

        // Si no es admin, proceder con validación normal
        if (isBatch) {
          validateBatchReports(patientIds);
        } else {
          validateSingleReport(actualRequiredPins);
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsAdmin(false);
        setAdminValidation(null);
      }
    };

    checkAdminAndValidate();
  }, [psychologistId, patientIds, actualRequiredPins, isBatch, validateSingleReport, validateBatchReports]);

  // Configuración de tamaños
  const sizeConfig = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-sm",
    lg: "px-6 py-3 text-base"
  };

  // Configuración de variantes
  const variantConfig = {
    primary: "bg-blue-600 hover:bg-blue-700 text-white",
    secondary: "bg-gray-600 hover:bg-gray-700 text-white",
    success: "bg-green-600 hover:bg-green-700 text-white",
    warning: "bg-yellow-600 hover:bg-yellow-700 text-white",
    danger: "bg-red-600 hover:bg-red-700 text-white"
  };

  const buttonClasses = `
    ${variantConfig[variant] || buttonClass}
    ${sizeConfig[size]}
    font-medium rounded-lg transition-colors duration-200
    flex items-center justify-center space-x-2
    disabled:opacity-50 disabled:cursor-not-allowed
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
  `.trim();

  // Manejar click del botón
  const handleClick = async () => {
    if (disabled || isGenerating || isValidating) return;

    // Si es administrador, proceder directamente sin validación
    if (isAdmin && adminValidation) {
      await executeGeneration();
      return;
    }

    // Revalidar antes de proceder para usuarios normales
    let validation;
    if (isBatch) {
      validation = await validateBatchReports(patientIds);
    } else {
      validation = await validateSingleReport(actualRequiredPins);
    }

    if (!validation.canProceed) {
      toast.error(validation.userMessage);
      return;
    }

    // Si hay advertencias, mostrar confirmación
    if (validation.hasWarning || validation.severity === 'warning' || validation.severity === 'critical') {
      setShowConfirmation(true);
      return;
    }

    // Proceder directamente si no hay advertencias
    await executeGeneration();
  };

  // Ejecutar la generación del informe
  const executeGeneration = async () => {
    try {
      setIsGenerating(true);
      setShowConfirmation(false);

      if (onGenerateReport) {
        if (isBatch) {
          await onGenerateReport(patientIds);
        } else {
          await onGenerateReport(patientId);
        }
      }

      const successMessage = isAdmin
        ? (isBatch
            ? `${patientIds.length} informes generados exitosamente (Acceso de administrador)`
            : 'Informe generado exitosamente (Acceso de administrador)')
        : (isBatch
            ? `${patientIds.length} informes generados exitosamente`
            : 'Informe generado exitosamente');

      toast.success(successMessage);

      // Revalidar después de la generación
      setTimeout(() => {
        if (isBatch) {
          validateBatchReports(patientIds);
        } else {
          validateSingleReport(actualRequiredPins);
        }
      }, 1000);

    } catch (error) {
      console.error('Error al generar informe:', error);
      toast.error('Error al generar el informe');
    } finally {
      setIsGenerating(false);
    }
  };

  // Determinar el estado del botón - Los administradores nunca están bloqueados
  const isButtonDisabled = disabled || isGenerating || isValidating || (isBlocked && !isAdmin);
  
  // Determinar el texto del botón
  let displayText = buttonText;
  if (isValidating) {
    displayText = "Validando...";
  } else if (isGenerating) {
    displayText = isBatch ? "Generando informes..." : "Generando informe...";
  } else if (isBlocked && !isAdmin) {
    displayText = "Bloqueado";
  } else if (isAdmin) {
    displayText = `${buttonText} (Admin)`;
  }

  // Determinar el icono
  let IconComponent = FaFileAlt;
  if (isValidating || isGenerating) {
    IconComponent = FaSpinner;
  } else if (isBlocked && !isAdmin) {
    IconComponent = FaLock;
  } else if (isAdmin) {
    IconComponent = FaCrown;
  } else if (hasWarning) {
    IconComponent = FaExclamationTriangle;
  }

  return (
    <div className="space-y-3">
      {/* Alerta de administrador */}
      {isAdmin && adminValidation && showValidationDetails && (
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-3">
          <div className="flex items-center">
            <FaCrown className="text-purple-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-purple-900">Acceso de Administrador</p>
              <p className="text-xs text-purple-700">Sin restricciones de pines - Acceso completo al sistema</p>
            </div>
          </div>
        </div>
      )}

      {/* Alerta de validación para usuarios normales */}
      {!isAdmin && showValidationDetails && validationResult && (
        <PinValidationAlert
          validationResult={validationResult}
          showDetails={true}
          className="text-sm"
        />
      )}

      {/* Botón principal */}
      <button
        onClick={handleClick}
        disabled={isButtonDisabled}
        className={buttonClasses}
        title={
          isBlocked 
            ? validationResult?.userMessage || "No se puede generar el informe"
            : `${displayText} - ${actualRequiredPins} pin${actualRequiredPins !== 1 ? 's' : ''} requerido${actualRequiredPins !== 1 ? 's' : ''}`
        }
      >
        <IconComponent 
          className={`h-4 w-4 ${(isValidating || isGenerating) ? 'animate-spin' : ''}`} 
        />
        <span>{displayText}</span>
        
        {/* Indicador de pines requeridos */}
        {!isBlocked && actualRequiredPins > 1 && (
          <span className="bg-white bg-opacity-20 px-2 py-0.5 rounded-full text-xs">
            {actualRequiredPins} pines
          </span>
        )}
      </button>

      {/* Confirmación para advertencias */}
      {showConfirmation && validationResult && (
        <PinValidationConfirmation
          validationResult={validationResult}
          onConfirm={executeGeneration}
          onCancel={() => setShowConfirmation(false)}
          confirmText={isBatch ? "Generar Informes" : "Generar Informe"}
          cancelText="Cancelar"
        />
      )}

      {/* Información adicional para lotes */}
      {isBatch && patientIds.length > 0 && (
        <div className="text-xs text-gray-600 text-center">
          Generación en lote: {patientIds.length} pacientes
        </div>
      )}

      {/* Prompt de recarga cuando no hay pines */}
      {showRechargePrompt && validationResult && (
        <PinRechargePrompt
          psychologistId={psychologistId}
          currentPins={validationResult.remainingPins || 0}
          requiredPins={actualRequiredPins}
          onClose={() => setShowRechargePrompt(false)}
          variant="inline"
        />
      )}
    </div>
  );
};

/**
 * Versión compacta del botón validado
 */
export const CompactValidatedReportButton = (props) => {
  return (
    <ValidatedReportButton
      {...props}
      size="sm"
      showValidationDetails={false}
    />
  );
};

/**
 * Botón para generación en lote
 */
export const BatchValidatedReportButton = (props) => {
  return (
    <ValidatedReportButton
      {...props}
      buttonText={`Generar ${props.patientIds?.length || 0} Informes`}
      variant="success"
    />
  );
};

export default ValidatedReportButton;
