import{r as e,u as s,Q as r,j as a,F as t,W as n,C as l,D as o}from"./vendor-BqMjyOVw.js";import{U as c}from"./UnifiedAuthService-Dpry20MH.js";import"./index-Bdl1jgS_.js";const i=()=>{const[i,d]=e.useState(""),[u,m]=e.useState(""),[x,p]=e.useState(""),[h,b]=e.useState(!1),[f,g]=e.useState(!1),[y,j]=e.useState(!1),[v,N]=e.useState(!1),[w,C]=e.useState(null),k=s();e.useEffect(()=>{const e=c.getCurrentUser();if(!e)return r.error("Debe iniciar sesión primero"),void k("/login");C(e),e.require_password_change||k("/student/questionnaire")},[k]);return w?a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"max-w-md w-full space-y-8",children:[a.jsxs("div",{children:[a.jsx("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100",children:a.jsx(n,{className:"h-6 w-6 text-blue-600"})}),a.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Cambiar Contraseña"}),a.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Hola ",a.jsx("span",{className:"font-medium text-blue-600",children:w.nombre}),", debe cambiar su contraseña temporal antes de continuar."]})]}),a.jsxs("form",{className:"mt-8 space-y-6",onSubmit:e=>{return s=null,a=null,t=function*(){e.preventDefault(),N(!0);try{if(!i.trim())return r.error("Ingrese su contraseña actual"),void N(!1);if(!u.trim())return r.error("Ingrese la nueva contraseña"),void N(!1);if(u!==x)return r.error("Las contraseñas no coinciden"),void N(!1);const e=c.validatePassword(u);if(!e.isValid)return r.error(e.errors[0]),void N(!1);const s=yield c.changePassword(w.id,i,u);s.success?(r.success("Contraseña actualizada correctamente"),k("/student/questionnaire")):r.error(s.error||"Error al cambiar la contraseña")}catch(s){r.error("Error interno del servidor")}finally{N(!1)}},new Promise((r,n)=>{var l=s=>{try{c(t.next(s))}catch(e){n(e)}},o=s=>{try{c(t.throw(s))}catch(e){n(e)}},c=e=>e.done?r(e.value):Promise.resolve(e.value).then(l,o);c((t=t.apply(s,a)).next())});var s,a,t},children:[a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{children:[a.jsx("label",{htmlFor:"current-password",className:"block text-sm font-medium text-gray-700",children:"Contraseña Actual"}),a.jsxs("div",{className:"mt-1 relative",children:[a.jsx("input",{id:"current-password",name:"current-password",type:h?"text":"password",required:!0,className:"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm pr-10",placeholder:"Ingrese su contraseña actual",value:i,onChange:e=>d(e.target.value),disabled:v}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>b(!h),children:h?a.jsx(l,{className:"h-4 w-4 text-gray-400"}):a.jsx(o,{className:"h-4 w-4 text-gray-400"})})]})]}),a.jsxs("div",{children:[a.jsx("label",{htmlFor:"new-password",className:"block text-sm font-medium text-gray-700",children:"Nueva Contraseña"}),a.jsxs("div",{className:"mt-1 relative",children:[a.jsx("input",{id:"new-password",name:"new-password",type:f?"text":"password",required:!0,className:"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm pr-10",placeholder:"Ingrese su nueva contraseña",value:u,onChange:e=>m(e.target.value),disabled:v}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>g(!f),children:f?a.jsx(l,{className:"h-4 w-4 text-gray-400"}):a.jsx(o,{className:"h-4 w-4 text-gray-400"})})]}),a.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Mínimo 6 caracteres, debe incluir letras y números"})]}),a.jsxs("div",{children:[a.jsx("label",{htmlFor:"confirm-password",className:"block text-sm font-medium text-gray-700",children:"Confirmar Nueva Contraseña"}),a.jsxs("div",{className:"mt-1 relative",children:[a.jsx("input",{id:"confirm-password",name:"confirm-password",type:y?"text":"password",required:!0,className:"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm pr-10",placeholder:"Confirme su nueva contraseña",value:x,onChange:e=>p(e.target.value),disabled:v}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>j(!y),children:y?a.jsx(l,{className:"h-4 w-4 text-gray-400"}):a.jsx(o,{className:"h-4 w-4 text-gray-400"})})]})]})]}),a.jsx("div",{children:a.jsx("button",{type:"submit",disabled:v,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:v?a.jsxs(a.Fragment,{children:[a.jsx(t,{className:"animate-spin -ml-1 mr-3 h-4 w-4"}),"Cambiando contraseña..."]}):a.jsxs(a.Fragment,{children:[a.jsx(n,{className:"-ml-1 mr-3 h-4 w-4"}),"Cambiar Contraseña"]})})}),a.jsx("div",{className:"text-center",children:a.jsx("button",{type:"button",onClick:()=>{c.logout(),k("/login")},className:"text-sm text-gray-600 hover:text-gray-900",children:"Cancelar y cerrar sesión"})})]})]})}):a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:a.jsxs("div",{className:"text-center",children:[a.jsx(t,{className:"animate-spin text-4xl text-blue-500 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Verificando sesión..."})]})})};export{i as default};
