import { supabase } from '../config/supabase';

/**
 * Servicio de autenticación específico para pacientes
 * Maneja la autenticación contra la tabla 'pacientes' directamente
 */
class PatientAuthService {
  /**
   * Autentica un paciente usando email/documento y contraseña
   * @param {string} identifier - Email o documento del paciente
   * @param {string} password - Contraseña del paciente
   * @returns {Promise<Object>} Resultado de la autenticación
   */
  static async login(identifier, password) {
    try {
      console.log('🔐 Iniciando autenticación de paciente...');
      
      // Llamar a la función de Supabase para autenticar paciente
      const { data, error } = await supabase.rpc('authenticate_patient', {
        identifier: identifier.trim(),
        password: password
      });

      if (error) {
        console.error('❌ Error en autenticación de paciente:', error);
        throw new Error(error.message || 'Error en la autenticación');
      }

      if (!data.success) {
        console.warn('⚠️ Autenticación fallida:', data.error);
        return {
          success: false,
          error: data.error
        };
      }

      const patient = data.patient;
      console.log('✅ Paciente autenticado exitosamente:', {
        id: patient.id,
        nombre: patient.nombre,
        email: patient.email,
        requirePasswordChange: patient.require_password_change
      });

      // Almacenar datos del paciente en localStorage
      const patientData = {
        id: patient.id,
        nombre: patient.nombre,
        apellido: patient.apellido,
        email: patient.email,
        documento: patient.documento,
        genero: patient.genero,
        fecha_nacimiento: patient.fecha_nacimiento,
        psicologo_id: patient.psicologo_id,
        tipo_usuario: 'paciente',
        require_password_change: patient.require_password_change,
        has_temp_password: patient.has_temp_password
      };

      localStorage.setItem('isLoggedIn', 'true');
      localStorage.setItem('userRole', 'paciente');
      localStorage.setItem('patientData', JSON.stringify(patientData));
      localStorage.setItem('userEmail', patient.email);

      return {
        success: true,
        patient: patientData,
        requirePasswordChange: patient.require_password_change
      };

    } catch (error) {
      console.error('❌ Error en login de paciente:', error);
      return {
        success: false,
        error: error.message || 'Error interno del servidor'
      };
    }
  }

  /**
   * Establece una contraseña temporal para un paciente
   * @param {string} patientId - ID del paciente
   * @param {string} tempPassword - Contraseña temporal
   * @returns {Promise<Object>} Resultado de la operación
   */
  static async setTempPassword(patientId, tempPassword) {
    try {
      console.log('🔑 Estableciendo contraseña temporal para paciente:', patientId);

      const { data, error } = await supabase.rpc('set_patient_temp_password', {
        patient_id: patientId,
        temp_password: tempPassword
      });

      if (error) {
        console.error('❌ Error al establecer contraseña temporal:', error);
        throw new Error(error.message);
      }

      if (!data.success) {
        console.warn('⚠️ Fallo al establecer contraseña temporal:', data.error);
        return {
          success: false,
          error: data.error
        };
      }

      console.log('✅ Contraseña temporal establecida exitosamente');
      return {
        success: true,
        message: data.message
      };

    } catch (error) {
      console.error('❌ Error al establecer contraseña temporal:', error);
      return {
        success: false,
        error: error.message || 'Error interno del servidor'
      };
    }
  }

  /**
   * Cambia la contraseña de un paciente
   * @param {string} patientId - ID del paciente
   * @param {string} oldPassword - Contraseña actual
   * @param {string} newPassword - Nueva contraseña
   * @returns {Promise<Object>} Resultado de la operación
   */
  static async changePassword(patientId, oldPassword, newPassword) {
    try {
      console.log('🔄 Cambiando contraseña para paciente:', patientId);

      const { data, error } = await supabase.rpc('change_patient_password', {
        patient_id: patientId,
        old_password: oldPassword,
        new_password: newPassword
      });

      if (error) {
        console.error('❌ Error al cambiar contraseña:', error);
        throw new Error(error.message);
      }

      if (!data.success) {
        console.warn('⚠️ Fallo al cambiar contraseña:', data.error);
        return {
          success: false,
          error: data.error
        };
      }

      console.log('✅ Contraseña cambiada exitosamente');
      
      // Actualizar datos en localStorage si es necesario
      const patientData = this.getCurrentPatient();
      if (patientData) {
        patientData.require_password_change = false;
        patientData.has_temp_password = false;
        localStorage.setItem('patientData', JSON.stringify(patientData));
      }

      return {
        success: true,
        message: data.message
      };

    } catch (error) {
      console.error('❌ Error al cambiar contraseña:', error);
      return {
        success: false,
        error: error.message || 'Error interno del servidor'
      };
    }
  }

  /**
   * Obtiene los datos del paciente actual desde localStorage
   * @returns {Object|null} Datos del paciente o null si no está logueado
   */
  static getCurrentPatient() {
    try {
      const patientData = localStorage.getItem('patientData');
      return patientData ? JSON.parse(patientData) : null;
    } catch (error) {
      console.error('❌ Error al obtener datos del paciente:', error);
      return null;
    }
  }

  /**
   * Verifica si hay un paciente logueado
   * @returns {boolean} True si hay un paciente logueado
   */
  static isPatientLoggedIn() {
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    const userRole = localStorage.getItem('userRole') === 'paciente';
    const patientData = this.getCurrentPatient();
    
    return isLoggedIn && userRole && patientData !== null;
  }

  /**
   * Cierra la sesión del paciente
   */
  static logout() {
    console.log('🚪 Cerrando sesión de paciente');
    
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('userRole');
    localStorage.removeItem('patientData');
    localStorage.removeItem('userEmail');
    
    console.log('✅ Sesión de paciente cerrada');
  }

  /**
   * Genera una contraseña temporal aleatoria
   * @param {number} length - Longitud de la contraseña (por defecto 8)
   * @returns {string} Contraseña temporal generada
   */
  static generateTempPassword(length = 8) {
    const charset = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    return password;
  }

  /**
   * Valida el formato de una contraseña
   * @param {string} password - Contraseña a validar
   * @returns {Object} Resultado de la validación
   */
  static validatePassword(password) {
    const errors = [];
    
    if (!password || password.length < 6) {
      errors.push('La contraseña debe tener al menos 6 caracteres');
    }
    
    if (password.length > 50) {
      errors.push('La contraseña no puede tener más de 50 caracteres');
    }
    
    if (!/[A-Za-z]/.test(password)) {
      errors.push('La contraseña debe contener al menos una letra');
    }
    
    if (!/[0-9]/.test(password)) {
      errors.push('La contraseña debe contener al menos un número');
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
}

export default PatientAuthService;
