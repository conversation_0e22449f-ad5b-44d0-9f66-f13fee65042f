import React, { useState } from 'react';
import { supabase } from '../../api/supabaseClient';
import { useAuth } from '../../contexts/AuthContext';

/**
 * Test rápido para verificar la carga de psicólogos
 */
const QuickPsychologistTest = () => {
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  const runTest = async () => {
    setLoading(true);
    setResult(null);

    const testResults = {
      user: user ? { id: user.id, email: user.email } : null,
      tests: []
    };

    try {
      // Test 1: Verificar conexión básica
      console.log('🔄 Test 1: Verificando conexión...');
      try {
        const { data: healthCheck, error: healthError } = await supabase
          .from('psicologos')
          .select('count', { count: 'exact', head: true });
        
        testResults.tests.push({
          name: 'Conexión básica',
          success: !healthError,
          data: healthCheck,
          error: healthError?.message
        });
      } catch (error) {
        testResults.tests.push({
          name: 'Conexión básica',
          success: false,
          error: error.message
        });
      }

      // Test 2: Consulta simple sin filtros
      console.log('🔄 Test 2: Consulta simple...');
      try {
        const { data: simpleData, error: simpleError } = await supabase
          .from('psicologos')
          .select('id, nombre')
          .limit(3);
        
        testResults.tests.push({
          name: 'Consulta simple (id, nombre)',
          success: !simpleError,
          data: simpleData,
          error: simpleError?.message
        });
      } catch (error) {
        testResults.tests.push({
          name: 'Consulta simple (id, nombre)',
          success: false,
          error: error.message
        });
      }

      // Test 3: Consulta completa
      console.log('🔄 Test 3: Consulta completa...');
      try {
        const { data: fullData, error: fullError } = await supabase
          .from('psicologos')
          .select('*')
          .limit(3);
        
        testResults.tests.push({
          name: 'Consulta completa (*)',
          success: !fullError,
          data: fullData,
          error: fullError?.message
        });
      } catch (error) {
        testResults.tests.push({
          name: 'Consulta completa (*)',
          success: false,
          error: error.message
        });
      }

      // Test 4: Consulta con JOIN
      console.log('🔄 Test 4: Consulta con JOIN...');
      try {
        const { data: joinData, error: joinError } = await supabase
          .from('psicologos')
          .select('*, instituciones(id, nombre)')
          .limit(3);
        
        testResults.tests.push({
          name: 'Consulta con JOIN (instituciones)',
          success: !joinError,
          data: joinData,
          error: joinError?.message
        });
      } catch (error) {
        testResults.tests.push({
          name: 'Consulta con JOIN (instituciones)',
          success: false,
          error: error.message
        });
      }

      // Test 5: Verificar estructura de tabla
      console.log('🔄 Test 5: Verificando estructura...');
      try {
        const { data: structureData, error: structureError } = await supabase
          .from('psicologos')
          .select('*')
          .limit(1);
        
        const columns = structureData && structureData.length > 0 
          ? Object.keys(structureData[0]) 
          : [];
        
        testResults.tests.push({
          name: 'Estructura de tabla',
          success: !structureError,
          data: { columns, sample: structureData?.[0] },
          error: structureError?.message
        });
      } catch (error) {
        testResults.tests.push({
          name: 'Estructura de tabla',
          success: false,
          error: error.message
        });
      }

    } catch (globalError) {
      testResults.globalError = globalError.message;
    }

    setResult(testResults);
    setLoading(false);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Test Rápido de Psicólogos</h2>
      
      {/* Usuario actual */}
      <div className="mb-4 p-4 bg-gray-100 rounded">
        <h3 className="font-semibold">Usuario Actual:</h3>
        <p>{user ? `${user.email} (ID: ${user.id})` : 'No autenticado'}</p>
      </div>

      {/* Botón de test */}
      <button
        onClick={runTest}
        disabled={loading}
        className="mb-6 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
      >
        {loading ? 'Ejecutando tests...' : 'Ejecutar Tests'}
      </button>

      {/* Resultados */}
      {result && (
        <div className="space-y-4">
          {result.globalError && (
            <div className="p-4 bg-red-100 text-red-800 rounded">
              <strong>Error Global:</strong> {result.globalError}
            </div>
          )}

          {result.tests.map((test, index) => (
            <div
              key={index}
              className={`p-4 rounded ${
                test.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}
            >
              <h4 className="font-semibold">
                {test.success ? '✅' : '❌'} {test.name}
              </h4>
              {test.error && <p className="text-sm mt-1">Error: {test.error}</p>}
              {test.data && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm font-medium">Ver datos</summary>
                  <pre className="mt-2 text-xs bg-white p-2 rounded overflow-auto max-h-40">
                    {JSON.stringify(test.data, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default QuickPsychologistTest;
