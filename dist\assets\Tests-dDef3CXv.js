import{j as s}from"./vendor-BqMjyOVw.js";import{C as e,b as a,a as t}from"./index-Bdl1jgS_.js";const n=()=>s.jsxs("div",{className:"container mx-auto py-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Gestión de Tests"}),s.jsxs(e,{children:[s.jsx(a,{children:s.jsx("h2",{className:"text-lg font-medium",children:"Asignación y Administración de Tests"})}),s.jsx(t,{children:s.jsx("p",{className:"text-gray-600",children:"Esta sección permitirá asignar y gestionar tests para estudiantes (componente en desarrollo)."})})]})]});export{n as default};
