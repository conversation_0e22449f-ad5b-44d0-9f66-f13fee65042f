import{r as e,u as a,j as i,C as s,D as r,F as l}from"./vendor-CIyllXGj.js";const o=()=>{const[o,n]=e.useState(""),[t,u]=e.useState(""),[m,c]=e.useState("candidato"),[d,g]=e.useState(!1),b=a();return i.jsxDEV("div",{className:"min-h-screen flex",children:[i.jsxDEV("div",{className:"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-blue-800 relative overflow-hidden",children:[i.jsxDEV("div",{className:"absolute inset-0 opacity-10",children:[i.jsxDEV("div",{className:"absolute top-20 left-20 w-32 h-32 border border-white rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:77,columnNumber:11},void 0),i.jsxDEV("div",{className:"absolute bottom-20 right-20 w-24 h-24 border border-white rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:78,columnNumber:11},void 0),i.jsxDEV("div",{className:"absolute top-1/2 left-10 w-16 h-16 border border-white rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:79,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:76,columnNumber:9},void 0),i.jsxDEV("div",{className:"relative z-10 flex flex-col justify-center px-12 text-white",children:[i.jsxDEV("div",{className:"mb-12",children:i.jsxDEV("div",{className:"flex items-center mb-4",children:[i.jsxDEV("div",{className:"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:i.jsxDEV("span",{className:"text-white font-bold text-xl",children:"B"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:88,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:87,columnNumber:15},void 0),i.jsxDEV("div",{children:[i.jsxDEV("h1",{className:"text-3xl font-bold",children:"BAT-7"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:91,columnNumber:17},void 0),i.jsxDEV("p",{className:"text-blue-200",children:"Sistema de Evaluación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:92,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:90,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:86,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:85,columnNumber:11},void 0),i.jsxDEV("div",{className:"mb-8",children:[i.jsxDEV("h2",{className:"text-4xl font-bold mb-4",children:["Bienvenido al",i.jsxDEV("br",{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:100,columnNumber:28},void 0),i.jsxDEV("span",{className:"text-orange-400",children:"Futuro de la Evaluación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:101,columnNumber:15},void 0),i.jsxDEV("br",{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:101,columnNumber:79},void 0),i.jsxDEV("span",{className:"text-blue-200",children:"Psicométrica"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:102,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:99,columnNumber:13},void 0),i.jsxDEV("p",{className:"text-blue-200 text-lg leading-relaxed",children:"Plataforma de nueva generación para evaluaciones psicológicas inteligentes y análisis avanzado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:104,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:98,columnNumber:11},void 0),i.jsxDEV("div",{className:"space-y-4",children:[i.jsxDEV("div",{className:"flex items-center",children:[i.jsxDEV("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:i.jsxDEV("span",{className:"text-white text-sm",children:"✓"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:113,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:112,columnNumber:15},void 0),i.jsxDEV("div",{children:[i.jsxDEV("h3",{className:"font-semibold",children:"Evaluaciones Inteligentes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:116,columnNumber:17},void 0),i.jsxDEV("p",{className:"text-blue-200 text-sm",children:"Adaptativas y avanzadas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:117,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:115,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:111,columnNumber:13},void 0),i.jsxDEV("div",{className:"flex items-center",children:[i.jsxDEV("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:i.jsxDEV("span",{className:"text-white text-sm",children:"✓"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:123,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:122,columnNumber:15},void 0),i.jsxDEV("div",{children:[i.jsxDEV("h3",{className:"font-semibold",children:"Dashboard Avanzado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:126,columnNumber:17},void 0),i.jsxDEV("p",{className:"text-blue-200 text-sm",children:"Análisis en tiempo real"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:127,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:125,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:121,columnNumber:13},void 0),i.jsxDEV("div",{className:"flex items-center",children:[i.jsxDEV("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:i.jsxDEV("span",{className:"text-white text-sm",children:"✓"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:133,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:132,columnNumber:15},void 0),i.jsxDEV("div",{children:[i.jsxDEV("h3",{className:"font-semibold",children:"Seguridad Total"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:136,columnNumber:17},void 0),i.jsxDEV("p",{className:"text-blue-200 text-sm",children:"Protección de datos garantizada"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:137,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:135,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:131,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:110,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:83,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:74,columnNumber:7},void 0),i.jsxDEV("div",{className:"w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50",children:i.jsxDEV("div",{className:"w-full max-w-md",children:[i.jsxDEV("div",{className:"lg:hidden text-center mb-8",children:[i.jsxDEV("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"BAT-7"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:149,columnNumber:13},void 0),i.jsxDEV("p",{className:"text-gray-600",children:"Sistema de Evaluación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:150,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:148,columnNumber:11},void 0),i.jsxDEV("div",{className:"bg-white rounded-lg shadow-lg p-8",children:i.jsxDEV("form",{onSubmit:e=>{return a=null,i=null,s=function*(){e.preventDefault(),g(!0),setTimeout(()=>{switch(localStorage.setItem("isLoggedIn","true"),localStorage.setItem("userRole",m),localStorage.setItem("userEmail",o),g(!1),m){case"administrador":b("/admin/administration");break;case"psicologo":b("/admin/candidates");break;default:b("/home")}},1e3)},new Promise((r,l)=>{var o=a=>{try{t(s.next(a))}catch(e){l(e)}},n=a=>{try{t(s.throw(a))}catch(e){l(e)}},t=e=>e.done?r(e.value):Promise.resolve(e.value).then(o,n);t((s=s.apply(a,i)).next())});var a,i,s},className:"space-y-6",children:[i.jsxDEV("div",{children:[i.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"TIPO DE USUARIO"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:158,columnNumber:17},void 0),i.jsxDEV("div",{className:"space-y-3",children:[i.jsxDEV("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-orange-50 transition-colors",children:[i.jsxDEV("input",{type:"radio",name:"userType",value:"candidato",checked:"candidato"===m,onChange:e=>c(e.target.value),className:"sr-only"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:163,columnNumber:21},void 0),i.jsxDEV("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center "+("candidato"===m?"border-orange-500 bg-orange-500":"border-gray-300"),children:"candidato"===m&&i.jsxDEV("div",{className:"w-2 h-2 bg-white rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:174,columnNumber:56},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:171,columnNumber:21},void 0),i.jsxDEV("div",{className:"flex items-center",children:[i.jsxDEV("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-3",children:i.jsxDEV(s,{className:"text-white text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:178,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:177,columnNumber:23},void 0),i.jsxDEV("div",{children:[i.jsxDEV("div",{className:"font-medium text-gray-800",children:"Candidato"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:181,columnNumber:25},void 0),i.jsxDEV("div",{className:"text-sm text-gray-500",children:"Acceso para realizar evaluaciones psicométricas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:182,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:180,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:176,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:162,columnNumber:19},void 0),i.jsxDEV("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors",children:[i.jsxDEV("input",{type:"radio",name:"userType",value:"psicologo",checked:"psicologo"===m,onChange:e=>c(e.target.value),className:"sr-only"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:188,columnNumber:21},void 0),i.jsxDEV("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center "+("psicologo"===m?"border-gray-500 bg-gray-500":"border-gray-300"),children:"psicologo"===m&&i.jsxDEV("div",{className:"w-2 h-2 bg-white rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:199,columnNumber:56},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:196,columnNumber:21},void 0),i.jsxDEV("div",{className:"flex items-center",children:[i.jsxDEV("div",{className:"w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-3",children:i.jsxDEV(s,{className:"text-white text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:203,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:202,columnNumber:23},void 0),i.jsxDEV("div",{children:[i.jsxDEV("div",{className:"font-medium text-gray-800",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:206,columnNumber:25},void 0),i.jsxDEV("div",{className:"text-sm text-gray-500",children:"Acceso para gestionar candidatos y resultados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:207,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:205,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:201,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:187,columnNumber:19},void 0),i.jsxDEV("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors",children:[i.jsxDEV("input",{type:"radio",name:"userType",value:"administrador",checked:"administrador"===m,onChange:e=>c(e.target.value),className:"sr-only"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:213,columnNumber:21},void 0),i.jsxDEV("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center "+("administrador"===m?"border-gray-500 bg-gray-500":"border-gray-300"),children:"administrador"===m&&i.jsxDEV("div",{className:"w-2 h-2 bg-white rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:224,columnNumber:60},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:221,columnNumber:21},void 0),i.jsxDEV("div",{className:"flex items-center",children:[i.jsxDEV("div",{className:"w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-3",children:i.jsxDEV(s,{className:"text-white text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:228,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:227,columnNumber:23},void 0),i.jsxDEV("div",{children:[i.jsxDEV("div",{className:"font-medium text-gray-800",children:"Administrador"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:231,columnNumber:25},void 0),i.jsxDEV("div",{className:"text-sm text-gray-500",children:"Acceso completo al sistema"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:232,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:230,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:226,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:212,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:161,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:157,columnNumber:15},void 0),i.jsxDEV("div",{children:[i.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"EMAIL O DOCUMENTO"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:241,columnNumber:17},void 0),i.jsxDEV("div",{className:"relative",children:[i.jsxDEV(s,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:245,columnNumber:19},void 0),i.jsxDEV("input",{type:"text",value:o,onChange:e=>n(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent",placeholder:"Número de documento",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:246,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:244,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:240,columnNumber:15},void 0),i.jsxDEV("div",{children:[i.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CONTRASEÑA"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:259,columnNumber:17},void 0),i.jsxDEV("div",{className:"relative",children:[i.jsxDEV(r,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:263,columnNumber:19},void 0),i.jsxDEV("input",{type:"password",value:t,onChange:e=>u(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent",placeholder:"Ingresa tu contraseña",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:264,columnNumber:19},void 0),i.jsxDEV("button",{type:"button",className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:i.jsxDEV("i",{className:"fas fa-eye"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:276,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:272,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:262,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:258,columnNumber:15},void 0),i.jsxDEV("div",{className:"flex items-center justify-between",children:[i.jsxDEV("label",{className:"flex items-center",children:[i.jsxDEV("input",{type:"checkbox",className:"rounded border-gray-300 text-orange-500 focus:ring-orange-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:284,columnNumber:19},void 0),i.jsxDEV("span",{className:"ml-2 text-sm text-gray-600",children:"Recordarme"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:285,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:283,columnNumber:17},void 0),i.jsxDEV("a",{href:"#",className:"text-sm text-orange-500 hover:text-orange-600",children:"¿Olvidaste tu contraseña?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:287,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:282,columnNumber:15},void 0),i.jsxDEV("button",{type:"submit",disabled:d,className:"w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center",children:d?i.jsxDEV(i.Fragment,{children:[i.jsxDEV(l,{className:"animate-spin mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:300,columnNumber:21},void 0),"Iniciando sesión..."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:299,columnNumber:19},void 0):i.jsxDEV(i.Fragment,{children:[i.jsxDEV(s,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:305,columnNumber:21},void 0),"Iniciar Sesión"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:304,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:293,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:155,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:154,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:146,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:145,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:72,columnNumber:5},void 0)};export{o as default};
