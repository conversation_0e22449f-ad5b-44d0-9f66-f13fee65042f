import { supabase } from '../../api/supabaseClient.js';

/**
 * Servicio completo para el control de pines
 * Maneja asignación, consumo, estadísticas y visualización
 */
class CompletePinControlService {

  /**
   * Maneja errores relacionados con RLS y notificaciones
   * @param {Object} error - Error de Supabase
   * @returns {boolean} - True si el error es relacionado con notificaciones y se puede ignorar
   */
  static isNotificationRLSError(error) {
    return error &&
           error.code === '42501' &&
           error.message &&
           error.message.includes('system_notifications');
  }

  /**
   * Ejecuta una operación de base de datos con manejo de errores RLS
   * @param {Function} operation - Función que ejecuta la operación
   * @param {string} operationName - Nombre de la operación para logging
   * @returns {Promise} - Resultado de la operación
   */
  static async executeWithRLSHandling(operation, operationName) {
    try {
      return await operation();
    } catch (error) {
      if (this.isNotificationRLSError(error)) {
        console.warn(`⚠️ [${operationName}] Error de RLS en notificaciones ignorado:`, error.message);
        // Retornar un resultado exitoso simulado para continuar el flujo
        return { data: null, error: null };
      }
      throw error;
    }
  }
  
  /**
   * Obtiene todos los psicólogos con su información de pines
   * @returns {Promise<Array>} Lista de psicólogos con estadísticas de pines
   */
  static async getPsychologistsWithPinInfo() {
    try {
      console.log('📊 Obteniendo psicólogos con información de pines...');

      const { data: psychologists, error } = await supabase
        .from('psicologos')
        .select(`
          id,
          nombre,
          apellido,
          email,
          psychologist_usage_control!left (
            total_uses,
            used_uses,
            is_unlimited,
            is_active,
            plan_type,
            updated_at
          )
        `)
        .order('nombre', { ascending: true });

      if (error) {
        console.error('❌ Error obteniendo psicólogos:', error);
        throw error;
      }

      // Transformar datos para incluir estadísticas calculadas
      const transformedData = (psychologists || []).map(psy => {
        const control = psy.psychologist_usage_control?.[0];
        const hasControl = !!control;
        
        return {
          id: psy.id,
          nombre: psy.nombre,
          apellido: psy.apellido,
          email: psy.email || 'Sin email',
          fullName: `${psy.nombre} ${psy.apellido}`.trim(),
          
          // Información de pines
          hasControl,
          totalPins: hasControl ? (control.total_uses || 0) : 0,
          usedPins: hasControl ? (control.used_uses || 0) : 0,
          remainingPins: hasControl ? Math.max(0, (control.total_uses || 0) - (control.used_uses || 0)) : 0,
          isUnlimited: hasControl ? (control.is_unlimited || false) : false,
          isActive: hasControl ? (control.is_active || false) : false,
          planType: hasControl ? (control.plan_type || 'assigned') : null,
          lastUpdated: hasControl ? control.updated_at : null,
          
          // Estado visual
          status: hasControl ? (control.is_active ? 'Activo' : 'Inactivo') : 'Sin pines',
          statusColor: hasControl ? (control.is_active ? 'green' : 'yellow') : 'red'
        };
      });

      console.log(`✅ ${transformedData.length} psicólogos obtenidos`);
      return transformedData;

    } catch (error) {
      console.error('❌ Error en getPsychologistsWithPinInfo:', error);
      throw error;
    }
  }

  /**
   * Asigna pines a un psicólogo
   * @param {string} psychologistId - ID del psicólogo
   * @param {number} pins - Cantidad de pines a asignar
   * @param {boolean} isUnlimited - Si es plan ilimitado
   * @returns {Promise<Object>} Resultado de la asignación
   */
  static async assignPins(psychologistId, pins, isUnlimited = false) {
    try {
      console.log('📌 Asignando pines:', { psychologistId, pins, isUnlimited });

      // Validaciones
      if (!psychologistId) {
        throw new Error('ID de psicólogo requerido');
      }

      if (!isUnlimited && (!pins || pins <= 0)) {
        throw new Error('Cantidad de pines debe ser mayor a 0');
      }

      // Verificar que el psicólogo existe
      const { data: psychologist, error: psychError } = await supabase
        .from('psicologos')
        .select('id, nombre, apellido, email')
        .eq('id', psychologistId)
        .single();

      if (psychError || !psychologist) {
        throw new Error('Psicólogo no encontrado');
      }

      // Preparar datos para inserción/actualización
      const controlData = {
        psychologist_id: psychologistId,
        total_uses: isUnlimited ? 999999 : pins,
        used_uses: 0, // Resetear usos al asignar nuevos pines
        // remaining_uses es una columna generada, no la incluimos
        is_unlimited: isUnlimited,
        is_active: true,
        plan_type: isUnlimited ? 'unlimited' : 'assigned',
        updated_at: new Date().toISOString()
      };

      // Verificar si ya existe un control para este psicólogo
      const { data: existingControl } = await supabase
        .from('psychologist_usage_control')
        .select('id')
        .eq('psychologist_id', psychologistId)
        .single();

      let controlResult, controlError;

      if (existingControl) {
        // Actualizar registro existente con manejo de RLS
        const result = await this.executeWithRLSHandling(async () => {
          return await supabase
            .from('psychologist_usage_control')
            .update(controlData)
            .eq('psychologist_id', psychologistId)
            .select()
            .single();
        }, 'UPDATE_CONTROL');

        controlResult = result.data;
        controlError = result.error;
      } else {
        // Crear nuevo registro con manejo de RLS
        const result = await this.executeWithRLSHandling(async () => {
          return await supabase
            .from('psychologist_usage_control')
            .insert([controlData])
            .select()
            .single();
        }, 'INSERT_CONTROL');

        controlResult = result.data;
        controlError = result.error;
      }

      if (controlError && !this.isNotificationRLSError(controlError)) {
        console.error('❌ Error en upsert de control:', controlError);
        throw controlError;
      }

      // Registrar en logs de uso con manejo de RLS
      const logResult = await this.executeWithRLSHandling(async () => {
        return await supabase
          .from('pin_usage_logs')
          .insert([{
            psychologist_id: psychologistId,
            action_type: 'pin_assigned',
            pins_consumed: 0,
            metadata: {
              assigned_pins: isUnlimited ? 'unlimited' : pins,
              assignment_type: isUnlimited ? 'unlimited' : 'manual',
              psychologist_name: `${psychologist.nombre} ${psychologist.apellido}`,
              pins_remaining: isUnlimited ? 999999 : pins
            }
          }]);
      }, 'INSERT_LOG');

      if (logResult.error && !this.isNotificationRLSError(logResult.error)) {
        console.warn('⚠️ Error registrando log:', logResult.error);
      }

      console.log('✅ Pines asignados exitosamente');
      
      return {
        success: true,
        message: `Pines ${isUnlimited ? 'ilimitados' : pins} asignados a ${psychologist.nombre} ${psychologist.apellido}`,
        data: controlResult
      };

    } catch (error) {
      console.error('❌ Error asignando pines:', error);
      throw error;
    }
  }

  /**
   * Consume pines de un psicólogo
   * @param {string} psychologistId - ID del psicólogo
   * @param {number} pinsToConsume - Cantidad de pines a consumir
   * @param {Object} metadata - Información adicional sobre el consumo
   * @returns {Promise<Object>} Resultado del consumo
   */
  static async consumePins(psychologistId, pinsToConsume = 1, metadata = {}) {
    try {
      console.log('🔥 Consumiendo pines:', { psychologistId, pinsToConsume });

      // Obtener control actual
      const { data: control, error: controlError } = await supabase
        .from('psychologist_usage_control')
        .select('*')
        .eq('psychologist_id', psychologistId)
        .eq('is_active', true)
        .single();

      if (controlError || !control) {
        throw new Error('No se encontró control de pines activo para este psicólogo');
      }

      // Verificar si tiene pines suficientes (excepto si es ilimitado)
      if (!control.is_unlimited) {
        const remainingPins = (control.total_uses || 0) - (control.used_uses || 0);
        if (remainingPins < pinsToConsume) {
          throw new Error(`Pines insuficientes. Disponibles: ${remainingPins}, Requeridos: ${pinsToConsume}`);
        }
      }

      // Actualizar control
      const newUsedPins = (control.used_uses || 0) + pinsToConsume;
      const newRemainingPins = control.is_unlimited ? 999999 : Math.max(0, (control.total_uses || 0) - newUsedPins);

      const { error: updateError } = await supabase
        .from('psychologist_usage_control')
        .update({
          used_uses: newUsedPins,
          // remaining_uses es calculada automáticamente
          updated_at: new Date().toISOString()
        })
        .eq('psychologist_id', psychologistId);

      if (updateError) {
        throw updateError;
      }

      // Registrar consumo en logs
      const { error: logError } = await supabase
        .from('pin_usage_logs')
        .insert([{
          psychologist_id: psychologistId,
          action_type: 'pin_consumed',
          pins_consumed: pinsToConsume,
          metadata: {
            ...metadata,
            consumed_at: new Date().toISOString(),
            pins_remaining: newRemainingPins
          }
        }]);

      if (logError) {
        console.warn('⚠️ Error registrando consumo:', logError);
      }

      console.log('✅ Pines consumidos exitosamente');
      
      return {
        success: true,
        remainingPins: newRemainingPins,
        isUnlimited: control.is_unlimited
      };

    } catch (error) {
      console.error('❌ Error consumiendo pines:', error);
      throw error;
    }
  }

  /**
   * Resta pines de un psicólogo
   * @param {string} psychologistId - ID del psicólogo
   * @param {number} pinsToSubtract - Cantidad de pines a restar
   * @returns {Promise<Object>} Resultado de la resta
   */
  static async subtractPins(psychologistId, pinsToSubtract) {
    try {
      console.log('➖ Restando pines:', { psychologistId, pinsToSubtract });

      // Validaciones
      if (!psychologistId) {
        throw new Error('ID de psicólogo requerido');
      }

      if (!pinsToSubtract || pinsToSubtract <= 0) {
        throw new Error('Cantidad de pines a restar debe ser mayor a 0');
      }

      // Obtener control actual
      const { data: control, error: controlError } = await supabase
        .from('psychologist_usage_control')
        .select('*')
        .eq('psychologist_id', psychologistId)
        .eq('is_active', true)
        .single();

      if (controlError || !control) {
        throw new Error('No se encontró control de pines activo para este psicólogo');
      }

      if (control.is_unlimited) {
        throw new Error('No se pueden restar pines de un plan ilimitado');
      }

      // Calcular nuevos valores
      const newTotalPins = Math.max(0, (control.total_uses || 0) - pinsToSubtract);
      const currentUsedPins = control.used_uses || 0;

      // Si los pines restados hacen que el total sea menor que los usados, ajustar
      const newUsedPins = Math.min(currentUsedPins, newTotalPins);

      // Actualizar control
      const { data: updatedControl, error: updateError } = await supabase
        .from('psychologist_usage_control')
        .update({
          total_uses: newTotalPins,
          used_uses: newUsedPins,
          updated_at: new Date().toISOString()
        })
        .eq('psychologist_id', psychologistId)
        .select()
        .single();

      if (updateError) {
        throw updateError;
      }

      // Registrar en logs
      const { error: logError } = await supabase
        .from('pin_usage_logs')
        .insert([{
          psychologist_id: psychologistId,
          action_type: 'pin_subtracted',
          pins_consumed: pinsToSubtract,
          metadata: {
            subtracted_pins: pinsToSubtract,
            previous_total: control.total_uses,
            new_total: newTotalPins,
            adjusted_used_pins: newUsedPins !== currentUsedPins,
            subtracted_at: new Date().toISOString()
          }
        }]);

      if (logError) {
        console.warn('⚠️ Error registrando log de resta:', logError);
      }

      console.log('✅ Pines restados exitosamente');

      return {
        success: true,
        message: `${pinsToSubtract} pines restados exitosamente`,
        data: {
          previousTotal: control.total_uses,
          newTotal: newTotalPins,
          usedPins: newUsedPins,
          remainingPins: Math.max(0, newTotalPins - newUsedPins)
        }
      };

    } catch (error) {
      console.error('❌ Error restando pines:', error);
      throw error;
    }
  }

  /**
   * Elimina completamente el control de pines de un psicólogo
   * @param {string} psychologistId - ID del psicólogo
   * @returns {Promise<Object>} Resultado de la eliminación
   */
  static async removePsychologistPins(psychologistId) {
    try {
      console.log('🗑️ Eliminando control de pines:', { psychologistId });

      // Validaciones
      if (!psychologistId) {
        throw new Error('ID de psicólogo requerido');
      }

      // Verificar que el psicólogo existe
      const { data: psychologist, error: psychError } = await supabase
        .from('psicologos')
        .select('id, nombre, apellido')
        .eq('id', psychologistId)
        .single();

      if (psychError || !psychologist) {
        throw new Error('Psicólogo no encontrado');
      }

      // Obtener control actual para logs
      const { data: control } = await supabase
        .from('psychologist_usage_control')
        .select('*')
        .eq('psychologist_id', psychologistId)
        .single();

      // Eliminar control de pines
      const { error: deleteError } = await supabase
        .from('psychologist_usage_control')
        .delete()
        .eq('psychologist_id', psychologistId);

      if (deleteError) {
        throw deleteError;
      }

      // Registrar eliminación en logs
      const { error: logError } = await supabase
        .from('pin_usage_logs')
        .insert([{
          psychologist_id: psychologistId,
          action_type: 'pin_control_removed',
          pins_consumed: 0,
          metadata: {
            removed_control: control || {},
            psychologist_name: `${psychologist.nombre} ${psychologist.apellido}`,
            removed_at: new Date().toISOString()
          }
        }]);

      if (logError) {
        console.warn('⚠️ Error registrando log de eliminación:', logError);
      }

      console.log('✅ Control de pines eliminado exitosamente');

      return {
        success: true,
        message: `Control de pines eliminado para ${psychologist.nombre} ${psychologist.apellido}`,
        data: {
          psychologist: psychologist,
          removedControl: control
        }
      };

    } catch (error) {
      console.error('❌ Error eliminando control de pines:', error);
      throw error;
    }
  }

  /**
   * Obtiene estadísticas generales del sistema de pines
   * @returns {Promise<Object>} Estadísticas del sistema
   */
  static async getSystemStats() {
    try {
      console.log('📈 Obteniendo estadísticas del sistema...');

      // Estadísticas de psicólogos
      const { data: psychStats, error: psychError } = await supabase
        .from('psychologist_usage_control')
        .select('total_uses, used_uses, is_unlimited, is_active');

      if (psychError) {
        console.warn('⚠️ Error obteniendo estadísticas de psicólogos:', psychError);
      }

      // Estadísticas de logs
      const { data: logStats, error: logError } = await supabase
        .from('pin_usage_logs')
        .select('pins_consumed, created_at')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()); // Últimos 30 días

      if (logError) {
        console.warn('⚠️ Error obteniendo logs:', logError);
      }

      // Calcular estadísticas
      const stats = {
        totalPsychologists: psychStats?.length || 0,
        activePsychologists: psychStats?.filter(p => p.is_active)?.length || 0,
        totalPinsAssigned: psychStats?.reduce((sum, p) => sum + (p.total_uses || 0), 0) || 0,
        totalPinsUsed: psychStats?.reduce((sum, p) => sum + (p.used_uses || 0), 0) || 0,
        unlimitedPlans: psychStats?.filter(p => p.is_unlimited)?.length || 0,
        recentConsumption: logStats?.reduce((sum, l) => sum + (l.pins_consumed || 0), 0) || 0
      };

      stats.remainingPins = stats.totalPinsAssigned - stats.totalPinsUsed;
      stats.usagePercentage = stats.totalPinsAssigned > 0 ? 
        Math.round((stats.totalPinsUsed / stats.totalPinsAssigned) * 100) : 0;

      console.log('✅ Estadísticas obtenidas:', stats);
      return stats;

    } catch (error) {
      console.error('❌ Error obteniendo estadísticas:', error);
      throw error;
    }
  }
}

export default CompletePinControlService;
