const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/InformesService-D-fQ1856.js","assets/vendor-BqMjyOVw.js","assets/vendor-B4zyQOk2.css","assets/index-Bdl1jgS_.js","assets/index-Csy2uUlu.css","assets/ImprovedPinControlService-BUPGzexy.js","assets/PinLogger-C2v3yGM1.js","assets/NotificationService-DiDbKBbI.js","assets/PinValidationService-Ki4hIVgd.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,s=Object.defineProperties,r=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable,n=(s,r,i)=>r in s?e(s,r,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[r]=i,o=(e,s)=>{for(var r in s||(s={}))t.call(s,r)&&n(e,r,s[r]);if(i)for(var r of i(s))a.call(s,r)&&n(e,r,s[r]);return e},l=(e,i)=>s(e,r(i)),d=(e,s,r)=>new Promise((i,t)=>{var a=e=>{try{o(r.next(e))}catch(s){t(s)}},n=e=>{try{o(r.throw(e))}catch(s){t(s)}},o=e=>e.done?i(e.value):Promise.resolve(e.value).then(a,n);o((r=r.apply(e,s)).next())});import{_ as c,r as m,Q as u,j as g,G as h,R as p,J as x,a6 as f,O as b,s as y,q as v,a2 as j}from"./vendor-BqMjyOVw.js";import{PinValidationService as N}from"./PinValidationService-Ki4hIVgd.js";import{s as P,C as A,b as I,a as _,B as w}from"./index-Bdl1jgS_.js";import{P as E}from"./PinLogger-C2v3yGM1.js";class S{static isAdmin(e){return d(this,null,function*(){try{if(!e)return!1;const{data:s,error:r}=yield P.from("usuarios").select("rol, tipo_usuario").eq("id",e).single();if(r)return E.logError("Error checking admin status",r),!1;const i="admin"===(null==s?void 0:s.role)||!0===(null==s?void 0:s.is_admin)||"administrator"===(null==s?void 0:s.role);return E.logInfo("Admin status checked",{userId:e,isAdmin:i}),i}catch(s){return E.logError("Error in isAdmin check",s),!1}})}static validateAdminAccess(e,s=1){return d(this,null,function*(){try{return(yield this.isAdmin(e))?{isValid:!0,canProceed:!0,isAdmin:!0,reason:"ADMIN_ACCESS",userMessage:"Acceso de administrador - Sin restricciones",severity:"success",remainingPins:"unlimited",requiredPins:s,isUnlimited:!0,hasWarning:!1,adminPrivileges:!0}:{isValid:!1,canProceed:!1,isAdmin:!1,reason:"NOT_ADMIN",userMessage:"Usuario no es administrador - Requiere validación de pines",severity:"info",requiresNormalValidation:!0}}catch(r){return E.logError("Error in admin validation",r),{isValid:!1,canProceed:!1,isAdmin:!1,reason:"VALIDATION_ERROR",userMessage:"Error verificando permisos de administrador",severity:"error",error:r.message}}})}static generateReportAsAdmin(e,s){return d(this,arguments,function*(e,s,r={}){try{if(!(yield this.isAdmin(e)))throw new Error("Usuario no tiene permisos de administrador");E.logInfo("Admin generating report without restrictions",{adminId:e,patientId:s});const i=(yield c(()=>d(this,null,function*(){const{default:e}=yield import("./InformesService-D-fQ1856.js");return{default:e}}),__vite__mapDeps([0,1,2,3,4,5,6,7]))).default,t=yield i.generarInformeCompleto(s,r.title,r.description,!1!==r.incluirInterpretaciones,!0);return E.logSuccess("Admin report generated successfully",{adminId:e,patientId:s,reportId:t}),{success:!0,reportId:t,message:"Informe generado exitosamente (Acceso de administrador)",adminGenerated:!0}}catch(i){throw E.logError("Error in admin report generation",i),i}})}static generateBatchReportsAsAdmin(e,s){return d(this,arguments,function*(e,s,r={}){try{if(!(yield this.isAdmin(e)))throw new Error("Usuario no tiene permisos de administrador");E.logInfo("Admin generating batch reports without restrictions",{adminId:e,patientCount:s.length});const i=(yield c(()=>d(this,null,function*(){const{default:e}=yield import("./InformesService-D-fQ1856.js");return{default:e}}),__vite__mapDeps([0,1,2,3,4,5,6,7]))).default,t=yield i.generarInformesEnLote(s,e,l(o({},r),{skipValidation:!0}));return E.logSuccess("Admin batch reports generated successfully",{adminId:e,totalProcessed:t.totalProcessed,successful:t.totalSuccessful}),l(o({},t),{adminGenerated:!0,message:`${t.totalSuccessful} informes generados exitosamente (Acceso de administrador)`})}catch(i){throw E.logError("Error in admin batch report generation",i),i}})}static getSystemStatistics(e){return d(this,null,function*(){try{if(!(yield this.isAdmin(e)))throw new Error("Usuario no tiene permisos de administrador");const[s,r,i]=yield Promise.all([this._getUsageStatistics(),this._getRechargeStatistics(),this._getSessionStatistics()]);return{usage:s,recharges:r,sessions:i,generatedAt:(new Date).toISOString(),adminAccess:!0}}catch(s){throw E.logError("Error getting system statistics",s),s}})}static _getUsageStatistics(){return d(this,null,function*(){try{const{data:e,error:s}=yield P.from("psychologist_usage_control").select("total_uses, used_uses, is_unlimited, is_active");if(s)throw s;const r=e.reduce((e,s)=>(e.totalPsychologists++,e.totalPinsAssigned+=s.total_uses||0,e.totalPinsUsed+=s.used_uses||0,s.is_unlimited&&e.unlimitedPlans++,s.is_active&&e.activePlans++,e),{totalPsychologists:0,totalPinsAssigned:0,totalPinsUsed:0,unlimitedPlans:0,activePlans:0});return r.remainingPins=r.totalPinsAssigned-r.totalPinsUsed,r.usageRate=r.totalPinsAssigned>0?(r.totalPinsUsed/r.totalPinsAssigned*100).toFixed(1):0,r}catch(e){return E.logError("Error getting usage statistics",e),{}}})}static _getRechargeStatistics(){return d(this,null,function*(){try{return{totalRequests:0,pendingRequests:0,approvedRequests:0,rejectedRequests:0,totalPinsRequested:0,totalPinsApproved:0}}catch(e){return E.logError("Error getting recharge statistics",e),{}}})}static _getSessionStatistics(){return d(this,null,function*(){try{const{data:e,error:s}=yield P.from("test_sessions").select("estado, pin_consumed_at").eq("estado","finalizado");if(s)throw s;const r=e.reduce((e,s)=>(e.totalSessions++,s.pin_consumed_at?e.sessionsWithPinConsumed++:e.sessionsPendingConsumption++,e),{totalSessions:0,sessionsWithPinConsumed:0,sessionsPendingConsumption:0});return r.consumptionRate=r.totalSessions>0?(r.sessionsWithPinConsumed/r.totalSessions*100).toFixed(1):0,r}catch(e){return E.logError("Error getting session statistics",e),{}}})}static smartValidation(e,s=1){return d(this,null,function*(){try{const r=yield this.validateAdminAccess(e,s);if(r.isAdmin)return r;const i=(yield c(()=>d(this,null,function*(){const{default:e}=yield import("./PinValidationService-Ki4hIVgd.js");return{default:e}}),__vite__mapDeps([8,1,2,6,3,4]))).default;return yield i.validateReportGeneration(e,s)}catch(r){return E.logError("Error in smart validation",r),{isValid:!1,canProceed:!1,reason:"VALIDATION_ERROR",userMessage:"Error en la validación",severity:"error",error:r.message}}})}}const C=(e,s={})=>{const{showToastAlerts:r=!0,autoValidate:i=!1,onValidationChange:t=null}=s,[a,n]=m.useState(null),[o,l]=m.useState(!1),[c,g]=m.useState(null),h=m.useCallback((s=1)=>d(null,null,function*(){if(!e){const e={isValid:!1,canProceed:!1,reason:"NO_PSYCHOLOGIST_ID",userMessage:"ID de psicólogo no proporcionado",severity:"error"};return n(e),e}try{l(!0);const i=yield S.smartValidation(e,s);return n(i),g(new Date),r&&N.displayValidationAlerts(i),t&&t(i),i}catch(i){const e={isValid:!1,canProceed:!1,reason:"VALIDATION_ERROR",userMessage:"Error al validar permisos",severity:"error",error:i.message};return n(e),r&&u.error("Error al validar permisos de generación"),e}finally{l(!1)}}),[e,r,t]),p=m.useCallback(s=>d(null,null,function*(){if(!e){const e={isValid:!1,canProceed:!1,reason:"NO_PSYCHOLOGIST_ID",userMessage:"ID de psicólogo no proporcionado",severity:"error"};return n(e),e}if(!Array.isArray(s)||0===s.length){const e={isValid:!1,canProceed:!1,reason:"INVALID_PATIENT_LIST",userMessage:"Lista de pacientes inválida",severity:"error"};return n(e),e}try{l(!0);const i=yield N.validateBatchReportGeneration(e,s);return n(i),g(new Date),r&&N.displayValidationAlerts(i),t&&t(i),i}catch(i){const e={isValid:!1,canProceed:!1,reason:"BATCH_VALIDATION_ERROR",userMessage:"Error al validar permisos para generación en lote",severity:"error",error:i.message};return n(e),r&&u.error("Error al validar permisos para generación en lote"),e}finally{l(!1)}}),[e,r,t]),x=m.useCallback(()=>{n(null),g(null)},[]),f=m.useCallback((e=1)=>d(null,null,function*(){return yield h(e)}),[h]),b=m.useCallback(()=>a?N.createValidationSummary(a):null,[a]),y=(null==a?void 0:a.canProceed)||!1,v=(null==a?void 0:a.hasWarning)||!1,j=a&&!a.canProceed;return m.useEffect(()=>{i&&e&&!a&&!o&&h(1)},[i,e,a,o,h]),{validationResult:a,isValidating:o,lastValidation:c,canProceed:y,hasWarning:v,isBlocked:j,validateSingleReport:h,validateBatchReports:p,revalidate:f,clearValidation:x,getValidationSummary:b,isValid:(null==a?void 0:a.isValid)||!1,severity:(null==a?void 0:a.severity)||"info",message:(null==a?void 0:a.userMessage)||"",suggestions:(null==a?void 0:a.suggestions)||[]}},R=({psychologistId:e,currentPins:s=0,requiredPins:r=1,onClose:i=null,showCloseButton:t=!0,variant:a="modal"})=>{const[n,o]=m.useState("email"),[l,d]=m.useState(!1),c=e=>{switch(e){case"email":window.location.href="mailto:<EMAIL>?subject=Solicitud de Recarga de Pines&body=Hola, necesito recargar mis pines para continuar generando informes.";break;case"phone":u.info("Contacte al administrador al teléfono: +****************");break;case"form":d(!0);break;default:u.info("Por favor contacte al administrador del sistema")}},N=`${{modal:"max-w-2xl mx-auto",inline:"w-full",banner:"w-full border-l-4 border-red-500"}[a]} ${"banner"===a?"bg-red-50":""}`;return g.jsxs(A,{className:N,children:[g.jsx(I,{className:("banner"===a?"bg-red-100":"bg-red-50")+" border-b",children:g.jsxs("div",{className:"flex items-center justify-between",children:[g.jsxs("div",{className:"flex items-center",children:[g.jsx(h,{className:"text-red-600 mr-3 h-6 w-6"}),g.jsxs("div",{children:[g.jsx("h3",{className:"text-lg font-semibold text-red-900",children:"Pines Insuficientes"}),g.jsx("p",{className:"text-sm text-red-700",children:"No puede generar informes sin pines disponibles"})]})]}),t&&i&&g.jsx("button",{onClick:i,className:"text-red-400 hover:text-red-600 transition-colors",children:g.jsx(p,{className:"h-5 w-5"})})]})}),g.jsxs(_,{className:"space-y-6",children:[g.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[g.jsxs("div",{className:"flex items-center justify-between mb-2",children:[g.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Estado Actual:"}),g.jsxs("div",{className:"flex items-center text-red-600",children:[g.jsx(x,{className:"mr-1 h-4 w-4"}),g.jsxs("span",{className:"font-semibold",children:[s," pines disponibles"]})]})]}),g.jsxs("div",{className:"flex items-center justify-between",children:[g.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Requeridos:"}),g.jsxs("div",{className:"flex items-center text-blue-600",children:[g.jsx(x,{className:"mr-1 h-4 w-4"}),g.jsxs("span",{className:"font-semibold",children:[r," pines"]})]})]}),g.jsx("div",{className:"mt-3 pt-3 border-t border-gray-200",children:g.jsxs("div",{className:"flex items-center justify-between",children:[g.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Faltan:"}),g.jsxs("div",{className:"flex items-center text-orange-600",children:[g.jsx(x,{className:"mr-1 h-4 w-4"}),g.jsxs("span",{className:"font-semibold",children:[Math.max(0,r-s)," pines"]})]})]})})]}),g.jsxs("div",{children:[g.jsxs("h4",{className:"text-md font-semibold text-gray-900 mb-4 flex items-center",children:[g.jsx(f,{className:"mr-2 text-blue-600"}),"Planes de Recarga Disponibles"]}),g.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[{id:"basic",name:"Plan Básico",pins:50,price:"$29.99",description:"Ideal para uso ocasional",recommended:!1},{id:"professional",name:"Plan Profesional",pins:150,price:"$79.99",description:"Para uso regular",recommended:!0},{id:"premium",name:"Plan Premium",pins:300,price:"$149.99",description:"Para uso intensivo",recommended:!1},{id:"unlimited",name:"Plan Ilimitado",pins:"Ilimitado",price:"$299.99/mes",description:"Sin límites de uso",recommended:!1}].map(e=>g.jsxs("div",{className:"relative border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md "+(e.recommended?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"),onClick:()=>(e=>{u.info(`Plan ${e.name} seleccionado. Contacte al administrador para proceder con la compra.`),c("email")})(e),children:[e.recommended&&g.jsx("div",{className:"absolute -top-2 left-4 bg-blue-600 text-white text-xs px-2 py-1 rounded-full",children:"Recomendado"}),g.jsxs("div",{className:"text-center",children:[g.jsx("h5",{className:"font-semibold text-gray-900 mb-1",children:e.name}),g.jsx("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:"number"==typeof e.pins?`${e.pins} pines`:e.pins}),g.jsx("div",{className:"text-lg text-gray-600 mb-2",children:e.price}),g.jsx("p",{className:"text-sm text-gray-500",children:e.description})]})]},e.id))})]}),g.jsxs("div",{children:[g.jsxs("h4",{className:"text-md font-semibold text-gray-900 mb-4 flex items-center",children:[g.jsx(b,{className:"mr-2 text-green-600"}),"Contactar Administrador"]}),g.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[g.jsxs(w,{onClick:()=>c("email"),className:"bg-blue-600 hover:bg-blue-700 text-white flex items-center justify-center",children:[g.jsx(y,{className:"mr-2"}),"Enviar Email"]}),g.jsxs(w,{onClick:()=>c("phone"),className:"bg-green-600 hover:bg-green-700 text-white flex items-center justify-center",children:[g.jsx(v,{className:"mr-2"}),"Llamar"]}),g.jsxs(w,{onClick:()=>c("form"),className:"bg-purple-600 hover:bg-purple-700 text-white flex items-center justify-center",children:[g.jsx(j,{className:"mr-2"}),"Formulario"]})]})]}),g.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:g.jsxs("div",{className:"flex items-start",children:[g.jsx(j,{className:"text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),g.jsxs("div",{className:"text-sm text-blue-800",children:[g.jsx("h5",{className:"font-medium mb-2",children:"Información Importante:"}),g.jsxs("ul",{className:"space-y-1 list-disc list-inside",children:[g.jsx("li",{children:"Los pines se consumen automáticamente al generar informes"}),g.jsx("li",{children:"Cada informe requiere exactamente 1 pin"}),g.jsx("li",{children:"Los pines no utilizados no caducan"}),g.jsx("li",{children:"El administrador procesará su solicitud en 24-48 horas"})]})]})]})}),l&&g.jsx(V,{psychologistId:e,onClose:()=>d(!1),requiredPins:r})]})]})},V=({psychologistId:e,onClose:s,requiredPins:r})=>{const[i,t]=m.useState({subject:"Solicitud de Recarga de Pines",message:`Hola,\n\nNecesito recargar mis pines para continuar generando informes.\n\nPines requeridos: ${r}\nID de psicólogo: ${e}\n\nGracias.`,urgency:"normal"}),a=e=>{t(s=>l(o({},s),{[e.target.name]:e.target.value}))};return g.jsxs("div",{className:"border-t pt-6",children:[g.jsx("h5",{className:"font-medium text-gray-900 mb-4",children:"Formulario de Contacto"}),g.jsxs("form",{onSubmit:e=>{e.preventDefault(),u.success("Solicitud enviada exitosamente. El administrador se contactará pronto."),s()},className:"space-y-4",children:[g.jsxs("div",{children:[g.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Asunto"}),g.jsx("input",{type:"text",name:"subject",value:i.subject,onChange:a,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),g.jsxs("div",{children:[g.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Urgencia"}),g.jsxs("select",{name:"urgency",value:i.urgency,onChange:a,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[g.jsx("option",{value:"low",children:"Baja"}),g.jsx("option",{value:"normal",children:"Normal"}),g.jsx("option",{value:"high",children:"Alta"}),g.jsx("option",{value:"urgent",children:"Urgente"})]})]}),g.jsxs("div",{children:[g.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Mensaje"}),g.jsx("textarea",{name:"message",value:i.message,onChange:a,rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),g.jsxs("div",{className:"flex space-x-3",children:[g.jsx(w,{type:"submit",className:"bg-blue-600 hover:bg-blue-700 text-white",children:"Enviar Solicitud"}),g.jsx(w,{type:"button",onClick:s,variant:"outline",children:"Cancelar"})]})]})]})};export{S as A,R as P,C as u};
