import{j as e,O as i,R as a,C as t,P as s,r as n,S as r,h as l,T as o,U as u,V as m,W as d,X as c,F as b,Y as N,Z as p,_ as g}from"./vendor-CIyllXGj.js";import{s as x,C as f,b as v,a as h,e as j}from"./index-CrXvaDRr.js";const V=({patient:s})=>{var n,r,l;const o=s.edad||(e=>{if(!e)return null;const i=new Date,a=new Date(e);if(isNaN(a.getTime()))return null;let t=i.getFullYear()-a.getFullYear();const s=i.getMonth()-a.getMonth();return(s<0||0===s&&i.getDate()<a.getDate())&&t--,t>=0?t:null})(s.fecha_nacimiento);return e.jsxDEV("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden patient-card",children:e.jsxDEV("div",{className:"p-4",children:[e.jsxDEV("div",{className:"flex items-center mb-4",children:[e.jsxDEV("div",{className:"patient-avatar "+("masculino"===(null==(n=s.genero)?void 0:n.toLowerCase())?"patient-avatar-male":"femenino"===(null==(r=s.genero)?void 0:r.toLowerCase())?"patient-avatar-female":"patient-avatar-other"),children:((e,i)=>{if(!e)return"";const a=e.charAt(0).toUpperCase(),t=i?i.charAt(0).toUpperCase():"";return t?`${a}${t}`:a})(s.nombre,s.apellido||s.apellidos)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:95,columnNumber:11},void 0),e.jsxDEV("div",{className:"ml-3",children:[e.jsxDEV("h3",{className:"patient-name",children:`${s.nombre||""} ${s.apellido||s.apellidos||""}`.trim()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:103,columnNumber:13},void 0),e.jsxDEV("p",{className:"patient-education",children:s.nivel_educativo?s.nivel_educativo:s.grado||"4° Medio"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:104,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:102,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:94,columnNumber:9},void 0),e.jsxDEV("div",{className:"patient-info-grid",children:[e.jsxDEV("div",{children:[e.jsxDEV("span",{className:"patient-info-label",children:"Edad:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:111,columnNumber:13},void 0)," ",o?`${o} años`:"No disponible"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:110,columnNumber:11},void 0),e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("span",{className:"patient-info-label",children:"Sexo:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:114,columnNumber:13},void 0),e.jsxDEV("span",{className:"flex items-center",children:[(()=>{const n=s.genero?s.genero.toLowerCase():"";return"masculino"===n?e.jsxDEV(i,{className:"text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:48,columnNumber:14},void 0):"femenino"===n?e.jsxDEV(a,{className:"text-pink-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:50,columnNumber:14},void 0):e.jsxDEV(t,{className:"text-gray-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:52,columnNumber:14},void 0)})(),e.jsxDEV("span",{className:"ml-1",children:s.genero||"No especificado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:117,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:115,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:113,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:109,columnNumber:9},void 0),e.jsxDEV("div",{className:"patient-psychologist",children:s.psicologo_id?e.jsxDEV("span",{children:["Psicólogo: ",(null==(l=s.psicologo)?void 0:l.nombre)||"Asignado"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:125,columnNumber:13},void 0):e.jsxDEV("span",{children:"Sin psicólogo asignado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:127,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:123,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:92,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/patient/PatientCard.jsx",lineNumber:91,columnNumber:5},void 0)};V.propTypes={patient:s.shape({id:s.string,nombre:s.string.isRequired,apellido:s.string,apellidos:s.string,fecha_nacimiento:s.string,genero:s.string,edad:s.number,nivel_educativo:s.string,grado:s.string,psicologo_id:s.string,psicologo:s.object}).isRequired};const C=({patients:i,loading:a,onSelectPatient:t})=>a?null:e.jsxDEV("div",{className:"overflow-x-auto",children:e.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxDEV("thead",{className:"bg-gray-50",children:e.jsxDEV("tr",{children:[e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Nombre"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:20,columnNumber:13},void 0),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:21,columnNumber:13},void 0),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Género"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:22,columnNumber:13},void 0),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fecha Nacimiento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:23,columnNumber:13},void 0),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:24,columnNumber:13},void 0),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:25,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:19,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:18,columnNumber:9},void 0),e.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:i.map(i=>e.jsxDEV("tr",{className:"hover:bg-gray-50",children:[e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxDEV("div",{className:"flex items-center",children:e.jsxDEV("div",{className:"ml-4",children:e.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:[i.nombre," ",i.apellidos]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:34,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:33,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:32,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:31,columnNumber:15},void 0),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxDEV("div",{className:"text-sm text-gray-900",children:i.documento_identidad},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:39,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:38,columnNumber:15},void 0),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxDEV("div",{className:"text-sm text-gray-900",children:i.genero},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:42,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:41,columnNumber:15},void 0),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxDEV("div",{className:"text-sm text-gray-900",children:new Date(i.fecha_nacimiento).toLocaleDateString()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:45,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:44,columnNumber:15},void 0),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxDEV("div",{className:"text-sm text-gray-900",children:i.psicologo?`${i.psicologo.nombre} ${i.psicologo.apellido}`:"No asignado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:48,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:47,columnNumber:15},void 0),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxDEV("button",{onClick:()=>t(i),className:"text-blue-600 hover:text-blue-900",children:"Ver detalles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:53,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:52,columnNumber:15},void 0)]},i.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:30,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:28,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:17,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:16,columnNumber:5},void 0),P=()=>{const[i,a]=n.useState([]),[s,P]=n.useState([]),[D,E]=n.useState(!0),[B,y]=n.useState(0),[w,k]=n.useState(1),[S,_]=n.useState(12),[$,F]=n.useState(""),[L,A]=n.useState("asc"),[M,q]=n.useState("nombre"),[T,O]=n.useState(""),[R,U]=n.useState("grid"),Y=n.useCallback(()=>{return e=null,i=null,t=function*(){try{E(!0);let e=x.from("pacientes").select("\n          *,\n          psicologo:psicologo_id (\n            id, nombre, apellido\n          )\n        ",{count:"exact"});$&&(e=e.or(`nombre.ilike.%${$}%,apellidos.ilike.%${$}%,documento_identidad.ilike.%${$}%`)),T&&(e=e.eq("genero",T)),e=e.order(M,{ascending:"asc"===L});const i=(w-1)*S,t=i+S-1,{data:s,error:n,count:r}=yield e.range(i,t);if(n)throw n;a(s||[]),P(s||[]),y(r||0)}catch(e){}finally{E(!1)}},new Promise((a,s)=>{var n=e=>{try{l(t.next(e))}catch(i){s(i)}},r=e=>{try{l(t.throw(e))}catch(i){s(i)}},l=e=>e.done?a(e.value):Promise.resolve(e.value).then(n,r);l((t=t.apply(e,i)).next())});var e,i,t},[$,T,L,M,w,S]);n.useEffect(()=>{Y()},[Y]);const z=n.useCallback(r.debounce(e=>{F(e),k(1)},500),[]),G=()=>{F(""),O(""),q("nombre"),A("asc"),k(1);const e=document.querySelector('input[type="text"]');e&&(e.value="")},W=()=>{window.scrollTo({top:0,behavior:"smooth"})},X=e=>{k(e),W()},Z=Math.ceil(B/S),H=e=>{};return e.jsxDEV("div",{className:"container mx-auto py-6",children:[e.jsxDEV("div",{className:"mb-8",children:e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"inline-flex items-center justify-center mb-3",children:[e.jsxDEV("div",{className:"bg-yellow-400 p-3 rounded-full mr-3 shadow-md",children:e.jsxDEV(t,{className:"text-white text-2xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:210,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:209,columnNumber:13},void 0),e.jsxDEV("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-800 to-indigo-950 bg-clip-text text-transparent",children:"Pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:212,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:208,columnNumber:11},void 0),e.jsxDEV("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Lista de pacientes registrados en el sistema para evaluaciones psicométricas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:214,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:207,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:206,columnNumber:7},void 0),e.jsxDEV("div",{className:"mb-8 bg-white p-4 rounded-lg shadow-sm",children:e.jsxDEV("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsxDEV("div",{className:"relative flex-grow",children:[e.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsxDEV(l,{className:"text-blue-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:224,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:223,columnNumber:13},void 0),e.jsxDEV("input",{type:"text",className:"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 shadow-sm",placeholder:"Buscar paciente por nombre, apellido o documento...",onChange:e=>{const i=e.target.value;e.target.value=i,z(i)}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:226,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:222,columnNumber:11},void 0),e.jsxDEV("div",{className:"flex flex-wrap gap-2",children:[e.jsxDEV("div",{className:"relative inline-block",children:[e.jsxDEV("select",{className:"appearance-none pl-8 pr-4 py-2 border border-gray-200 rounded-lg bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 shadow-sm",value:T,onChange:e=>{O(e.target.value),k(1)},children:[e.jsxDEV("option",{value:"",children:"Todos los géneros"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:246,columnNumber:17},void 0),e.jsxDEV("option",{value:"Masculino",children:"Masculino"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:247,columnNumber:17},void 0),e.jsxDEV("option",{value:"Femenino",children:"Femenino"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:248,columnNumber:17},void 0),e.jsxDEV("option",{value:"Otro",children:"Otro"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:249,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:238,columnNumber:15},void 0),e.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsxDEV(o,{className:"text-blue-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:252,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:251,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:237,columnNumber:13},void 0),e.jsxDEV("div",{className:"relative inline-block",children:[e.jsxDEV("select",{className:"appearance-none pl-8 pr-4 py-2 border border-gray-200 rounded-lg bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 shadow-sm",value:M,onChange:e=>{q(e.target.value),k(1)},children:[e.jsxDEV("option",{value:"nombre",children:"Nombre"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:263,columnNumber:17},void 0),e.jsxDEV("option",{value:"apellidos",children:"Apellidos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:264,columnNumber:17},void 0),e.jsxDEV("option",{value:"fecha_nacimiento",children:"Fecha de nacimiento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:265,columnNumber:17},void 0),e.jsxDEV("option",{value:"documento_identidad",children:"Documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:266,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:258,columnNumber:15},void 0),e.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsxDEV(o,{className:"text-blue-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:269,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:268,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:257,columnNumber:13},void 0),e.jsxDEV("button",{className:"flex items-center px-4 py-2 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm",onClick:()=>{A(e=>"asc"===e?"desc":"asc"),k(1)},children:["asc"===L?e.jsxDEV(u,{className:"mr-2 text-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:279,columnNumber:17},void 0):e.jsxDEV(m,{className:"mr-2 text-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:281,columnNumber:17},void 0),e.jsxDEV("span",{children:"Ordenar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:283,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:274,columnNumber:13},void 0),e.jsxDEV("button",{className:"flex items-center px-4 py-2 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm",onClick:()=>U("grid"===R?"table":"grid"),children:"grid"===R?e.jsxDEV(e.Fragment,{children:[e.jsxDEV(d,{className:"mr-2 text-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:293,columnNumber:19},void 0),e.jsxDEV("span",{children:"Vista tabla"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:294,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:292,columnNumber:17},void 0):e.jsxDEV(e.Fragment,{children:[e.jsxDEV(c,{className:"mr-2 text-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:298,columnNumber:19},void 0),e.jsxDEV("span",{children:"Vista tarjetas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:299,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:297,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:287,columnNumber:13},void 0),($||T||"nombre"!==M||"asc"!==L)&&e.jsxDEV("button",{className:"px-4 py-2 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm",onClick:G,children:"Limpiar filtros"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:306,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:235,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:220,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:219,columnNumber:7},void 0),e.jsxDEV(f,{className:"shadow-md border-0 rounded-xl overflow-hidden",children:[e.jsxDEV(v,{className:"bg-gradient-to-r from-blue-900 to-indigo-950 text-white border-0",children:e.jsxDEV("div",{className:"flex items-center justify-center",children:[e.jsxDEV("h2",{className:"text-xl font-semibold",children:"Lista de Pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:321,columnNumber:13},void 0),e.jsxDEV("span",{className:"ml-3 bg-white text-blue-600 rounded-full px-3 py-1 text-sm font-medium",children:B},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:322,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:320,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:319,columnNumber:9},void 0),e.jsxDEV(h,{className:"p-6",children:[D?e.jsxDEV("div",{className:"text-center py-12",children:[e.jsxDEV(b,{className:"animate-spin text-blue-500 text-4xl mx-auto mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:330,columnNumber:15},void 0),e.jsxDEV("p",{className:"text-gray-600",children:"Cargando pacientes..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:331,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:329,columnNumber:13},void 0):0===s.length?e.jsxDEV("div",{className:"text-center py-12",children:$||T?e.jsxDEV("div",{children:[e.jsxDEV("p",{className:"text-gray-500 mb-2",children:"No se encontraron pacientes que coincidan con los filtros aplicados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:337,columnNumber:19},void 0),e.jsxDEV("button",{className:"text-blue-500 hover:text-blue-700 font-medium",onClick:G,children:"Limpiar filtros"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:338,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:336,columnNumber:17},void 0):e.jsxDEV("p",{className:"text-gray-500",children:"No hay pacientes registrados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:346,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:334,columnNumber:13},void 0):"grid"===R?e.jsxDEV("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-2",children:s.map(i=>e.jsxDEV(V,{patient:i,onClick:()=>{}},i.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:352,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:350,columnNumber:13},void 0):e.jsxDEV(C,{patients:s,loading:D,onSelectPatient:H},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:360,columnNumber:13},void 0),Z>1&&e.jsxDEV("div",{className:"flex items-center justify-center mt-6",children:e.jsxDEV("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[e.jsxDEV("button",{onClick:()=>X(w-1),disabled:1===w,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium "+(1===w?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"),children:[e.jsxDEV("span",{className:"sr-only",children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:380,columnNumber:19},void 0),e.jsxDEV(N,{className:"h-5 w-5","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:381,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:371,columnNumber:17},void 0),[...Array(Z)].map((i,a)=>{const t=a+1;return 1===t||t===Z||t>=w-1&&t<=w+1?e.jsxDEV("button",{onClick:()=>X(t),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium "+(w===t?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"),children:t},t,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:394,columnNumber:23},void 0):2===t&&w>3||t===Z-1&&w<Z-2?e.jsxDEV("span",{className:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700",children:"..."},t,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:412,columnNumber:23},void 0):null}),e.jsxDEV("button",{onClick:()=>X(w+1),disabled:w===Z,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium "+(w===Z?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"),children:[e.jsxDEV("span",{className:"sr-only",children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:432,columnNumber:19},void 0),e.jsxDEV(p,{className:"h-5 w-5","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:433,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:423,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:370,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:369,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:327,columnNumber:9},void 0),e.jsxDEV(j,{className:"bg-gray-50 border-t border-gray-100 p-4 flex justify-between items-center",children:[e.jsxDEV("p",{className:"text-sm text-gray-600",children:["Mostrando ",s.length," de ",i.length," pacientes"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:440,columnNumber:11},void 0),e.jsxDEV("button",{onClick:W,className:"flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:[e.jsxDEV(g,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:447,columnNumber:13},void 0)," Volver arriba"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:443,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:439,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:318,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Patients.jsx",lineNumber:205,columnNumber:5},void 0)};export{P as default};
