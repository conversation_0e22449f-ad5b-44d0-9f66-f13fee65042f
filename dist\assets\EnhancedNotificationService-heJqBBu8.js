var t=Object.defineProperty,i=Object.defineProperties,e=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,r=(i,e,o)=>e in i?t(i,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):i[e]=o,a=(t,i)=>{for(var e in i||(i={}))n.call(i,e)&&r(t,e,i[e]);if(o)for(var e of o(i))s.call(i,e)&&r(t,e,i[e]);return t},c=(t,o)=>i(t,e(o)),l=(t,i,e)=>r(t,"symbol"!=typeof i?i+"":i,e),d=(t,i,e)=>new Promise((o,n)=>{var s=t=>{try{a(e.next(t))}catch(i){n(i)}},r=t=>{try{a(e.throw(t))}catch(i){n(i)}},a=t=>t.done?o(t.value):Promise.resolve(t.value).then(s,r);a((e=e.apply(t,i)).next())});import{s as I}from"./index-Bdl1jgS_.js";import{P as u}from"./PinLogger-C2v3yGM1.js";import{Q as p}from"./vendor-BqMjyOVw.js";import{P as g}from"./pinNotifications-BMRChPcj.js";class N{static createLowPinNotification(t,i){return d(this,arguments,function*(t,i,e={}){const{channels:o=[this.NOTIFICATION_CHANNELS.IN_APP,this.NOTIFICATION_CHANNELS.TOAST],priority:n="normal",includeUsageStats:s=!0}=e;try{u.logInfo("Creating low pin notification",{psychologistId:t,remainingPins:i,channels:o});const e=this._determineNotificationType(i),n=yield this._getPsychologistInfo(t),r=yield this._createDatabaseNotification(t,e,n,s),a=yield Promise.allSettled([o.includes(this.NOTIFICATION_CHANNELS.IN_APP)?this._sendInAppNotification(t,e,n):Promise.resolve(),o.includes(this.NOTIFICATION_CHANNELS.TOAST)?this._sendToastNotification(e,n):Promise.resolve(),o.includes(this.NOTIFICATION_CHANNELS.EMAIL)?this._sendEmailNotification(t,e,n):Promise.resolve()]),c=a.filter(t=>"fulfilled"===t.status).length;return u.logInfo(`Notification sent through ${c}/${o.length} channels`),{notificationId:r,channelsUsed:o,successCount:c,results:a}}catch(r){throw u.logError("Error creating low pin notification",r),r}})}static createPinConsumptionNotification(t,i){return d(this,arguments,function*(t,i,e={}){try{const{patientId:t,reportId:o,testSessionId:n}=e;if(i>this.NOTIFICATION_THRESHOLDS.LOW)return null;const s={type:this.NOTIFICATION_TYPES.PIN_CONSUMED,title:"Pin Consumido",message:`Se ha consumido 1 pin. Quedan ${i} pines disponibles.`,severity:i<=this.NOTIFICATION_THRESHOLDS.CRITICAL?"critical":"warning",metadata:{remainingPins:i,patientId:t,reportId:o,testSessionId:n,consumedAt:(new Date).toISOString()}};return yield this._sendToastNotification(s),s}catch(o){throw u.logError("Error creating pin consumption notification",o),o}})}static createPinAssignmentNotification(t,i,e){return d(this,null,function*(){try{const o={type:this.NOTIFICATION_TYPES.PINS_ASSIGNED,title:"Pines Asignados",message:`Se han asignado ${i} pines. Total disponible: ${e}`,severity:"success",metadata:{assignedPins:i,totalPins:e,assignedAt:(new Date).toISOString()}};return yield Promise.all([this._sendToastNotification(o),g.createPinAssignmentNotification(t,i,e>0)]),o}catch(o){throw u.logError("Error creating pin assignment notification",o),o}})}static getUnreadNotifications(t){return d(this,null,function*(){try{const{data:i,error:e}=yield I.from("pin_notifications").select("*").eq("psychologist_id",t).eq("read",!1).order("created_at",{ascending:!1});if(e)throw e;return i||[]}catch(i){throw u.logError("Error getting unread notifications",i),i}})}static markAsRead(t){return d(this,null,function*(){try{const{error:i}=yield I.from("pin_notifications").update({read:!0,read_at:(new Date).toISOString()}).eq("id",t);if(i)throw i}catch(i){throw u.logError("Error marking notification as read",i),i}})}static _determineNotificationType(t){return 0===t?{type:this.NOTIFICATION_TYPES.EXHAUSTED_PINS,title:"Pines Agotados",message:"No tienes pines disponibles. Contacta al administrador para obtener más.",severity:"error"}:t<=this.NOTIFICATION_THRESHOLDS.CRITICAL?{type:this.NOTIFICATION_TYPES.CRITICAL_PINS,title:"Pines Críticos",message:`Solo quedan ${t} pines disponibles. Es urgente recargar.`,severity:"critical"}:t<=this.NOTIFICATION_THRESHOLDS.LOW?{type:this.NOTIFICATION_TYPES.LOW_PINS,title:"Pines Bajos",message:`Quedan ${t} pines disponibles. Considera recargar pronto.`,severity:"warning"}:{type:this.NOTIFICATION_TYPES.LOW_PINS,title:"Estado de Pines",message:`Tienes ${t} pines disponibles.`,severity:"info"}}static _getPsychologistInfo(t){return d(this,null,function*(){try{const{data:i,error:e}=yield I.from("psicologos").select("id, email, nombre, apellido").eq("id",t).single();if(e)throw e;return i}catch(i){return u.logError("Error getting psychologist info",i),{id:t,email:null,nombre:"Usuario",apellido:""}}})}static _createDatabaseNotification(t,i,e=null,o=!1){return d(this,null,function*(){try{let n=null;if(o){const{data:i}=yield I.from("psychologist_usage_control").select("total_uses, used_uses").eq("psychologist_id",t).eq("is_active",!0).single();n=i}const{data:s,error:r}=yield I.from("pin_notifications").insert({psychologist_id:t,type:i.type,title:i.title,message:i.message,severity:i.severity,metadata:c(a({},i.metadata),{usageStats:n,psychologistInfo:e}),read:!1}).select().single();if(r)throw r;return s.id}catch(n){throw u.logError("Error creating database notification",n),n}})}static _sendInAppNotification(t,i,e){return d(this,null,function*(){return u.logInfo("In-app notification sent",{psychologistId:t,type:i.type}),!0})}static _sendToastNotification(t,i=null){return d(this,null,function*(){const{severity:e,title:o,message:n,type:s}=t,r=`${s}-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,l={toastId:r,position:"top-right",hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0};switch(e){case"error":case"critical":p.error(`${o}: ${n}`,c(a({},l),{autoClose:8e3}));break;case"warning":p.warning(`${o}: ${n}`,c(a({},l),{autoClose:6e3}));break;case"success":p.success(`${o}: ${n}`,c(a({},l),{autoClose:4e3}));break;default:p.info(`${o}: ${n}`,c(a({},l),{autoClose:5e3}))}return u.logInfo("Toast notification sent",{toastId:r,severity:e,title:o,psychologist:(null==i?void 0:i.name)||"Unknown"}),!0})}static _sendEmailNotification(t,i,e){return d(this,null,function*(){try{return u.logInfo("Email notification would be sent",{psychologistId:t,email:null==e?void 0:e.email,type:i.type}),!0}catch(o){return u.logError("Error sending email notification",o),!1}})}}l(N,"NOTIFICATION_THRESHOLDS",{CRITICAL:2,LOW:5,MODERATE:10,EXHAUSTED:0}),l(N,"NOTIFICATION_TYPES",{LOW_PINS:"low_pins",CRITICAL_PINS:"critical_pins",EXHAUSTED_PINS:"exhausted_pins",PIN_CONSUMED:"pin_consumed",PINS_ASSIGNED:"pins_assigned",USAGE_REPORT:"usage_report"}),l(N,"NOTIFICATION_CHANNELS",{IN_APP:"in_app",EMAIL:"email",TOAST:"toast",SYSTEM:"system"});export{N as EnhancedNotificationService,N as default};
