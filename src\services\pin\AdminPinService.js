import { supabase } from '../../api/supabaseClient.js';
import { PinLogger } from './PinLogger.js';

/**
 * Servicio especial para administradores - Sin restricciones de pines
 * Los administradores tienen acceso libre a todas las funcionalidades
 */
export class AdminPinService {
  
  /**
   * Verificar si un usuario es administrador
   * @param {string} userId - ID del usuario
   * @returns {Promise<boolean>} True si es administrador
   */
  static async isAdmin(userId) {
    try {
      if (!userId) return false;

      // Verificar en la tabla de usuarios si tiene rol de admin
      const { data: user, error } = await supabase
        .from('usuarios')
        .select('rol, tipo_usuario')
        .eq('id', userId)
        .single();

      if (error) {
        PinLogger.logError('Error checking admin status', error);
        return false;
      }

      // Verificar múltiples condiciones para determinar si es admin
      const isAdmin = user?.role === 'admin' || 
                     user?.is_admin === true || 
                     user?.role === 'administrator';

      PinLogger.logInfo('Admin status checked', { userId, isAdmin });
      return isAdmin;

    } catch (error) {
      PinLogger.logError('Error in isAdmin check', error);
      return false;
    }
  }

  /**
   * Validación especial para administradores - Siempre permite
   * @param {string} userId - ID del usuario
   * @param {number} requiredPins - Pines requeridos (ignorado para admins)
   * @returns {Promise<Object>} Resultado de validación
   */
  static async validateAdminAccess(userId, requiredPins = 1) {
    try {
      const isAdminUser = await this.isAdmin(userId);

      if (isAdminUser) {
        return {
          isValid: true,
          canProceed: true,
          isAdmin: true,
          reason: 'ADMIN_ACCESS',
          userMessage: 'Acceso de administrador - Sin restricciones',
          severity: 'success',
          remainingPins: 'unlimited',
          requiredPins,
          isUnlimited: true,
          hasWarning: false,
          adminPrivileges: true
        };
      }

      // Si no es admin, retornar que necesita validación normal
      return {
        isValid: false,
        canProceed: false,
        isAdmin: false,
        reason: 'NOT_ADMIN',
        userMessage: 'Usuario no es administrador - Requiere validación de pines',
        severity: 'info',
        requiresNormalValidation: true
      };

    } catch (error) {
      PinLogger.logError('Error in admin validation', error);
      return {
        isValid: false,
        canProceed: false,
        isAdmin: false,
        reason: 'VALIDATION_ERROR',
        userMessage: 'Error verificando permisos de administrador',
        severity: 'error',
        error: error.message
      };
    }
  }

  /**
   * Generar informe sin restricciones para administradores
   * @param {string} adminId - ID del administrador
   * @param {string} patientId - ID del paciente
   * @param {Object} reportOptions - Opciones del informe
   * @returns {Promise<Object>} Resultado de la generación
   */
  static async generateReportAsAdmin(adminId, patientId, reportOptions = {}) {
    try {
      const isAdminUser = await this.isAdmin(adminId);

      if (!isAdminUser) {
        throw new Error('Usuario no tiene permisos de administrador');
      }

      PinLogger.logInfo('Admin generating report without restrictions', { 
        adminId, 
        patientId 
      });

      // Importar el servicio de informes dinámicamente para evitar dependencias circulares
      const InformesService = (await import('../InformesService.js')).default;

      // Generar informe saltando todas las validaciones de pines
      const reportId = await InformesService.generarInformeCompleto(
        patientId,
        reportOptions.title,
        reportOptions.description,
        reportOptions.incluirInterpretaciones !== false, // Por defecto true
        true // skipValidation = true para admins
      );

      PinLogger.logSuccess('Admin report generated successfully', { 
        adminId, 
        patientId, 
        reportId 
      });

      return {
        success: true,
        reportId,
        message: 'Informe generado exitosamente (Acceso de administrador)',
        adminGenerated: true
      };

    } catch (error) {
      PinLogger.logError('Error in admin report generation', error);
      throw error;
    }
  }

  /**
   * Generar múltiples informes sin restricciones para administradores
   * @param {string} adminId - ID del administrador
   * @param {Array<string>} patientIds - IDs de pacientes
   * @param {Object} reportOptions - Opciones de los informes
   * @returns {Promise<Object>} Resultado de la generación en lote
   */
  static async generateBatchReportsAsAdmin(adminId, patientIds, reportOptions = {}) {
    try {
      const isAdminUser = await this.isAdmin(adminId);

      if (!isAdminUser) {
        throw new Error('Usuario no tiene permisos de administrador');
      }

      PinLogger.logInfo('Admin generating batch reports without restrictions', { 
        adminId, 
        patientCount: patientIds.length 
      });

      // Importar el servicio de informes dinámicamente
      const InformesService = (await import('../InformesService.js')).default;

      // Generar informes en lote saltando todas las validaciones
      const result = await InformesService.generarInformesEnLote(
        patientIds,
        adminId,
        {
          ...reportOptions,
          skipValidation: true // Saltar validación para admins
        }
      );

      PinLogger.logSuccess('Admin batch reports generated successfully', { 
        adminId, 
        totalProcessed: result.totalProcessed,
        successful: result.totalSuccessful
      });

      return {
        ...result,
        adminGenerated: true,
        message: `${result.totalSuccessful} informes generados exitosamente (Acceso de administrador)`
      };

    } catch (error) {
      PinLogger.logError('Error in admin batch report generation', error);
      throw error;
    }
  }

  /**
   * Obtener estadísticas completas del sistema para administradores
   * @param {string} adminId - ID del administrador
   * @returns {Promise<Object>} Estadísticas del sistema
   */
  static async getSystemStatistics(adminId) {
    try {
      const isAdminUser = await this.isAdmin(adminId);

      if (!isAdminUser) {
        throw new Error('Usuario no tiene permisos de administrador');
      }

      // Obtener estadísticas completas del sistema
      const [usageStats, rechargeStats, sessionStats] = await Promise.all([
        this._getUsageStatistics(),
        this._getRechargeStatistics(),
        this._getSessionStatistics()
      ]);

      return {
        usage: usageStats,
        recharges: rechargeStats,
        sessions: sessionStats,
        generatedAt: new Date().toISOString(),
        adminAccess: true
      };

    } catch (error) {
      PinLogger.logError('Error getting system statistics', error);
      throw error;
    }
  }

  /**
   * Obtener estadísticas de uso
   * @private
   */
  static async _getUsageStatistics() {
    try {
      const { data, error } = await supabase
        .from('psychologist_usage_control')
        .select('total_uses, used_uses, is_unlimited, is_active');

      if (error) throw error;

      const stats = data.reduce((acc, item) => {
        acc.totalPsychologists++;
        acc.totalPinsAssigned += item.total_uses || 0;
        acc.totalPinsUsed += item.used_uses || 0;
        if (item.is_unlimited) acc.unlimitedPlans++;
        if (item.is_active) acc.activePlans++;
        return acc;
      }, {
        totalPsychologists: 0,
        totalPinsAssigned: 0,
        totalPinsUsed: 0,
        unlimitedPlans: 0,
        activePlans: 0
      });

      stats.remainingPins = stats.totalPinsAssigned - stats.totalPinsUsed;
      stats.usageRate = stats.totalPinsAssigned > 0 
        ? (stats.totalPinsUsed / stats.totalPinsAssigned * 100).toFixed(1)
        : 0;

      return stats;

    } catch (error) {
      PinLogger.logError('Error getting usage statistics', error);
      return {};
    }
  }

  /**
   * Obtener estadísticas de recargas
   * @private
   */
  static async _getRechargeStatistics() {
    try {
      // Simular estadísticas de recargas (implementar cuando esté la tabla)
      return {
        totalRequests: 0,
        pendingRequests: 0,
        approvedRequests: 0,
        rejectedRequests: 0,
        totalPinsRequested: 0,
        totalPinsApproved: 0
      };

    } catch (error) {
      PinLogger.logError('Error getting recharge statistics', error);
      return {};
    }
  }

  /**
   * Obtener estadísticas de sesiones
   * @private
   */
  static async _getSessionStatistics() {
    try {
      const { data, error } = await supabase
        .from('test_sessions')
        .select('estado, pin_consumed_at')
        .eq('estado', 'finalizado');

      if (error) throw error;

      const stats = data.reduce((acc, session) => {
        acc.totalSessions++;
        if (session.pin_consumed_at) {
          acc.sessionsWithPinConsumed++;
        } else {
          acc.sessionsPendingConsumption++;
        }
        return acc;
      }, {
        totalSessions: 0,
        sessionsWithPinConsumed: 0,
        sessionsPendingConsumption: 0
      });

      stats.consumptionRate = stats.totalSessions > 0
        ? (stats.sessionsWithPinConsumed / stats.totalSessions * 100).toFixed(1)
        : 0;

      return stats;

    } catch (error) {
      PinLogger.logError('Error getting session statistics', error);
      return {};
    }
  }

  /**
   * Crear un wrapper para validación que detecta automáticamente si es admin
   * @param {string} userId - ID del usuario
   * @param {number} requiredPins - Pines requeridos
   * @returns {Promise<Object>} Resultado de validación
   */
  static async smartValidation(userId, requiredPins = 1) {
    try {
      // Primero verificar si es admin
      const adminValidation = await this.validateAdminAccess(userId, requiredPins);
      
      if (adminValidation.isAdmin) {
        return adminValidation;
      }

      // Si no es admin, usar validación normal
      const PinValidationService = (await import('./PinValidationService.js')).default;
      return await PinValidationService.validateReportGeneration(userId, requiredPins);

    } catch (error) {
      PinLogger.logError('Error in smart validation', error);
      return {
        isValid: false,
        canProceed: false,
        reason: 'VALIDATION_ERROR',
        userMessage: 'Error en la validación',
        severity: 'error',
        error: error.message
      };
    }
  }
}

export default AdminPinService;
