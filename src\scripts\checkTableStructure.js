/**
 * Script para verificar la estructura real de la tabla usuarios
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function main() {
  console.log('🔍 VERIFICANDO ESTRUCTURA DE LA TABLA USUARIOS');
  console.log('==============================================\n');
  
  try {
    // Obtener todos los usuarios para ver qué columnas existen
    const { data: users, error } = await supabase
      .from('usuarios')
      .select('*')
      .limit(1);

    if (error) {
      console.error('❌ Error obteniendo usuarios:', error);
      return;
    }

    if (users.length > 0) {
      console.log('📋 COLUMNAS ENCONTRADAS EN LA TABLA USUARIOS:');
      const columns = Object.keys(users[0]);
      columns.forEach((col, index) => {
        console.log(`   ${index + 1}. ${col}: ${typeof users[0][col]} = ${users[0][col]}`);
      });
      
      console.log('\n📋 USUARIO DE EJEMPLO:');
      console.log(JSON.stringify(users[0], null, 2));
    } else {
      console.log('⚠️  No hay usuarios en la tabla');
    }

    // Buscar específicamente el administrador
    console.log('\n🔍 BUSCANDO USUARIO ADMINISTRADOR:');
    const { data: admin, error: adminError } = await supabase
      .from('usuarios')
      .select('*')
      .eq('documento', '13716261')
      .single();

    if (adminError) {
      console.error('❌ Error buscando administrador:', adminError);
    } else {
      console.log('✅ Administrador encontrado:');
      console.log(JSON.stringify(admin, null, 2));
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

main();
