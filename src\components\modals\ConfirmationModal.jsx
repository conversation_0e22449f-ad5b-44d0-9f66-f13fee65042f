import React from 'react';
import { FaExclamationTriangle, FaTimes, FaTrash, FaEyeSlash, FaEye } from 'react-icons/fa';

/**
 * Modal de confirmación reutilizable para acciones críticas
 */
const ConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  subtitle,
  confirmText = 'Confirmar',
  cancelText = 'Cancelar',
  type = 'warning', // 'warning', 'danger', 'info'
  loading = false,
  userName = ''
}) => {
  if (!isOpen) return null;

  // Configuración de iconos y colores según el tipo
  const getTypeConfig = () => {
    switch (type) {
      case 'danger':
        return {
          icon: FaTrash,
          iconColor: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          buttonColor: 'bg-red-600 hover:bg-red-700',
          buttonTextColor: 'text-white'
        };
      case 'warning':
        return {
          icon: FaExclamationTriangle,
          iconColor: 'text-yellow-500',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          buttonColor: 'bg-yellow-600 hover:bg-yellow-700',
          buttonTextColor: 'text-white'
        };
      case 'deactivate':
        return {
          icon: FaEyeSlash,
          iconColor: 'text-orange-500',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          buttonColor: 'bg-orange-600 hover:bg-orange-700',
          buttonTextColor: 'text-white'
        };
      case 'activate':
        return {
          icon: FaEye,
          iconColor: 'text-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          buttonColor: 'bg-green-600 hover:bg-green-700',
          buttonTextColor: 'text-white'
        };
      default:
        return {
          icon: FaExclamationTriangle,
          iconColor: 'text-blue-500',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          buttonColor: 'bg-blue-600 hover:bg-blue-700',
          buttonTextColor: 'text-white'
        };
    }
  };

  const config = getTypeConfig();
  const IconComponent = config.icon;

  const handleConfirm = () => {
    onConfirm();
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div className="mt-3">
          {/* Header */}
          <div className="flex justify-between items-start mb-4">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-full ${config.bgColor} ${config.borderColor} border`}>
                <IconComponent className={`w-6 h-6 ${config.iconColor}`} />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  {title}
                </h3>
                {userName && (
                  <p className="text-sm text-gray-600 mt-1">
                    Usuario: <span className="font-medium">{userName}</span>
                  </p>
                )}
              </div>
            </div>
            <button
              onClick={handleClose}
              disabled={loading}
              className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
            >
              <FaTimes className="w-5 h-5" />
            </button>
          </div>

          {/* Contenido */}
          <div className="mb-6">
            <p className="text-gray-700 mb-2">
              {message}
            </p>
            {subtitle && (
              <p className="text-sm text-gray-500">
                {subtitle}
              </p>
            )}
          </div>

          {/* Botones */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleClose}
              disabled={loading}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:opacity-50 transition-colors"
            >
              {cancelText}
            </button>
            <button
              type="button"
              onClick={handleConfirm}
              disabled={loading}
              className={`px-4 py-2 rounded-md disabled:opacity-50 transition-colors flex items-center space-x-2 ${config.buttonColor} ${config.buttonTextColor}`}
            >
              {loading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              )}
              <span>
                {loading ? 'Procesando...' : confirmText}
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
