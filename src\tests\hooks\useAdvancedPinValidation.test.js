import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useAdvancedPinValidation } from '../../hooks/useAdvancedPinValidation';
import PinValidationService from '../../services/pin/PinValidationService';
import { toast } from 'react-toastify';

// Mocks
vi.mock('../../services/pin/PinValidationService');
vi.mock('react-toastify', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}));

describe('useAdvancedPinValidation', () => {
  const mockPsychologistId = 'psy-123';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId)
      );

      expect(result.current.validationResult).toBeNull();
      expect(result.current.isValidating).toBe(false);
      expect(result.current.canProceed).toBe(false);
      expect(result.current.isBlocked).toBe(false);
      expect(result.current.hasWarning).toBe(false);
    });

    it('should not validate without psychologist ID', () => {
      const { result } = renderHook(() => 
        useAdvancedPinValidation(null)
      );

      expect(PinValidationService.validateReportGeneration).not.toHaveBeenCalled();
      expect(result.current.validationResult).toBeNull();
    });
  });

  describe('validateSingleReport', () => {
    it('should validate single report successfully', async () => {
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE',
        userMessage: 'Puede generar el informe',
        severity: 'success',
        remainingPins: 10
      };

      PinValidationService.validateReportGeneration.mockResolvedValue(mockValidationResult);

      const { result } = renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { showToastAlerts: false })
      );

      let validationResult;
      await act(async () => {
        validationResult = await result.current.validateSingleReport(1);
      });

      expect(PinValidationService.validateReportGeneration).toHaveBeenCalledWith(mockPsychologistId, 1);
      expect(validationResult).toEqual(mockValidationResult);
      expect(result.current.validationResult).toEqual(mockValidationResult);
      expect(result.current.canProceed).toBe(true);
      expect(result.current.isValidating).toBe(false);
    });

    it('should handle validation error', async () => {
      const error = new Error('Validation failed');
      PinValidationService.validateReportGeneration.mockRejectedValue(error);

      const { result } = renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { showToastAlerts: false })
      );

      let validationResult;
      await act(async () => {
        validationResult = await result.current.validateSingleReport(1);
      });

      expect(validationResult.isValid).toBe(false);
      expect(validationResult.canProceed).toBe(false);
      expect(validationResult.reason).toBe('VALIDATION_ERROR');
      expect(validationResult.error).toBe('Validation failed');
    });

    it('should show toast alerts when enabled', async () => {
      const mockValidationResult = {
        isValid: false,
        canProceed: false,
        reason: 'INSUFFICIENT_PINS',
        userMessage: 'No hay suficientes pines',
        severity: 'error'
      };

      PinValidationService.validateReportGeneration.mockResolvedValue(mockValidationResult);
      PinValidationService.displayValidationAlerts.mockReturnValue(mockValidationResult);

      const { result } = renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { showToastAlerts: true })
      );

      await act(async () => {
        await result.current.validateSingleReport(1);
      });

      expect(PinValidationService.displayValidationAlerts).toHaveBeenCalledWith(mockValidationResult);
    });

    it('should call onValidationChange callback', async () => {
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE'
      };

      const onValidationChange = vi.fn();
      PinValidationService.validateReportGeneration.mockResolvedValue(mockValidationResult);

      const { result } = renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { 
          showToastAlerts: false,
          onValidationChange 
        })
      );

      await act(async () => {
        await result.current.validateSingleReport(1);
      });

      expect(onValidationChange).toHaveBeenCalledWith(mockValidationResult);
    });
  });

  describe('validateBatchReports', () => {
    it('should validate batch reports successfully', async () => {
      const patientIds = ['patient-1', 'patient-2', 'patient-3'];
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE',
        batchInfo: {
          patientCount: 3,
          requiredPins: 3,
          patientIds
        }
      };

      PinValidationService.validateBatchReportGeneration.mockResolvedValue(mockValidationResult);

      const { result } = renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { showToastAlerts: false })
      );

      let validationResult;
      await act(async () => {
        validationResult = await result.current.validateBatchReports(patientIds);
      });

      expect(PinValidationService.validateBatchReportGeneration).toHaveBeenCalledWith(mockPsychologistId, patientIds);
      expect(validationResult).toEqual(mockValidationResult);
      expect(result.current.validationResult).toEqual(mockValidationResult);
    });

    it('should handle invalid patient list', async () => {
      const { result } = renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { showToastAlerts: false })
      );

      let validationResult;
      await act(async () => {
        validationResult = await result.current.validateBatchReports([]);
      });

      expect(validationResult.isValid).toBe(false);
      expect(validationResult.reason).toBe('INVALID_PATIENT_LIST');
    });
  });

  describe('auto-validation', () => {
    it('should auto-validate when enabled', async () => {
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE'
      };

      PinValidationService.validateReportGeneration.mockResolvedValue(mockValidationResult);

      renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { 
          autoValidate: true,
          showToastAlerts: false 
        })
      );

      await waitFor(() => {
        expect(PinValidationService.validateReportGeneration).toHaveBeenCalledWith(mockPsychologistId, 1);
      });
    });

    it('should not auto-validate when disabled', () => {
      renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { 
          autoValidate: false,
          showToastAlerts: false 
        })
      );

      expect(PinValidationService.validateReportGeneration).not.toHaveBeenCalled();
    });
  });

  describe('utility functions', () => {
    it('should provide validation summary', async () => {
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE'
      };

      const mockSummary = {
        status: 'allowed',
        title: 'Operación Permitida'
      };

      PinValidationService.validateReportGeneration.mockResolvedValue(mockValidationResult);
      PinValidationService.createValidationSummary.mockReturnValue(mockSummary);

      const { result } = renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { showToastAlerts: false })
      );

      await act(async () => {
        await result.current.validateSingleReport(1);
      });

      const summary = result.current.getValidationSummary();
      expect(summary).toEqual(mockSummary);
      expect(PinValidationService.createValidationSummary).toHaveBeenCalledWith(mockValidationResult);
    });

    it('should clear validation', async () => {
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE'
      };

      PinValidationService.validateReportGeneration.mockResolvedValue(mockValidationResult);

      const { result } = renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { showToastAlerts: false })
      );

      // First validate
      await act(async () => {
        await result.current.validateSingleReport(1);
      });

      expect(result.current.validationResult).toEqual(mockValidationResult);

      // Then clear
      act(() => {
        result.current.clearValidation();
      });

      expect(result.current.validationResult).toBeNull();
      expect(result.current.lastValidation).toBeNull();
    });

    it('should revalidate', async () => {
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE'
      };

      PinValidationService.validateReportGeneration.mockResolvedValue(mockValidationResult);

      const { result } = renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { showToastAlerts: false })
      );

      await act(async () => {
        await result.current.revalidate(2);
      });

      expect(PinValidationService.validateReportGeneration).toHaveBeenCalledWith(mockPsychologistId, 2);
    });
  });

  describe('derived states', () => {
    it('should calculate derived states correctly', async () => {
      const mockValidationResult = {
        isValid: true,
        canProceed: true,
        hasWarning: true,
        severity: 'warning',
        userMessage: 'Advertencia de pines bajos',
        suggestions: ['Recargue pines pronto']
      };

      PinValidationService.validateReportGeneration.mockResolvedValue(mockValidationResult);

      const { result } = renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { showToastAlerts: false })
      );

      await act(async () => {
        await result.current.validateSingleReport(1);
      });

      expect(result.current.canProceed).toBe(true);
      expect(result.current.hasWarning).toBe(true);
      expect(result.current.isBlocked).toBe(false);
      expect(result.current.isValid).toBe(true);
      expect(result.current.severity).toBe('warning');
      expect(result.current.message).toBe('Advertencia de pines bajos');
      expect(result.current.suggestions).toEqual(['Recargue pines pronto']);
    });

    it('should handle blocked state', async () => {
      const mockValidationResult = {
        isValid: false,
        canProceed: false,
        hasWarning: false,
        severity: 'error',
        userMessage: 'Sin pines disponibles'
      };

      PinValidationService.validateReportGeneration.mockResolvedValue(mockValidationResult);

      const { result } = renderHook(() => 
        useAdvancedPinValidation(mockPsychologistId, { showToastAlerts: false })
      );

      await act(async () => {
        await result.current.validateSingleReport(1);
      });

      expect(result.current.canProceed).toBe(false);
      expect(result.current.isBlocked).toBe(true);
      expect(result.current.isValid).toBe(false);
      expect(result.current.severity).toBe('error');
    });
  });
});
