import{j as e}from"./vendor-BqMjyOVw.js";import{C as s,a,g as l,b as t}from"./index-Bdl1jgS_.js";const i=()=>e.jsxs("div",{children:[e.jsx("div",{className:"mb-8",children:e.jsx(s,{className:"shadow-md rounded-lg overflow-hidden hover:shadow-xl transition-all duration-500 transform hover:scale-[1.02]",children:e.jsx(a,{className:"p-0",children:e.jsxs("div",{className:"relative w-full h-[350px] overflow-hidden rounded-lg",children:[e.jsxs("div",{className:"absolute inset-0",children:[e.jsx("img",{src:l("banner.png"),alt:"BAT-7 Evaluación de Aptitudes",className:"w-full h-full object-cover transition-transform duration-700 hover:scale-105"}),e.jsxs("div",{className:"absolute inset-0 opacity-30",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/30 to-cyan-500/20 animate-pulse"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-l from-indigo-500/20 via-pink-500/20 to-blue-500/30 banner-shimmer"})]}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600/10 via-transparent to-purple-600/10 banner-glow"}),e.jsx("div",{className:"absolute inset-0 overflow-hidden",children:e.jsx("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 animate-pulse"})}),e.jsxs("div",{className:"absolute inset-0 overflow-hidden opacity-40",children:[e.jsx("div",{className:"absolute top-10 left-10 w-2 h-2 bg-cyan-300 rounded-full animate-pulse"}),e.jsx("div",{className:"absolute top-20 right-20 w-1 h-1 bg-yellow-300 rounded-full animate-ping"}),e.jsx("div",{className:"absolute bottom-20 left-1/4 w-3 h-3 bg-pink-300 rounded-full animate-bounce"}),e.jsx("div",{className:"absolute bottom-10 right-1/3 w-2 h-2 bg-green-300 rounded-full animate-pulse"}),e.jsx("div",{className:"absolute top-1/2 left-1/2 w-1 h-1 bg-purple-300 rounded-full animate-ping"})]})]}),e.jsx("div",{className:"relative z-10 flex items-center justify-center h-full",children:e.jsxs("div",{className:"absolute bottom-6 right-6 flex space-x-2 opacity-70",children:[e.jsx("div",{className:"w-2 h-2 bg-cyan-400 rounded-full animate-pulse"}),e.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-100"}),e.jsx("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-200"}),e.jsx("div",{className:"w-2 h-2 bg-pink-400 rounded-full animate-pulse delay-300"}),e.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse delay-400"})]})}),e.jsx("div",{className:"absolute inset-0 opacity-30",children:e.jsx("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 animate-pulse"})})]})})})}),e.jsx("div",{className:"mb-8",children:e.jsxs(s,{className:"shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300",children:[e.jsx(t,{className:"bg-gradient-to-r from-[#31375a] to-[#31375a] border-b border-blue-300",children:e.jsxs("h3",{className:"text-xl font-semibold text-white text-center py-2 flex items-center justify-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Descripción"]})}),e.jsx(a,{children:e.jsx("p",{className:"text-gray-700 mb-4 text-center leading-relaxed px-4 py-2",children:"A través de esta plataforma digital, podrás realizar la prueba de forma segura y eficiente. El objetivo es obtener una visión integral de tus fortalezas y potencial, asegurando un proceso de selección equitativo y orientado a identificar a los candidatos mejor preparados para los desafíos y oportunidades que ofrece cada institución."})})]})}),e.jsx("div",{className:"mb-8",children:e.jsxs(s,{className:"shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300",children:[e.jsx(t,{className:"bg-gradient-to-r from-[#31375a] to-[#31375a] border-b border-green-300",children:e.jsxs("h3",{className:"text-xl font-semibold text-white text-center py-2 flex items-center justify-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),"Aptitudes evaluadas"]})}),e.jsx(a,{children:e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mb-2",children:e.jsx("span",{className:"font-semibold",children:"V"})}),e.jsx("span",{className:"font-medium",children:"Verbal"}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Comprensión y razonamiento verbal"})]}),e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mb-2",children:e.jsx("span",{className:"font-semibold",children:"E"})}),e.jsx("span",{className:"font-medium",children:"Espacial"}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Visualización y orientación espacial"})]}),e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mb-2",children:e.jsx("span",{className:"font-semibold",children:"A"})}),e.jsx("span",{className:"font-medium",children:"Atención"}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Capacidad de atención sostenida"})]}),e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-red-100 flex items-center justify-center text-red-600 mb-2",children:e.jsx("span",{className:"font-semibold",children:"R"})}),e.jsx("span",{className:"font-medium",children:"Razonamiento"}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Pensamiento lógico y analítico"})]}),e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mb-2",children:e.jsx("span",{className:"font-semibold",children:"N"})}),e.jsx("span",{className:"font-medium",children:"Numérica"}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Habilidades matemáticas y cálculo"})]}),e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 mb-2",children:e.jsx("span",{className:"font-semibold",children:"M"})}),e.jsx("span",{className:"font-medium",children:"Mecánica"}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Comprensión de principios mecánicos"})]}),e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600 mb-2",children:e.jsx("span",{className:"font-semibold",children:"O"})}),e.jsx("span",{className:"font-medium",children:"Ortografía"}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Corrección ortográfica y lingüística"})]})]})})]})})]});export{i as default};
