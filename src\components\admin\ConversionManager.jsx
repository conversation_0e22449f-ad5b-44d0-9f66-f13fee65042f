import React, { useState } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { Button } from '../ui/Button';
import { SupabaseConversionService } from '../../services/supabaseConversionService';
import { toast } from 'react-toastify';

const ConversionManager = () => {
  const [loading, setLoading] = useState(false);
  
  // Múltiples pruebas de conversión
  const [pruebas, setPruebas] = useState([
    { id: 1, puntajeDirecto: 25, aptitudCodigo: 'V', edad: 13, resultado: null },
    { id: 2, puntajeDirecto: 35, aptitudCodigo: 'E', edad: 13, resultado: null },
    { id: 3, puntajeDirecto: 45, aptitudCodigo: 'A', edad: 13, resultado: null },
    { id: 4, puntajeDirecto: 30, aptitudCodigo: 'C', edad: 13, resultado: null },
    { id: 5, puntajeDirecto: 20, aptitudCodigo: 'R', edad: 13, resultado: null }
  ]);

  // Opciones de aptitudes incluyendo Concentración
  const aptitudes = [
    { codigo: 'V', nombre: 'Verbal' },
    { codigo: 'E', nombre: 'Espacial' },
    { codigo: 'A', nombre: 'Atención' },
    { codigo: 'C', nombre: 'Concentración' },
    { codigo: 'R', nombre: 'Razonamiento' },
    { codigo: 'N', nombre: 'Numérica' },
    { codigo: 'M', nombre: 'Mecánica' },
    { codigo: 'O', nombre: 'Ortografía' }
  ];

  // Función para probar una conversión individual
  const probarConversion = async (pruebaId) => {
    setLoading(true);
    try {
      const prueba = pruebas.find(p => p.id === pruebaId);
      if (!prueba) return;

      const resultado = await SupabaseConversionService.probarConversion(
        prueba.puntajeDirecto,
        prueba.aptitudCodigo,
        prueba.edad
      );
      
      if (resultado.success) {
        // Actualizar el resultado en la prueba
        setPruebas(prev => prev.map(p => 
          p.id === pruebaId 
            ? { ...p, resultado: resultado.percentil }
            : p
        ));
        toast.success(`Conversión exitosa: PD ${prueba.puntajeDirecto} → PC ${resultado.percentil}`);
      } else {
        toast.error(`Error en conversión: ${resultado.error?.message || 'Error desconocido'}`);
      }
    } catch (error) {
      console.error('Error probando conversión:', error);
      toast.error('Error al probar conversión');
    } finally {
      setLoading(false);
    }
  };

  // Función para probar todas las conversiones
  const probarTodasLasConversiones = async () => {
    setLoading(true);
    try {
      const resultadosTemp = [];
      
      for (const prueba of pruebas) {
        const resultado = await SupabaseConversionService.probarConversion(
          prueba.puntajeDirecto,
          prueba.aptitudCodigo,
          prueba.edad
        );
        
        if (resultado.success) {
          resultadosTemp.push({
            ...prueba,
            resultado: resultado.percentil
          });
        } else {
          resultadosTemp.push({
            ...prueba,
            resultado: 'Error'
          });
        }
      }
      
      setPruebas(resultadosTemp);
      toast.success('Todas las conversiones completadas');
    } catch (error) {
      console.error('Error probando conversiones:', error);
      toast.error('Error al probar conversiones');
    } finally {
      setLoading(false);
    }
  };

  // Función para actualizar una prueba
  const actualizarPrueba = (pruebaId, campo, valor) => {
    setPruebas(prev => prev.map(p => 
      p.id === pruebaId 
        ? { ...p, [campo]: valor, resultado: null }
        : p
    ));
  };

  // Función para agregar nueva prueba
  const agregarPrueba = () => {
    const nuevaId = Math.max(...pruebas.map(p => p.id)) + 1;
    setPruebas(prev => [...prev, {
      id: nuevaId,
      puntajeDirecto: 25,
      aptitudCodigo: 'V',
      edad: 13,
      resultado: null
    }]);
  };

  // Función para eliminar prueba
  const eliminarPrueba = (pruebaId) => {
    if (pruebas.length > 1) {
      setPruebas(prev => prev.filter(p => p.id !== pruebaId));
    }
  };

  return (
    <div className="space-y-6">
      {/* Prueba de Conversión PD → PC */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">
            🔄 Probar Conversión PD → PC
          </h3>
          <p className="text-sm text-gray-600">
            Convierte puntajes directos (PD) a percentiles (PC) para las 8 aptitudes del BAT-7
          </p>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            {/* Botones de acción */}
            <div className="flex gap-3 mb-6">
              <Button
                onClick={probarTodasLasConversiones}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {loading ? 'Procesando...' : 'Probar Todas'}
              </Button>
              <Button
                onClick={agregarPrueba}
                disabled={loading}
                className="bg-green-600 hover:bg-green-700"
              >
                + Agregar Prueba
              </Button>
            </div>

            {/* Tabla de pruebas */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Aptitud
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      PD
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Edad
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      PC
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Acciones
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {pruebas.map((prueba) => (
                    <tr key={prueba.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          value={prueba.aptitudCodigo}
                          onChange={(e) => actualizarPrueba(prueba.id, 'aptitudCodigo', e.target.value)}
                          className="border border-gray-300 rounded px-3 py-1 text-sm"
                        >
                          {aptitudes.map(apt => (
                            <option key={apt.codigo} value={apt.codigo}>
                              {apt.codigo} - {apt.nombre}
                            </option>
                          ))}
                        </select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          value={prueba.puntajeDirecto}
                          onChange={(e) => actualizarPrueba(prueba.id, 'puntajeDirecto', parseInt(e.target.value) || 0)}
                          className="border border-gray-300 rounded px-3 py-1 text-sm w-20"
                          min="0"
                          max="100"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          value={prueba.edad}
                          onChange={(e) => actualizarPrueba(prueba.id, 'edad', parseInt(e.target.value) || 13)}
                          className="border border-gray-300 rounded px-3 py-1 text-sm w-20"
                          min="12"
                          max="14"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-sm rounded ${
                          prueba.resultado === null 
                            ? 'bg-gray-100 text-gray-500' 
                            : prueba.resultado === 'Error'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {prueba.resultado === null ? 'Pendiente' : prueba.resultado}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button
                          onClick={() => probarConversion(prueba.id)}
                          disabled={loading}
                          className="text-blue-600 hover:text-blue-900 disabled:text-gray-400"
                        >
                          Probar
                        </button>
                        {pruebas.length > 1 && (
                          <button
                            onClick={() => eliminarPrueba(prueba.id)}
                            disabled={loading}
                            className="text-red-600 hover:text-red-900 disabled:text-gray-400"
                          >
                            Eliminar
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Información de aptitudes */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Aptitudes Disponibles:</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-blue-800">
                {aptitudes.map(apt => (
                  <div key={apt.codigo}>
                    <strong>{apt.codigo}:</strong> {apt.nombre}
                  </div>
                ))}
              </div>
              <p className="text-xs text-blue-700 mt-2">
                * Edades soportadas: 12-14 años | PD: Puntaje Directo | PC: Percentil
              </p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default ConversionManager;
