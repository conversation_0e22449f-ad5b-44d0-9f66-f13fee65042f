/**
 * Hook para obtener interpretaciones reales desde Supabase
 */

import { useState, useEffect } from 'react';
import InterpretacionesSupabaseService from '../services/InterpretacionesSupabaseService';

export const useInterpretacionesReales = (resultados = []) => {
  const [interpretaciones, setInterpretaciones] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!resultados || resultados.length === 0) {
      return;
    }

    const cargarInterpretaciones = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const interpretacionesMap = {};
        
        // Cargar interpretaciones para cada resultado
        for (const resultado of resultados) {
          const aptitudCodigo = resultado.aptitudes?.codigo;
          const percentil = resultado.percentil;
          
          if (aptitudCodigo && percentil !== null && percentil !== undefined) {
            console.log(`🔍 Cargando interpretación para ${aptitudCodigo}-${percentil}`);
            
            const interpretacion = await InterpretacionesSupabaseService.obtenerInterpretacionOficial(
              aptitudCodigo, 
              percentil
            );
            
            interpretacionesMap[aptitudCodigo] = interpretacion;
          }
        }
        
        setInterpretaciones(interpretacionesMap);
        console.log('✅ Interpretaciones cargadas:', Object.keys(interpretacionesMap));
        
      } catch (err) {
        console.error('❌ Error cargando interpretaciones:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    cargarInterpretaciones();
  }, [resultados]);

  // Función para obtener interpretación específica
  const obtenerInterpretacion = (aptitudCodigo) => {
    return interpretaciones[aptitudCodigo] || null;
  };

  // Función para verificar si hay interpretación disponible
  const tieneInterpretacion = (aptitudCodigo) => {
    return !!interpretaciones[aptitudCodigo];
  };

  return {
    interpretaciones,
    loading,
    error,
    obtenerInterpretacion,
    tieneInterpretacion
  };
};
