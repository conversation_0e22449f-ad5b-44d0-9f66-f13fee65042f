var e=Object.defineProperty,s=Object.defineProperties,t=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,i=(s,t,a)=>t in s?e(s,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):s[t]=a,n=(e,s)=>{for(var t in s||(s={}))l.call(s,t)&&i(e,t,s[t]);if(a)for(var t of a(s))r.call(s,t)&&i(e,t,s[t]);return e},c=(e,a)=>s(e,t(a)),o=(e,s,t)=>new Promise((a,l)=>{var r=e=>{try{n(t.next(e))}catch(s){l(s)}},i=e=>{try{n(t.throw(e))}catch(s){l(s)}},n=e=>e.done?a(e.value):Promise.resolve(e.value).then(r,i);n((t=t.apply(e,s)).next())});import{j as d,r as m,O as x,t as u,a4 as g,am as h,u as f,Q as p,an as b}from"./vendor-BqMjyOVw.js";import{P as j,h as N,s as v,u as y}from"./index-Bdl1jgS_.js";import{T as w}from"./TestCard-CFSJq7a1.js";import{P as C}from"./PageHeader-DzW86ZOX.js";const _=({completedTests:e=[],allTests:s=[],totalTime:t=0})=>{if(0===s.length)return d.jsx("div",{className:"flex items-center justify-center h-64",children:d.jsx("p",{className:"text-gray-500",children:"No hay tests disponibles"})});const a=[{codigo:"V",nombre:"Verbal",color:"#3B82F6"},{codigo:"E",nombre:"Espacial",color:"#6366F1"},{codigo:"A",nombre:"Atención",color:"#EF4444"},{codigo:"R",nombre:"Razonamiento",color:"#F59E0B"},{codigo:"N",nombre:"Numérica",color:"#14B8A6"},{codigo:"M",nombre:"Mecánica",color:"#64748B"},{codigo:"O",nombre:"Ortografía",color:"#10B981"}].map(t=>{const a=e.find(e=>{var s;return(null==(s=e.aptitudes)?void 0:s.codigo)===t.codigo}),l=s.find(e=>e.abbreviation===t.codigo);return c(n({},t),{completed:!!a,available:!!l,puntaje:(null==a?void 0:a.puntaje_directo)||0,fecha:(null==a?void 0:a.created_at)||null})}),l=({aptitud:e})=>{const s=e.completed?100:0,t=2*Math.PI*30,a=`${s/100*t} ${t}`;return d.jsxs("div",{className:"flex flex-col items-center flex-1 min-w-0",children:[d.jsxs("div",{className:"relative mb-3",children:[d.jsxs("svg",{viewBox:"0 0 80 80",className:"w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28",children:[d.jsx("circle",{cx:"40",cy:"40",r:"30",fill:"none",stroke:"#E5E7EB",strokeWidth:"6"}),e.available&&d.jsx("circle",{cx:"40",cy:"40",r:"30",fill:"none",stroke:e.completed?e.color:"#E5E7EB",strokeWidth:"6",strokeDasharray:a,strokeDashoffset:"0",transform:"rotate(-90 40 40)",strokeLinecap:"round"})]}),d.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:d.jsx("span",{className:"text-sm sm:text-base font-bold",style:{color:e.color},children:e.codigo})})]}),d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-xs sm:text-sm font-medium text-gray-700 mb-1",children:e.nombre}),e.completed?d.jsxs("div",{className:"text-xs sm:text-sm text-green-600 font-semibold",children:["PD: ",e.puntaje]}):e.available?d.jsx("div",{className:"text-xs sm:text-sm text-gray-500",children:"Pendiente"}):d.jsx("div",{className:"text-xs sm:text-sm text-gray-400",children:"No disponible"})]})]})},r=a.filter(e=>e.completed).length,i=a.filter(e=>e.available).length;return d.jsxs("div",{className:"flex flex-col items-center",children:[d.jsx("div",{className:"w-full max-w-6xl",children:d.jsx("div",{className:"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-7 gap-2 sm:gap-4 mb-6",children:a.map((e,s)=>d.jsx(l,{aptitud:e},s))})}),d.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-6 text-center",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("div",{className:"w-4 h-4 bg-green-500 rounded-full"}),d.jsxs("span",{className:"text-sm text-gray-700",children:["Completados: ",d.jsx("span",{className:"font-semibold",children:r})]})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("div",{className:"w-4 h-4 bg-gray-300 rounded-full"}),d.jsxs("span",{className:"text-sm text-gray-700",children:["Pendientes: ",d.jsx("span",{className:"font-semibold",children:i-r})]})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("i",{className:"fas fa-clock text-purple-600"}),d.jsxs("span",{className:"text-sm text-gray-700",children:["Tiempo total: ",d.jsx("span",{className:"font-semibold",children:(e=>{if(e<60)return`${e}s`;if(e<3600){const s=Math.floor(e/60),t=e%60;return t>0?`${s}m ${t}s`:`${s}m`}{const s=Math.floor(e/3600),t=Math.floor(e%3600/60);return t>0?`${s}h ${t}m`:`${s}h`}})(t)})]})]})]}),d.jsxs("div",{className:"mt-4 text-center",children:[d.jsxs("div",{className:"text-lg font-semibold text-gray-800",children:[r," de ",i," tests"]}),d.jsx("div",{className:"text-sm text-gray-500",children:i>0?`${(r/i*100).toFixed(0)}% completado`:"Sin progreso"})]})]})},S=({results:e=[],completedTests:s=[],selectedLevel:t="E"})=>{var a,l;const[r,i]=m.useState(!1),[n,c]=m.useState(null),o=e.filter(e=>null!==e.respuestas_correctas&&null!==e.respuestas_incorrectas&&null!==e.respuestas_sin_contestar),x=(null==(a={E:[{id:"verbal",name:"Aptitud Verbal",code:"V"},{id:"espacial",name:"Aptitud Espacial",code:"E"},{id:"atencion",name:"Atención y Concentración",code:"A"},{id:"razonamiento",name:"Razonamiento",code:"R"},{id:"numerico",name:"Aptitud Numérica",code:"N"},{id:"mecanico",name:"Comprensión Mecánica",code:"M"},{id:"ortografia",name:"Ortografía",code:"O"}],B:[],S:[]}[t])?void 0:a.length)||0,u=s.length||0,g=x-u,h=x>0?Math.round(u/x*100):0;if(0===o.length)return d.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[d.jsxs("h3",{className:"text-lg font-semibold text-gray-800 mb-4 flex items-center",children:[d.jsx("i",{className:"fas fa-chart-pie mr-2 text-blue-600"}),"Resultados de Tests Aplicados"]}),d.jsxs("div",{className:"text-center py-8",children:[d.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4",children:d.jsx("i",{className:"fas fa-chart-pie text-2xl text-gray-400"})}),d.jsx("p",{className:"text-gray-500",children:"No hay resultados detallados disponibles"}),d.jsx("p",{className:"text-sm text-gray-400 mt-1",children:"Los gráficos aparecerán cuando se completen tests"})]})]});const f=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`;return d.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[d.jsxs("h3",{className:"text-lg font-semibold text-gray-800 mb-6 flex items-center",children:[d.jsx("i",{className:"fas fa-chart-pie mr-2 text-blue-600"}),"Resultados de Tests Aplicados"]}),d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:o.map(e=>{var s,t,a;const l=e.total_preguntas||0,r=l>0?((e.respuestas_correctas||0)/l*100).toFixed(1):0;return d.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-shadow",children:[d.jsxs("div",{className:"flex items-center justify-between mb-3",children:[d.jsxs("div",{className:"flex items-center",children:[d.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white mr-2",style:{backgroundColor:(n=null==(s=e.aptitudes)?void 0:s.codigo,{V:"#3B82F6",E:"#6366F1",A:"#EF4444",R:"#F59E0B",N:"#14B8A6",M:"#64748B",O:"#10B981"}[n]||"#6B7280")},children:(null==(t=e.aptitudes)?void 0:t.codigo)||"N/A"}),d.jsxs("div",{children:[d.jsx("h4",{className:"text-sm font-semibold text-gray-800",children:(null==(a=e.aptitudes)?void 0:a.nombre)||"Test"}),d.jsx("p",{className:"text-xs text-gray-500",children:new Date(e.created_at).toLocaleDateString("es-ES")})]})]}),d.jsxs("button",{onClick:()=>{c(e),i(!0)},className:"text-blue-600 hover:text-blue-800 text-sm font-medium",title:"Ver gráfico",children:[d.jsx("i",{className:"fas fa-chart-pie mr-1"}),"Gráfico"]})]}),d.jsxs("div",{className:"space-y-2",children:[d.jsxs("div",{className:"flex justify-between items-center p-2 bg-white rounded",children:[d.jsx("span",{className:"text-sm text-gray-600",children:"Porcentaje de Aciertos:"}),d.jsxs("span",{className:"text-lg font-bold text-green-600",children:[r,"%"]})]}),d.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"Correctas:"}),d.jsx("span",{className:"font-medium text-green-600",children:e.respuestas_correctas||0})]}),d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"Incorrectas:"}),d.jsx("span",{className:"font-medium text-red-600",children:e.respuestas_incorrectas||0})]}),d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"PD:"}),d.jsx("span",{className:"font-medium text-blue-600",children:e.puntaje_directo||0})]}),d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"PC:"}),d.jsx("span",{className:"font-medium text-purple-600",children:e.percentil||0})]}),d.jsxs("div",{className:"flex justify-between col-span-2",children:[d.jsx("span",{className:"text-gray-600",children:"Tiempo:"}),d.jsx("span",{className:"font-medium text-gray-800",children:f(e.tiempo_segundos||0)})]})]})]})]},e.id);var n})}),d.jsxs("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[d.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-center",children:[d.jsxs("div",{className:"bg-blue-50 rounded-lg p-3",children:[d.jsx("div",{className:"text-lg font-bold text-blue-600",children:u}),d.jsx("div",{className:"text-xs text-gray-600",children:"Tests Completados"})]}),d.jsxs("div",{className:"bg-orange-50 rounded-lg p-3",children:[d.jsx("div",{className:"text-lg font-bold text-orange-600",children:g}),d.jsx("div",{className:"text-xs text-gray-600",children:"Tests Pendientes"})]}),d.jsxs("div",{className:"bg-purple-50 rounded-lg p-3",children:[d.jsxs("div",{className:"text-lg font-bold text-purple-600",children:[h,"%"]}),d.jsx("div",{className:"text-xs text-gray-600",children:"Progreso Total"})]}),d.jsxs("div",{className:"bg-green-50 rounded-lg p-3",children:[d.jsx("div",{className:"text-lg font-bold text-green-600",children:o.reduce((e,s)=>e+(s.respuestas_correctas||0),0)}),d.jsx("div",{className:"text-xs text-gray-600",children:"Total Correctas"})]}),d.jsxs("div",{className:"bg-gray-50 rounded-lg p-3",children:[d.jsx("div",{className:"text-lg font-bold text-gray-600",children:f(o.reduce((e,s)=>e+(s.tiempo_segundos||0),0))}),d.jsx("div",{className:"text-xs text-gray-600",children:"Tiempo Total"})]})]}),d.jsxs("div",{className:"mt-4",children:[d.jsxs("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[d.jsx("span",{children:"Progreso de Evaluación"}),d.jsxs("span",{children:[u," de ",x," tests"]})]}),d.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:d.jsx("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500",style:{width:`${h}%`}})})]})]}),r&&n&&d.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:()=>i(!1),children:d.jsxs("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:[d.jsxs("div",{className:"flex justify-between items-center mb-6",children:[d.jsxs("h3",{className:"text-xl font-semibold text-gray-800",children:["Resultados Detallados - ",(null==(l=n.aptitudes)?void 0:l.nombre)||"Test"]}),d.jsx("button",{onClick:()=>i(!1),className:"text-gray-400 hover:text-gray-600 text-2xl",children:d.jsx("i",{className:"fas fa-times"})})]}),d.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[d.jsxs("div",{className:"text-center",children:[d.jsx("h5",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Resultados"}),d.jsx("div",{className:"flex justify-center mb-4",children:d.jsx("div",{className:"w-48 h-48",children:d.jsx(j,{data:[{name:"Correctas",value:n.respuestas_correctas||0,color:"#10B981"},{name:"Incorrectas",value:n.respuestas_incorrectas||0,color:"#EF4444"},{name:"Sin Responder",value:n.respuestas_sin_contestar||0,color:"#6B7280"}].filter(e=>e.value>0),width:192,height:192,centerText:`${((n.respuestas_correctas||0)/(n.total_preguntas||1)*100).toFixed(1)}%`,centerSubtext:"Aciertos"})})})]}),d.jsxs("div",{children:[d.jsx("h5",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Estadísticas"}),d.jsxs("div",{className:"space-y-3",children:[d.jsxs("div",{className:"flex justify-between items-center p-3 bg-white rounded-lg border",children:[d.jsxs("div",{className:"flex items-center",children:[d.jsx("div",{className:"w-4 h-4 bg-green-500 rounded-full mr-3"}),d.jsx("span",{className:"text-gray-700",children:"Respuestas correctas"})]}),d.jsxs("span",{className:"font-semibold text-gray-800",children:[n.respuestas_correctas||0," de ",n.total_preguntas||0]})]}),d.jsxs("div",{className:"flex justify-between items-center p-3 bg-white rounded-lg border",children:[d.jsxs("div",{className:"flex items-center",children:[d.jsx("div",{className:"w-4 h-4 bg-red-500 rounded-full mr-3"}),d.jsx("span",{className:"text-gray-700",children:"Respuestas incorrectas"})]}),d.jsx("span",{className:"font-semibold text-gray-800",children:n.respuestas_incorrectas||0})]}),d.jsxs("div",{className:"flex justify-between items-center p-3 bg-white rounded-lg border",children:[d.jsxs("div",{className:"flex items-center",children:[d.jsx("div",{className:"w-4 h-4 bg-gray-500 rounded-full mr-3"}),d.jsx("span",{className:"text-gray-700",children:"Sin responder"})]}),d.jsx("span",{className:"font-semibold text-gray-800",children:n.respuestas_sin_contestar||0})]}),d.jsxs("div",{className:"flex justify-between items-center p-3 bg-blue-50 rounded-lg border border-blue-200",children:[d.jsx("span",{className:"text-blue-700 font-medium",children:"Tiempo utilizado"}),d.jsx("span",{className:"font-semibold text-blue-800",children:f(n.tiempo_segundos||0)})]}),d.jsxs("div",{className:"flex justify-between items-center p-3 bg-purple-50 rounded-lg border border-purple-200",children:[d.jsx("span",{className:"text-purple-700 font-medium",children:"Puntaje Directo"}),d.jsx("span",{className:"font-bold text-purple-800 text-lg",children:n.puntaje_directo||0})]}),d.jsxs("div",{className:"flex justify-between items-center p-3 bg-indigo-50 rounded-lg border border-indigo-200",children:[d.jsx("span",{className:"text-indigo-700 font-medium",children:"Percentil (PC)"}),d.jsx("span",{className:"font-bold text-indigo-800 text-lg",children:n.percentil||0})]})]}),d.jsxs("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200",children:[d.jsxs("h6",{className:"text-sm font-semibold text-blue-800 mb-2",children:[d.jsx("i",{className:"fas fa-lightbulb mr-1"}),"Recomendaciones"]}),d.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[d.jsxs("li",{className:"flex items-start",children:[d.jsx("i",{className:"fas fa-check text-green-600 mr-2 mt-0.5 text-xs"}),"Continúa practicando ejercicios similares para mejorar tu desempeño"]}),d.jsxs("li",{className:"flex items-start",children:[d.jsx("i",{className:"fas fa-check text-green-600 mr-2 mt-0.5 text-xs"}),"Revisa los conceptos básicos relacionados con este tipo de prueba"]}),d.jsxs("li",{className:"flex items-start",children:[d.jsx("i",{className:"fas fa-check text-green-600 mr-2 mt-0.5 text-xs"}),"Analiza las preguntas que te resultaron más difíciles para identificar áreas de mejora"]})]})]})]})]})]})})]})},E=({className:e=""})=>{const{selectedPatient:s,isSessionActive:t,selectedLevel:a,completedTests:l,sessionDuration:r,hasActiveSession:i}=N();if(!i)return null;return d.jsx("div",{className:`bg-blue-50 border border-blue-200 rounded-lg p-4 ${e}`,children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("div",{className:"flex items-center space-x-3",children:d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(x,{className:"text-blue-600"}),d.jsxs("div",{children:[d.jsxs("p",{className:"font-medium text-gray-900",children:[s.nombre," ",s.apellido]}),d.jsxs("p",{className:"text-sm text-gray-600",children:["Nivel: ",{E:"Escolar",B:"Bachillerato",S:"Superior"}[a]||a]})]})]})}),d.jsxs("div",{className:"flex items-center space-x-4",children:[d.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[d.jsx(u,{className:"text-gray-500"}),d.jsxs("span",{children:[r," min"]})]}),d.jsxs("div",{className:"flex items-center space-x-1 text-sm text-green-600",children:[d.jsx(g,{className:"text-green-500"}),d.jsxs("span",{children:[l.length," completados"]})]}),d.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse",title:"Sesión activa"})]})]})})};class k{static startSession(e,s,t,a=null){return o(this,null,function*(){try{const l={paciente_id:e,test_id:s,usuario_id:null==t?void 0:t.id,aptitud_id:a,fecha_inicio:(new Date).toISOString(),estado:"iniciado",ip_address:yield this.getClientIP(),user_agent:navigator.userAgent},{data:r,error:i}=yield v.from("test_sessions").insert(l).select().single();if(i){if(406===i.status)return{id:`mock-session-${Date.now()}`,paciente_id:e,test_id:s,estado:"iniciado",fecha_inicio:(new Date).toISOString()};throw i}return r}catch(l){if(406===l.status)return{id:`mock-session-${Date.now()}`,paciente_id:e,test_id:s,estado:"iniciado",fecha_inicio:(new Date).toISOString()};throw l}})}static finishSession(e,s,t=null){return o(this,null,function*(){try{if(e&&e.startsWith("mock-session-"))return{id:e,estado:"finalizado",fecha_fin:(new Date).toISOString()};const s={fecha_fin:(new Date).toISOString(),estado:"finalizado",updated_at:(new Date).toISOString()};t&&(s.resultados=t);const{data:a,error:l}=yield v.from("test_sessions").update(s).eq("id",e).select().single();if(l){if(406===l.status)return{id:e,estado:"finalizado",fecha_fin:(new Date).toISOString()};throw l}return a}catch(s){if(406===s.status)return{id:e,estado:"finalizado",fecha_fin:(new Date).toISOString()};throw s}})}static cancelSession(e,s,t="Cancelado por usuario"){return o(this,null,function*(){try{const{data:s,error:a}=yield v.from("test_sessions").update({fecha_fin:(new Date).toISOString(),estado:"cancelado",updated_at:(new Date).toISOString(),resultados:{cancelled:!0,reason:t}}).eq("id",e).select().single();if(a)throw a;return s}catch(s){throw s}})}static getActiveSession(e){return o(this,null,function*(){try{const{data:s,error:t}=yield v.from("test_sessions").select("*").eq("paciente_id",e).eq("estado","iniciado").order("fecha_inicio",{ascending:!1}).limit(1).single();if(t){if("PGRST116"===t.code||406===t.status)return null;throw t}return s||null}catch(s){return s.status,null}})}static getPatientSessions(e,s=10){return o(this,null,function*(){try{const{data:t,error:a}=yield v.from("test_sessions").select("\n          *,\n          aptitudes (\n            codigo,\n            nombre\n          )\n        ").eq("paciente_id",e).order("fecha_inicio",{ascending:!1}).limit(s);if(a)throw a;return t||[]}catch(t){throw t}})}static getSessionsPendingPinConsumption(e){return o(this,null,function*(){try{const{data:s,error:t}=yield v.from("test_sessions").select("*").eq("paciente_id",e).eq("estado","finalizado").is("pin_consumed_at",null).order("fecha_fin",{ascending:!1});if(t)throw t;return s||[]}catch(s){throw s}})}static markSessionPinConsumed(e){return o(this,null,function*(){try{const{data:s,error:t}=yield v.from("test_sessions").update({pin_consumed_at:(new Date).toISOString(),updated_at:(new Date).toISOString()}).eq("id",e).select().single();if(t)throw t;return s}catch(s){throw s}})}static getSessionStats(){return o(this,arguments,function*(e={}){try{let s=v.from("test_sessions").select("*");e.pacienteId&&(s=s.eq("paciente_id",e.pacienteId)),e.estado&&(s=s.eq("estado",e.estado)),e.fechaDesde&&(s=s.gte("fecha_inicio",e.fechaDesde)),e.fechaHasta&&(s=s.lte("fecha_inicio",e.fechaHasta));const{data:t,error:a}=yield s;if(a)throw a;return{total:t.length,iniciadas:t.filter(e=>"iniciado"===e.estado).length,finalizadas:t.filter(e=>"finalizado"===e.estado).length,canceladas:t.filter(e=>"cancelado"===e.estado).length,pendientesPinConsumo:t.filter(e=>"finalizado"===e.estado&&!e.pin_consumed_at).length,conPinConsumido:t.filter(e=>e.pin_consumed_at).length}}catch(s){throw s}})}static getClientIP(){return o(this,null,function*(){try{return"127.0.0.1"}catch(e){return"unknown"}})}static cleanupOldSessions(e=30){return o(this,null,function*(){try{const s=new Date;s.setDate(s.getDate()-e);const{data:t,error:a}=yield v.from("test_sessions").delete().lt("fecha_inicio",s.toISOString()).eq("estado","cancelado").select();if(a)throw a;return(null==t?void 0:t.length)||0}catch(s){throw s}})}}const P={verbal:"V",espacial:"E",atencion:"A",razonamiento:"R",numerico:"N",mecanico:"M",ortografia:"O"},$=()=>{const e=h(),s=f(),{user:t}=y(),{selectedPatient:a,selectedLevel:l,isSessionActive:r,completedTests:i,startPatientSession:x,endPatientSession:u,markTestCompleted:g,isTestCompleted:j,updateSelectedLevel:$,hasActiveSession:D}=N(),[T,z]=m.useState([]),[A,O]=m.useState(""),[q,B]=m.useState(!1),[I,R]=m.useState([]),[M,L]=m.useState(!1),[F,V]=m.useState(null),[G,H]=m.useState(null),[W,Y]=m.useState(!1),[U,J]=m.useState(null),[K,Q]=m.useState(!1),[X,Z]=m.useState({genero:"",nivel_educativo:"",edad_min:"",edad_max:""});m.useEffect(()=>{ee()},[]),m.useEffect(()=>{a&&se(a.id)},[a]),m.useEffect(()=>{(()=>{if(!I||0===I.length)return;I.forEach(e=>{var s;if(null==(s=e.aptitudes)?void 0:s.codigo){const s=Object.keys(P).find(s=>P[s]===e.aptitudes.codigo);s&&!j(s)&&g(s)}})})()},[I]),m.useEffect(()=>{var s;if((null==(s=e.state)?void 0:s.selectedPatient)&&T.length>0){const s=e.state.selectedPatient;if(s.id){const e=T.find(e=>e.id===s.id);e&&setSelectedPatient(e)}}},[e.state,T]);const ee=()=>o(null,null,function*(){try{B(!0);const{data:e,error:s}=yield v.from("pacientes").select("\n          id,\n          nombre,\n          apellido,\n          documento,\n          email,\n          genero,\n          fecha_nacimiento,\n          nivel_educativo,\n          ocupacion\n        ").order("nombre",{ascending:!0});if(s)throw s;z(e||[])}catch(e){p.error("Error al cargar la lista de pacientes")}finally{B(!1)}}),se=e=>o(null,null,function*(){try{L(!0);const{data:s,error:t}=yield v.from("resultados").select("\n          *,\n          aptitudes:aptitud_id (\n            codigo,\n            nombre,\n            descripcion\n          )\n        ").eq("paciente_id",e).order("created_at",{ascending:!1});if(t)throw t;R(s||[])}catch(s){p.error("Error al cargar los resultados del paciente")}finally{L(!1)}}),te=e=>{if(!e)return null;const s=new Date,t=new Date(e);let a=s.getFullYear()-t.getFullYear();const l=s.getMonth()-t.getMonth();return(l<0||0===l&&s.getDate()<t.getDate())&&a--,a},ae=T.filter(e=>{const s=!A||e.nombre.toLowerCase().includes(A.toLowerCase())||e.apellido.toLowerCase().includes(A.toLowerCase())||e.documento&&e.documento.toLowerCase().includes(A.toLowerCase())||e.email&&e.email.toLowerCase().includes(A.toLowerCase()),t=!X.genero||e.genero===X.genero,a=!X.nivel_educativo||e.nivel_educativo===X.nivel_educativo,l=te(e.fecha_nacimiento),r=!X.edad_min||null!==l&&l>=parseInt(X.edad_min),i=!X.edad_max||null!==l&&l<=parseInt(X.edad_max);return s&&t&&a&&r&&i}),le=()=>{setSelectedPatient(null),O(""),R([])},re=(e,s)=>{Z(t=>c(n({},t),{[e]:s}))},ie=()=>{Z({genero:"",nivel_educativo:"",edad_min:"",edad_max:""}),O("")},ne="estudiante"===(null==t?void 0:t.tipo_usuario)||"estudiante"===(null==t?void 0:t.rol);m.useEffect(()=>{a&&ce()},[a]);const ce=()=>o(null,null,function*(){try{const e=yield k.getActiveSession(a.id);V(e),H(null==e?void 0:e.id)}catch(e){}}),oe={E:{code:"E",name:"Nivel E (Escolar)",subtitle:"Estudiantes Básicos",description:"Tests diseñados para estudiantes de educación básica y media",icon:"fas fa-graduation-cap",color:"green",bgClass:"bg-green-50",borderClass:"border-green-200",textClass:"text-green-700",iconBg:"bg-green-100",available:!0},M:{code:"M",name:"Nivel M (Media)",subtitle:"Media Vocacional",description:"Tests para estudiantes de educación media vocacional y técnica",icon:"fas fa-tools",color:"blue",bgClass:"bg-blue-50",borderClass:"border-blue-200",textClass:"text-blue-700",iconBg:"bg-blue-100",available:!1},S:{code:"S",name:"Nivel S (Superior)",subtitle:"Selección Laboral",description:"Tests para selección de personal y evaluación profesional",icon:"fas fa-briefcase",color:"purple",bgClass:"bg-purple-50",borderClass:"border-purple-200",textClass:"text-purple-700",iconBg:"bg-purple-100",available:!1}},de={E:[{id:"verbal",title:"Aptitud Verbal",description:"Evaluación de analogías verbales y comprensión de relaciones entre conceptos",time:12,questions:32,iconClass:"fas fa-comments",bgClass:"bg-blue-100",textClass:"text-blue-600",buttonColor:"blue",abbreviation:"V"},{id:"espacial",title:"Aptitud Espacial",description:"Razonamiento espacial con cubos y redes geométricas",time:15,questions:28,iconClass:"fas fa-cube",bgClass:"bg-indigo-100",textClass:"text-indigo-600",buttonColor:"indigo",abbreviation:"E"},{id:"atencion",title:"Atención",description:"Rapidez y precisión en la localización de símbolos específicos",time:8,questions:80,iconClass:"fas fa-eye",bgClass:"bg-red-100",textClass:"text-red-600",buttonColor:"red",abbreviation:"A"},{id:"razonamiento",title:"Razonamiento",description:"Continuar series lógicas de figuras y patrones",time:20,questions:32,iconClass:"fas fa-puzzle-piece",bgClass:"bg-amber-100",textClass:"text-amber-600",buttonColor:"amber",abbreviation:"R"},{id:"numerico",title:"Aptitud Numérica",description:"Resolución de igualdades, series numéricas y análisis de datos",time:20,questions:30,iconClass:"fas fa-calculator",bgClass:"bg-teal-100",textClass:"text-teal-600",buttonColor:"teal",abbreviation:"N"},{id:"mecanico",title:"Aptitud Mecánica",description:"Comprensión de principios físicos y mecánicos básicos",time:12,questions:28,iconClass:"fas fa-cogs",bgClass:"bg-slate-100",textClass:"text-slate-600",buttonColor:"slate",abbreviation:"M"},{id:"ortografia",title:"Ortografía",description:"Identificación de palabras con errores ortográficos",time:10,questions:32,iconClass:"fas fa-spell-check",bgClass:"bg-green-100",textClass:"text-green-600",buttonColor:"green",abbreviation:"O"}],M:[],S:[]};return d.jsxs("div",{children:[d.jsx(C,{title:d.jsxs("span",{children:[d.jsx("span",{className:"text-white",children:"BAT-7"})," ",d.jsx("span",{className:"text-white",children:"Batería de Aptitudes"})]}),subtitle:"Selecciona un paciente para ver sus resultados y aplicar nuevos tests",icon:b}),d.jsxs("div",{className:"container mx-auto px-4 py-8",children:[d.jsx(E,{className:"mb-6"}),d.jsxs("div",{className:"mb-8",children:[d.jsxs("div",{className:"text-center mb-6",children:[d.jsxs("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:[d.jsx("i",{className:"fas fa-layer-group mr-2 text-indigo-600"}),"Seleccionar Nivel de Evaluación"]}),d.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full mx-auto"}),d.jsx("p",{className:"text-gray-600 mt-4",children:"Elige el nivel educativo apropiado para la evaluación del paciente"})]}),d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto",children:Object.values(oe).map(e=>d.jsxs("div",{onClick:()=>e.available&&$(e.code),className:"relative p-6 rounded-xl border-2 transition-all duration-300 cursor-pointer transform hover:scale-105 "+(l===e.code?`${e.borderClass} ${e.bgClass} shadow-lg ring-2 ring-${e.color}-300`:e.available?`border-gray-200 bg-white hover:${e.bgClass} hover:${e.borderClass} shadow-md`:"border-gray-200 bg-gray-50 cursor-not-allowed opacity-60"),children:[d.jsx("div",{className:"absolute top-3 right-3",children:e.available?d.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[d.jsx("i",{className:"fas fa-check-circle mr-1"}),"Disponible"]}):d.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:[d.jsx("i",{className:"fas fa-clock mr-1"}),"En desarrollo"]})}),d.jsx("div",{className:`inline-flex items-center justify-center w-16 h-16 ${e.iconBg} rounded-full mb-4`,children:d.jsx("i",{className:`${e.icon} text-2xl ${e.textClass}`})}),d.jsxs("div",{className:"text-center",children:[d.jsxs("h3",{className:`text-lg font-bold mb-1 ${l===e.code?e.textClass:"text-gray-900"}`,children:["📗 ",e.name]}),d.jsx("p",{className:`text-sm font-medium mb-2 ${l===e.code?e.textClass:"text-gray-600"}`,children:e.subtitle}),d.jsx("p",{className:`text-sm ${l===e.code?e.textClass:"text-gray-500"}`,children:e.description})]}),l===e.code&&d.jsx("div",{className:"absolute inset-0 rounded-xl border-2 border-transparent",children:d.jsx("div",{className:`absolute top-2 left-2 w-6 h-6 ${e.iconBg} rounded-full flex items-center justify-center`,children:d.jsx("i",{className:`fas fa-check text-sm ${e.textClass}`})})}),!e.available&&d.jsx("div",{className:"mt-4 p-3 bg-gray-100 rounded-lg",children:d.jsxs("p",{className:"text-xs text-gray-600 text-center",children:[d.jsx("i",{className:"fas fa-info-circle mr-1"}),"Este nivel estará disponible próximamente"]})})]},e.code))}),l&&d.jsx("div",{className:"mt-6 max-w-3xl mx-auto",children:d.jsx("div",{className:`p-4 rounded-lg ${oe[l].bgClass} ${oe[l].borderClass} border`,children:d.jsxs("div",{className:"flex items-center justify-center",children:[d.jsx("div",{className:`w-8 h-8 ${oe[l].iconBg} rounded-full flex items-center justify-center mr-3`,children:d.jsx("i",{className:`${oe[l].icon} ${oe[l].textClass}`})}),d.jsxs("div",{className:"text-center",children:[d.jsxs("p",{className:`font-medium ${oe[l].textClass}`,children:["Nivel seleccionado: ",oe[l].name]}),d.jsxs("p",{className:`text-sm ${oe[l].textClass} opacity-80`,children:[de[l].length," tests disponibles para este nivel"]})]})]})})})]}),d.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 mb-8",children:[d.jsxs("div",{className:"flex items-center justify-between mb-6",children:[d.jsxs("h2",{className:"text-2xl font-bold text-gray-800",children:[d.jsx("i",{className:"fas fa-search mr-3 text-blue-600"}),"Buscar Paciente"]}),d.jsxs("button",{onClick:()=>Q(!K),className:"flex items-center px-4 py-2 rounded-lg transition-all duration-200 "+(K?"bg-blue-600 text-white shadow-md":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:[d.jsx("i",{className:"fas fa-filter mr-2 "+(K?"text-white":"text-gray-500")}),K?"Ocultar Filtros":"Filtros Avanzados"]})]}),d.jsxs("div",{className:"relative mb-4",children:[d.jsx("input",{type:"text",placeholder:"Buscar por nombre, apellido, documento o email...",value:A,onChange:e=>O(e.target.value),className:"w-full px-4 py-3 pl-12 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-700 placeholder-gray-400"}),d.jsx("i",{className:"fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"}),(A||Object.values(X).some(e=>e))&&d.jsx("button",{onClick:()=>{le(),ie()},className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200",title:"Limpiar búsqueda y filtros",children:d.jsx("i",{className:"fas fa-times"})})]}),K&&d.jsxs("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 mb-4 border border-blue-100",children:[d.jsxs("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:[d.jsx("i",{className:"fas fa-sliders-h mr-2 text-blue-600"}),"Filtros Avanzados"]}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[d.jsxs("div",{children:[d.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[d.jsx("i",{className:"fas fa-venus-mars mr-1 text-pink-500"}),"Género"]}),d.jsxs("select",{value:X.genero,onChange:e=>re("genero",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsx("option",{value:"",children:"Todos"}),d.jsx("option",{value:"masculino",children:"Masculino"}),d.jsx("option",{value:"femenino",children:"Femenino"}),d.jsx("option",{value:"otro",children:"Otro"})]})]}),d.jsxs("div",{children:[d.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[d.jsx("i",{className:"fas fa-graduation-cap mr-1 text-green-500"}),"Nivel Educativo"]}),d.jsxs("select",{value:X.nivel_educativo,onChange:e=>re("nivel_educativo",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsx("option",{value:"",children:"Todos"}),d.jsx("option",{value:"primaria",children:"Primaria"}),d.jsx("option",{value:"secundaria",children:"Secundaria"}),d.jsx("option",{value:"bachillerato",children:"Bachillerato"}),d.jsx("option",{value:"tecnico",children:"Técnico"}),d.jsx("option",{value:"universitario",children:"Universitario"}),d.jsx("option",{value:"posgrado",children:"Posgrado"})]})]}),d.jsxs("div",{children:[d.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[d.jsx("i",{className:"fas fa-calendar-alt mr-1 text-orange-500"}),"Edad Mínima"]}),d.jsx("input",{type:"number",placeholder:"Ej: 18",value:X.edad_min,onChange:e=>re("edad_min",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"120"})]}),d.jsxs("div",{children:[d.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[d.jsx("i",{className:"fas fa-calendar-check mr-1 text-purple-500"}),"Edad Máxima"]}),d.jsx("input",{type:"number",placeholder:"Ej: 65",value:X.edad_max,onChange:e=>re("edad_max",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"120"})]})]}),d.jsx("div",{className:"flex justify-end mt-4",children:d.jsxs("button",{onClick:ie,className:"flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200",children:[d.jsx("i",{className:"fas fa-eraser mr-2"}),"Limpiar Filtros"]})})]}),(A||Object.values(X).some(e=>e))&&d.jsx("div",{className:"mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200",children:d.jsxs("p",{className:"text-sm text-blue-700",children:[d.jsx("i",{className:"fas fa-info-circle mr-2"}),"Se encontraron ",d.jsx("span",{className:"font-semibold",children:ae.length})," paciente(s) que coinciden con los criterios de búsqueda"]})}),A&&!a&&d.jsx("div",{className:"mt-4 max-h-60 overflow-y-auto border border-gray-200 rounded-lg",children:q?d.jsxs("div",{className:"p-4 text-center",children:[d.jsx("i",{className:"fas fa-spinner fa-spin mr-2"}),"Cargando pacientes..."]}):ae.length>0?ae.map(e=>d.jsx("div",{onClick:()=>(e=>{x(e,l),O(`${e.nombre} ${e.apellido}`),p.success(`Paciente ${e.nombre} ${e.apellido} seleccionado para evaluación`)})(e),className:"p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsxs("p",{className:"font-medium text-gray-900",children:[e.nombre," ",e.apellido]}),d.jsxs("p",{className:"text-sm text-gray-500",children:[e.documento&&`Doc: ${e.documento}`,e.email&&` • ${e.email}`]})]}),d.jsx("i",{className:"fas fa-chevron-right text-gray-400"})]})},e.id)):d.jsx("div",{className:"p-4 text-center text-gray-500",children:"No se encontraron pacientes que coincidan con la búsqueda"})})]}),a&&d.jsxs("div",{className:"mb-8",children:[d.jsxs("div",{className:"text-center mb-6",children:[d.jsxs("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:[d.jsx("i",{className:"fas fa-user-check mr-2 text-green-600"}),"Paciente Seleccionado"]}),d.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-green-500 to-blue-600 rounded-full mx-auto"})]}),d.jsxs("div",{className:"bg-gradient-to-br from-white to-blue-50 rounded-2xl shadow-xl border border-blue-100 overflow-hidden max-w-5xl mx-auto",children:[d.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("h3",{className:"text-xl font-bold text-white flex items-center",children:[d.jsx("div",{className:"w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3",children:d.jsx("i",{className:"fas fa-user text-white"})}),"Información del Paciente"]}),d.jsx("button",{onClick:le,className:"text-white hover:text-red-200 transition-colors duration-200 p-2 rounded-full hover:bg-white hover:bg-opacity-10",title:"Deseleccionar paciente",children:d.jsx("i",{className:"fas fa-times"})})]})}),d.jsxs("div",{className:"p-6",children:[d.jsxs("div",{className:"flex items-start space-x-6 mb-6",children:[d.jsx("div",{className:"flex-shrink-0",children:d.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg",children:d.jsxs("span",{className:"text-2xl font-bold text-white",children:[a.nombre.charAt(0),a.apellido.charAt(0)]})})}),d.jsxs("div",{className:"flex-1",children:[d.jsxs("h4",{className:"text-2xl font-bold text-gray-800 mb-1",children:[a.nombre," ",a.apellido]}),d.jsxs("p",{className:"text-gray-600 flex items-center",children:[d.jsx("i",{className:"fas fa-id-card mr-2 text-blue-500"}),a.documento]}),d.jsxs("p",{className:"text-gray-600 flex items-center mt-1",children:[d.jsx("i",{className:"fas fa-envelope mr-2 text-green-500"}),a.email]})]}),d.jsx("div",{className:"text-right",children:d.jsxs("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[d.jsx("i",{className:"fas fa-check-circle mr-1"}),"Seleccionado"]})})]}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[d.jsxs("div",{className:"bg-white rounded-xl p-4 shadow-sm border border-gray-100",children:[d.jsxs("div",{className:"flex items-center mb-2",children:[d.jsx("div",{className:"w-8 h-8 bg-pink-100 rounded-lg flex items-center justify-center mr-3",children:d.jsx("i",{className:"fas fa-venus-mars text-pink-600"})}),d.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Género"})]}),d.jsx("p",{className:"text-lg font-semibold text-gray-800 capitalize",children:a.genero})]}),d.jsxs("div",{className:"bg-white rounded-xl p-4 shadow-sm border border-gray-100",children:[d.jsxs("div",{className:"flex items-center mb-2",children:[d.jsx("div",{className:"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3",children:d.jsx("i",{className:"fas fa-birthday-cake text-orange-600"})}),d.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Edad"})]}),d.jsxs("p",{className:"text-lg font-semibold text-gray-800",children:[te(a.fecha_nacimiento)," años"]}),d.jsx("p",{className:"text-xs text-gray-500 mt-1",children:new Date(a.fecha_nacimiento).toLocaleDateString("es-ES")})]}),d.jsxs("div",{className:"bg-white rounded-xl p-4 shadow-sm border border-gray-100",children:[d.jsxs("div",{className:"flex items-center mb-2",children:[d.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3",children:d.jsx("i",{className:"fas fa-graduation-cap text-green-600"})}),d.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Educación"})]}),d.jsx("p",{className:"text-lg font-semibold text-gray-800 capitalize",children:a.nivel_educativo})]}),d.jsxs("div",{className:"bg-white rounded-xl p-4 shadow-sm border border-gray-100 md:col-span-2 lg:col-span-3",children:[d.jsxs("div",{className:"flex items-center mb-2",children:[d.jsx("div",{className:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3",children:d.jsx("i",{className:"fas fa-briefcase text-purple-600"})}),d.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Ocupación"})]}),d.jsx("p",{className:"text-lg font-semibold text-gray-800",children:a.ocupacion||"No especificada"})]})]})]})]})]}),a&&d.jsxs("div",{className:"mb-8",children:[d.jsxs("div",{className:"text-center mb-6",children:[d.jsxs("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:[d.jsx("i",{className:"fas fa-chart-bar mr-2 text-purple-600"}),"Resultados de Tests Aplicados"]}),d.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full mx-auto"})]}),d.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 max-w-6xl mx-auto overflow-hidden",children:M?d.jsxs("div",{className:"text-center py-12",children:[d.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4",children:d.jsx("i",{className:"fas fa-spinner fa-spin text-purple-600 text-xl"})}),d.jsx("p",{className:"text-gray-600 font-medium",children:"Cargando resultados..."})]}):I.length>0?d.jsx("div",{className:"p-6",children:d.jsx("div",{className:"mb-8",children:d.jsxs("div",{className:"bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200",children:[d.jsxs("h3",{className:"text-lg font-semibold text-gray-800 mb-4 text-center",children:[d.jsx("i",{className:"fas fa-chart-pie mr-2 text-purple-600"}),"Progreso de Evaluación"]}),d.jsx(_,{completedTests:I,allTests:de[l]||[],totalTime:I.reduce((e,s)=>e+(s.tiempo_segundos||0),0)})]})})}):d.jsxs("div",{className:"text-center py-12",children:[d.jsx("div",{className:"inline-flex items-center justify-center w-20 h-20 bg-gray-100 rounded-full mb-4",children:d.jsx("i",{className:"fas fa-clipboard-check text-3xl text-gray-400"})}),d.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Sin resultados registrados"}),d.jsx("p",{className:"text-gray-500 mb-4",children:"Este paciente no tiene resultados de tests registrados"}),d.jsx("p",{className:"text-sm text-gray-400",children:"Aplica tests usando las opciones de abajo"})]})})]}),d.jsxs("div",{className:"mb-8",children:[d.jsxs("div",{className:"text-center mb-6",children:[d.jsxs("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:[d.jsx("i",{className:"fas fa-clipboard-list mr-2 text-blue-600"}),"Tests Disponibles - ",oe[l].name]}),d.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto"}),d.jsx("p",{className:"text-gray-600 mt-4",children:oe[l].description})]}),a&&oe[l].available&&(()=>{const e=(()=>{var e;const s=(null==(e=de[l])?void 0:e.length)||0,t=i.length||0;return{total:s,completed:t,pending:s-t,percentage:s>0?Math.round(t/s*100):0}})();return d.jsxs("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-200 max-w-5xl mx-auto",children:[d.jsxs("div",{className:"text-center mb-6",children:[d.jsxs("h3",{className:"text-xl font-bold text-gray-800 mb-2",children:[d.jsx("i",{className:"fas fa-chart-pie mr-2 text-blue-600"}),"Progreso de Evaluación"]}),d.jsx("p",{className:"text-blue-700 font-medium",children:(s=e.percentage,0===s?"¡Comienza tu evaluación! Selecciona un test para empezar.":s<25?"¡Buen comienzo! Continúa con los siguientes tests.":s<50?"¡Vas por buen camino! Ya completaste una cuarta parte.":s<75?"¡Excelente progreso! Estás a mitad de camino.":s<100?"¡Casi terminas! Solo faltan algunos tests más.":"¡Felicitaciones! Has completado todos los tests disponibles.")})]}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[d.jsxs("div",{className:"bg-white rounded-lg p-4 text-center shadow-sm border border-green-200",children:[d.jsxs("div",{className:"flex items-center justify-center mb-2",children:[d.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-2",children:d.jsx("i",{className:"fas fa-check text-green-600"})}),d.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Completados"})]}),d.jsx("div",{className:"text-2xl font-bold text-green-600",children:e.completed})]}),d.jsxs("div",{className:"bg-white rounded-lg p-4 text-center shadow-sm border border-orange-200",children:[d.jsxs("div",{className:"flex items-center justify-center mb-2",children:[d.jsx("div",{className:"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-2",children:d.jsx("i",{className:"fas fa-clock text-orange-600"})}),d.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Pendientes"})]}),d.jsx("div",{className:"text-2xl font-bold text-orange-600",children:e.pending})]}),d.jsxs("div",{className:"bg-white rounded-lg p-4 text-center shadow-sm border border-blue-200",children:[d.jsxs("div",{className:"flex items-center justify-center mb-2",children:[d.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2",children:d.jsx("i",{className:"fas fa-list text-blue-600"})}),d.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Total"})]}),d.jsx("div",{className:"text-2xl font-bold text-blue-600",children:e.total})]})]}),d.jsxs("div",{className:"mb-4",children:[d.jsxs("div",{className:"flex justify-between items-center mb-2",children:[d.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Progreso General"}),d.jsxs("span",{className:"text-sm font-bold text-gray-800",children:[e.percentage,"%"]})]}),d.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:d.jsx("div",{className:"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-500 ease-out",style:{width:`${e.percentage}%`}})})]}),d.jsx("div",{className:"text-center",children:d.jsxs("div",{className:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium "+(100===e.percentage?"bg-green-100 text-green-800 border border-green-200":e.percentage>0?"bg-blue-100 text-blue-800 border border-blue-200":"bg-gray-100 text-gray-800 border border-gray-200"),children:[d.jsx("i",{className:"mr-2 "+(100===e.percentage?"fas fa-trophy":e.percentage>0?"fas fa-play-circle":"fas fa-info-circle")}),100===e.percentage?"¡Evaluación Completa!":e.percentage>0?`${e.completed} de ${e.total} tests completados`:"Listo para comenzar"]})})]});var s})(),!a&&d.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 max-w-4xl mx-auto",children:d.jsxs("div",{className:"flex items-center justify-center",children:[d.jsx("i",{className:"fas fa-info-circle text-yellow-600 mr-2"}),d.jsx("p",{className:"text-yellow-800",children:"Selecciona un paciente para poder aplicar los tests y guardar los resultados"})]})}),!oe[l].available&&d.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-6 mb-6 max-w-4xl mx-auto",children:d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-4",children:d.jsx("i",{className:"fas fa-tools text-orange-600 text-2xl"})}),d.jsx("h3",{className:"text-lg font-semibold text-orange-800 mb-2",children:"Nivel en Desarrollo"}),d.jsxs("p",{className:"text-orange-700 mb-4",children:["Los tests para ",oe[l].name," están actualmente en desarrollo."]}),d.jsx("p",{className:"text-sm text-orange-600",children:"Por favor, selecciona el Nivel E (Escolar) que está completamente disponible."})]})}),oe[l].available&&d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 auto-rows-fr max-w-7xl mx-auto",children:de[l].map(e=>d.jsx(w,{test:e,iconClass:e.iconClass,bgClass:e.bgClass,textClass:e.textClass,buttonColor:e.buttonColor,abbreviation:e.abbreviation,showButton:!!a,disabled:!a,patientId:null==a?void 0:a.id,level:l,isCompleted:j(e.id),onRepeatTest:()=>(e=>{J(e),Y(!0)})(e)},e.id))}),a&&I.length>0&&d.jsxs("div",{className:"mt-8",children:[d.jsx(S,{results:I,completedTests:i,selectedLevel:l}),d.jsx("div",{className:"mt-8 text-center",children:d.jsxs("div",{className:"bg-gradient-to-r from-red-50 to-orange-50 rounded-xl p-6 border border-red-200",children:[d.jsxs("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:[d.jsx("i",{className:"fas fa-flag-checkered mr-2 text-red-600"}),"Finalizar Evaluación"]}),d.jsx("p",{className:"text-gray-600 mb-4",children:ne?"Cuando hayas completado todos los tests que necesites, puedes finalizar tu evaluación.":"Finaliza la evaluación del paciente cuando consideres que ha completado los tests necesarios."}),d.jsxs("div",{className:"bg-white rounded-lg p-4 mb-4 border border-gray-200",children:[d.jsxs("div",{className:"text-sm text-gray-600 mb-2",children:[d.jsx("strong",{children:"Paciente:"})," ",a.nombre," ",a.apellido]}),a.documento&&d.jsxs("div",{className:"text-sm text-gray-600 mb-2",children:[d.jsx("strong",{children:"Documento:"})," ",a.documento]}),F&&d.jsxs("div",{className:"text-sm text-gray-600",children:[d.jsx("strong",{children:"Sesión activa:"})," ",new Date(F.fecha_inicio).toLocaleString()]})]}),d.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4",children:d.jsxs("p",{className:"text-yellow-800 text-sm",children:[d.jsx("strong",{children:"Importante:"})," Al finalizar la evaluación, se cerrará la sesión actual. Asegúrate de que el paciente haya completado todos los tests necesarios."]})}),d.jsxs("button",{onClick:()=>o(null,null,function*(){if(!a)return;if(window.confirm("¿Estás seguro de que deseas terminar la evaluación completa? Esta acción cerrará la sesión actual."))try{const{error:e}=yield v.from("pacientes").update({evaluacion_finalizada:!0,fecha_finalizacion:(new Date).toISOString(),updated_at:(new Date).toISOString()}).eq("id",a.id);if(e)throw e;F&&(yield k.finishSession(F.id,t)),V(null),H(null),p.success("Evaluación finalizada correctamente. Ahora puedes generar el informe desde la sección de Resultados."),u(),O(""),R([]),ne&&s("/home")}catch(e){p.error("Error al finalizar la evaluación: "+e.message)}}),className:"px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium",children:[d.jsx("i",{className:"fas fa-stop-circle mr-2"}),"Terminar Prueba"]}),d.jsx("div",{className:"mt-4 text-xs text-gray-500",children:d.jsxs("p",{children:["Después de finalizar, podrás generar informes desde la sección de Resultados.",!ne&&" También podrás seleccionar otro paciente para evaluar."]})})]})})]})]})]}),W&&U&&d.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:d.jsxs("div",{className:"bg-white rounded-xl p-6 max-w-md mx-4 shadow-2xl",children:[d.jsxs("div",{className:"text-center mb-6",children:[d.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4",children:d.jsx("i",{className:"fas fa-exclamation-triangle text-orange-600 text-2xl"})}),d.jsx("h3",{className:"text-xl font-bold text-gray-800 mb-2",children:"Repetir Test"}),d.jsxs("p",{className:"text-gray-600",children:["¿Estás seguro de que deseas repetir el test de ",d.jsx("strong",{children:U.title}),"?"]})]}),d.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:d.jsxs("div",{className:"flex items-start",children:[d.jsx("i",{className:"fas fa-info-circle text-yellow-600 mr-2 mt-0.5"}),d.jsxs("div",{className:"text-sm text-yellow-800",children:[d.jsx("p",{className:"font-medium mb-1",children:"Importante:"}),d.jsx("p",{children:"El resultado anterior será eliminado y sobrescrito con el nuevo resultado."})]})]})}),d.jsxs("div",{className:"flex space-x-3",children:[d.jsx("button",{onClick:()=>{Y(!1),J(null)},className:"flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors font-medium",children:"Cancelar"}),d.jsxs("button",{onClick:()=>o(null,null,function*(){var e;if(U&&a)try{const t=yield(e=U.id,o(null,null,function*(){try{const s=P[e];if(!s)throw new Error(`Tipo de test no reconocido: ${e}`);const{data:t,error:a}=yield v.from("aptitudes").select("id").eq("codigo",s).single();if(a)throw a;return t.id}catch(l){throw l}})),{error:l}=yield v.from("resultados").delete().eq("paciente_id",a.id).eq("aptitud_id",t);if(l)throw l;yield se(a.id),p.success("Resultado anterior eliminado. Puedes realizar el test nuevamente."),s(`/test/instructions/${U.id}`,{state:{patientId:a.id}})}catch(t){p.error("Error al preparar la repetición del test")}finally{Y(!1),J(null)}}),className:"flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium",children:[d.jsx("i",{className:"fas fa-redo mr-2"}),"Repetir Test"]})]})]})})]})};export{$ as default};
