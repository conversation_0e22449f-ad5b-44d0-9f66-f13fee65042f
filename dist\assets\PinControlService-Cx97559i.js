const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/EnhancedNotificationService-heJqBBu8.js","assets/index-Bdl1jgS_.js","assets/vendor-BqMjyOVw.js","assets/vendor-B4zyQOk2.css","assets/index-Csy2uUlu.css","assets/PinLogger-C2v3yGM1.js","assets/pinNotifications-BMRChPcj.js"])))=>i.map(i=>d[i]);
var t=(t,i,n)=>new Promise((s,r)=>{var o=t=>{try{l(n.next(t))}catch(i){r(i)}},e=t=>{try{l(n.throw(t))}catch(i){r(i)}},l=t=>t.done?s(t.value):Promise.resolve(t.value).then(o,e);l((n=n.apply(t,i)).next())});import{_ as i}from"./vendor-BqMjyOVw.js";import{s as n}from"./index-Bdl1jgS_.js";import{a as s,P as r}from"./PinLogger-C2v3yGM1.js";import{P as o,N as e,a as l}from"./NotificationService-DiDbKBbI.js";const a=new class{constructor(){this.repository=new o,this.notificationService=new e}assignPins(n,o){return t(this,arguments,function*(n,o,e=!1,a=s.PLAN_TYPES.ASSIGNED){const c=l.validateAssignPins(n,o,e,a);if(!c.isValid)throw new Error(`Validation failed: ${c.errors.join(", ")}`);try{r.logInfo("Assigning pins",{psychologistId:n,pins:o,isUnlimited:e,planType:a});const l=yield this.repository.upsertPsychologistUsage(n,o,e,a);if(yield r.logAction(n,s.ACTION_TYPES.PIN_ASSIGNED,{pins_assigned:o,is_unlimited:e,plan_type:a}),!e&&o>0)try{const s=(yield i(()=>t(this,null,function*(){const{default:t}=yield import("./EnhancedNotificationService-heJqBBu8.js");return{default:t}}),__vite__mapDeps([0,1,2,3,4,5,6]))).default;yield s.createPinAssignmentNotification(n,o,l.total_uses||o)}catch(u){r.logError("Error creating assignment notification",u)}return r.logSuccess("Pins assigned successfully"),l}catch(d){throw r.logError("Error assigning pins",d),d}})}consumePin(i,n=null,o=null,e=null){return t(this,null,function*(){const t=l.validateConsumePin(i,n,o,e);if(!t.isValid)throw new Error(`Validation failed: ${t.errors.join(", ")}`);try{r.logInfo("Consuming pin",{psychologistId:i,patientId:n,testSessionId:o,reportId:e});const t=yield this.repository.getPsychologistUsage(i);if(!t)throw new Error(s.ERROR_CODES.PSYCHOLOGIST_NOT_FOUND);return t.is_unlimited?yield this._handleUnlimitedPinConsumption(i,n,o,e):yield this._handleLimitedPinConsumption(t,i,n,o,e)}catch(a){throw r.logError("Error consuming pin",a),a}})}checkPsychologistUsage(i){return t(this,null,function*(){try{const t=yield this.repository.getPsychologistUsage(i);if(!t)return{canUse:!1,reason:"No pins assigned",remainingPins:0,isUnlimited:!1};if(t.is_unlimited)return{canUse:!0,reason:"Unlimited plan",remainingPins:null,isUnlimited:!0};const n=t.total_uses-t.used_uses;return{canUse:n>0,reason:n>0?"Pins available":"No pins available",remainingPins:n,isUnlimited:!1,totalPins:t.total_uses,usedPins:t.used_uses}}catch(t){throw r.logError("Error checking psychologist usage",t),t}})}getPinConsumptionStats(){return t(this,null,function*(){try{r.logInfo("Getting pin consumption stats");const{data:t,error:i}=yield n.rpc("get_pin_consumption_stats");if(i)throw i;return r.logSuccess(`Pin stats retrieved: ${(null==t?void 0:t.length)||0} psychologists`),t||[]}catch(t){throw r.logError("Error getting pin consumption stats",t),t}})}getPinUsageHistory(){return t(this,arguments,function*(t=null,i=s.DEFAULT_HISTORY_LIMIT){try{return yield this.repository.getPinUsageHistory(t,i)}catch(n){throw r.logError("Error getting pin usage history",n),n}})}getPinConsumptionAlerts(){return t(this,null,function*(){try{const{data:t,error:i}=yield n.rpc("get_pin_consumption_alerts");if(i)throw i;return t||[]}catch(t){throw r.logError("Error getting pin consumption alerts",t),t}})}getSystemSummary(){return t(this,null,function*(){try{const{data:t,error:i}=yield n.rpc("get_system_usage_summary");if(i)throw i;return t||{}}catch(t){throw r.logError("Error getting system summary",t),t}})}_handleUnlimitedPinConsumption(i,n,o,e){return t(this,null,function*(){return yield r.logAction(i,s.ACTION_TYPES.PIN_CONSUMED,{patient_id:n,test_session_id:o,report_id:e,is_unlimited:!0},n,o,e),r.logSuccess("Pin consumed (unlimited plan)"),!0})}_handleLimitedPinConsumption(n,o,e,l,a){return t(this,null,function*(){const c=n.total_uses-n.used_uses;if(c<=0)throw new Error(s.ERROR_CODES.NO_PINS_AVAILABLE);try{yield this.repository.incrementUsedPins(n.id)}catch(d){if("P0001"===(null==d?void 0:d.code))throw new Error(s.ERROR_CODES.NO_PINS_AVAILABLE);throw d}yield r.logAction(o,s.ACTION_TYPES.PIN_CONSUMED,{pins_before:c,pins_after:c-1,patient_id:e,test_session_id:l,report_id:a},e,l,a);const u=c-1;if(u<=s.THRESHOLDS.LOW_PIN_WARNING)try{const n=(yield i(()=>t(this,null,function*(){const{default:t}=yield import("./EnhancedNotificationService-heJqBBu8.js");return{default:t}}),__vite__mapDeps([0,1,2,3,4,5,6]))).default;yield n.createPinConsumptionNotification(o,u,{patientId:e,testSessionId:l,reportId:a})}catch(g){r.logError("Error creating consumption notification",g)}return r.logSuccess(`Pin consumed. Remaining pins: ${u}`),!0})}};export{a as default};
