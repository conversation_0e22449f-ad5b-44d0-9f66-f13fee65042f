const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/PinControlService-Cx97559i.js","assets/vendor-BqMjyOVw.js","assets/vendor-B4zyQOk2.css","assets/index-Bdl1jgS_.js","assets/index-Csy2uUlu.css","assets/PinLogger-C2v3yGM1.js","assets/NotificationService-DiDbKBbI.js"])))=>i.map(i=>d[i]);
var t,e=Object.defineProperty,r=(t,e,r)=>new Promise((o,s)=>{var n=t=>{try{a(r.next(t))}catch(e){s(e)}},i=t=>{try{a(r.throw(t))}catch(e){s(e)}},a=t=>t.done?o(t.value):Promise.resolve(t.value).then(n,i);a((r=r.apply(t,e)).next())});import{_ as o}from"./vendor-BqMjyOVw.js";import{P as s}from"./PinLogger-C2v3yGM1.js";import{PinValidationService as n}from"./PinValidationService-Ki4hIVgd.js";import"./index-Bdl1jgS_.js";import i from"./InformesService-D-fQ1856.js";import"./ImprovedPinControlService-BUPGzexy.js";import"./NotificationService-DiDbKBbI.js";class a{static processBatchReports(t,e){return r(this,arguments,function*(t,e,r={}){const{strategy:o=this.BATCH_STRATEGIES.INDIVIDUAL,title:i=null,description:a=null,incluirInterpretaciones:l=!0,maxConcurrent:c=3,continueOnError:u=!0}=r;try{s.logInfo("Starting batch processing",{psychologistId:t,patientCount:e.length,strategy:o});const i=yield n.validateBatchReportGeneration(t,e);if(!i.canProceed)throw new Error(`Validación de lote falló: ${i.userMessage}`);switch(o){case this.BATCH_STRATEGIES.INDIVIDUAL:return yield this._processIndividualStrategy(t,e,r);case this.BATCH_STRATEGIES.BULK_UPFRONT:return yield this._processBulkUpfrontStrategy(t,e,r);case this.BATCH_STRATEGIES.BULK_SUCCESS:return yield this._processBulkSuccessStrategy(t,e,r);case this.BATCH_STRATEGIES.OPTIMISTIC:return yield this._processOptimisticStrategy(t,e,r);default:throw new Error(`Estrategia no soportada: ${o}`)}}catch(d){throw s.logError("Error in batch processing",d),d}})}static _processIndividualStrategy(t,e,o){return r(this,null,function*(){const r={strategy:this.BATCH_STRATEGIES.INDIVIDUAL,successful:[],failed:[],totalProcessed:0,totalSuccessful:0,totalFailed:0,pinsConsumed:0,startTime:(new Date).toISOString()};for(const l of e)try{r.totalProcessed++;const e=yield n.validateReportGeneration(t,1);if(!e.canProceed){r.failed.push({patientId:l,error:e.userMessage,reason:"validation_failed",timestamp:(new Date).toISOString()}),r.totalFailed++;continue}const s=yield i.generarInformeCompleto(l,o.title,o.description,o.incluirInterpretaciones,!1);r.successful.push({patientId:l,informeId:s,timestamp:(new Date).toISOString()}),r.totalSuccessful++,r.pinsConsumed++}catch(a){if(s.logError(`Error processing patient ${l}`,a),r.failed.push({patientId:l,error:a.message,reason:"processing_error",timestamp:(new Date).toISOString()}),r.totalFailed++,!o.continueOnError)break}return r.endTime=(new Date).toISOString(),r.duration=new Date(r.endTime)-new Date(r.startTime),s.logInfo("Individual strategy completed",{successful:r.totalSuccessful,failed:r.totalFailed,pinsConsumed:r.pinsConsumed}),r})}static _processBulkUpfrontStrategy(t,e,n){return r(this,null,function*(){const a={strategy:this.BATCH_STRATEGIES.BULK_UPFRONT,successful:[],failed:[],totalProcessed:0,totalSuccessful:0,totalFailed:0,pinsConsumed:e.length,startTime:(new Date).toISOString()};try{const c=(yield o(()=>r(this,null,function*(){const{default:t}=yield import("./PinControlService-Cx97559i.js");return{default:t}}),__vite__mapDeps([0,1,2,3,4,5,6]))).default;for(let r=0;r<e.length;r++)yield c.consumePin(t,e[r],null,null);for(const t of e)try{a.totalProcessed++;const e=yield i.generarInformeCompleto(t,n.title,n.description,n.incluirInterpretaciones,!0);a.successful.push({patientId:t,informeId:e,timestamp:(new Date).toISOString()}),a.totalSuccessful++}catch(l){s.logError(`Error processing patient ${t}`,l),a.failed.push({patientId:t,error:l.message,reason:"processing_error",timestamp:(new Date).toISOString()}),a.totalFailed++}}catch(l){throw s.logError("Error in bulk upfront strategy",l),l}return a.endTime=(new Date).toISOString(),a.duration=new Date(a.endTime)-new Date(a.startTime),a})}static _processBulkSuccessStrategy(t,e,n){return r(this,null,function*(){const a={strategy:this.BATCH_STRATEGIES.BULK_SUCCESS,successful:[],failed:[],totalProcessed:0,totalSuccessful:0,totalFailed:0,pinsConsumed:0,startTime:(new Date).toISOString()};for(const t of e)try{a.totalProcessed++;const e=yield i.generarInformeCompleto(t,n.title,n.description,n.incluirInterpretaciones,!0);a.successful.push({patientId:t,informeId:e,timestamp:(new Date).toISOString()}),a.totalSuccessful++}catch(l){s.logError(`Error processing patient ${t}`,l),a.failed.push({patientId:t,error:l.message,reason:"processing_error",timestamp:(new Date).toISOString()}),a.totalFailed++}if(a.totalSuccessful>0)try{const e=(yield o(()=>r(this,null,function*(){const{default:t}=yield import("./PinControlService-Cx97559i.js");return{default:t}}),__vite__mapDeps([0,1,2,3,4,5,6]))).default;for(const r of a.successful)yield e.consumePin(t,r.patientId,null,r.informeId),a.pinsConsumed++}catch(l){s.logError("Error consuming pins for successful reports",l)}return a.endTime=(new Date).toISOString(),a.duration=new Date(a.endTime)-new Date(a.startTime),a})}static _processOptimisticStrategy(t,e,a){return r(this,null,function*(){const l={strategy:this.BATCH_STRATEGIES.OPTIMISTIC,successful:[],failed:[],totalProcessed:0,totalSuccessful:0,totalFailed:0,pinsConsumed:0,rollbackPerformed:!1,startTime:(new Date).toISOString()},c=[];try{for(const t of e)try{l.totalProcessed++;const e=yield i.generarInformeCompleto(t,a.title,a.description,a.incluirInterpretaciones,!0);c.push({patientId:t,informeId:e}),l.successful.push({patientId:t,informeId:e,timestamp:(new Date).toISOString()}),l.totalSuccessful++}catch(u){l.failed.push({patientId:t,error:u.message,reason:"processing_error",timestamp:(new Date).toISOString()}),l.totalFailed++}const f=yield n.validateReportGeneration(t,l.totalSuccessful);if(!f.canProceed){l.rollbackPerformed=!0;for(const t of c)try{s.logInfo(`Rolling back report ${t.informeId}`)}catch(d){s.logError("Error during rollback",d)}throw new Error(`Rollback realizado: ${f.userMessage}`)}{const e=(yield o(()=>r(this,null,function*(){const{default:t}=yield import("./PinControlService-Cx97559i.js");return{default:t}}),__vite__mapDeps([0,1,2,3,4,5,6]))).default;for(const r of l.successful)yield e.consumePin(t,r.patientId,null,r.informeId),l.pinsConsumed++}}catch(u){if(!l.rollbackPerformed)for(const t of c)try{s.logInfo(`Emergency rollback for report ${t.informeId}`)}catch(d){s.logError("Error during emergency rollback",d)}throw u}return l.endTime=(new Date).toISOString(),l.duration=new Date(l.endTime)-new Date(l.startTime),l})}static getRecommendedStrategy(t,e,r={}){const{reliability:o=.95,riskTolerance:s="medium"}=r;return t<=5?this.BATCH_STRATEGIES.INDIVIDUAL:e<1.2*t?this.BATCH_STRATEGIES.BULK_SUCCESS:o>.9&&"high"===s?this.BATCH_STRATEGIES.OPTIMISTIC:e>2*t?this.BATCH_STRATEGIES.BULK_UPFRONT:this.BATCH_STRATEGIES.INDIVIDUAL}}((t,r,o)=>{r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[r]=o})(a,"symbol"!=typeof(t="BATCH_STRATEGIES")?t+"":t,{INDIVIDUAL:"individual",BULK_UPFRONT:"bulk_upfront",BULK_SUCCESS:"bulk_success",OPTIMISTIC:"optimistic"});export{a as BatchProcessingService,a as default};
