var e=(e,a,s)=>new Promise((l,i)=>{var t=e=>{try{r(s.next(e))}catch(a){i(a)}},n=e=>{try{r(s.throw(e))}catch(a){i(a)}},r=e=>e.done?l(e.value):Promise.resolve(e.value).then(t,n);r((s=s.apply(e,a)).next())});import{E as a,u as s,r as l,j as i,Q as t}from"./vendor-BqMjyOVw.js";import{C as n,a as r,B as d,b as c,s as o,c as m}from"./index-Bdl1jgS_.js";import{o as x}from"./interpretacionesAptitudes-Bt_sak-B.js";const p=()=>{const{patientId:p}=a(),u=s(),[h,g]=l.useState(null),[b,j]=l.useState([]),[f,v]=l.useState(!0),[N,y]=l.useState(!1);l.useEffect(()=>{p&&e(null,null,function*(){try{v(!0);const{data:e,error:a}=yield o.from("pacientes").select("*").eq("id",p).single();if(a)throw a;const{data:s,error:l}=yield o.from("resultados").select("\n            id,\n            puntaje_directo,\n            percentil,\n            errores,\n            tiempo_segundos,\n            concentracion,\n            created_at,\n            aptitudes:aptitud_id (\n              codigo,\n              nombre,\n              descripcion\n            )\n          ").eq("paciente_id",p).order("created_at",{ascending:!1});if(l)throw l;g(e),j(s||[])}catch(e){t.error("Error al cargar los datos del paciente"),u("/admin/reports")}finally{v(!1)}})},[p,u]);const w=e=>{if(!e)return"N/A";const a=new Date,s=new Date(e);let l=a.getFullYear()-s.getFullYear();const i=a.getMonth()-s.getMonth();return(i<0||0===i&&a.getDate()<s.getDate())&&l--,l};if(f)return i.jsx("div",{className:"container mx-auto py-6",children:i.jsxs("div",{className:"py-16 text-center",children:[i.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),i.jsx("p",{className:"text-gray-500",children:"Cargando informe completo..."})]})});if(!h)return i.jsx("div",{className:"container mx-auto py-6",children:i.jsx(n,{children:i.jsx(r,{children:i.jsxs("div",{className:"py-8 text-center",children:[i.jsx("i",{className:"fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4"}),i.jsx("p",{className:"text-gray-500",children:"No se pudo cargar la información del paciente."}),i.jsx(d,{onClick:()=>u("/admin/reports"),className:"mt-4",children:"Volver a Resultados"})]})})})});const D=b.filter(e=>e.percentil).length>0?Math.round(b.filter(e=>e.percentil).reduce((e,a)=>e+a.percentil,0)/b.filter(e=>e.percentil).length):null,_=b.filter(e=>e.percentil),C=e=>_.find(a=>{var s;return(null==(s=a.aptitudes)?void 0:s.codigo)===e}),E=C("R"),A=C("N"),P=[null==E?void 0:E.percentil,null==A?void 0:A.percentil].filter(e=>void 0!==e),k=P.length>0?Math.round(P.reduce((e,a)=>e+a,0)/P.length):null,S=C("V"),z=C("O"),I=[null==S?void 0:S.percentil,null==z?void 0:z.percentil].filter(e=>void 0!==e),G=I.length>0?Math.round(I.reduce((e,a)=>e+a,0)/I.length):null,$=D,q=(e,a)=>{if(!a)return{nivel:"No evaluado",descripcion:"No hay datos suficientes para evaluar este índice."};let s,l,i;switch(s=a>=75?"Alto":a>=25?"Promedio":"Bajo",e){case"g":"Alto"===s?(l="Capacidad general elevada para comprender situaciones complejas, razonar y resolver problemas de manera efectiva.",i=["Habilidad para resolver eficientemente problemas complejos y novedosos","Buena capacidad para formular y contrastar hipótesis","Facilidad para abstraer información e integrarla con conocimiento previo","Elevado potencial para adquirir nuevos conocimientos"]):"Promedio"===s?(l="Capacidad general dentro del rango esperado para resolver problemas y comprender situaciones.",i=["Capacidad adecuada para resolver problemas de complejidad moderada","Habilidades de razonamiento en desarrollo","Potencial de aprendizaje dentro del rango promedio"]):(l="Dificultades en la capacidad general para resolver problemas complejos y comprender relaciones abstractas.",i=["Dificultades para aplicar el razonamiento a problemas complejos","Limitaciones para formar juicios que requieran abstracción","Posible necesidad de enseñanza más directiva y supervisada"]);break;case"Gf":"Alto"===s?(l="Excelente capacidad para el razonamiento inductivo y deductivo con problemas novedosos.",i=["Habilidad sobresaliente para aplicar razonamiento a problemas novedosos","Facilidad para identificar reglas y formular hipótesis","Nivel alto de razonamiento analítico","Buena integración de información visual y verbal"]):"Promedio"===s?(l="Capacidad adecuada para el razonamiento con contenidos abstractos y formales.",i=["Habilidades de razonamiento en desarrollo","Capacidad moderada para resolver problemas novedosos","Estrategias de resolución en proceso de consolidación"]):(l="Dificultades en el razonamiento inductivo y deductivo con problemas abstractos.",i=["Uso de estrategias poco eficaces para problemas novedosos","Falta de flexibilidad en soluciones alternativas","Dificultades para identificar reglas subyacentes","Integración defectuosa de información visual y verbal"]);break;case"Gc":"Alto"===s?(l="Excelente dominio de conocimientos adquiridos culturalmente y habilidades verbales.",i=["Habilidad para captar relaciones entre conceptos verbales","Buena capacidad de comprensión y expresión del lenguaje","Buen nivel de conocimiento léxico y ortográfico","Posiblemente buen nivel de cultura general"]):"Promedio"===s?(l="Conocimientos verbales y culturales dentro del rango esperado.",i=["Comprensión verbal adecuada para la edad","Conocimientos léxicos en desarrollo","Habilidades de expresión en proceso de consolidación"]):(l="Limitaciones en conocimientos verbales y habilidades de lenguaje adquiridas culturalmente.",i=["Procesamiento parcial de relaciones entre conceptos verbales","Dificultades en comprensión y expresión del lenguaje","Limitaciones en conocimiento léxico y ortográfico","Posible nivel bajo de cultura general"]);break;default:l="Interpretación no disponible para este índice.",i=[]}return{nivel:s,descripcion:l,caracteristicas:i}};return i.jsxs("div",{className:"container mx-auto py-6 max-w-6xl",children:[i.jsxs("div",{className:"mb-6 text-center",children:[i.jsxs("div",{className:"flex items-center justify-center mb-4",children:[i.jsx("div",{className:`w-16 h-16 bg-${"masculino"===(null==h?void 0:h.genero)?"blue":"pink"}-100 rounded-full flex items-center justify-center mr-4`,children:i.jsx("i",{className:`fas ${"masculino"===(null==h?void 0:h.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} text-2xl`})}),i.jsxs("div",{children:[i.jsx("h1",{className:"text-3xl font-bold text-blue-800",children:"Informe General BAT-7"}),i.jsxs("p",{className:"text-gray-600",children:[null==h?void 0:h.nombre," ",null==h?void 0:h.apellido]})]})]}),i.jsxs("div",{className:"flex justify-center space-x-4 print-hide",children:[i.jsxs(d,{onClick:()=>u("/admin/reports"),variant:"outline",children:[i.jsx("i",{className:"fas fa-arrow-left mr-2"}),"Volver"]}),i.jsxs(d,{onClick:()=>e(null,null,function*(){var e;try{y(!0);const a={resultado_id:null==(e=b[0])?void 0:e.id,paciente_id:h.id,titulo:`Informe Completo Admin - ${null==h?void 0:h.nombre} ${null==h?void 0:h.apellido}`,contenido:{paciente:h,resultados:b.map(e=>{var a,s,l;return{id:e.id,test:{codigo:null==(a=e.aptitudes)?void 0:a.codigo,nombre:null==(s=e.aptitudes)?void 0:s.nombre,descripcion:null==(l=e.aptitudes)?void 0:l.descripcion},puntajes:{puntaje_directo:e.puntaje_directo,percentil:e.percentil,errores:e.errores,tiempo_segundos:e.tiempo_segundos,concentracion:e.concentracion},interpretacion:e.percentil?m.obtenerInterpretacionPC(e.percentil):{nivel:"Pendiente",color:"text-gray-600",bg:"bg-gray-100"},fecha_evaluacion:e.created_at}}),resumen:{total_tests:b.length,promedio_percentil:b.filter(e=>e.percentil).length>0?Math.round(b.filter(e=>e.percentil).reduce((e,a)=>e+a.percentil,0)/b.filter(e=>e.percentil).length):null,fecha_primera_evaluacion:b.length>0?b[b.length-1].created_at:null,fecha_ultima_evaluacion:b.length>0?b[0].created_at:null},fecha_generacion:(new Date).toISOString()},generado_por:"Administrador",tipo_informe:"evaluacion_completa",estado:"generado"},{data:s,error:l}=yield o.from("informes").insert([a]).select().single();if(l)return void t.error("Error al guardar el informe completo");t.success("Informe completo guardado exitosamente")}catch(a){t.error("Error al guardar el informe completo")}finally{y(!1)}}),disabled:N,variant:"primary",children:[i.jsx("i",{className:"fas fa-save mr-2"}),N?"Guardando...":"Guardar Informe"]}),i.jsxs(d,{onClick:()=>{const e=document.querySelectorAll(".print-hide");e.forEach(e=>e.style.display="none");const a=document.querySelector(".container"),s=document.body,l=document.documentElement,i=null==a?void 0:a.className,t=null==s?void 0:s.className,n=null==l?void 0:l.className;a&&(a.className+=" print-optimize"),s&&(s.className+=" print-optimize"),l&&(l.className+=" print-optimize");const r=document.createElement("style");r.textContent="\n      @media print {\n        * {\n          -webkit-print-color-adjust: exact !important;\n          print-color-adjust: exact !important;\n        }\n        .space-y-6 > * + * {\n          margin-top: 0.5rem !important;\n        }\n        .mb-6 {\n          margin-bottom: 0.5rem !important;\n        }\n        .py-6 {\n          padding-top: 0.5rem !important;\n          padding-bottom: 0.5rem !important;\n        }\n      }\n    ",document.head.appendChild(r);const d=document.title;document.title=`Informe_${null==h?void 0:h.nombre}_${null==h?void 0:h.apellido}_${(new Date).toLocaleDateString("es-ES").replace(/\//g,"-")}`,window.print(),setTimeout(()=>{e.forEach(e=>e.style.display=""),a&&i&&(a.className=i),s&&t&&(s.className=t),l&&n&&(l.className=n),document.head.removeChild(r),document.title=d},1e3)},variant:"secondary",children:[i.jsx("i",{className:"fas fa-file-pdf mr-2"}),"Generar PDF"]}),i.jsxs(d,{onClick:()=>window.print(),variant:"outline",children:[i.jsx("i",{className:"fas fa-print mr-2"}),"Imprimir"]})]})]}),i.jsx("div",{className:"print-only mb-6",children:i.jsx("div",{className:"informe-header-main",children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsx("i",{className:"fas fa-file-medical-alt text-3xl mr-4"}),i.jsxs("div",{children:[i.jsx("h1",{className:"text-3xl font-bold",children:"INFORME PSICOLÓGICO"}),i.jsx("h2",{className:"text-lg",children:"Batería de Aptitudes Diferenciales y Generales - BAT-7"}),i.jsx("p",{className:"text-sm",children:"Evaluación Psicológica Integral"})]})]}),i.jsxs("div",{className:"text-right",children:[i.jsxs("div",{className:"bg-white bg-opacity-20 px-3 py-1 rounded",children:[i.jsx("i",{className:"fas fa-file-pdf mr-2"}),i.jsx("span",{className:"text-sm",children:"PDF"})]}),i.jsxs("div",{className:"bg-white bg-opacity-20 px-3 py-1 rounded mt-2",children:[i.jsx("i",{className:"fas fa-print mr-2"}),i.jsx("span",{className:"text-sm",children:"Imprimir"})]})]})]})})}),i.jsxs(n,{className:"mb-6 print-keep-together shadow-lg border-l-4 border-blue-500 print-patient-info",children:[i.jsx(c,{className:"bg-gradient-to-r from-blue-50 to-green-50 border-b-2 border-blue-200",children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:`w-12 h-12 bg-${"masculino"===(null==h?void 0:h.genero)?"blue":"pink"}-100 rounded-full flex items-center justify-center mr-4`,children:i.jsx("i",{className:`fas ${"masculino"===(null==h?void 0:h.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} text-xl`})}),i.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[i.jsx("i",{className:"fas fa-user mr-2"}),"Información del Paciente"]})]})}),i.jsxs(r,{className:"bg-white",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 print:hidden",children:[i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400",children:[i.jsx("p",{className:"text-xs font-medium text-blue-600 uppercase tracking-wide mb-1",children:"Nombre Completo"}),i.jsxs("p",{className:"text-lg font-bold text-gray-900",children:[null==h?void 0:h.nombre," ",null==h?void 0:h.apellido]})]}),(null==h?void 0:h.documento)&&i.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg border-l-4 border-gray-400",children:[i.jsx("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide mb-1",children:"Documento"}),i.jsx("p",{className:"text-base font-semibold text-gray-900",children:h.documento})]})]}),i.jsxs("div",{className:"space-y-4",children:[(null==h?void 0:h.fecha_nacimiento)&&i.jsxs("div",{className:"bg-green-50 p-4 rounded-lg border-l-4 border-green-400",children:[i.jsx("p",{className:"text-xs font-medium text-green-600 uppercase tracking-wide mb-1",children:"Fecha de Nacimiento"}),i.jsx("p",{className:"text-base font-semibold text-gray-900",children:new Date(h.fecha_nacimiento).toLocaleDateString("es-ES")})]}),i.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg border-l-4 border-purple-400",children:[i.jsx("p",{className:"text-xs font-medium text-purple-600 uppercase tracking-wide mb-1",children:"Edad"}),i.jsxs("p",{className:"text-base font-semibold text-gray-900",children:[w(null==h?void 0:h.fecha_nacimiento)," años"]})]})]}),i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:`bg-${"masculino"===(null==h?void 0:h.genero)?"blue":"pink"}-50 p-4 rounded-lg border-l-4 border-${"masculino"===(null==h?void 0:h.genero)?"blue":"pink"}-400`,children:[i.jsx("p",{className:`text-xs font-medium text-${"masculino"===(null==h?void 0:h.genero)?"blue":"pink"}-600 uppercase tracking-wide mb-1`,children:"Género"}),i.jsxs("p",{className:"text-base font-semibold text-gray-900 capitalize flex items-center",children:[i.jsx("i",{className:`fas ${"masculino"===(null==h?void 0:h.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} mr-2`}),null==h?void 0:h.genero]})]}),(null==h?void 0:h.email)&&i.jsxs("div",{className:"bg-orange-50 p-4 rounded-lg border-l-4 border-orange-400",children:[i.jsx("p",{className:"text-xs font-medium text-orange-600 uppercase tracking-wide mb-1",children:"Email"}),i.jsx("p",{className:"text-sm font-semibold text-gray-900 break-all",children:h.email})]})]})]}),i.jsx("div",{className:"hidden print:block",children:i.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[i.jsxs("div",{children:[i.jsxs("div",{className:"mb-2",children:[i.jsx("span",{className:"font-medium text-blue-600",children:"Nombre:"}),i.jsxs("span",{className:"ml-2 font-bold",children:[null==h?void 0:h.nombre," ",null==h?void 0:h.apellido]})]}),(null==h?void 0:h.documento)&&i.jsxs("div",{className:"mb-2",children:[i.jsx("span",{className:"font-medium text-gray-600",children:"Documento:"}),i.jsx("span",{className:"ml-2",children:h.documento})]}),(null==h?void 0:h.email)&&i.jsxs("div",{className:"mb-2",children:[i.jsx("span",{className:"font-medium text-orange-600",children:"Email:"}),i.jsx("span",{className:"ml-2 text-xs",children:h.email})]})]}),i.jsxs("div",{children:[(null==h?void 0:h.fecha_nacimiento)&&i.jsxs("div",{className:"mb-2",children:[i.jsx("span",{className:"font-medium text-green-600",children:"Fecha de Nacimiento:"}),i.jsx("span",{className:"ml-2",children:new Date(h.fecha_nacimiento).toLocaleDateString("es-ES")})]}),i.jsxs("div",{className:"mb-2",children:[i.jsx("span",{className:"font-medium text-purple-600",children:"Edad:"}),i.jsxs("span",{className:"ml-2",children:[w(null==h?void 0:h.fecha_nacimiento)," años"]})]}),i.jsxs("div",{className:"mb-2",children:[i.jsx("span",{className:`font-medium text-${"masculino"===(null==h?void 0:h.genero)?"blue":"pink"}-600`,children:"Género:"}),i.jsxs("span",{className:"ml-2 capitalize",children:[i.jsx("i",{className:`fas ${"masculino"===(null==h?void 0:h.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} mr-1`}),null==h?void 0:h.genero]})]})]})]})})]})]}),i.jsxs(n,{className:"mb-6",children:[i.jsx(c,{className:"bg-green-50 border-b",children:i.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[i.jsx("i",{className:"fas fa-chart-pie mr-2"}),"Resumen General"]})}),i.jsxs(r,{className:"print-compact",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 print-grid-horizontal gap-6 mb-6",children:[i.jsxs("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[i.jsx("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:b.length}),i.jsx("div",{className:"text-sm font-medium text-blue-700",children:"Tests Completados"})]}),i.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[i.jsx("div",{className:"text-3xl font-bold text-green-600 mb-2",children:D||"N/A"}),i.jsx("div",{className:"text-sm font-medium text-green-700",children:"Percentil Promedio"})]}),i.jsxs("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[i.jsx("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:b.filter(e=>e.percentil&&e.percentil>=75).length}),i.jsx("div",{className:"text-sm font-medium text-purple-700",children:"Aptitudes Altas (≥75)"})]}),i.jsxs("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[i.jsx("div",{className:"text-3xl font-bold text-orange-600 mb-2",children:b.filter(e=>e.percentil&&e.percentil<=25).length}),i.jsx("div",{className:"text-sm font-medium text-orange-700",children:"Aptitudes a Reforzar (≤25)"})]})]}),i.jsxs("div",{className:"border-t pt-6",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4 text-center",children:"Índices Especializados BAT-7"}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 print-grid-horizontal gap-6",children:[i.jsxs("div",{className:"text-center p-4 bg-indigo-50 rounded-lg border-2 border-indigo-200",children:[i.jsx("div",{className:"text-2xl font-bold text-indigo-600 mb-2",children:D||"N/A"}),i.jsx("div",{className:"text-sm font-medium text-indigo-700",children:"Total BAT"}),i.jsx("div",{className:"text-xs text-indigo-600 mt-1",children:"Capacidad General"})]}),i.jsxs("div",{className:"text-center p-4 bg-cyan-50 rounded-lg border-2 border-cyan-200",children:[i.jsx("div",{className:"text-2xl font-bold text-cyan-600 mb-2",children:$||"N/A"}),i.jsx("div",{className:"text-sm font-medium text-cyan-700",children:"Índice g"}),i.jsx("div",{className:"text-xs text-cyan-600 mt-1",children:"Capacidad General"})]}),i.jsxs("div",{className:"text-center p-4 bg-teal-50 rounded-lg border-2 border-teal-200",children:[i.jsx("div",{className:"text-2xl font-bold text-teal-600 mb-2",children:k||"N/A"}),i.jsx("div",{className:"text-sm font-medium text-teal-700",children:"Índice Gf"}),i.jsx("div",{className:"text-xs text-teal-600 mt-1",children:"Inteligencia Fluida"})]}),i.jsxs("div",{className:"text-center p-4 bg-emerald-50 rounded-lg border-2 border-emerald-200",children:[i.jsx("div",{className:"text-2xl font-bold text-emerald-600 mb-2",children:G||"N/A"}),i.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Índice Gc"}),i.jsx("div",{className:"text-xs text-emerald-600 mt-1",children:"Inteligencia Cristalizada"})]})]})]}),b.length>0&&i.jsxs("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[i.jsxs("div",{children:[i.jsx("span",{className:"text-gray-500",children:"Primera evaluación:"}),i.jsx("span",{className:"ml-2 font-medium",children:new Date(b[b.length-1].created_at).toLocaleDateString("es-ES")})]}),i.jsxs("div",{children:[i.jsx("span",{className:"text-gray-500",children:"Última evaluación:"}),i.jsx("span",{className:"ml-2 font-medium",children:new Date(b[0].created_at).toLocaleDateString("es-ES")})]})]})]})]}),i.jsxs(n,{className:"mb-6 print-keep-together",children:[i.jsx(c,{className:"bg-gray-50 border-b",children:i.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[i.jsx("i",{className:"fas fa-list-alt mr-2"}),"Resultados Detallados por Aptitud"]})}),i.jsx(r,{className:"p-0",children:0===b.length?i.jsxs("div",{className:"py-8 text-center",children:[i.jsx("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 mb-4"}),i.jsx("p",{className:"text-gray-500",children:"No hay resultados de tests disponibles para este paciente."})]}):i.jsx("div",{className:"overflow-x-auto",children:i.jsxs("table",{className:"w-full",children:[i.jsx("thead",{children:i.jsxs("tr",{className:"bg-slate-800 text-white",children:[i.jsx("th",{className:"px-4 py-3 text-left font-semibold",children:"S"}),i.jsx("th",{className:"px-4 py-3 text-left font-semibold",children:"APTITUDES EVALUADAS"}),i.jsx("th",{className:"px-4 py-3 text-center font-semibold",children:"PD"}),i.jsx("th",{className:"px-4 py-3 text-center font-semibold",children:"PC"}),i.jsx("th",{className:"px-4 py-3 text-left font-semibold",children:"PERFIL DE LAS APTITUDES"})]})}),i.jsx("tbody",{children:b.map((e,a)=>{var s,l,t;null==(s=e.aptitudes)||s.codigo;const n=e.percentil||0;let r="bg-blue-500";return r=n>=80?"bg-orange-500":n>=60?"bg-blue-500":n>=40?"bg-blue-400":"bg-blue-300",i.jsxs("tr",{className:a%2==0?"bg-white":"bg-gray-50",children:[i.jsx("td",{className:"px-4 py-3",children:i.jsx("div",{className:"w-8 h-8 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:null==(l=e.aptitudes)?void 0:l.codigo})}),i.jsx("td",{className:"px-4 py-3",children:i.jsx("div",{className:"font-medium text-gray-900",children:null==(t=e.aptitudes)?void 0:t.nombre})}),i.jsx("td",{className:"px-4 py-3 text-center font-bold text-gray-900",children:e.puntaje_directo}),i.jsx("td",{className:"px-4 py-3 text-center font-bold text-gray-900",children:e.percentil||"N/A"}),i.jsx("td",{className:"px-4 py-3",children:i.jsx("div",{className:"flex items-center",children:i.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-6 mr-3",children:i.jsx("div",{className:`${r} h-6 rounded-full flex items-center justify-end pr-2`,style:{width:`${Math.max(n,5)}%`},children:i.jsx("span",{className:"text-white text-xs font-bold",children:n>0?n:""})})})})})]},e.id)})})]})})})]}),b.length>0&&i.jsxs(n,{className:"mb-6 print-keep-together",children:[i.jsx(c,{className:"bg-purple-50 border-b",children:i.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[i.jsx("i",{className:"fas fa-brain mr-2"}),"Interpretación Cualitativa de Aptitudes"]})}),i.jsx(r,{children:i.jsx("div",{className:"space-y-6",children:b.map((e,a)=>{var s,l,t;const n=x(null==(s=e.aptitudes)?void 0:s.codigo,e.percentil||0);return n?i.jsxs("div",{className:"border-l-4 border-blue-500 pl-6 py-4 bg-gray-50 rounded-r-lg",children:[i.jsxs("div",{className:"flex items-center mb-3",children:[i.jsx("div",{className:"w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold mr-3",children:null==(l=e.aptitudes)?void 0:l.codigo}),i.jsxs("div",{children:[i.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:[null==(t=e.aptitudes)?void 0:t.nombre," - Nivel ",n.nivel]}),i.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",e.percentil||"N/A"]})]})]}),i.jsxs("div",{className:"mb-4",children:[i.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),i.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:n.descripcion})]}),i.jsxs("div",{children:[i.jsxs("h4",{className:"font-medium text-gray-800 mb-2",children:["Características ","Alto"===n.nivel?"Fortalezas":"Áreas de Mejora",":"]}),i.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:n.caracteristicas.map((e,a)=>i.jsx("li",{className:"leading-relaxed",children:e},a))})]})]},e.id):null})})})]}),i.jsxs(n,{className:"mb-6 print-keep-together",children:[i.jsx(c,{className:"bg-indigo-50 border-b",children:i.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[i.jsx("i",{className:"fas fa-chart-line mr-2"}),"Interpretación Cualitativa de Índices Especializados"]})}),i.jsx(r,{children:i.jsxs("div",{className:"space-y-6",children:[D&&i.jsxs("div",{className:"border-l-4 border-indigo-500 pl-6 py-4 bg-indigo-50 rounded-r-lg",children:[i.jsxs("div",{className:"flex items-center mb-3",children:[i.jsx("div",{className:"w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"g"}),i.jsxs("div",{children:[i.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice g - Capacidad General: ",q("g",$).nivel]}),i.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",$]})]})]}),i.jsxs("div",{className:"mb-4",children:[i.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),i.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:q("g",$).descripcion})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"}),i.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:q("g",$).caracteristicas.map((e,a)=>i.jsx("li",{className:"leading-relaxed",children:e},a))})]})]}),k&&i.jsxs("div",{className:"border-l-4 border-teal-500 pl-6 py-4 bg-teal-50 rounded-r-lg",children:[i.jsxs("div",{className:"flex items-center mb-3",children:[i.jsx("div",{className:"w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"Gf"}),i.jsxs("div",{children:[i.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice Gf - Inteligencia Fluida: ",q("Gf",k).nivel]}),i.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",k," (basado en R + N)"]})]})]}),i.jsxs("div",{className:"mb-4",children:[i.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),i.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:q("Gf",k).descripcion})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"}),i.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:q("Gf",k).caracteristicas.map((e,a)=>i.jsx("li",{className:"leading-relaxed",children:e},a))})]})]}),G&&i.jsxs("div",{className:"border-l-4 border-emerald-500 pl-6 py-4 bg-emerald-50 rounded-r-lg",children:[i.jsxs("div",{className:"flex items-center mb-3",children:[i.jsx("div",{className:"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"Gc"}),i.jsxs("div",{children:[i.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice Gc - Inteligencia Cristalizada: ",q("Gc",G).nivel]}),i.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",G," (basado en V + O)"]})]})]}),i.jsxs("div",{className:"mb-4",children:[i.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),i.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:q("Gc",G).descripcion})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"}),i.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:q("Gc",G).caracteristicas.map((e,a)=>i.jsx("li",{className:"leading-relaxed",children:e},a))})]})]}),k&&G&&Math.abs(k-G)>15&&i.jsxs("div",{className:"border-l-4 border-yellow-500 pl-6 py-4 bg-yellow-50 rounded-r-lg",children:[i.jsxs("div",{className:"flex items-center mb-3",children:[i.jsx("div",{className:"w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:i.jsx("i",{className:"fas fa-balance-scale"})}),i.jsx("div",{children:i.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Análisis de Disparidad entre Índices"})})]}),i.jsx("div",{children:i.jsxs("p",{className:"text-gray-700 text-sm leading-relaxed",children:["Se observa una diferencia significativa entre la Inteligencia Fluida (Gf: ",k,") y la Inteligencia Cristalizada (Gc: ",G,"). Esta disparidad sugiere un perfil cognitivo heterogéneo que requiere consideración especial en las recomendaciones de intervención.",k>G?" El evaluado muestra mayor fortaleza en razonamiento abstracto que en conocimientos adquiridos.":" El evaluado muestra mayor fortaleza en conocimientos adquiridos que en razonamiento abstracto."]})})]})]})})]}),i.jsxs("div",{className:"text-center text-sm text-gray-500 border-t pt-4",children:[i.jsxs("p",{children:["Informe completo generado el ",(new Date).toLocaleDateString("es-ES")," a las ",(new Date).toLocaleTimeString("es-ES")]}),i.jsx("p",{className:"mt-1",children:"Sistema de Evaluación Psicológica - BAT-7 - Panel de Administración"})]})]})};export{p as default};
