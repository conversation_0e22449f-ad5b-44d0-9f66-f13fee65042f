var e=Object.defineProperty,r=Object.defineProperties,a=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,l=(r,a,s)=>a in r?e(r,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[a]=s,t=(e,r)=>{for(var a in r||(r={}))i.call(r,a)&&l(e,a,r[a]);if(s)for(var a of s(r))o.call(r,a)&&l(e,a,r[a]);return e},n=(e,s)=>r(e,a(s)),m=(e,r,a)=>new Promise((s,i)=>{var o=e=>{try{t(a.next(e))}catch(r){i(r)}},l=e=>{try{t(a.throw(e))}catch(r){i(r)}},t=e=>e.done?s(e.value):Promise.resolve(e.value).then(o,l);t((a=a.apply(e,r)).next())});import{Q as u,r as d,j as c,F as f,C as b,s as N,f as p,E as g,w as x}from"./vendor-CIyllXGj.js";import{s as h}from"./index-CrXvaDRr.js";class v{static getCurrentUser(){return m(this,null,function*(){try{const{data:{session:e}}=yield h.auth.getSession();if(!(null==e?void 0:e.user))return null;const{data:r,error:a}=yield h.from("usuarios").select("*").eq("id",e.user.id).single();return a?e.user:t(t({},e.user),r)}catch(e){return null}})}static login(e){return m(this,arguments,function*({identifier:e,password:r}){try{let a;if(e.includes("@")){const{data:s,error:i}=yield h.auth.signInWithPassword({email:e,password:r});if(i)throw i;a=s}else{const{data:s,error:i}=yield h.from("usuarios").select("id").eq("documento",e).single();if(i||!s)throw new Error("Usuario no encontrado con ese documento");const{data:o,error:l}=yield h.auth.admin.getUserById(s.id);if(l||!o.user)throw new Error("Error al obtener datos de autenticación");const{data:t,error:n}=yield h.auth.signInWithPassword({email:o.user.email,password:r});if(n)throw n;a=t}const s=yield this.getCurrentUser();return s&&(yield h.from("usuarios").update({ultimo_acceso:(new Date).toISOString()}).eq("id",s.id)),u.success("Inicio de sesión exitoso"),{success:!0,user:s,session:a.session}}catch(a){return u.error(a.message||"Error al iniciar sesión"),{success:!1,message:a.message}}})}static register(e){return m(this,null,function*(){try{const{email:r,password:a,nombre:s,apellido:i,documento:o,rol:l="estudiante"}=e;if(o){const{data:e}=yield h.from("usuarios").select("id").eq("documento",o).single();if(e)throw new Error("Ya existe un usuario con ese documento")}const{data:t,error:n}=yield h.auth.signUp({email:r,password:a,options:{data:{nombre:s,apellido:i}}});if(n)throw n;const{error:m}=yield h.from("usuarios").insert([{id:t.user.id,documento:o,nombre:s,apellido:i,rol:l,activo:!0,fecha_creacion:(new Date).toISOString()}]);if(m)throw m;return u.success("Registro exitoso"),{success:!0,user:t.user,message:"Registro exitoso. Revisa tu email para confirmar tu cuenta."}}catch(r){return u.error(r.message||"Error al registrar usuario"),{success:!1,message:r.message}}})}static logout(){return m(this,null,function*(){try{const{error:e}=yield h.auth.signOut();if(e)throw e;return u.info("Sesión cerrada correctamente"),{success:!0}}catch(e){return u.error(e.message||"Error al cerrar sesión"),{success:!1,message:e.message}}})}static resetPassword(e){return m(this,null,function*(){try{const{error:r}=yield h.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/reset-password`});if(r)throw r;return u.success("Se ha enviado un enlace para restablecer la contraseña"),{success:!0}}catch(r){return u.error(r.message||"Error al restablecer contraseña"),{success:!1,message:r.message}}})}static updatePassword(e){return m(this,null,function*(){try{const{error:r}=yield h.auth.updateUser({password:e});if(r)throw r;return u.success("Contraseña actualizada correctamente"),{success:!0}}catch(r){return u.error(r.message||"Error al actualizar contraseña"),{success:!1,message:r.message}}})}static hasRole(e,r){var a;if(!e)return!1;return((null==(a=e.rol)?void 0:a.toLowerCase())||"")===r.toLowerCase()}static isAdmin(e){return this.hasRole(e,"administrador")}static isPsychologist(e){return this.hasRole(e,"psicologo")}static isStudent(e){return this.hasRole(e,"estudiante")}}const j=()=>{var e;const[r,a]=d.useState(null),[s,i]=d.useState({nombre:"",apellidos:"",telefono:""}),[o,l]=d.useState(!0),[j,V]=d.useState(!1),[w,P]=d.useState(!1),[C,y]=d.useState({currentPassword:"",newPassword:"",confirmPassword:""}),[E,D]=d.useState({});d.useEffect(()=>{m(null,null,function*(){l(!0);try{const e=yield v.getCurrentUser();if(a(e),e){const{data:r,error:a}=yield h.from("perfiles").select("*").eq("user_id",e.id).single();r&&!a&&i({nombre:r.nombre||"",apellidos:r.apellidos||"",telefono:r.telefono||""})}}catch(e){u.error("No se pudieron cargar los datos del perfil")}finally{l(!1)}})},[]);const B=e=>{const{name:r,value:a}=e.target;i(n(t({},s),{[r]:a}))},S=e=>{const{name:r,value:a}=e.target;y(n(t({},C),{[r]:a}))};return o?c.jsxDEV("div",{className:"flex justify-center items-center p-12",children:[c.jsxDEV(f,{className:"animate-spin text-blue-600 text-3xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:196,columnNumber:9},void 0),c.jsxDEV("span",{className:"ml-2",children:"Cargando perfil..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:197,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:195,columnNumber:7},void 0):c.jsxDEV("div",{className:"max-w-3xl mx-auto",children:[c.jsxDEV("header",{className:"mb-6",children:[c.jsxDEV("h1",{className:"text-2xl font-bold text-gray-900",children:"Mi Perfil"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:205,columnNumber:9},void 0),c.jsxDEV("p",{className:"mt-1 text-sm text-gray-600",children:"Administra tu información personal y de acceso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:206,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:204,columnNumber:7},void 0),c.jsxDEV("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:[c.jsxDEV("div",{className:"p-6 border-b border-gray-200",children:[c.jsxDEV("div",{className:"flex items-center mb-6",children:[c.jsxDEV("div",{className:"h-16 w-16 rounded-full bg-blue-600 flex items-center justify-center text-white text-xl font-semibold",children:(null==(e=null==r?void 0:r.email)?void 0:e.charAt(0).toUpperCase())||c.jsxDEV(b,{className:"h-8 w-8"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:216,columnNumber:56},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:215,columnNumber:13},void 0),c.jsxDEV("div",{className:"ml-4",children:[c.jsxDEV("h2",{className:"text-lg font-medium text-gray-900",children:s.nombre?`${s.nombre} ${s.apellidos}`:"Usuario"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:219,columnNumber:15},void 0),c.jsxDEV("p",{className:"text-sm text-gray-500",children:null==r?void 0:r.email},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:222,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:218,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:214,columnNumber:11},void 0),c.jsxDEV("form",{onSubmit:e=>m(null,null,function*(){if(e.preventDefault(),(()=>{const e={};return s.nombre&&s.nombre.length<2&&(e.nombre="El nombre debe tener al menos 2 caracteres"),s.apellidos&&s.apellidos.length<2&&(e.apellidos="Los apellidos deben tener al menos 2 caracteres"),D(e),0===Object.keys(e).length})()){V(!0);try{if(!r)throw new Error("No hay usuario autenticado");const{error:e}=yield h.from("perfiles").upsert({user_id:r.id,nombre:s.nombre,apellidos:s.apellidos,telefono:s.telefono,updated_at:new Date});if(e)throw e;u.success("Perfil actualizado correctamente")}catch(a){u.error("Error al guardar los cambios del perfil")}finally{V(!1)}}}),children:[c.jsxDEV("div",{className:"grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6",children:[c.jsxDEV("div",{className:"sm:col-span-3",children:[c.jsxDEV("label",{htmlFor:"nombre",className:"block text-sm font-medium text-gray-700",children:"Nombre"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:229,columnNumber:17},void 0),c.jsxDEV("div",{className:"mt-1 relative",children:[c.jsxDEV("input",{type:"text",name:"nombre",id:"nombre",value:s.nombre,onChange:B,className:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm "+(E.nombre?"border-red-500":"")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:233,columnNumber:19},void 0),E.nombre&&c.jsxDEV("p",{className:"mt-1 text-sm text-red-600",children:E.nombre},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:244,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:232,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:228,columnNumber:15},void 0),c.jsxDEV("div",{className:"sm:col-span-3",children:[c.jsxDEV("label",{htmlFor:"apellidos",className:"block text-sm font-medium text-gray-700",children:"Apellidos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:250,columnNumber:17},void 0),c.jsxDEV("div",{className:"mt-1",children:[c.jsxDEV("input",{type:"text",name:"apellidos",id:"apellidos",value:s.apellidos,onChange:B,className:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm "+(E.apellidos?"border-red-500":"")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:254,columnNumber:19},void 0),E.apellidos&&c.jsxDEV("p",{className:"mt-1 text-sm text-red-600",children:E.apellidos},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:265,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:253,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:249,columnNumber:15},void 0),c.jsxDEV("div",{className:"sm:col-span-6",children:[c.jsxDEV("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:271,columnNumber:17},void 0),c.jsxDEV("div",{className:"mt-1 relative rounded-md shadow-sm",children:[c.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:c.jsxDEV(N,{className:"h-5 w-5 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:276,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:275,columnNumber:19},void 0),c.jsxDEV("input",{type:"text",name:"email",id:"email",disabled:!0,value:(null==r?void 0:r.email)||"",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 bg-gray-50 rounded-md leading-5 text-gray-500 sm:text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:278,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:274,columnNumber:17},void 0),c.jsxDEV("p",{className:"mt-1 text-xs text-gray-500",children:"El email no se puede modificar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:287,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:270,columnNumber:15},void 0),c.jsxDEV("div",{className:"sm:col-span-4",children:[c.jsxDEV("label",{htmlFor:"telefono",className:"block text-sm font-medium text-gray-700",children:"Teléfono"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:291,columnNumber:17},void 0),c.jsxDEV("div",{className:"mt-1",children:[c.jsxDEV("input",{type:"text",name:"telefono",id:"telefono",value:s.telefono,onChange:B,className:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm "+(E.telefono?"border-red-500":"")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:295,columnNumber:19},void 0),E.telefono&&c.jsxDEV("p",{className:"mt-1 text-sm text-red-600",children:E.telefono},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:306,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:294,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:290,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:227,columnNumber:13},void 0),c.jsxDEV("div",{className:"mt-6",children:c.jsxDEV("button",{type:"submit",disabled:j,className:`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${j?"bg-blue-400":"bg-blue-600 hover:bg-blue-700"} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`,children:j?c.jsxDEV(c.Fragment,{children:[c.jsxDEV(f,{className:"animate-spin mr-2 h-4 w-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:322,columnNumber:21},void 0),"Guardando..."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:321,columnNumber:19},void 0):c.jsxDEV(c.Fragment,{children:[c.jsxDEV(p,{className:"mr-2 h-4 w-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:327,columnNumber:21},void 0),"Guardar Cambios"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:326,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:313,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:312,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:226,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:213,columnNumber:9},void 0),c.jsxDEV("div",{className:"p-6",children:[c.jsxDEV("div",{className:"flex items-center justify-between mb-4",children:[c.jsxDEV("h3",{className:"text-lg font-medium text-gray-900",children:"Seguridad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:339,columnNumber:13},void 0),c.jsxDEV("button",{type:"button",onClick:()=>P(!w),className:"text-sm font-medium text-blue-600 hover:text-blue-500",children:w?"Cancelar":"Cambiar contraseña"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:340,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:338,columnNumber:11},void 0),w?c.jsxDEV("form",{onSubmit:e=>m(null,null,function*(){if(e.preventDefault(),(()=>{const e={};return C.currentPassword||(e.currentPassword="La contraseña actual es requerida"),C.newPassword?C.newPassword.length<6&&(e.newPassword="La nueva contraseña debe tener al menos 6 caracteres"):e.newPassword="La nueva contraseña es requerida",C.newPassword!==C.confirmPassword&&(e.confirmPassword="Las contraseñas no coinciden"),D(e),0===Object.keys(e).length})()){V(!0);try{const{error:e}=yield h.auth.updateUser({password:C.newPassword});if(e)throw e;y({currentPassword:"",newPassword:"",confirmPassword:""}),P(!1),u.success("Contraseña actualizada correctamente")}catch(r){u.error("Error al cambiar la contraseña")}finally{V(!1)}}}),children:c.jsxDEV("div",{className:"space-y-4",children:[c.jsxDEV("div",{children:[c.jsxDEV("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-700",children:"Contraseña actual"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:353,columnNumber:19},void 0),c.jsxDEV("div",{className:"mt-1 relative rounded-md shadow-sm",children:[c.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:c.jsxDEV(g,{className:"h-5 w-5 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:358,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:357,columnNumber:21},void 0),c.jsxDEV("input",{type:"password",name:"currentPassword",id:"currentPassword",value:C.currentPassword,onChange:S,className:`block w-full pl-10 pr-3 py-2 border ${E.currentPassword?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:360,columnNumber:21},void 0),E.currentPassword&&c.jsxDEV("p",{className:"mt-1 text-sm text-red-600",children:E.currentPassword},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:371,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:356,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:352,columnNumber:17},void 0),c.jsxDEV("div",{children:[c.jsxDEV("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700",children:"Nueva contraseña"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:377,columnNumber:19},void 0),c.jsxDEV("div",{className:"mt-1 relative rounded-md shadow-sm",children:[c.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:c.jsxDEV(g,{className:"h-5 w-5 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:382,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:381,columnNumber:21},void 0),c.jsxDEV("input",{type:"password",name:"newPassword",id:"newPassword",value:C.newPassword,onChange:S,className:`block w-full pl-10 pr-3 py-2 border ${E.newPassword?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:384,columnNumber:21},void 0),E.newPassword&&c.jsxDEV("p",{className:"mt-1 text-sm text-red-600",children:E.newPassword},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:395,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:380,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:376,columnNumber:17},void 0),c.jsxDEV("div",{children:[c.jsxDEV("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirmar nueva contraseña"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:401,columnNumber:19},void 0),c.jsxDEV("div",{className:"mt-1 relative rounded-md shadow-sm",children:[c.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:c.jsxDEV(g,{className:"h-5 w-5 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:406,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:405,columnNumber:21},void 0),c.jsxDEV("input",{type:"password",name:"confirmPassword",id:"confirmPassword",value:C.confirmPassword,onChange:S,className:`block w-full pl-10 pr-3 py-2 border ${E.confirmPassword?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:408,columnNumber:21},void 0),E.confirmPassword&&c.jsxDEV("p",{className:"mt-1 text-sm text-red-600",children:E.confirmPassword},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:419,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:404,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:400,columnNumber:17},void 0),c.jsxDEV("div",{className:"rounded-md bg-yellow-50 p-4",children:c.jsxDEV("div",{className:"flex",children:[c.jsxDEV("div",{className:"flex-shrink-0",children:c.jsxDEV(x,{className:"h-5 w-5 text-yellow-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:428,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:427,columnNumber:21},void 0),c.jsxDEV("div",{className:"ml-3",children:[c.jsxDEV("h3",{className:"text-sm font-medium text-yellow-800",children:"Información importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:431,columnNumber:23},void 0),c.jsxDEV("div",{className:"mt-2 text-sm text-yellow-700",children:c.jsxDEV("p",{children:"Por seguridad, cierre todas las sesiones activas después de cambiar su contraseña."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:435,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:434,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:430,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:426,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:425,columnNumber:17},void 0),c.jsxDEV("div",{className:"mt-4",children:c.jsxDEV("button",{type:"submit",disabled:j,className:`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${j?"bg-blue-400":"bg-blue-600 hover:bg-blue-700"} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`,children:j?c.jsxDEV(c.Fragment,{children:[c.jsxDEV(f,{className:"animate-spin mr-2 h-4 w-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:453,columnNumber:25},void 0),"Actualizando..."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:452,columnNumber:23},void 0):"Actualizar contraseña"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:444,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:443,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:351,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:350,columnNumber:13},void 0):c.jsxDEV("p",{className:"text-sm text-gray-600",children:"Es recomendable cambiar su contraseña regularmente para mantener la seguridad de su cuenta."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:464,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:337,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:211,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Profile.jsx",lineNumber:203,columnNumber:5},void 0)};export{j as default};
