import React, { useState, useEffect } from 'react';
import { 
  FaUserPlus, 
  FaEdit, 
  FaTrash, 
  FaSearch, 
  FaFilter, 
  FaUsers, 
  FaUserCheck, 
  FaUserTimes,
  FaEye,
  FaEyeSlash,
  FaBroom,
  FaUserShield,
  FaSpinner
} from 'react-icons/fa';

// Hooks existentes
import { usePagination } from '../../hooks/usePagination';
import { useDebounce } from '../../hooks/useDebounce';
import { toast } from 'react-toastify';
import { supabase } from '../../api/supabaseClient';

/**
 * Componente de gestión de usuarios funcional y mejorado
 */
const SimpleUserManagementPanel = () => {
  // Estados principales
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Estados para búsqueda y filtrado
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  // Estados para modales
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  // Estados para formulario
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    nombre: '',
    apellido: '',
    documento: '',
    rol: 'paciente',
    activo: true
  });

  // Estados para estadísticas
  const [stats, setStats] = useState({
    total: 0,
    activos: 0,
    inactivos: 0,
    administradores: 0,
    psicologos: 0,
    pacientes: 0
  });

  // Debounce para búsqueda
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Cargar usuarios al montar el componente
  useEffect(() => {
    fetchUsers();
  }, []);

  // Función para cargar usuarios
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('usuarios')
        .select('*')
        .order('fecha_creacion', { ascending: false });

      if (error) {
        throw error;
      }

      setUsers(data || []);
      calculateStats(data || []);
      
    } catch (error) {
      console.error('Error fetching users:', error);
      setError('Error al cargar usuarios: ' + error.message);
      toast.error('Error al cargar usuarios');
    } finally {
      setLoading(false);
    }
  };

  // Calcular estadísticas
  const calculateStats = (userData) => {
    const total = userData.length;
    const activos = userData.filter(u => u.activo).length;
    const inactivos = total - activos;
    const administradores = userData.filter(u => u.rol === 'administrador').length;
    const psicologos = userData.filter(u => u.rol === 'psicologo').length;
    const pacientes = userData.filter(u => u.rol === 'paciente').length;

    setStats({
      total,
      activos,
      inactivos,
      administradores,
      psicologos,
      pacientes
    });
  };

  // Función para filtrar usuarios
  const getFilteredUsers = () => {
    let filtered = [...users];

    // Filtro por término de búsqueda
    if (debouncedSearchTerm.trim()) {
      const term = debouncedSearchTerm.toLowerCase().trim();
      filtered = filtered.filter(user => 
        user.nombre?.toLowerCase().includes(term) ||
        user.apellido?.toLowerCase().includes(term) ||
        user.email?.toLowerCase().includes(term) ||
        user.documento?.toLowerCase().includes(term)
      );
    }

    // Filtro por rol
    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.rol === roleFilter);
    }

    // Filtro por estado
    if (statusFilter !== 'all') {
      const isActive = statusFilter === 'active';
      filtered = filtered.filter(user => user.activo === isActive);
    }

    return filtered;
  };

  // Usar paginación con datos filtrados
  const filteredUsers = getFilteredUsers();
  const { 
    paginatedData: paginatedUsers, 
    currentPage, 
    totalPages, 
    goToPage, 
    goToNextPage, 
    goToPreviousPage,
    pageSize,
    setPageSize,
    startIndex,
    endIndex
  } = usePagination(filteredUsers, 10);

  // Función para limpiar filtros
  const clearFilters = () => {
    setSearchTerm('');
    setRoleFilter('all');
    setStatusFilter('all');
  };

  // Función para crear usuario usando signUp
  const handleCreateUser = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);

      console.log('👤 Iniciando creación de usuario...');

      // Validaciones básicas
      if (!formData.email || !formData.password || !formData.nombre || !formData.apellido) {
        toast.error('Por favor completa todos los campos requeridos');
        return;
      }

      // Validar formato de email
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        toast.error('Por favor ingresa un email válido');
        return;
      }

      // Validar longitud de contraseña
      if (formData.password.length < 6) {
        toast.error('La contraseña debe tener al menos 6 caracteres');
        return;
      }

      console.log('✅ Validaciones básicas completadas');

      // Verificar que el email no esté en uso en la tabla usuarios
      console.log('🔍 Verificando email único...');
      const { data: existingUser, error: checkError } = await supabase
        .from('usuarios')
        .select('id, email')
        .eq('email', formData.email.toLowerCase().trim())
        .maybeSingle();

      if (checkError && checkError.code !== 'PGRST116') {
        console.error('❌ Error verificando email:', checkError);
        throw new Error('Error verificando email: ' + checkError.message);
      }

      if (existingUser) {
        console.log('⚠️ Email ya existe:', formData.email);
        toast.error('Ya existe un usuario con ese email');
        return;
      }

      console.log('✅ Email disponible');

      // Crear usuario usando admin.createUser (no requiere confirmación de email)
      console.log('🔐 Creando cuenta de autenticación...');
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: formData.email.toLowerCase().trim(),
        password: formData.password,
        email_confirm: true, // Confirmar email automáticamente
        user_metadata: {
          nombre: formData.nombre.trim(),
          apellido: formData.apellido.trim(),
          documento: formData.documento?.trim() || null,
          rol: formData.rol
        }
      });

      if (authError) {
        console.error('❌ Error creando cuenta de auth:', authError);
        throw new Error('Error creando cuenta: ' + authError.message);
      }

      if (!authData.user) {
        throw new Error('No se pudo crear la cuenta de usuario');
      }

      console.log('✅ Cuenta de autenticación creada:', authData.user.id);

      // Crear/actualizar perfil en tabla usuarios con datos adicionales del admin
      console.log('💾 Creando perfil completo en base de datos...');
      const { error: profileError } = await supabase
        .from('usuarios')
        .upsert([{
          id: authData.user.id,
          email: formData.email.toLowerCase().trim(),
          nombre: formData.nombre.trim(),
          apellido: formData.apellido.trim(),
          documento: formData.documento?.trim() || null,
          rol: formData.rol,
          activo: formData.activo,
          fecha_creacion: new Date().toISOString(),
          require_password_change: false // El admin ya estableció la contraseña
        }], {
          onConflict: 'id'
        });

      if (profileError) {
        console.error('❌ Error creando perfil:', profileError);
        // Intentar limpiar la cuenta de auth si falló el perfil
        try {
          await supabase.auth.admin.deleteUser(authData.user.id);
        } catch (cleanupError) {
          console.error('⚠️ No se pudo limpiar cuenta de auth:', cleanupError);
        }
        throw new Error('Error creando perfil: ' + profileError.message);
      }

      console.log('✅ Perfil creado exitosamente');

      // Mostrar mensaje de éxito
      toast.success(
        `Usuario ${formData.nombre} ${formData.apellido} creado exitosamente. ` +
        `El usuario puede hacer login inmediatamente con sus credenciales.`,
        { duration: 5000 }
      );

      console.log('🎉 Usuario creado completamente - puede hacer login inmediatamente');

      // Limpiar formulario y cerrar modal
      setShowCreateModal(false);
      setFormData({
        email: '',
        password: '',
        nombre: '',
        apellido: '',
        documento: '',
        rol: 'paciente',
        activo: true
      });

      // Recargar usuarios
      console.log('🔄 Recargando lista de usuarios...');
      await fetchUsers();

    } catch (error) {
      console.error('❌ Error creating user:', error);

      // Mensajes de error más específicos
      let errorMessage = 'Error al crear usuario';

      if (error.message.includes('User already registered')) {
        errorMessage = 'Ya existe un usuario registrado con ese email';
      } else if (error.message.includes('duplicate key')) {
        errorMessage = 'Ya existe un usuario con ese email';
      } else if (error.message.includes('invalid input')) {
        errorMessage = 'Datos inválidos. Verifica la información ingresada';
      } else if (error.message.includes('Password should be at least')) {
        errorMessage = 'La contraseña debe tener al menos 6 caracteres';
      } else if (error.message.includes('Invalid email')) {
        errorMessage = 'El formato del email no es válido';
      } else if (error.message) {
        errorMessage = 'Error: ' + error.message;
      }

      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Función para editar usuario
  const handleEditUser = async (e) => {
    e.preventDefault();

    if (!selectedUser) return;

    try {
      setLoading(true);

      console.log(`🔄 Actualizando usuario: ${selectedUser.nombre} ${selectedUser.apellido}`);
      console.log('📝 Datos a actualizar:', {
        nombre: formData.nombre,
        apellido: formData.apellido,
        documento: formData.documento,
        rol: formData.rol,
        activo: formData.activo
      });

      // Actualizar en tabla usuarios (sin fecha_actualizacion que no existe)
      const { error } = await supabase
        .from('usuarios')
        .update({
          nombre: formData.nombre,
          apellido: formData.apellido,
          documento: formData.documento,
          rol: formData.rol,
          activo: formData.activo
        })
        .eq('id', selectedUser.id);

      if (error) {
        console.error('❌ Error actualizando perfil:', error);
        throw error;
      }

      console.log('✅ Perfil actualizado exitosamente');

      // Si se cambió la contraseña, actualizar en auth
      if (formData.password && formData.password.trim()) {
        console.log('🔐 Actualizando contraseña...');
        const { error: authError } = await supabase.auth.admin.updateUserById(
          selectedUser.id,
          { password: formData.password }
        );

        if (authError) {
          console.warn('⚠️ Error updating password:', authError);
          toast.warn('Usuario actualizado, pero hubo un problema al cambiar la contraseña');
        } else {
          console.log('✅ Contraseña actualizada exitosamente');
        }
      }

      // Mensaje de éxito específico para cambio de rol
      const rolChanged = selectedUser.rol !== formData.rol;
      if (rolChanged) {
        const roleNames = {
          'paciente': 'Paciente',
          'psicologo': 'Psicólogo',
          'administrador': 'Administrador'
        };
        toast.success(`Usuario actualizado. Rol cambiado a: ${roleNames[formData.rol]}`);
        console.log(`🎯 Rol cambiado de "${selectedUser.rol}" a "${formData.rol}"`);
      } else {
        toast.success('Usuario actualizado exitosamente');
      }

      // Cerrar modal y limpiar estado
      setShowEditModal(false);
      setSelectedUser(null);
      setFormData({
        email: '',
        password: '',
        nombre: '',
        apellido: '',
        documento: '',
        rol: 'paciente',
        activo: true
      });

      // Recargar usuarios para reflejar cambios inmediatamente
      console.log('🔄 Recargando lista de usuarios...');
      await fetchUsers();
      console.log('✅ Lista de usuarios actualizada');

    } catch (error) {
      console.error('❌ Error updating user:', error);
      toast.error('Error al actualizar usuario: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Función para eliminar usuario completamente
  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      setLoading(true);

      console.log(`🗑️ Iniciando eliminación del usuario: ${selectedUser.nombre} ${selectedUser.apellido} (ID: ${selectedUser.id})`);

      // Paso 1: Eliminar datos relacionados del usuario (si existen)
      const relatedTables = ['resultados', 'sesiones', 'evaluaciones', 'asignaciones'];

      for (const table of relatedTables) {
        try {
          console.log(`🔍 Verificando datos en tabla: ${table}`);

          // Intentar eliminar registros relacionados
          const { error: relatedError } = await supabase
            .from(table)
            .delete()
            .eq('usuario_id', selectedUser.id);

          if (relatedError && !relatedError.message.includes('relation') && !relatedError.message.includes('does not exist')) {
            console.warn(`⚠️ Error al eliminar de ${table}:`, relatedError.message);
          } else if (!relatedError) {
            console.log(`✅ Datos eliminados de ${table}`);
          }
        } catch (tableError) {
          // Si la tabla no existe, continuamos
          console.log(`ℹ️ Tabla ${table} no existe o no tiene datos relacionados`);
        }
      }

      // Paso 2: Eliminar el perfil del usuario de la tabla 'usuarios'
      console.log('🗑️ Eliminando perfil de usuario...');
      const { error: profileError } = await supabase
        .from('usuarios')
        .delete()
        .eq('id', selectedUser.id);

      if (profileError) {
        console.error('❌ Error eliminando perfil:', profileError);
        throw new Error(`Error al eliminar perfil: ${profileError.message}`);
      }

      console.log('✅ Perfil de usuario eliminado exitosamente');

      // Paso 3: Eliminar de Supabase Auth
      console.log('🔐 Eliminando cuenta de autenticación...');
      const { error: authError } = await supabase.auth.admin.deleteUser(selectedUser.id);

      if (authError) {
        if (authError.message === 'User not found') {
          console.log('ℹ️ Usuario no encontrado en auth (ya eliminado)');
        } else {
          console.error('⚠️ Error eliminando cuenta de auth:', authError);
          toast.warn('El perfil fue eliminado, pero hubo un problema con la cuenta de autenticación. Contacte a soporte.');
        }
      } else {
        console.log('✅ Cuenta de autenticación eliminada exitosamente');
      }

      // Paso 4: Mostrar mensaje de éxito y actualizar interfaz
      toast.success(`Usuario ${selectedUser.nombre} ${selectedUser.apellido} eliminado exitosamente`);
      console.log('🎉 Usuario eliminado completamente del sistema');

      // Cerrar modal y limpiar estado
      setShowDeleteModal(false);
      setSelectedUser(null);

      // Recargar lista de usuarios
      await fetchUsers();

    } catch (error) {
      console.error('❌ Error durante eliminación:', error);
      toast.error(`Error al eliminar usuario: ${error.message}`);

      // En caso de error, mantener el modal abierto para que el usuario pueda reintentar
    } finally {
      setLoading(false);
    }
  };

  // Función para verificar dependencias del usuario antes de eliminar
  const checkUserDependencies = async (userId) => {
    const dependencies = [];
    const relatedTables = [
      { table: 'resultados', field: 'usuario_id', description: 'resultados de evaluaciones' },
      { table: 'sesiones', field: 'usuario_id', description: 'sesiones activas' },
      { table: 'evaluaciones', field: 'usuario_id', description: 'evaluaciones asignadas' },
      { table: 'asignaciones', field: 'usuario_id', description: 'asignaciones de pacientes' }
    ];

    for (const { table, field, description } of relatedTables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('id')
          .eq(field, userId)
          .limit(1);

        if (!error && data && data.length > 0) {
          dependencies.push(description);
        }
      } catch (error) {
        // Si la tabla no existe, continuamos
        console.log(`Tabla ${table} no existe o no es accesible`);
      }
    }

    return dependencies;
  };

  // Función para cambiar estado del usuario
  const toggleUserStatus = async (user) => {
    try {
      const newStatus = !user.activo;
      const action = newStatus ? 'activar' : 'desactivar';

      if (!confirm(`¿Estás seguro de que quieres ${action} a ${user.nombre} ${user.apellido}?`)) {
        return;
      }

      const { error } = await supabase
        .from('usuarios')
        .update({
          activo: newStatus,
          fecha_actualizacion: new Date().toISOString()
        })
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      toast.success(`Usuario ${action}do exitosamente`);

      // Recargar usuarios
      await fetchUsers();

    } catch (error) {
      console.error('Error toggling user status:', error);
      toast.error('Error al cambiar estado del usuario');
    }
  };

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Gestión de Usuarios</h2>
        <p className="text-gray-600 mt-2">Administra usuarios del sistema</p>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <FaUsers className="w-8 h-8 text-blue-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Total</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <FaUserCheck className="w-8 h-8 text-green-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Activos</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activos}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <FaUserTimes className="w-8 h-8 text-red-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Inactivos</p>
              <p className="text-2xl font-bold text-gray-900">{stats.inactivos}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <FaUserShield className="w-8 h-8 text-purple-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Admins</p>
              <p className="text-2xl font-bold text-gray-900">{stats.administradores}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <FaUsers className="w-8 h-8 text-indigo-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Psicólogos</p>
              <p className="text-2xl font-bold text-gray-900">{stats.psicologos}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <FaUsers className="w-8 h-8 text-teal-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Pacientes</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pacientes}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Barra de búsqueda y filtros */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          {/* Búsqueda */}
          <div className="flex-1 max-w-md">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Buscar por nombre, email o documento..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              {searchTerm !== debouncedSearchTerm && (
                <FaSpinner className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 animate-spin" />
              )}
            </div>
          </div>

          {/* Filtros */}
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            {/* Filtro por rol */}
            <div className="flex items-center space-x-2">
              <FaFilter className="text-gray-400 w-4 h-4" />
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="all">Todos los roles</option>
                <option value="administrador">Administradores</option>
                <option value="psicologo">Psicólogos</option>
                <option value="paciente">Pacientes</option>
              </select>
            </div>

            {/* Filtro por estado */}
            <div className="flex items-center space-x-2">
              <FaUserCheck className="text-gray-400 w-4 h-4" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="all">Todos los estados</option>
                <option value="active">Activos</option>
                <option value="inactive">Inactivos</option>
              </select>
            </div>

            {/* Botón limpiar filtros */}
            {(searchTerm || roleFilter !== 'all' || statusFilter !== 'all') && (
              <button
                onClick={clearFilters}
                className="px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors text-sm flex items-center space-x-1"
                title="Limpiar filtros"
              >
                <FaBroom className="w-4 h-4" />
                <span>Limpiar</span>
              </button>
            )}

            {/* Botón crear usuario */}
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <FaUserPlus className="mr-2" />
              Crear Usuario
            </button>
          </div>
        </div>

        {/* Contador de resultados */}
        <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
          <span>
            Mostrando {startIndex + 1} a {Math.min(endIndex, filteredUsers.length)} de {filteredUsers.length} usuarios
          </span>
          {(searchTerm || roleFilter !== 'all' || statusFilter !== 'all') && (
            <span className="text-blue-600">
              Filtros activos
            </span>
          )}
        </div>
      </div>

      {/* Tabla de usuarios */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usuario
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Documento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rol
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan="6" className="px-6 py-12 text-center">
                    <div className="flex items-center justify-center space-x-2">
                      <FaSpinner className="animate-spin text-blue-500 w-5 h-5" />
                      <span className="text-gray-600">Cargando usuarios...</span>
                    </div>
                  </td>
                </tr>
              ) : paginatedUsers.length === 0 ? (
                <tr>
                  <td colSpan="6" className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center space-y-3">
                      <FaSearch className="w-12 h-12 text-gray-300" />
                      <div className="text-gray-500">
                        <p className="text-lg font-medium">No se encontraron usuarios</p>
                        <p className="text-sm">
                          {(searchTerm || roleFilter !== 'all' || statusFilter !== 'all')
                            ? 'Intenta ajustar los filtros de búsqueda'
                            : 'No hay usuarios registrados en el sistema'
                          }
                        </p>
                      </div>
                      {(searchTerm || roleFilter !== 'all' || statusFilter !== 'all') && (
                        <button
                          onClick={clearFilters}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                        >
                          Limpiar filtros
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ) : (
                paginatedUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                            <FaUsers className="h-5 w-5 text-gray-600" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user.nombre} {user.apellido}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {user.id?.substring(0, 8)}...
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.email || 'Sin email'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.documento || 'Sin documento'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.rol === 'administrador' ? 'bg-purple-100 text-purple-800' :
                        user.rol === 'psicologo' ? 'bg-indigo-100 text-indigo-800' :
                        'bg-teal-100 text-teal-800'
                      }`}>
                        {user.rol === 'administrador' ? 'Administrador' :
                         user.rol === 'psicologo' ? 'Psicólogo' : 'Paciente'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.activo ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {user.activo ? 'Activo' : 'Inactivo'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setSelectedUser(user);
                            setFormData({
                              email: user.email || '',
                              password: '',
                              nombre: user.nombre || '',
                              apellido: user.apellido || '',
                              documento: user.documento || '',
                              rol: user.rol || 'paciente',
                              activo: user.activo
                            });
                            setShowEditModal(true);
                          }}
                          className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                          title="Editar usuario"
                        >
                          <FaEdit className="w-4 h-4" />
                        </button>

                        <button
                          onClick={() => toggleUserStatus(user)}
                          className={`p-1 rounded ${user.activo ? 'text-red-600 hover:text-red-900 hover:bg-red-50' : 'text-green-600 hover:text-green-900 hover:bg-green-50'}`}
                          title={user.activo ? 'Desactivar usuario' : 'Activar usuario'}
                        >
                          {user.activo ? <FaEyeSlash className="w-4 h-4" /> : <FaEye className="w-4 h-4" />}
                        </button>

                        <button
                          onClick={() => {
                            setSelectedUser(user);
                            setShowDeleteModal(true);
                          }}
                          className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                          title="Eliminar usuario"
                        >
                          <FaTrash className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Paginación */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={goToPreviousPage}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Anterior
              </button>
              <button
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Siguiente
              </button>
            </div>

            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div className="flex items-center space-x-4">
                <p className="text-sm text-gray-700">
                  Mostrando{' '}
                  <span className="font-medium">{startIndex + 1}</span>
                  {' '}a{' '}
                  <span className="font-medium">{Math.min(endIndex, filteredUsers.length)}</span>
                  {' '}de{' '}
                  <span className="font-medium">{filteredUsers.length}</span>
                  {' '}usuarios
                </p>

                <select
                  value={pageSize}
                  onChange={(e) => setPageSize(Number(e.target.value))}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={5}>5 por página</option>
                  <option value={10}>10 por página</option>
                  <option value={25}>25 por página</option>
                  <option value={50}>50 por página</option>
                </select>
              </div>

              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={goToPreviousPage}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Anterior</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>

                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <button
                        key={page}
                        onClick={() => goToPage(page)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          page === currentPage
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    );
                  })}

                  <button
                    onClick={goToNextPage}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Siguiente</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Error state */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error al cargar usuarios</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={fetchUsers}
                  className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                >
                  Reintentar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal para crear usuario */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Crear Nuevo Usuario</h3>

              {/* Información sobre el proceso */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">
                      ✅ Creación Completa de Usuario
                    </h3>
                    <div className="mt-2 text-sm text-green-700">
                      <p>
                        Se creará una cuenta completa con acceso inmediato. El usuario podrá
                        hacer login inmediatamente con las credenciales proporcionadas.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <form onSubmit={handleCreateUser} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email *
                  </label>
                  <input
                    type="email"
                    required
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contraseña *
                  </label>
                  <input
                    type="password"
                    required
                    value={formData.password}
                    onChange={(e) => setFormData({...formData, password: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Mínimo 6 caracteres"
                    minLength="6"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    El usuario podrá hacer login inmediatamente con esta contraseña.
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nombre *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.nombre}
                      onChange={(e) => setFormData({...formData, nombre: e.target.value})}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Apellido *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.apellido}
                      onChange={(e) => setFormData({...formData, apellido: e.target.value})}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Documento
                  </label>
                  <input
                    type="text"
                    value={formData.documento}
                    onChange={(e) => setFormData({...formData, documento: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Número de documento"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Rol *
                  </label>
                  <select
                    required
                    value={formData.rol}
                    onChange={(e) => setFormData({...formData, rol: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="paciente">Paciente</option>
                    <option value="psicologo">Psicólogo</option>
                    <option value="administrador">Administrador</option>
                  </select>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="activo"
                    checked={formData.activo}
                    onChange={(e) => setFormData({...formData, activo: e.target.checked})}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="activo" className="ml-2 text-sm text-gray-700">
                    Usuario activo
                  </label>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={() => {
                      setShowCreateModal(false);
                      setFormData({
                        email: '',
                        password: '',
                        nombre: '',
                        apellido: '',
                        documento: '',
                        rol: 'paciente',
                        activo: true
                      });
                    }}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                  >
                    {loading ? 'Creando...' : 'Crear Usuario'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Modal para editar usuario */}
      {showEditModal && selectedUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Editar Usuario: {selectedUser.nombre} {selectedUser.apellido}
              </h3>

              <form onSubmit={handleEditUser} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    disabled
                    className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-100 text-gray-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">El email no se puede modificar</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nueva Contraseña (opcional)
                  </label>
                  <input
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData({...formData, password: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Dejar vacío para no cambiar"
                    minLength="6"
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nombre *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.nombre}
                      onChange={(e) => setFormData({...formData, nombre: e.target.value})}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Apellido *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.apellido}
                      onChange={(e) => setFormData({...formData, apellido: e.target.value})}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Documento
                  </label>
                  <input
                    type="text"
                    value={formData.documento}
                    onChange={(e) => setFormData({...formData, documento: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Rol *
                  </label>
                  <select
                    required
                    value={formData.rol}
                    onChange={(e) => setFormData({...formData, rol: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="paciente">Paciente</option>
                    <option value="psicologo">Psicólogo</option>
                    <option value="administrador">Administrador</option>
                  </select>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="activo-edit"
                    checked={formData.activo}
                    onChange={(e) => setFormData({...formData, activo: e.target.checked})}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="activo-edit" className="ml-2 text-sm text-gray-700">
                    Usuario activo
                  </label>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={() => {
                      setShowEditModal(false);
                      setSelectedUser(null);
                      setFormData({
                        email: '',
                        password: '',
                        nombre: '',
                        apellido: '',
                        documento: '',
                        rol: 'paciente',
                        activo: true
                      });
                    }}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                  >
                    {loading ? 'Guardando...' : 'Guardar Cambios'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Modal para eliminar usuario */}
      {showDeleteModal && selectedUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mx-auto">
                <FaTrash className="h-6 w-6 text-red-600" />
              </div>

              <h3 className="text-lg leading-6 font-medium text-gray-900 mt-4 text-center">
                Eliminar Usuario
              </h3>

              <div className="mt-4 px-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">
                        ⚠️ Acción Irreversible
                      </h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>Esta acción eliminará permanentemente:</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">Usuario a eliminar:</h4>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p><strong>Nombre:</strong> {selectedUser.nombre} {selectedUser.apellido}</p>
                    <p><strong>Email:</strong> {selectedUser.email}</p>
                    <p><strong>Rol:</strong> {selectedUser.rol}</p>
                    <p><strong>Estado:</strong> {selectedUser.activo ? 'Activo' : 'Inactivo'}</p>
                  </div>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-red-800 mb-2">Se eliminarán:</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• Perfil del usuario en la base de datos</li>
                    <li>• Cuenta de autenticación</li>
                    <li>• Todos los datos relacionados (resultados, sesiones, etc.)</li>
                    <li>• Historial de actividad del usuario</li>
                  </ul>
                </div>

                <div className="text-center text-sm text-gray-600 mb-6">
                  <p>
                    ¿Estás seguro de que quieres eliminar a{' '}
                    <strong className="text-gray-900">{selectedUser.nombre} {selectedUser.apellido}</strong>?
                  </p>
                  <p className="mt-1 text-red-600 font-medium">
                    Esta acción no se puede deshacer.
                  </p>
                </div>
              </div>

              <div className="flex justify-center items-center space-x-4 mt-6 px-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedUser(null);
                  }}
                  className="px-6 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
                  disabled={loading}
                >
                  Cancelar
                </button>
                <button
                  type="button"
                  onClick={handleDeleteUser}
                  disabled={loading}
                  className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 transition-colors flex items-center"
                >
                  {loading && <FaSpinner className="animate-spin -ml-1 mr-2 h-4 w-4" />}
                  {loading ? 'Eliminando...' : 'Eliminar Definitivamente'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleUserManagementPanel;
