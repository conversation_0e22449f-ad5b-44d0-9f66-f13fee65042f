/**
 * Script para verificar los pines del psicólogo
 */

const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://ydglduxhgwajqdseqzpy.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjMxMjg0MSwiZXhwIjoyMDYxODg4ODQxfQ.zwk3Wiay5jjeYOrg8B1M6T98B2esCqRFI43At-AFV3A'
);

async function checkPsychologistData() {
  try {
    console.log('Verificando datos del psicologo 5e02c38d-47af-40ec-abce-6c7bd1c1e7ae...');
    
    const { data, error } = await supabase
      .from('psychologist_usage_control')
      .select('*')
      .eq('psychologist_id', '5e02c38d-47af-40ec-abce-6c7bd1c1e7ae');
    
    if (error) {
      console.error('Error:', error);
      return;
    }
    
    console.log('Registros encontrados:', data.length);
    data.forEach((record, index) => {
      console.log(`Registro ${index + 1}:`);
      console.log('   ID:', record.id);
      console.log('   Psychologist ID:', record.psychologist_id);
      console.log('   Total Uses:', record.total_uses);
      console.log('   Used Uses:', record.used_uses);
      console.log('   Is Unlimited:', record.is_unlimited);
      console.log('   Is Active:', record.is_active);
      console.log('   Created At:', record.created_at);
      console.log('   Updated At:', record.updated_at);
      console.log('   ---');
    });
    
    // Verificar con filtro is_active = true
    const { data: activeData, error: activeError } = await supabase
      .from('psychologist_usage_control')
      .select('*')
      .eq('psychologist_id', '5e02c38d-47af-40ec-abce-6c7bd1c1e7ae')
      .eq('is_active', true);
    
    if (activeError) {
      console.error('Error con filtro activo:', activeError);
      return;
    }
    
    console.log('Registros activos encontrados:', activeData.length);
    if (activeData.length > 0) {
      const active = activeData[0];
      console.log('Registro activo:');
      console.log('   Total Uses:', active.total_uses);
      console.log('   Used Uses:', active.used_uses);
      console.log('   Pines disponibles:', active.total_uses - active.used_uses);
      console.log('   Is Unlimited:', active.is_unlimited);
    } else {
      console.log('No hay registros activos para este psicologo');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkPsychologistData();
