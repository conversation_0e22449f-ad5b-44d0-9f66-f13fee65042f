/**
 * @file aptitudeConstants.js
 * @description Configuración centralizada de colores, iconos y propiedades para cada aptitud
 * Basado en las imágenes de referencia proporcionadas para mantener consistencia visual
 */

import { 
  HiOutlineChatAlt2, 
  HiOutlineCube, 
  HiOutlineEye, 
  HiOutlineCalculator, 
  HiOutlineCog, 
  HiOutlineBookOpen 
} from 'react-icons/hi';
import { FaBullseye, FaPuzzlePiece } from 'react-icons/fa';

/**
 * Configuración estandarizada de aptitudes según las imágenes de referencia
 * Cada aptitud tiene colores e iconos específicos que deben mantenerse consistentes
 * en todas las vistas: pantalla, impresión y PDF
 */
export const APTITUDE_CONFIG = {
  'V': {
    code: 'V',
    name: 'Aptitud Verbal',
    shortName: 'Verbal',
    description: 'Evaluación de analogías verbales y comprensión de relaciones entre contextos',
    // Colores basados en la imagen de referencia - AZUL
    color: '#2563EB',           // Color principal (azul)
    bgColor: 'bg-blue-500',     // Clase Tailwind para fondo
    textColor: 'text-blue-600', // Clase Tailwind para texto
    borderColor: 'border-blue-500', // Clase Tailwind para borde
    lightBg: 'bg-blue-50',      // Fondo claro
    // Icono específico
    icon: HiOutlineChatAlt2,
    iconClass: 'fas fa-comments',
    // Configuración para diferentes contextos
    badge: {
      bg: 'bg-orange-500',      // Naranja para el badge "PENDIENTE"
      text: 'text-white'
    },
    button: {
      bg: 'bg-blue-500',
      hover: 'hover:bg-blue-600',
      text: 'text-white'
    }
  },
  'E': {
    code: 'E',
    name: 'Aptitud Espacial',
    shortName: 'Espacial',
    description: 'Razonamiento espacial con cubos y redes geométricas',
    // Colores basados en la imagen de referencia - MORADO/PÚRPURA
    color: '#6D28D9',           // Color principal (morado)
    bgColor: 'bg-purple-500',   // Clase Tailwind para fondo
    textColor: 'text-purple-600', // Clase Tailwind para texto
    borderColor: 'border-purple-500', // Clase Tailwind para borde
    lightBg: 'bg-purple-50',    // Fondo claro
    // Icono específico
    icon: HiOutlineCube,
    iconClass: 'fas fa-cube',
    // Configuración para diferentes contextos
    badge: {
      bg: 'bg-orange-500',      // Naranja para el badge "PENDIENTE"
      text: 'text-white'
    },
    button: {
      bg: 'bg-purple-500',
      hover: 'hover:bg-purple-600',
      text: 'text-white'
    }
  },
  'A': {
    code: 'A',
    name: 'Atención',
    shortName: 'Atención',
    description: 'Rapidez y precisión en la localización de símbolos específicos',
    // Colores basados en la imagen de referencia - ROJO
    color: '#DC2626',           // Color principal (rojo)
    bgColor: 'bg-red-500',      // Clase Tailwind para fondo
    textColor: 'text-red-600',  // Clase Tailwind para texto
    borderColor: 'border-red-500', // Clase Tailwind para borde
    lightBg: 'bg-red-50',       // Fondo claro
    // Icono específico
    icon: HiOutlineEye,
    iconClass: 'fas fa-eye',
    // Configuración para diferentes contextos
    badge: {
      bg: 'bg-green-500',       // Verde para el badge "COMPLETADO"
      text: 'text-white'
    },
    button: {
      bg: 'bg-orange-500',      // Naranja para "Repetir Test"
      hover: 'hover:bg-orange-600',
      text: 'text-white'
    }
  },
  'C': {
    code: 'C',
    name: 'Concentración',
    shortName: 'Concentración',
    description: 'Precisión en el procesamiento de operaciones mentales simples',
    // Colores basados en la imagen de referencia - AMARILLO
    color: '#EAB308',           // Color principal (amarillo)
    bgColor: 'bg-yellow-500',   // Clase Tailwind para fondo
    textColor: 'text-yellow-600', // Clase Tailwind para texto
    borderColor: 'border-yellow-500', // Clase Tailwind para borde
    lightBg: 'bg-yellow-50',    // Fondo claro
    // Icono específico
    icon: HiOutlineCalculator,
    iconClass: 'fas fa-crosshairs',
    // Configuración para diferentes contextos
    badge: {
      bg: 'bg-green-500',       // Verde para el badge "COMPLETADO"
      text: 'text-white'
    },
    button: {
      bg: 'bg-yellow-500',
      hover: 'hover:bg-yellow-600',
      text: 'text-white'
    }
  },
  'R': {
    code: 'R',
    name: 'Razonamiento',
    shortName: 'Razonamiento',
    description: 'Continuar series lógicas de figuras y patrones',
    // Colores basados en la imagen de referencia - NARANJA
    color: '#D97706',           // Color principal (naranja)
    bgColor: 'bg-orange-500',   // Clase Tailwind para fondo
    textColor: 'text-orange-600', // Clase Tailwind para texto
    borderColor: 'border-orange-500', // Clase Tailwind para borde
    lightBg: 'bg-orange-50',    // Fondo claro
    // Icono específico
    icon: FaPuzzlePiece,
    iconClass: 'fas fa-puzzle-piece',
    // Configuración para diferentes contextos
    badge: {
      bg: 'bg-orange-500',      // Naranja para el badge "PENDIENTE"
      text: 'text-white'
    },
    button: {
      bg: 'bg-orange-500',
      hover: 'hover:bg-orange-600',
      text: 'text-white'
    }
  },
  'N': {
    code: 'N',
    name: 'Aptitud Numérica',
    shortName: 'Numérica',
    description: 'Resolución de igualdades, series numéricas y análisis de datos',
    // Colores basados en la imagen de referencia - VERDE AZULADO/TEAL
    color: '#0F766E',           // Color principal (teal)
    bgColor: 'bg-teal-500',     // Clase Tailwind para fondo
    textColor: 'text-teal-600', // Clase Tailwind para texto
    borderColor: 'border-teal-500', // Clase Tailwind para borde
    lightBg: 'bg-teal-50',      // Fondo claro
    // Icono específico
    icon: HiOutlineCalculator,
    iconClass: 'fas fa-calculator',
    // Configuración para diferentes contextos
    badge: {
      bg: 'bg-orange-500',      // Naranja para el badge "PENDIENTE"
      text: 'text-white'
    },
    button: {
      bg: 'bg-teal-500',
      hover: 'hover:bg-teal-600',
      text: 'text-white'
    }
  },
  'M': {
    code: 'M',
    name: 'Aptitud Mecánica',
    shortName: 'Mecánica',
    description: 'Comprensión de principios físicos y mecánicos básicos',
    // Colores basados en la imagen de referencia - GRIS
    color: '#374151',           // Color principal (gris oscuro)
    bgColor: 'bg-gray-500',     // Clase Tailwind para fondo
    textColor: 'text-gray-600', // Clase Tailwind para texto
    borderColor: 'border-gray-500', // Clase Tailwind para borde
    lightBg: 'bg-gray-50',      // Fondo claro
    // Icono específico
    icon: HiOutlineCog,
    iconClass: 'fas fa-cog',
    // Configuración para diferentes contextos
    badge: {
      bg: 'bg-orange-500',      // Naranja para el badge "PENDIENTE"
      text: 'text-white'
    },
    button: {
      bg: 'bg-gray-500',
      hover: 'hover:bg-gray-600',
      text: 'text-white'
    }
  },
  'O': {
    code: 'O',
    name: 'Ortografía',
    shortName: 'Ortografía',
    description: 'Identificación de palabras con errores ortográficos',
    // Colores basados en la imagen de referencia - VERDE
    color: '#16A34A',           // Color principal (verde)
    bgColor: 'bg-green-500',    // Clase Tailwind para fondo
    textColor: 'text-green-600', // Clase Tailwind para texto
    borderColor: 'border-green-500', // Clase Tailwind para borde
    lightBg: 'bg-green-50',     // Fondo claro
    // Icono específico
    icon: HiOutlineBookOpen,
    iconClass: 'fas fa-spell-check',
    // Configuración para diferentes contextos
    badge: {
      bg: 'bg-orange-500',      // Naranja para el badge "PENDIENTE"
      text: 'text-white'
    },
    button: {
      bg: 'bg-green-500',
      hover: 'hover:bg-green-600',
      text: 'text-white'
    }
  }
};

/**
 * Configuración de niveles de percentil con colores estandarizados
 */
export const PERCENTIL_LEVELS = {
  VERY_HIGH: { 
    min: 95, 
    max: 100, 
    level: 'Muy Alto', 
    color: '#8B5CF6', 
    textColor: 'text-purple-800', 
    bgColor: 'bg-purple-100',
    bgClass: 'bg-purple-500'
  },
  HIGH: { 
    min: 81, 
    max: 94, 
    level: 'Alto', 
    color: '#10B981', 
    textColor: 'text-green-800', 
    bgColor: 'bg-green-100',
    bgClass: 'bg-green-500'
  },
  MEDIUM_HIGH: { 
    min: 61, 
    max: 80, 
    level: 'Medio-Alto', 
    color: '#3B82F6', 
    textColor: 'text-blue-800', 
    bgColor: 'bg-blue-100',
    bgClass: 'bg-blue-500'
  },
  MEDIUM: { 
    min: 41, 
    max: 60, 
    level: 'Medio', 
    color: '#6B7280', 
    textColor: 'text-gray-800', 
    bgColor: 'bg-gray-100',
    bgClass: 'bg-gray-500'
  },
  MEDIUM_LOW: { 
    min: 21, 
    max: 40, 
    level: 'Medio-Bajo', 
    color: '#F59E0B', 
    textColor: 'text-yellow-800', 
    bgColor: 'bg-yellow-100',
    bgClass: 'bg-yellow-500'
  },
  LOW: { 
    min: 6, 
    max: 20, 
    level: 'Bajo', 
    color: '#F97316', 
    textColor: 'text-orange-800', 
    bgColor: 'bg-orange-100',
    bgClass: 'bg-orange-500'
  },
  VERY_LOW: { 
    min: 0, 
    max: 5, 
    level: 'Muy Bajo', 
    color: '#EF4444', 
    textColor: 'text-red-800', 
    bgColor: 'bg-red-100',
    bgClass: 'bg-red-500'
  }
};

/**
 * Función para obtener la configuración de una aptitud por código
 * @param {string} code - Código de la aptitud (V, E, A, R, N, M, O)
 * @returns {Object} Configuración de la aptitud
 */
export const getAptitudeConfig = (code) => {
  return APTITUDE_CONFIG[code] || {
    code: code,
    name: 'Desconocido',
    shortName: 'Desconocido',
    description: 'Aptitud no identificada',
    color: '#374151',
    bgColor: 'bg-gray-500',
    textColor: 'text-gray-600',
    borderColor: 'border-gray-500',
    lightBg: 'bg-gray-50',
    icon: FaPuzzlePiece,
    iconClass: 'fas fa-question',
    badge: { bg: 'bg-gray-500', text: 'text-white' },
    button: { bg: 'bg-gray-500', hover: 'hover:bg-gray-600', text: 'text-white' }
  };
};

/**
 * Función para obtener el nivel de percentil
 * @param {number} percentil - Valor del percentil
 * @returns {Object} Configuración del nivel de percentil
 */
export const getPercentilLevel = (percentil) => {
  const levels = Object.values(PERCENTIL_LEVELS);
  return levels.find(level => percentil >= level.min && percentil <= level.max) || PERCENTIL_LEVELS.VERY_LOW;
};

/**
 * Mapeo de colores para compatibilidad con código existente
 */
export const TEST_COLORS = {
  'V': APTITUDE_CONFIG.V.color,
  'E': APTITUDE_CONFIG.E.color,
  'A': APTITUDE_CONFIG.A.color,
  'C': APTITUDE_CONFIG.C.color,
  'R': APTITUDE_CONFIG.R.color,
  'N': APTITUDE_CONFIG.N.color,
  'M': APTITUDE_CONFIG.M.color,
  'O': APTITUDE_CONFIG.O.color
};

export default APTITUDE_CONFIG;

// Configuración actualizada para incluir Concentración (C)
