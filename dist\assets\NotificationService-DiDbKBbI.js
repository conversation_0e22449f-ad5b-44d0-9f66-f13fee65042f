var t=(t,e,i)=>new Promise((s,o)=>{var r=t=>{try{a(i.next(t))}catch(e){o(e)}},n=t=>{try{a(i.throw(t))}catch(e){o(e)}},a=t=>t.done?s(t.value):Promise.resolve(t.value).then(r,n);a((i=i.apply(t,e)).next())});import{a as e,P as i}from"./PinLogger-C2v3yGM1.js";import{s}from"./index-Bdl1jgS_.js";class o{static validateAssignPins(t,i,s,o){const r=[];return t&&"string"==typeof t||r.push("psychologistId must be a valid string"),s||i&&!(i<0)&&Number.isInteger(i)||r.push("pins must be a positive integer when not unlimited"),o&&!Object.values(e.PLAN_TYPES).includes(o)&&r.push("planType must be a valid plan type"),{isValid:0===r.length,errors:r}}static validateConsumePin(t,e,i,s){const o=[];return t&&"string"==typeof t||o.push("psychologistId must be a valid string"),e&&"string"!=typeof e&&o.push("patientId must be a string if provided"),i&&"string"!=typeof i&&o.push("testSessionId must be a string if provided"),s&&"string"!=typeof s&&o.push("reportId must be a string if provided"),{isValid:0===o.length,errors:o}}}class r{getPsychologistUsage(e){return t(this,null,function*(){const{data:t,error:i}=yield s.from("psychologist_usage_control").select("*").eq("psychologist_id",e).eq("is_active",!0).single();if(i&&"PGRST116"!==i.code)throw i;return t})}upsertPsychologistUsage(e,i,s,o){return t(this,null,function*(){const t=yield this.getPsychologistUsage(e);return t?yield this._updateExistingUsage(t,i,s,o):yield this._createNewUsage(e,i,s,o)})}incrementUsedPins(e){return t(this,null,function*(){const{data:t,error:i}=yield s.rpc("consume_pin",{p_control_id:e});if(i)throw i;return t})}getPinUsageHistory(){return t(this,arguments,function*(t=null,i=e.DEFAULT_HISTORY_LIMIT){let o=s.from("pin_usage_logs").select("\n        *,\n        psychologist:psicologos(nombre, apellido, email),\n        patient:pacientes(nombre, apellido, documento)\n      ").order("created_at",{ascending:!1}).limit(i);t&&(o=o.eq("psychologist_id",t));const{data:r,error:n}=yield o;if(n)throw n;return r||[]})}_updateExistingUsage(e,i,o,r){return t(this,null,function*(){const t=o?e.total_uses:e.total_uses+i,{data:n,error:a}=yield s.from("psychologist_usage_control").update({total_uses:t,is_unlimited:o,plan_type:r,updated_at:(new Date).toISOString(),is_active:!0}).eq("id",e.id).select().single();if(a)throw a;return n})}_createNewUsage(e,i,o,r){return t(this,null,function*(){const{data:t,error:n}=yield s.from("psychologist_usage_control").insert({psychologist_id:e,total_uses:o?0:i,used_uses:0,is_unlimited:o,plan_type:r,is_active:!0}).select().single();if(n)throw n;return t})}}class n{createLowPinNotification(e,o){return t(this,null,function*(){try{const{error:t}=yield s.rpc("create_low_pin_notification",{p_psychologist_id:e,p_remaining_pins:o});if(t)throw i.logError("Error creating low pin notification",t),t;i.logInfo(`Low pin notification created for psychologist ${e}`)}catch(t){throw i.logError("Error in createLowPinNotification",t),t}})}createPinExhaustedNotification(e){return t(this,null,function*(){try{const{error:t}=yield s.rpc("create_pin_exhausted_notification",{p_psychologist_id:e});if(t)throw i.logError("Error creating pin exhausted notification",t),t;i.logInfo(`Pin exhausted notification created for psychologist ${e}`)}catch(t){throw i.logError("Error in createPinExhaustedNotification",t),t}})}}export{n as N,r as P,o as a};
