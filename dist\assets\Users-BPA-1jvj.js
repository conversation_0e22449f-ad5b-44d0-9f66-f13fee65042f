var e=Object.defineProperty,s=Object.defineProperties,a=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,i=(s,a,r)=>a in s?e(s,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):s[a]=r,o=(e,s)=>{for(var a in s||(s={}))l.call(s,a)&&i(e,a,s[a]);if(r)for(var a of r(s))t.call(s,a)&&i(e,a,s[a]);return e},n=(e,r)=>s(e,a(r)),d=(e,s,a)=>new Promise((r,l)=>{var t=e=>{try{o(a.next(e))}catch(s){l(s)}},i=e=>{try{o(a.throw(e))}catch(s){l(s)}},o=e=>e.done?r(e.value):Promise.resolve(e.value).then(t,i);o((a=a.apply(e,s)).next())});import{r as c,Q as m,j as x,v as u,m as p,w as h,x as g,y as b,h as f,z as v,A as y,B as j,C as N,D as w}from"./vendor-BqMjyOVw.js";import{s as C}from"./index-Bdl1jgS_.js";const k=()=>{const[e,s]=c.useState([]),[a,r]=c.useState(!0),[l,t]=c.useState(null),[i,k]=c.useState(""),[S,A]=c.useState("all"),[P,E]=c.useState("all"),[L,q]=c.useState(1),[D,z]=c.useState(10),[U,O]=c.useState(!1),[M,R]=c.useState(!1),[I,_]=c.useState(null),[B,F]=c.useState({email:"",password:"",nombre:"",apellido:"",documento:"",rol:"paciente",activo:!0}),[$,G]=c.useState({total:0,activos:0,inactivos:0,administradores:0,psicologos:0,estudiantes:0});c.useEffect(()=>{T()},[]);const T=()=>d(null,null,function*(){try{r(!0),t(null);const{data:e,error:a}=yield C.from("usuarios").select("*").order("fecha_creacion",{ascending:!1});if(a)throw a;s(e||[]),Y(e||[])}catch(e){t("Error al cargar usuarios: "+e.message),m.error("Error al cargar usuarios")}finally{r(!1)}}),Y=e=>{const s=e.length,a=e.filter(e=>e.activo).length,r=s-a,l=e.filter(e=>"administrador"===e.rol).length,t=e.filter(e=>"psicologo"===e.rol).length,i=e.filter(e=>"paciente"===e.rol).length;G({total:s,activos:a,inactivos:r,administradores:l,psicologos:t,pacientes:i})},H=()=>{let s=[...e];if(i.trim()){const e=i.toLowerCase().trim();s=s.filter(s=>{var a,r,l,t;return(null==(a=s.nombre)?void 0:a.toLowerCase().includes(e))||(null==(r=s.apellido)?void 0:r.toLowerCase().includes(e))||(null==(l=s.email)?void 0:l.toLowerCase().includes(e))||(null==(t=s.documento)?void 0:t.toLowerCase().includes(e))})}if("all"!==S&&(s=s.filter(e=>e.rol===S)),"all"!==P){const e="active"===P;s=s.filter(s=>s.activo===e)}return s},J=()=>{const e=H(),s=(L-1)*D,a=s+D;return e.slice(s,a)},K=()=>Math.ceil(H().length/D),Q=()=>{k(""),A("all"),E("all"),q(1)};c.useEffect(()=>{q(1)},[i,S,P]);return x.jsxs("div",{className:"container mx-auto py-6 space-y-6",children:[x.jsxs("div",{className:"flex justify-between items-center",children:[x.jsxs("div",{children:[x.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"Gestión de Usuarios"}),x.jsx("p",{className:"text-gray-600 mt-1",children:"Administra las cuentas de usuario del sistema"})]}),x.jsxs("button",{onClick:()=>O(!0),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[x.jsx(u,{className:"mr-2"}),"Crear Usuario"]})]}),x.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[x.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:x.jsxs("div",{className:"flex items-center",children:[x.jsx(p,{className:"w-8 h-8 text-blue-500 mr-3"}),x.jsxs("div",{children:[x.jsx("p",{className:"text-sm text-gray-600",children:"Total"}),x.jsx("p",{className:"text-2xl font-bold text-gray-900",children:$.total})]})]})}),x.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:x.jsxs("div",{className:"flex items-center",children:[x.jsx(h,{className:"w-8 h-8 text-green-500 mr-3"}),x.jsxs("div",{children:[x.jsx("p",{className:"text-sm text-gray-600",children:"Activos"}),x.jsx("p",{className:"text-2xl font-bold text-gray-900",children:$.activos})]})]})}),x.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:x.jsxs("div",{className:"flex items-center",children:[x.jsx(g,{className:"w-8 h-8 text-red-500 mr-3"}),x.jsxs("div",{children:[x.jsx("p",{className:"text-sm text-gray-600",children:"Inactivos"}),x.jsx("p",{className:"text-2xl font-bold text-gray-900",children:$.inactivos})]})]})}),x.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:x.jsxs("div",{className:"flex items-center",children:[x.jsx(b,{className:"w-8 h-8 text-purple-500 mr-3"}),x.jsxs("div",{children:[x.jsx("p",{className:"text-sm text-gray-600",children:"Admins"}),x.jsx("p",{className:"text-2xl font-bold text-gray-900",children:$.administradores})]})]})}),x.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:x.jsxs("div",{className:"flex items-center",children:[x.jsx(p,{className:"w-8 h-8 text-indigo-500 mr-3"}),x.jsxs("div",{children:[x.jsx("p",{className:"text-sm text-gray-600",children:"Psicólogos"}),x.jsx("p",{className:"text-2xl font-bold text-gray-900",children:$.psicologos})]})]})}),x.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:x.jsxs("div",{className:"flex items-center",children:[x.jsx(p,{className:"w-8 h-8 text-teal-500 mr-3"}),x.jsxs("div",{children:[x.jsx("p",{className:"text-sm text-gray-600",children:"Pacientes"}),x.jsx("p",{className:"text-2xl font-bold text-gray-900",children:$.pacientes})]})]})})]}),x.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[x.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4",children:[x.jsx("div",{className:"flex-1 max-w-md",children:x.jsxs("div",{className:"relative",children:[x.jsx(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),x.jsx("input",{type:"text",placeholder:"Buscar por nombre, email o documento...",value:i,onChange:e=>k(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),x.jsxs("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3",children:[x.jsxs("div",{className:"flex items-center space-x-2",children:[x.jsx(v,{className:"text-gray-400 w-4 h-4"}),x.jsxs("select",{value:S,onChange:e=>A(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[x.jsx("option",{value:"all",children:"Todos los roles"}),x.jsx("option",{value:"administrador",children:"Administradores"}),x.jsx("option",{value:"psicologo",children:"Psicólogos"}),x.jsx("option",{value:"paciente",children:"Pacientes"})]})]}),x.jsxs("div",{className:"flex items-center space-x-2",children:[x.jsx(h,{className:"text-gray-400 w-4 h-4"}),x.jsxs("select",{value:P,onChange:e=>E(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[x.jsx("option",{value:"all",children:"Todos los estados"}),x.jsx("option",{value:"active",children:"Activos"}),x.jsx("option",{value:"inactive",children:"Inactivos"})]})]}),x.jsxs("button",{onClick:Q,className:"px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors text-sm flex items-center space-x-1",title:"Limpiar filtros",children:[x.jsx(y,{className:"w-4 h-4"}),x.jsx("span",{children:"Limpiar"})]})]})]}),x.jsxs("div",{className:"mt-4 flex items-center justify-between text-sm text-gray-600",children:[x.jsxs("span",{children:["Mostrando ",J().length," de ",H().length," usuarios"]}),(i||"all"!==S||"all"!==P)&&x.jsx("span",{className:"text-blue-600",children:"Filtros activos"})]})]}),x.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[x.jsx("div",{className:"overflow-x-auto",children:x.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[x.jsx("thead",{className:"bg-gray-50",children:x.jsxs("tr",{children:[x.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Usuario"}),x.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),x.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Documento"}),x.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rol"}),x.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Estado"}),x.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fecha Creación"}),x.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acciones"})]})}),x.jsxs("tbody",{className:"bg-white divide-y divide-gray-200",children:[J().map(e=>x.jsxs("tr",{className:"hover:bg-gray-50",children:[x.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:x.jsxs("div",{className:"flex items-center",children:[x.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:x.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:x.jsx(p,{className:"h-5 w-5 text-gray-600"})})}),x.jsx("div",{className:"ml-4",children:x.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]})})]})}),x.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.email||"Sin email"}),x.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.documento||"Sin documento"}),x.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:x.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full "+("administrador"===e.rol?"bg-purple-100 text-purple-800":"psicologo"===e.rol?"bg-indigo-100 text-indigo-800":"bg-teal-100 text-teal-800"),children:"administrador"===e.rol?"Administrador":"psicologo"===e.rol?"Psicólogo":"Paciente"})}),x.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:x.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full "+(e.activo?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.activo?"Activo":"Inactivo"})}),x.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.fecha_creacion?new Date(e.fecha_creacion).toLocaleDateString():"N/A"}),x.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:x.jsxs("div",{className:"flex space-x-2",children:[x.jsx("button",{onClick:()=>{_(e),F({email:e.email||"",password:"",nombre:e.nombre||"",apellido:e.apellido||"",documento:e.documento||"",rol:e.rol||"estudiante",activo:e.activo}),R(!0)},className:"text-blue-600 hover:text-blue-900",title:"Editar usuario",children:x.jsx(j,{className:"w-4 h-4"})}),x.jsx("button",{onClick:()=>(e=>d(null,null,function*(){try{const s=!e.activo,a=s?"activar":"desactivar";if(!confirm(`¿Estás seguro de que quieres ${a} a ${e.nombre} ${e.apellido}?`))return;const{error:r}=yield C.from("usuarios").update({activo:s,fecha_actualizacion:(new Date).toISOString()}).eq("id",e.id);if(r)throw r;m.success(`Usuario ${a}do exitosamente`),yield T()}catch(s){m.error("Error al cambiar estado del usuario")}}))(e),className:""+(e.activo?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"),title:e.activo?"Desactivar usuario":"Activar usuario",children:e.activo?x.jsx(N,{className:"w-4 h-4"}):x.jsx(w,{className:"w-4 h-4"})})]})})]},e.id)),0===J().length&&0===H().length&&x.jsx("tr",{children:x.jsx("td",{colSpan:"7",className:"px-6 py-12 text-center",children:x.jsxs("div",{className:"flex flex-col items-center space-y-3",children:[x.jsx(f,{className:"w-12 h-12 text-gray-300"}),x.jsxs("div",{className:"text-gray-500",children:[x.jsx("p",{className:"text-lg font-medium",children:"No se encontraron usuarios"}),x.jsx("p",{className:"text-sm",children:i||"all"!==S||"all"!==P?"Intenta ajustar los filtros de búsqueda":"No hay usuarios registrados en el sistema"})]}),(i||"all"!==S||"all"!==P)&&x.jsx("button",{onClick:Q,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm",children:"Limpiar filtros"})]})})})]})]})}),K()>1&&x.jsxs("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[x.jsxs("div",{className:"flex-1 flex justify-between sm:hidden",children:[x.jsx("button",{onClick:()=>q(L-1),disabled:1===L,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Anterior"}),x.jsx("button",{onClick:()=>q(L+1),disabled:L===K(),className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Siguiente"})]}),x.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[x.jsxs("div",{className:"flex items-center space-x-4",children:[x.jsxs("p",{className:"text-sm text-gray-700",children:["Mostrando"," ",x.jsx("span",{className:"font-medium",children:Math.min((L-1)*D+1,H().length)})," ","a"," ",x.jsx("span",{className:"font-medium",children:Math.min(L*D,H().length)})," ","de"," ",x.jsx("span",{className:"font-medium",children:H().length})," ","usuarios"]}),x.jsxs("select",{value:D,onChange:e=>{z(Number(e.target.value)),q(1)},className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[x.jsx("option",{value:5,children:"5 por página"}),x.jsx("option",{value:10,children:"10 por página"}),x.jsx("option",{value:25,children:"25 por página"}),x.jsx("option",{value:50,children:"50 por página"})]})]}),x.jsx("div",{children:x.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[x.jsxs("button",{onClick:()=>q(L-1),disabled:1===L,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[x.jsx("span",{className:"sr-only",children:"Anterior"}),x.jsx("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:x.jsx("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:Math.min(5,K())},(e,s)=>{const a=s+1;return x.jsx("button",{onClick:()=>q(a),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium "+(a===L?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"),children:a},a)}),x.jsxs("button",{onClick:()=>q(L+1),disabled:L===K(),className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[x.jsx("span",{className:"sr-only",children:"Siguiente"}),x.jsx("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:x.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})]}),a&&x.jsxs("div",{className:"flex justify-center items-center py-12",children:[x.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),x.jsx("span",{className:"ml-2 text-gray-600",children:"Cargando usuarios..."})]}),l&&x.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:x.jsxs("div",{className:"flex",children:[x.jsx("div",{className:"flex-shrink-0",children:x.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:x.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),x.jsxs("div",{className:"ml-3",children:[x.jsx("h3",{className:"text-sm font-medium text-red-800",children:"Error al cargar usuarios"}),x.jsx("div",{className:"mt-2 text-sm text-red-700",children:x.jsx("p",{children:l})}),x.jsx("div",{className:"mt-4",children:x.jsx("button",{onClick:T,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"Reintentar"})})]})]})}),U&&x.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:x.jsx("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:x.jsxs("div",{className:"mt-3",children:[x.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Crear Nuevo Usuario"}),x.jsxs("form",{onSubmit:e=>d(null,null,function*(){e.preventDefault();try{if(r(!0),!(B.email&&B.password&&B.nombre&&B.apellido))return void m.error("Por favor completa todos los campos requeridos");const{data:e}=yield C.from("usuarios").select("id").eq("email",B.email).single();if(e)return void m.error("Ya existe un usuario con ese email");const{data:s,error:a}=yield C.auth.admin.createUser({email:B.email,password:B.password,email_confirm:!0,user_metadata:{nombre:B.nombre,apellido:B.apellido,documento:B.documento,rol:B.rol}});if(a)throw a;const{error:l}=yield C.from("usuarios").insert([{id:s.user.id,email:B.email,nombre:B.nombre,apellido:B.apellido,documento:B.documento,rol:B.rol,activo:B.activo,fecha_creacion:(new Date).toISOString()}]);if(l)throw l;m.success("Usuario creado exitosamente"),O(!1),F({email:"",password:"",nombre:"",apellido:"",documento:"",rol:"paciente",activo:!0}),yield T()}catch(s){m.error("Error al crear usuario: "+s.message)}finally{r(!1)}}),className:"space-y-4",children:[x.jsxs("div",{children:[x.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email *"}),x.jsx("input",{type:"email",required:!0,value:B.email,onChange:e=>F(n(o({},B),{email:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"<EMAIL>"})]}),x.jsxs("div",{children:[x.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Contraseña *"}),x.jsx("input",{type:"password",required:!0,value:B.password,onChange:e=>F(n(o({},B),{password:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Mínimo 6 caracteres",minLength:"6"})]}),x.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[x.jsxs("div",{children:[x.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre *"}),x.jsx("input",{type:"text",required:!0,value:B.nombre,onChange:e=>F(n(o({},B),{nombre:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),x.jsxs("div",{children:[x.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido *"}),x.jsx("input",{type:"text",required:!0,value:B.apellido,onChange:e=>F(n(o({},B),{apellido:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),x.jsxs("div",{children:[x.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"}),x.jsx("input",{type:"text",value:B.documento,onChange:e=>F(n(o({},B),{documento:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Número de documento"})]}),x.jsxs("div",{children:[x.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rol *"}),x.jsxs("select",{required:!0,value:B.rol,onChange:e=>F(n(o({},B),{rol:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[x.jsx("option",{value:"paciente",children:"Paciente"}),x.jsx("option",{value:"psicologo",children:"Psicólogo"}),x.jsx("option",{value:"administrador",children:"Administrador"})]})]}),x.jsxs("div",{className:"flex items-center",children:[x.jsx("input",{type:"checkbox",id:"activo",checked:B.activo,onChange:e=>F(n(o({},B),{activo:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),x.jsx("label",{htmlFor:"activo",className:"ml-2 text-sm text-gray-700",children:"Usuario activo"})]}),x.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[x.jsx("button",{type:"button",onClick:()=>{O(!1),F({email:"",password:"",nombre:"",apellido:"",documento:"",rol:"paciente",activo:!0})},className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors",children:"Cancelar"}),x.jsx("button",{type:"submit",disabled:a,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors",children:a?"Creando...":"Crear Usuario"})]})]})]})})}),M&&I&&x.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:x.jsx("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:x.jsxs("div",{className:"mt-3",children:[x.jsxs("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:["Editar Usuario: ",I.nombre," ",I.apellido]}),x.jsxs("form",{onSubmit:e=>d(null,null,function*(){if(e.preventDefault(),I)try{r(!0);const{error:e}=yield C.from("usuarios").update({nombre:B.nombre,apellido:B.apellido,documento:B.documento,rol:B.rol,activo:B.activo,fecha_actualizacion:(new Date).toISOString()}).eq("id",I.id);if(e)throw e;if(B.password){const{error:e}=yield C.auth.admin.updateUserById(I.id,{password:B.password})}m.success("Usuario actualizado exitosamente"),R(!1),_(null),F({email:"",password:"",nombre:"",apellido:"",documento:"",rol:"paciente",activo:!0}),yield T()}catch(s){m.error("Error al actualizar usuario: "+s.message)}finally{r(!1)}}),className:"space-y-4",children:[x.jsxs("div",{children:[x.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),x.jsx("input",{type:"email",value:B.email,disabled:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-100 text-gray-500"}),x.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"El email no se puede modificar"})]}),x.jsxs("div",{children:[x.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nueva Contraseña (opcional)"}),x.jsx("input",{type:"password",value:B.password,onChange:e=>F(n(o({},B),{password:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Dejar vacío para no cambiar",minLength:"6"})]}),x.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[x.jsxs("div",{children:[x.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre *"}),x.jsx("input",{type:"text",required:!0,value:B.nombre,onChange:e=>F(n(o({},B),{nombre:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),x.jsxs("div",{children:[x.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido *"}),x.jsx("input",{type:"text",required:!0,value:B.apellido,onChange:e=>F(n(o({},B),{apellido:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),x.jsxs("div",{children:[x.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"}),x.jsx("input",{type:"text",value:B.documento,onChange:e=>F(n(o({},B),{documento:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),x.jsxs("div",{children:[x.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rol *"}),x.jsxs("select",{required:!0,value:B.rol,onChange:e=>F(n(o({},B),{rol:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[x.jsx("option",{value:"paciente",children:"Paciente"}),x.jsx("option",{value:"psicologo",children:"Psicólogo"}),x.jsx("option",{value:"administrador",children:"Administrador"})]})]}),x.jsxs("div",{className:"flex items-center",children:[x.jsx("input",{type:"checkbox",id:"activo-edit",checked:B.activo,onChange:e=>F(n(o({},B),{activo:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),x.jsx("label",{htmlFor:"activo-edit",className:"ml-2 text-sm text-gray-700",children:"Usuario activo"})]}),x.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[x.jsx("button",{type:"button",onClick:()=>{R(!1),_(null),F({email:"",password:"",nombre:"",apellido:"",documento:"",rol:"paciente",activo:!0})},className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors",children:"Cancelar"}),x.jsx("button",{type:"submit",disabled:a,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors",children:a?"Guardando...":"Guardar Cambios"})]})]})]})})})]})};export{k as default};
