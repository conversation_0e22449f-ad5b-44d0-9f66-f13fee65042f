-- Migración para crear las tablas de interpretaciones oficiales del BAT-7
-- Basado en el documento oficial "Interpretacion de aptitudes y Generalidaes.txt"

-- Tabla para almacenar los niveles de percentiles
CREATE TABLE IF NOT EXISTS public.niveles_percentil (
    id INTEGER PRIMARY KEY,
    nombre VARCHAR(20) NOT NULL,
    rango_descripcion VARCHAR(50) NOT NULL,
    percentil_min INTEGER NOT NULL,
    percentil_max INTEGER,
    descripcion TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla para almacenar las aptitudes
CREATE TABLE IF NOT EXISTS public.aptitudes (
    codigo VARCHAR(2) PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    descripcion TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla principal para almacenar las interpretaciones oficiales
CREATE TABLE IF NOT EXISTS public.interpretaciones_oficiales (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    aptitud_codigo VARCHAR(2) NOT NULL REFERENCES public.aptitudes(codigo),
    nivel_id INTEGER NOT NULL REFERENCES public.niveles_percentil(id),
    rendimiento TEXT NOT NULL,
    academico TEXT NOT NULL,
    vocacional TEXT NOT NULL,
    es_oficial BOOLEAN DEFAULT TRUE,
    fuente VARCHAR(255) DEFAULT 'Documento oficial BAT-7',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(aptitud_codigo, nivel_id)
);

-- Insertar los niveles de percentiles según documento oficial
INSERT INTO public.niveles_percentil (id, nombre, rango_descripcion, percentil_min, percentil_max, descripcion) VALUES
(1, 'Muy Bajo', 'Percentil ≤ 5', 0, 5, 'Percentil ≤ 5'),
(2, 'Bajo', 'Percentil 6 - 20', 6, 20, 'Percentil 6 - 20'),
(3, 'Medio-Bajo', 'Percentil 21 - 40', 21, 40, 'Percentil 21 - 40'),
(4, 'Medio', 'Percentil 41 - 60', 41, 60, 'Percentil 41 - 60'),
(5, 'Medio-Alto', 'Percentil 61 - 80', 61, 80, 'Percentil 61 - 80'),
(6, 'Alto', 'Percentil 81 - 95', 81, 95, 'Percentil 81 - 95'),
(7, 'Muy Alto', 'Percentil > 95', 96, 100, 'Percentil > 95')
ON CONFLICT (id) DO UPDATE SET
    nombre = EXCLUDED.nombre,
    rango_descripcion = EXCLUDED.rango_descripcion,
    percentil_min = EXCLUDED.percentil_min,
    percentil_max = EXCLUDED.percentil_max,
    descripcion = EXCLUDED.descripcion,
    updated_at = NOW();

-- Insertar las aptitudes
INSERT INTO public.aptitudes (codigo, nombre, descripcion) VALUES
('V', 'Aptitud Verbal', 'Capacidad para establecer relaciones entre términos lingüísticos y resolver problemas expresados verbalmente'),
('E', 'Aptitud Espacial', 'Capacidad para analizar, sintetizar y manipular mentalmente formas u objetos'),
('A', 'Concentración/Atención', 'Velocidad de procesamiento de operaciones mentales y capacidad de concentración'),
('R', 'Razonamiento', 'Capacidad para resolver problemas abstractos y novedosos mediante razonamiento deductivo e inductivo'),
('N', 'Aptitud Numérica', 'Capacidad para resolver problemas que requieren el uso de números y operaciones aritméticas'),
('M', 'Aptitud Mecánica', 'Capacidad para comprender principios físicos y mecánicos básicos'),
('O', 'Ortografía', 'Capacidad para reconocer y aplicar reglas ortográficas')
ON CONFLICT (codigo) DO UPDATE SET
    nombre = EXCLUDED.nombre,
    descripcion = EXCLUDED.descripcion,
    updated_at = NOW();

-- Función para obtener nivel por percentil
CREATE OR REPLACE FUNCTION public.obtener_nivel_por_percentil(percentil_valor INTEGER)
RETURNS TABLE(
    nivel_id INTEGER,
    nivel_nombre VARCHAR(20),
    rango_descripcion VARCHAR(50)
) 
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        np.id,
        np.nombre,
        np.rango_descripcion
    FROM public.niveles_percentil np
    WHERE percentil_valor >= np.percentil_min 
    AND (np.percentil_max IS NULL OR percentil_valor <= np.percentil_max)
    ORDER BY np.id DESC
    LIMIT 1;
END;
$$;

-- Función para obtener interpretación por aptitud y percentil
CREATE OR REPLACE FUNCTION public.obtener_interpretacion_oficial(
    aptitud_codigo_param VARCHAR(2),
    percentil_valor INTEGER
)
RETURNS TABLE(
    aptitud_codigo VARCHAR(2),
    aptitud_nombre VARCHAR(100),
    nivel_id INTEGER,
    nivel_nombre VARCHAR(20),
    percentil_rango VARCHAR(50),
    rendimiento TEXT,
    academico TEXT,
    vocacional TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    nivel_info RECORD;
BEGIN
    -- Obtener el nivel correspondiente al percentil
    SELECT * INTO nivel_info FROM public.obtener_nivel_por_percentil(percentil_valor);
    
    -- Retornar la interpretación
    RETURN QUERY
    SELECT 
        io.aptitud_codigo,
        a.nombre as aptitud_nombre,
        nivel_info.nivel_id,
        nivel_info.nivel_nombre,
        nivel_info.rango_descripcion,
        io.rendimiento,
        io.academico,
        io.vocacional
    FROM public.interpretaciones_oficiales io
    JOIN public.aptitudes a ON io.aptitud_codigo = a.codigo
    WHERE io.aptitud_codigo = aptitud_codigo_param
    AND io.nivel_id = nivel_info.nivel_id;
END;
$$;

-- Crear índices para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS idx_interpretaciones_aptitud_nivel ON public.interpretaciones_oficiales(aptitud_codigo, nivel_id);
CREATE INDEX IF NOT EXISTS idx_niveles_percentil_rango ON public.niveles_percentil(percentil_min, percentil_max);

-- Habilitar RLS (Row Level Security) si es necesario
ALTER TABLE public.niveles_percentil ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.aptitudes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.interpretaciones_oficiales ENABLE ROW LEVEL SECURITY;

-- Políticas de acceso (permitir lectura a todos los usuarios autenticados)
CREATE POLICY "Allow read access to niveles_percentil" ON public.niveles_percentil
    FOR SELECT USING (true);

CREATE POLICY "Allow read access to aptitudes" ON public.aptitudes
    FOR SELECT USING (true);

CREATE POLICY "Allow read access to interpretaciones_oficiales" ON public.interpretaciones_oficiales
    FOR SELECT USING (true);

-- Comentarios para documentación
COMMENT ON TABLE public.niveles_percentil IS 'Niveles de percentiles según documento oficial BAT-7';
COMMENT ON TABLE public.aptitudes IS 'Aptitudes evaluadas en el BAT-7';
COMMENT ON TABLE public.interpretaciones_oficiales IS 'Interpretaciones oficiales copiadas a pie de letra del documento oficial';
COMMENT ON FUNCTION public.obtener_nivel_por_percentil IS 'Función para obtener el nivel correspondiente a un percentil';
COMMENT ON FUNCTION public.obtener_interpretacion_oficial IS 'Función para obtener interpretación oficial por aptitud y percentil';
