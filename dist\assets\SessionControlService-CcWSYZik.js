var e,n=Object.defineProperty,t=(e,n,t)=>new Promise((s,o)=>{var i=e=>{try{a(t.next(e))}catch(n){o(n)}},r=e=>{try{a(t.throw(e))}catch(n){o(n)}},a=e=>e.done?s(e.value):Promise.resolve(e.value).then(i,r);a((t=t.apply(e,n)).next())});import{s}from"./index-Bdl1jgS_.js";import{P as o}from"./PinLogger-C2v3yGM1.js";import"./vendor-BqMjyOVw.js";class i{static checkSessionPinStatus(e){return t(this,null,function*(){try{o.logInfo("Checking session pin status",{sessionId:e});const{data:n,error:t}=yield s.from("test_sessions").select("\n          id,\n          paciente_id,\n          estado,\n          fecha_fin,\n          pin_consumed_at,\n          pin_consumption_id,\n          pacientes!inner(psicologo_id, nombre, apellido)\n        ").eq("id",e).single();if(t)throw o.logError("Error checking session pin status",t),t;if(!n)return{exists:!1,canConsume:!1,reason:"SESSION_NOT_FOUND",message:"Sesión no encontrada"};if("finalizado"!==n.estado)return{exists:!0,canConsume:!1,reason:"SESSION_NOT_COMPLETED",message:"La sesión no está finalizada",session:n};if(n.pin_consumed_at)return{exists:!0,canConsume:!1,reason:"PIN_ALREADY_CONSUMED",message:"Ya se consumió un pin para esta sesión",session:n,consumedAt:n.pin_consumed_at};const i=yield this.checkExistingReport(n.paciente_id);return{exists:!0,canConsume:!i.exists,reason:i.exists?"REPORT_ALREADY_EXISTS":"CAN_CONSUME",message:i.exists?"Ya existe un informe para este paciente":"Puede consumir pin para esta sesión",session:n,existingReport:i.exists?i.report:null}}catch(n){throw o.logError("Error in checkSessionPinStatus",n),n}})}static checkExistingReport(e){return t(this,null,function*(){try{const{data:n,error:t}=yield s.from("informes").select("id, titulo, created_at, tipo_informe").eq("paciente_id",e).order("created_at",{ascending:!1}).limit(1);if(t)throw o.logError("Error checking existing report",t),t;return{exists:n&&n.length>0,report:n&&n.length>0?n[0]:null,count:n?n.length:0}}catch(n){throw o.logError("Error in checkExistingReport",n),n}})}static markSessionPinConsumed(e,n,i=null){return t(this,null,function*(){try{o.logInfo("Marking session pin as consumed",{sessionId:e,consumptionId:n,reportId:i});const t={pin_consumed_at:(new Date).toISOString(),pin_consumption_id:n};i&&(t.report_id=i);const{error:r}=yield s.from("test_sessions").update(t).eq("id",e);if(r)throw o.logError("Error marking session pin as consumed",r),r;return o.logSuccess(`Session ${e} marked as pin consumed`),!0}catch(t){throw o.logError("Error in markSessionPinConsumed",t),t}})}static getPendingSessions(e){return t(this,null,function*(){try{o.logInfo("Getting pending sessions",{psychologistId:e});const{data:n,error:t}=yield s.from("test_sessions").select("\n          id,\n          paciente_id,\n          fecha_fin,\n          estado,\n          pin_consumed_at,\n          pacientes!inner(\n            id,\n            nombre,\n            apellido,\n            psicologo_id\n          )\n        ").eq("pacientes.psicologo_id",e).eq("estado","finalizado").is("pin_consumed_at",null).order("fecha_fin",{ascending:!1});if(t)throw o.logError("Error getting pending sessions",t),t;return n||[]}catch(n){throw o.logError("Error in getPendingSessions",n),n}})}static getConsumptionHistory(e){return t(this,arguments,function*(e,n={}){const{limit:t=50,offset:i=0,startDate:r=null,endDate:a=null}=n;try{o.logInfo("Getting consumption history",{psychologistId:e,options:n});let c=s.from("test_sessions").select("\n          id,\n          paciente_id,\n          fecha_fin,\n          pin_consumed_at,\n          pin_consumption_id,\n          report_id,\n          pacientes!inner(\n            id,\n            nombre,\n            apellido,\n            psicologo_id\n          ),\n          informes(\n            id,\n            titulo,\n            created_at\n          )\n        ").eq("pacientes.psicologo_id",e).eq("estado","finalizado").not("pin_consumed_at","is",null).order("pin_consumed_at",{ascending:!1}).range(i,i+t-1);r&&(c=c.gte("pin_consumed_at",r)),a&&(c=c.lte("pin_consumed_at",a));const d=[{id:"session-001",paciente_id:"patient-001",fecha_fin:new Date(Date.now()-1728e5).toISOString(),pin_consumed_at:new Date(Date.now()-1728e5+18e5).toISOString(),pin_consumption_id:"consumption-001",report_id:"report-001",pacientes:{id:"patient-001",nombre:"Juan",apellido:"Pérez",psicologo_id:e},informes:{id:"report-001",titulo:"Informe BAT-7 - Juan Pérez",created_at:new Date(Date.now()-1728e5+27e5).toISOString()}},{id:"session-002",paciente_id:"patient-002",fecha_fin:new Date(Date.now()-2592e5).toISOString(),pin_consumed_at:new Date(Date.now()-2592e5+27e5).toISOString(),pin_consumption_id:"consumption-002",report_id:"report-002",pacientes:{id:"patient-002",nombre:"María",apellido:"González",psicologo_id:e},informes:{id:"report-002",titulo:"Informe BAT-7 - María González",created_at:new Date(Date.now()-2592e5+36e5).toISOString()}},{id:"session-003",paciente_id:"patient-003",fecha_fin:new Date(Date.now()-432e6).toISOString(),pin_consumed_at:new Date(Date.now()-3456e5).toISOString(),pin_consumption_id:"consumption-003",report_id:"report-003",pacientes:{id:"patient-003",nombre:"Carlos",apellido:"Rodríguez",psicologo_id:e},informes:{id:"report-003",titulo:"Informe BAT-7 - Carlos Rodríguez",created_at:new Date(Date.now()-3456e5+18e5).toISOString()}}];let l=d;r&&(l=l.filter(e=>new Date(e.pin_consumed_at)>=new Date(r))),a&&(l=l.filter(e=>new Date(e.pin_consumed_at)<=new Date(a)));const p=l.slice(i,i+t);return o.logSuccess("Mock consumption history retrieved",{psychologistId:e,total:d.length,filtered:l.length,returned:p.length}),p}catch(c){throw o.logError("Error in getConsumptionHistory",c),c}})}static validateSessionForPinConsumption(e,n){return t(this,null,function*(){try{o.logInfo("Validating session for pin consumption",{sessionId:e,psychologistId:n});const t=yield this.checkSessionPinStatus(e);return t.exists?t.canConsume?t.session.pacientes.psicologo_id!==n?{isValid:!1,canProceed:!1,reason:"UNAUTHORIZED_PSYCHOLOGIST",message:"No tienes permisos para esta sesión",sessionStatus:t}:{isValid:!0,canProceed:!0,reason:"VALIDATION_PASSED",message:"La sesión puede consumir un pin",sessionStatus:t}:{isValid:!1,canProceed:!1,reason:t.reason,message:t.message,sessionStatus:t}:{isValid:!1,canProceed:!1,reason:"SESSION_NOT_FOUND",message:"La sesión no existe",sessionStatus:t}}catch(t){return o.logError("Error in validateSessionForPinConsumption",t),{isValid:!1,canProceed:!1,reason:"VALIDATION_ERROR",message:`Error en la validación: ${t.message}`,error:t.message}}})}static getSessionStatistics(e){return t(this,null,function*(){try{o.logInfo("Getting session statistics",{psychologistId:e});const[n,t,i]=yield Promise.all([s.from("test_sessions").select("id",{count:"exact"}).eq("pacientes.psicologo_id",e).eq("estado","finalizado"),s.from("test_sessions").select("id",{count:"exact"}).eq("pacientes.psicologo_id",e).eq("estado","finalizado").is("pin_consumed_at",null),s.from("test_sessions").select("id",{count:"exact"}).eq("pacientes.psicologo_id",e).eq("estado","finalizado").not("pin_consumed_at","is",null)]);return{totalCompleted:n.count||0,pendingConsumption:t.count||0,alreadyConsumed:i.count||0,consumptionRate:n.count>0?((i.count||0)/n.count*100).toFixed(1):0}}catch(n){throw o.logError("Error getting session statistics",n),n}})}}((e,t,s)=>{t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s})(i,"symbol"!=typeof(e="SESSION_STATES")?e+"":e,{PENDING:"pending",PIN_CONSUMED:"pin_consumed",REPORT_GENERATED:"report_generated",DUPLICATE_BLOCKED:"duplicate_blocked"});export{i as SessionControlService,i as default};
