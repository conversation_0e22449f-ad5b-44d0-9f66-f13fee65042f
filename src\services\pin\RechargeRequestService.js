import { supabase } from '../../api/supabaseClient.js';
import { PinLogger } from './PinLogger.js';
import PinRechargeRequestsAPI from '../../api/endpoints/pinRechargeRequests.js';
import EnhancedNotificationService from './EnhancedNotificationService.js';

/**
 * Servicio para gestionar solicitudes de recarga de pines
 * Maneja el flujo completo desde la solicitud hasta la aprobación
 */
export class RechargeRequestService {
  
  /**
   * Estados de solicitud
   */
  static REQUEST_STATUS = {
    PENDING: 'pending',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    CANCELLED: 'cancelled'
  };

  /**
   * Niveles de urgencia
   */
  static URGENCY_LEVELS = {
    LOW: 'low',
    NORMAL: 'normal',
    HIGH: 'high',
    URGENT: 'urgent'
  };

  /**
   * Crear una nueva solicitud de recarga
   * @param {string} psychologistId - ID del psicólogo
   * @param {Object} requestData - Datos de la solicitud
   * @returns {Promise<Object>} Solicitud creada
   */
  static async createRechargeRequest(psychologistId, requestData) {
    const {
      requestedPins,
      urgency = this.URGENCY_LEVELS.NORMAL,
      reason,
      contactMethod = 'email',
      additionalInfo = {}
    } = requestData;

    try {
      PinLogger.logInfo('Creating recharge request', { psychologistId, requestedPins, urgency });

      // Validar datos de entrada
      if (!psychologistId || !requestedPins || !reason) {
        throw new Error('Datos de solicitud incompletos');
      }

      if (requestedPins <= 0 || requestedPins > 1000) {
        throw new Error('Cantidad de pines inválida (1-1000)');
      }

      // Obtener información del psicólogo
      const { data: psychologist, error: psychError } = await supabase
        .from('psicologos')
        .select('id, email, nombre, apellido')
        .eq('id', psychologistId)
        .single();

      if (psychError || !psychologist) {
        throw new Error('Psicólogo no encontrado');
      }

      // Verificar si ya existe una solicitud pendiente
      const { data: existingRequest } = await supabase
        .from('pin_recharge_requests')
        .select('id, status')
        .eq('psychologist_id', psychologistId)
        .eq('status', this.REQUEST_STATUS.PENDING)
        .single();

      if (existingRequest) {
        throw new Error('Ya tienes una solicitud pendiente. Espera a que sea procesada.');
      }

      // Obtener estadísticas del psicólogo para contexto
      const usage_stats = await this._getPsychologistUsageStats(psychologistId);

      // Crear la solicitud
      const { data: request, error: requestError } = await supabase
        .from('pin_recharge_requests')
        .insert({
          psychologist_id: psychologistId,
          psychologist_email: psychologist.email,
          psychologist_name: `${psychologist.nombre} ${psychologist.apellido}`,
          requested_pins: requestedPins,
          urgency,
          reason,
          contact_method: contactMethod,
          status: this.REQUEST_STATUS.PENDING,
          metadata: {
            usage_stats,
            additional_info: additionalInfo,
            created_from: 'web_app'
          }
        })
        .select()
        .single();

      if (requestError) {
        throw requestError;
      }

      // Enviar notificación al administrador
      await this._notifyAdministrators(request);

      // Enviar confirmación al psicólogo
      await EnhancedNotificationService.createPinAssignmentNotification(
        psychologistId,
        0, // No se asignan pines aún
        0,
        {
          type: 'request_created',
          message: `Tu solicitud de ${requestedPins} pines ha sido enviada y está siendo revisada.`
        }
      );

      PinLogger.logSuccess('Recharge request created successfully', { requestId: request.id });

      return {
        success: true,
        request,
        message: 'Solicitud enviada exitosamente'
      };

    } catch (error) {
      PinLogger.logError('Error creating recharge request', error);
      throw error;
    }
  }

  /**
   * Obtener solicitudes pendientes para administradores
   * @param {Object} filters - Filtros de búsqueda
   * @returns {Promise<Array>} Lista de solicitudes
   */
  static async getPendingRequests(filters = {}) {
    const {
      urgency = null,
      dateFrom = null,
      dateTo = null,
      limit = 50,
      offset = 0
    } = filters;

    try {
      // Usar la API real para obtener solicitudes
      const result = await PinRechargeRequestsAPI.getRequests({
        status: 'pending',
        urgency,
        date_from: dateFrom,
        date_to: dateTo,
        limit,
        offset
      });

      if (!result.success) {
        throw new Error(result.error || 'Error al obtener solicitudes');
      }

      PinLogger.logInfo('Recharge requests loaded from API', {
        total: result.pagination?.total || 0,
        returned: result.data?.length || 0,
        filters
      });

      return result.data || [];

      /*
      // Implementación real cuando esté la tabla
      let query = supabase
        .from('pin_recharge_requests')
        .select('*')
        .eq('status', this.REQUEST_STATUS.PENDING)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (urgency) {
        query = query.eq('urgency', urgency);
      }

      if (dateFrom) {
        query = query.gte('created_at', dateFrom);
      }

      if (dateTo) {
        query = query.lte('created_at', dateTo);
      }

      const { data, error } = await query;

      if (error) throw error;

      return data || [];
      */

    } catch (error) {
      PinLogger.logError('Error getting pending requests', error);
      throw error;
    }
  }

  /**
   * Aprobar una solicitud de recarga
   * @param {string} requestId - ID de la solicitud
   * @param {number} approvedPins - Pines aprobados
   * @param {string} adminId - ID del administrador
   * @param {string} notes - Notas del administrador
   * @returns {Promise<Object>} Resultado de la aprobación
   */
  static async approveRequest(requestId, approvedPins, adminId, notes = '') {
    try {
      PinLogger.logInfo('Approving recharge request', { requestId, approvedPins, adminId });

      // Obtener la solicitud
      const { data: request, error: requestError } = await supabase
        .from('pin_recharge_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      if (requestError || !request) {
        throw new Error('Solicitud no encontrada');
      }

      if (request.status !== this.REQUEST_STATUS.PENDING) {
        throw new Error('La solicitud ya fue procesada');
      }

      // Actualizar la solicitud
      const { error: updateError } = await supabase
        .from('pin_recharge_requests')
        .update({
          status: this.REQUEST_STATUS.APPROVED,
          approved_pins: approvedPins,
          processed_by: adminId,
          processed_at: new Date().toISOString(),
          admin_notes: notes
        })
        .eq('id', requestId);

      if (updateError) throw updateError;

      // Asignar los pines al psicólogo
      const PinControlService = (await import('./PinControlService.js')).default;
      await PinControlService.assignPins(
        request.psychologist_id,
        approvedPins,
        false, // No es ilimitado
        'standard'
      );

      // Notificar al psicólogo
      await EnhancedNotificationService.createPinAssignmentNotification(
        request.psychologist_id,
        approvedPins,
        approvedPins // Total después de la asignación
      );

      PinLogger.logSuccess('Recharge request approved', { requestId, approvedPins });

      return {
        success: true,
        message: `Solicitud aprobada: ${approvedPins} pines asignados`
      };

    } catch (error) {
      PinLogger.logError('Error approving recharge request', error);
      throw error;
    }
  }

  /**
   * Rechazar una solicitud de recarga
   * @param {string} requestId - ID de la solicitud
   * @param {string} adminId - ID del administrador
   * @param {string} reason - Motivo del rechazo
   * @returns {Promise<Object>} Resultado del rechazo
   */
  static async rejectRequest(requestId, adminId, reason) {
    try {
      PinLogger.logInfo('Rejecting recharge request', { requestId, adminId });

      // Obtener la solicitud
      const { data: request, error: requestError } = await supabase
        .from('pin_recharge_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      if (requestError || !request) {
        throw new Error('Solicitud no encontrada');
      }

      if (request.status !== this.REQUEST_STATUS.PENDING) {
        throw new Error('La solicitud ya fue procesada');
      }

      // Actualizar la solicitud
      const { error: updateError } = await supabase
        .from('pin_recharge_requests')
        .update({
          status: this.REQUEST_STATUS.REJECTED,
          processed_by: adminId,
          processed_at: new Date().toISOString(),
          rejection_reason: reason
        })
        .eq('id', requestId);

      if (updateError) throw updateError;

      // Notificar al psicólogo
      await EnhancedNotificationService.createLowPinNotification(
        request.psychologist_id,
        0, // Pines actuales (se obtendrán del servicio)
        {
          channels: [EnhancedNotificationService.NOTIFICATION_CHANNELS.EMAIL],
          customMessage: `Tu solicitud de ${request.requested_pins} pines fue rechazada. Motivo: ${reason}`
        }
      );

      PinLogger.logSuccess('Recharge request rejected', { requestId });

      return {
        success: true,
        message: 'Solicitud rechazada'
      };

    } catch (error) {
      PinLogger.logError('Error rejecting recharge request', error);
      throw error;
    }
  }

  /**
   * Obtener historial de solicitudes de un psicólogo
   * @param {string} psychologistId - ID del psicólogo
   * @param {number} limit - Límite de resultados
   * @returns {Promise<Array>} Historial de solicitudes
   */
  static async getPsychologistRequestHistory(psychologistId, limit = 20) {
    try {
      const { data, error } = await supabase
        .from('pin_recharge_requests')
        .select('*')
        .eq('psychologist_id', psychologistId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return data || [];

    } catch (error) {
      PinLogger.logError('Error getting psychologist request history', error);
      throw error;
    }
  }

  /**
   * Obtener estadísticas de solicitudes
   * @param {Object} filters - Filtros de fecha
   * @returns {Promise<Object>} Estadísticas
   */
  static async getRequestStatistics(filters = {}) {
    try {
      const { dateFrom, dateTo } = filters;

      let query = supabase
        .from('pin_recharge_requests')
        .select('status, requested_pins, approved_pins, created_at');

      if (dateFrom) query = query.gte('created_at', dateFrom);
      if (dateTo) query = query.lte('created_at', dateTo);

      const { data, error } = await query;

      if (error) throw error;

      const stats = {
        total: data.length,
        pending: data.filter(r => r.status === this.REQUEST_STATUS.PENDING).length,
        approved: data.filter(r => r.status === this.REQUEST_STATUS.APPROVED).length,
        rejected: data.filter(r => r.status === this.REQUEST_STATUS.REJECTED).length,
        totalRequested: data.reduce((sum, r) => sum + (r.requested_pins || 0), 0),
        totalApproved: data.reduce((sum, r) => sum + (r.approved_pins || 0), 0),
        approvalRate: data.length > 0 
          ? (data.filter(r => r.status === this.REQUEST_STATUS.APPROVED).length / data.length * 100).toFixed(1)
          : 0
      };

      return stats;

    } catch (error) {
      PinLogger.logError('Error getting request statistics', error);
      throw error;
    }
  }

  /**
   * Obtener estadísticas de uso del psicólogo
   * @private
   */
  static async _getPsychologistUsageStats(psychologistId) {
    try {
      // Obtener estadísticas de uso
      const { data: usage } = await supabase
        .from('psychologist_usage_control')
        .select('total_uses, used_uses, is_unlimited')
        .eq('psychologist_id', psychologistId)
        .eq('is_active', true)
        .single();

      // Obtener historial reciente de informes
      const { data: recentReports } = await supabase
        .from('informes')
        .select('created_at')
        .eq('psicologo_id', psychologistId)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Últimos 30 días
        .order('created_at', { ascending: false });

      return {
        current_pins: usage ? (usage.total_uses - usage.used_uses) : 0,
        is_unlimited: usage?.is_unlimited || false,
        recent_reports_count: recentReports?.length || 0,
        last_activity: recentReports?.[0]?.created_at || null
      };

    } catch (error) {
      PinLogger.logError('Error getting psychologist usage stats', error);
      return {
        current_pins: 0,
        is_unlimited: false,
        recent_reports_count: 0,
        last_activity: null
      };
    }
  }

  /**
   * Notificar a los administradores sobre nueva solicitud
   * @private
   */
  static async _notifyAdministrators(request) {
    try {
      // En producción, aquí se enviarían emails a los administradores
      PinLogger.logInfo('Notifying administrators about new recharge request', { 
        requestId: request.id,
        urgency: request.urgency 
      });

      // Simular notificación por email
      // await EmailService.sendToAdmins({
      //   subject: `Nueva solicitud de pines - ${request.urgency}`,
      //   template: 'recharge_request',
      //   data: request
      // });

    } catch (error) {
      PinLogger.logError('Error notifying administrators', error);
      // No fallar la operación principal por error de notificación
    }
  }
}

export default RechargeRequestService;
