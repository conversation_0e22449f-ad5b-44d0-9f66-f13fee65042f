/**
 * Script para limpiar usuarios de prueba y crear usuario administrador real
 * 
 * Este script:
 * 1. Elimina todos los usuarios de prueba existentes
 * 2. Crea el usuario administrador real con las credenciales proporcionadas
 * 3. Configura correctamente los permisos y roles
 */

import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Datos del administrador real
const REAL_ADMIN = {
  email: '<EMAIL>',
  password: '13716261',
  documento: '13716261',
  nombre: 'Administrador',
  apellido: 'Principal',
  tipo_usuario: 'administrador'
};

// Lista de emails de usuarios de prueba a eliminar
const TEST_USER_EMAILS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

/**
 * Elimina usuarios de prueba de la tabla usuarios
 */
async function cleanupTestUsers() {
  console.log('🧹 Iniciando limpieza de usuarios de prueba...');
  
  try {
    // Obtener todos los usuarios de prueba
    const { data: testUsers, error: fetchError } = await supabase
      .from('usuarios')
      .select('id, email, nombre, apellido, tipo_usuario')
      .or(TEST_USER_EMAILS.map(email => `email.eq.${email}`).join(','));

    if (fetchError) {
      console.error('❌ Error al obtener usuarios de prueba:', fetchError);
      return false;
    }

    if (!testUsers || testUsers.length === 0) {
      console.log('✅ No se encontraron usuarios de prueba para eliminar');
      return true;
    }

    console.log(`📋 Encontrados ${testUsers.length} usuarios de prueba:`);
    testUsers.forEach(user => {
      console.log(`  - ${user.email} (${user.nombre} ${user.apellido}) - ${user.tipo_usuario}`);
    });

    // Eliminar usuarios de la tabla usuarios
    const userIds = testUsers.map(user => user.id);
    const { error: deleteError } = await supabase
      .from('usuarios')
      .delete()
      .in('id', userIds);

    if (deleteError) {
      console.error('❌ Error al eliminar usuarios de prueba:', deleteError);
      return false;
    }

    console.log(`✅ ${testUsers.length} usuarios de prueba eliminados de la tabla usuarios`);
    
    // Nota: Los usuarios en auth.users se eliminarán automáticamente por CASCADE
    // o pueden requerir eliminación manual desde el panel de Supabase
    console.log('⚠️  NOTA: Los usuarios en auth.users pueden requerir eliminación manual desde el panel de Supabase');
    
    return true;
  } catch (error) {
    console.error('❌ Error durante la limpieza:', error);
    return false;
  }
}

/**
 * Crea el usuario administrador real
 */
async function createRealAdmin() {
  console.log('👤 Creando usuario administrador real...');
  
  try {
    // Verificar si el usuario ya existe
    const { data: existingUser, error: checkError } = await supabase
      .from('usuarios')
      .select('id, email, nombre, tipo_usuario')
      .eq('email', REAL_ADMIN.email)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('❌ Error al verificar usuario existente:', checkError);
      return false;
    }

    if (existingUser) {
      console.log('⚠️  El usuario administrador ya existe:', existingUser.email);
      console.log('   Actualizando información...');
      
      // Actualizar información del usuario existente
      const { error: updateError } = await supabase
        .from('usuarios')
        .update({
          documento: REAL_ADMIN.documento,
          nombre: REAL_ADMIN.nombre,
          apellido: REAL_ADMIN.apellido,
          tipo_usuario: REAL_ADMIN.tipo_usuario,
          activo: true,
          ultimo_acceso: new Date().toISOString()
        })
        .eq('id', existingUser.id);

      if (updateError) {
        console.error('❌ Error al actualizar usuario:', updateError);
        return false;
      }

      console.log('✅ Usuario administrador actualizado exitosamente');
      return true;
    }

    // Crear nuevo usuario en auth.users
    console.log('📧 Creando usuario en auth.users...');
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: REAL_ADMIN.email,
      password: REAL_ADMIN.password,
      options: {
        data: {
          nombre: REAL_ADMIN.nombre,
          apellido: REAL_ADMIN.apellido,
          documento: REAL_ADMIN.documento,
          tipo_usuario: REAL_ADMIN.tipo_usuario
        }
      }
    });

    if (authError) {
      console.error('❌ Error al crear usuario en auth.users:', authError);
      return false;
    }

    if (!authData.user) {
      console.error('❌ No se pudo crear el usuario en auth.users');
      return false;
    }

    console.log('✅ Usuario creado en auth.users:', authData.user.id);

    // Crear registro en tabla usuarios
    console.log('📝 Creando registro en tabla usuarios...');
    const { error: insertError } = await supabase
      .from('usuarios')
      .insert({
        id: authData.user.id,
        documento: REAL_ADMIN.documento,
        nombre: REAL_ADMIN.nombre,
        apellido: REAL_ADMIN.apellido,
        tipo_usuario: REAL_ADMIN.tipo_usuario,
        activo: true,
        fecha_creacion: new Date().toISOString()
      });

    if (insertError) {
      console.error('❌ Error al crear registro en tabla usuarios:', insertError);
      return false;
    }

    console.log('✅ Usuario administrador creado exitosamente');
    console.log('📋 Detalles del usuario:');
    console.log(`   Email: ${REAL_ADMIN.email}`);
    console.log(`   Documento: ${REAL_ADMIN.documento}`);
    console.log(`   Nombre: ${REAL_ADMIN.nombre} ${REAL_ADMIN.apellido}`);
    console.log(`   Tipo: ${REAL_ADMIN.tipo_usuario}`);
    
    return true;
  } catch (error) {
    console.error('❌ Error durante la creación del administrador:', error);
    return false;
  }
}

/**
 * Verifica la configuración final
 */
async function verifySetup() {
  console.log('🔍 Verificando configuración final...');
  
  try {
    // Contar usuarios por tipo
    const { data: usuarios, error } = await supabase
      .from('usuarios')
      .select('tipo_usuario, email, nombre, apellido, activo');

    if (error) {
      console.error('❌ Error al verificar usuarios:', error);
      return false;
    }

    console.log('📊 Estado actual de usuarios:');
    const usersByType = usuarios.reduce((acc, user) => {
      acc[user.tipo_usuario] = (acc[user.tipo_usuario] || 0) + 1;
      return acc;
    }, {});

    Object.entries(usersByType).forEach(([tipo, count]) => {
      console.log(`   ${tipo}: ${count} usuario(s)`);
    });

    console.log('\n👥 Lista de usuarios activos:');
    usuarios.forEach(user => {
      const status = user.activo ? '✅' : '❌';
      console.log(`   ${status} ${user.email} - ${user.nombre} ${user.apellido} (${user.tipo_usuario})`);
    });

    return true;
  } catch (error) {
    console.error('❌ Error durante la verificación:', error);
    return false;
  }
}

/**
 * Función principal
 */
async function main() {
  console.log('🚀 Iniciando proceso de limpieza y configuración de usuarios...\n');
  
  try {
    // Paso 1: Limpiar usuarios de prueba
    const cleanupSuccess = await cleanupTestUsers();
    if (!cleanupSuccess) {
      console.error('❌ Falló la limpieza de usuarios de prueba');
      process.exit(1);
    }
    
    console.log(''); // Línea en blanco
    
    // Paso 2: Crear administrador real
    const createSuccess = await createRealAdmin();
    if (!createSuccess) {
      console.error('❌ Falló la creación del administrador real');
      process.exit(1);
    }
    
    console.log(''); // Línea en blanco
    
    // Paso 3: Verificar configuración
    const verifySuccess = await verifySetup();
    if (!verifySuccess) {
      console.error('❌ Falló la verificación final');
      process.exit(1);
    }
    
    console.log('\n🎉 ¡Proceso completado exitosamente!');
    console.log('🔐 Credenciales del administrador:');
    console.log(`   Email: ${REAL_ADMIN.email}`);
    console.log(`   Contraseña: ${REAL_ADMIN.password}`);
    console.log(`   Documento: ${REAL_ADMIN.documento}`);
    
  } catch (error) {
    console.error('❌ Error fatal durante el proceso:', error);
    process.exit(1);
  }
}

// Ejecutar el script
main();
