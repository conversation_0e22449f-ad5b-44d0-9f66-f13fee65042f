import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import pinControlService from '../services/pin/ImprovedPinControlService';
import supabaseService from '../services/supabaseService';

/**
 * Custom hook for managing psychologists data
 * Follows the established service layer pattern
 */
export const usePsychologists = () => {
  const [psychologists, setPsychologists] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchPsychologists = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Get ALL psychologists from the psicologos table, not just those with pin controls
      const { data, error } = await supabaseService.getPsychologists();

      if (error) {
        throw error;
      }

      // Transform to expected format for the modal
      const transformedData = (data || []).map(psychologist => ({
        id: psychologist.id,
        nombre: psychologist.nombre || '',
        apellido: psychologist.apellidos || psychologist.apellido || '',
        email: psychologist.email || '',
        currentPins: 0, // Default to 0 for new assignments
        hasControl: false, // Default to false for new assignments
        // Add full name for better display
        fullName: `${psychologist.nombre || ''} ${psychologist.apellidos || psychologist.apellido || ''}`.trim()
      }));

      console.log('✅ Psicólogos cargados para asignación:', transformedData.length);
      setPsychologists(transformedData);
    } catch (error) {
      console.error('❌ Error fetching psychologists:', error);
      setError('Error al cargar la lista de psicólogos: ' + error.message);
      toast.error('Error al cargar la lista de psicólogos: ' + error.message);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchPsychologists();
  }, [fetchPsychologists]);

  return {
    psychologists,
    loading,
    error,
    refetch: fetchPsychologists
  };
};

export default usePsychologists;