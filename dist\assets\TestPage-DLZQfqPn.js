var e=(e,a,t)=>new Promise((s,i)=>{var r=e=>{try{l(t.next(e))}catch(a){i(a)}},n=e=>{try{l(t.throw(e))}catch(a){i(a)}},l=e=>e.done?s(e.value):Promise.resolve(e.value).then(r,n);l((t=t.apply(e,a)).next())});import{r as a,j as t}from"./vendor-BqMjyOVw.js";import{s,C as i,b as r,a as n,B as l}from"./index-Bdl1jgS_.js";import{e as o}from"./enhancedSupabaseService-D53fSbDl.js";const c=(e,a,t)=>{t.error||t.isOffline},d=()=>e(null,null,function*(){const e=yield o.getInstitutions();c(0,0,e);const a=yield o.createInstitution({nombre:`Test Institución ${Date.now()}`,direccion:"Dirección de prueba",telefono:"123456789"});if(c(0,0,a),a.data&&!a.error){const e=a.data.id,t=yield o.updateInstitution(e,{nombre:`${a.data.nombre} (Actualizada)`,direccion:"Dirección actualizada"});c(0,0,t);const s=yield o.deleteInstitution(e);c(0,0,s)}return"Pruebas de instituciones completadas"}),u=()=>e(null,null,function*(){const e=yield o.getInstitutions();if(!e.data||0===e.data.length)return"Error: No hay instituciones disponibles";const a=yield o.getPsychologists();c(0,0,a);const t=Date.now(),i=`test.psicologo.${t}@example.com`,{data:r,error:n}=yield s.auth.signUp({email:i,password:"Temporal123!",options:{data:{rol:"psicologo",nombre_completo:`Test Psicólogo ${t}`}}});if(n)return"Error al crear usuario para psicólogo";const l=yield o.createPsychologist({nombre:"Test",apellidos:`Psicólogo ${t}`,email:i,documento_identidad:`DOC-${t}`,telefono:"987654321",institucion_id:e.data[0].id,usuario_id:r.user.id});if(c(0,0,l),l.data&&!l.error){const e=l.data.id,a=yield o.updatePsychologist(e,{nombre:"Test (Actualizado)",apellidos:`Psicólogo ${t}`,documento_identidad:`DOC-${t}-UPD`,telefono:"987654322"});c(0,0,a);const s=yield o.deletePsychologist(e);c(0,0,s)}return"Pruebas de psicólogos completadas"}),m=()=>e(null,null,function*(){const e=yield o.getInstitutions();if(!e.data||0===e.data.length)return"Error: No hay instituciones disponibles";const a=yield o.getPsychologists(),t=a.data&&a.data.length>0?a.data[0].id:null,s=yield o.getPatients();c(0,0,s);const i=Date.now(),r=yield o.createPatient({nombre:`Test Paciente ${i}`,fecha_nacimiento:"2000-01-01",genero:"Masculino",institucion_id:e.data[0].id,psicologo_id:t,notas:"Paciente de prueba"});if(c(0,0,r),r.data&&!r.error){const e=r.data.id,a=yield o.updatePatient(e,{nombre:`Test Paciente ${i} (Actualizado)`,fecha_nacimiento:"2000-02-02",notas:"Paciente de prueba actualizado"});c(0,0,a);const t=yield o.deletePatient(e);c(0,0,t)}return"Pruebas de pacientes completadas"}),p=()=>e(null,null,function*(){const e=o.getSyncStatus();if(e.pendingCount>0){e.operations.forEach((e,a)=>{});const a=yield o.syncPendingOperations();a.errors.length>0&&a.errors.forEach((e,a)=>{})}return"Pruebas de sincronización completadas"}),h=()=>e(null,null,function*(){try{const{data:{user:e}}=yield s.auth.getUser();return e?(yield d(),yield u(),yield m(),yield p(),"Todas las pruebas completadas con éxito"):"Error: Usuario no autenticado"}catch(e){return`Error al ejecutar pruebas: ${e.message}`}}),b=d,g=u,x=m,y=p,j=()=>{const[s,o]=a.useState(!1),[c,d]=a.useState([]),[u,m]=a.useState("all"),p=[{value:"all",label:"Todas las pruebas"},{value:"instituciones",label:"Pruebas de Instituciones"},{value:"psicologos",label:"Pruebas de Psicólogos"},{value:"pacientes",label:"Pruebas de Pacientes"},{value:"sincronizacion",label:"Pruebas de Sincronización"}];return t.jsxs("div",{className:"container mx-auto py-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Página de Pruebas"}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[t.jsxs(i,{className:"md:col-span-1",children:[t.jsx(r,{children:t.jsx("h2",{className:"text-lg font-medium",children:"Panel de Control"})}),t.jsx(n,{children:t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Seleccionar prueba"}),t.jsx("select",{className:"form-select w-full",value:u,onChange:e=>m(e.target.value),disabled:s,children:p.map(e=>t.jsx("option",{value:e.value,children:e.label},e.value))})]}),t.jsx(l,{variant:"primary",className:"w-full",onClick:()=>{return a=u,e(null,null,function*(){o(!0);try{let e;switch(a){case"all":e=yield h();break;case"instituciones":e=yield b();break;case"psicologos":e=yield g();break;case"pacientes":e=yield x();break;case"sincronizacion":e=yield y();break;default:e="Prueba no válida"}d(t=>[{id:Date.now(),test:a,result:e,timestamp:(new Date).toLocaleString()},...t])}catch(e){d(t=>[{id:Date.now(),test:a,result:`Error: ${e.message}`,timestamp:(new Date).toLocaleString(),error:!0},...t])}finally{o(!1)}});var a},disabled:s,children:s?"Ejecutando...":"Ejecutar Prueba"}),t.jsxs("div",{className:"text-sm text-gray-500",children:[t.jsx("p",{children:"Esta página permite ejecutar pruebas manuales para verificar el funcionamiento de las operaciones CRUD."}),t.jsx("p",{className:"mt-2",children:"Las pruebas se ejecutan contra la base de datos real, así que úsala con precaución."})]})]})})]}),t.jsxs(i,{className:"md:col-span-2",children:[t.jsx(r,{children:t.jsx("h2",{className:"text-lg font-medium",children:"Resultados"})}),t.jsx(n,{children:0===c.length?t.jsx("div",{className:"text-center py-8 text-gray-500",children:"No hay resultados. Ejecuta una prueba para ver los resultados aquí."}):t.jsx("div",{className:"space-y-4",children:c.map(e=>{var a;return t.jsxs("div",{className:"p-4 rounded-lg border "+(e.error?"border-red-200 bg-red-50":"border-green-200 bg-green-50"),children:[t.jsxs("div",{className:"flex justify-between items-start",children:[t.jsxs("div",{children:[t.jsx("h3",{className:"font-medium",children:(null==(a=p.find(a=>a.value===e.test))?void 0:a.label)||e.test}),t.jsx("p",{className:"text-sm text-gray-500",children:e.timestamp})]}),t.jsx("span",{className:"px-2 py-1 text-xs rounded-full "+(e.error?"bg-red-100 text-red-800":"bg-green-100 text-green-800"),children:e.error?"Error":"Éxito"})]}),t.jsx("div",{className:"mt-2",children:t.jsx("pre",{className:"text-sm whitespace-pre-wrap bg-white p-2 rounded border",children:e.result})})]},e.id)})})})]})]}),t.jsx("div",{className:"mt-6 text-sm text-gray-500",children:t.jsx("p",{children:"Nota: Los resultados detallados de las pruebas se muestran en la consola del navegador."})})]})};export{j as default};
