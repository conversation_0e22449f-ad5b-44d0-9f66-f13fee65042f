import{j as e,L as s,Z as a}from"./vendor-BqMjyOVw.js";const i=({test:a,iconClass:i,bgClass:t,textClass:r,buttonColor:l,abbreviation:n,showButton:c=!0,disabled:d=!1,patientId:o=null,level:b="E",isCompleted:g=!1,onRepeatTest:m=null})=>e.jsx("div",{className:"test-card-container",children:e.jsxs("div",{className:"test-card "+(g?"test-card-completed":"test-card-pending"),children:[n&&e.jsx("div",{className:`abbreviation-circle ${{blue:"bg-blue-600",green:"bg-green-600",red:"bg-red-600",amber:"bg-amber-600",indigo:"bg-indigo-600",gray:"bg-gray-700",slate:"bg-slate-600",teal:"bg-teal-600",purple:"bg-purple-600",pink:"bg-pink-600"}[l]}`,children:n}),g&&e.jsx("div",{className:"completion-check",children:e.jsx("i",{className:"fas fa-check-circle"})}),e.jsx("div",{className:"test-card-status-badge",children:e.jsxs("span",{className:"status-badge "+(g?"status-badge-completed":"status-badge-pending"),children:[e.jsx("i",{className:`fas ${g?"fa-check-circle":"fa-clock"} mr-1`}),g?"Completado":"Pendiente"]})}),e.jsx("div",{className:"test-card-level-badge",children:e.jsxs("span",{className:"level-badge "+("E"===b?"level-badge-green":"M"===b?"level-badge-blue":"S"===b?"level-badge-purple":"level-badge-gray"),children:["📗 Nivel ",b]})}),e.jsxs("div",{className:"test-card-header",children:[e.jsx("div",{className:`test-card-icon ${t}`,children:e.jsx("i",{className:`${i} ${r}`})}),e.jsx("h3",{className:"test-card-title",children:a.title})]}),e.jsx("div",{className:"test-card-description",children:e.jsx("p",{children:a.description})}),e.jsxs("div",{className:"test-card-info-container",children:[e.jsxs("div",{className:"test-card-info",children:[e.jsx("span",{className:"info-label",children:"Tiempo"}),e.jsx("span",{className:"info-value",children:a.time}),e.jsx("span",{className:"info-unit",children:"minutos"})]}),e.jsxs("div",{className:"test-card-info",children:[e.jsx("span",{className:"info-label",children:"Preguntas"}),e.jsx("span",{className:"info-value",children:a.questions})]})]}),e.jsx("div",{className:"test-card-button-container",children:d?e.jsxs("button",{disabled:!0,className:"test-card-button bg-gray-400 cursor-not-allowed opacity-50",children:[e.jsx("i",{className:"fas fa-lock mr-2"}),"Selecciona Paciente"]}):g?e.jsxs("button",{onClick:m,className:"test-card-button bg-orange-600 hover:bg-orange-700",children:[e.jsx("i",{className:"fas fa-redo mr-2"}),"Repetir Test"]}):e.jsxs(s,{to:a.path||`/test/instructions/${a.id}`,state:{patientId:o},className:`test-card-button ${{blue:"bg-blue-600 hover:bg-blue-700",green:"bg-green-600 hover:bg-green-700",red:"bg-red-600 hover:bg-red-700",amber:"bg-amber-600 hover:bg-amber-700",indigo:"bg-indigo-600 hover:bg-indigo-700",gray:"bg-gray-700 hover:bg-gray-800",slate:"bg-slate-600 hover:bg-slate-700",teal:"bg-teal-600 hover:bg-teal-700",purple:"bg-purple-600 hover:bg-purple-700",pink:"bg-pink-600 hover:bg-pink-700"}[l]}`,children:[e.jsx("i",{className:"fas fa-play-circle mr-2"}),"Iniciar Test"]})})]})});i.propTypes={test:a.shape({id:a.string.isRequired,title:a.string.isRequired,description:a.string.isRequired,time:a.number.isRequired,questions:a.number.isRequired,path:a.string}).isRequired,iconClass:a.string.isRequired,bgClass:a.string.isRequired,textClass:a.string.isRequired,buttonColor:a.string.isRequired,abbreviation:a.string,showButton:a.bool,disabled:a.bool,patientId:a.string,level:a.string,isCompleted:a.bool,onRepeatTest:a.func};export{i as T};
