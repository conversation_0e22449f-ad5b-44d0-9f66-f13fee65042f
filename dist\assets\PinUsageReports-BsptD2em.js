const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/PinControlService-Cx97559i.js","assets/vendor-BqMjyOVw.js","assets/vendor-B4zyQOk2.css","assets/index-Bdl1jgS_.js","assets/index-Csy2uUlu.css","assets/PinLogger-C2v3yGM1.js","assets/NotificationService-DiDbKBbI.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,s=Object.defineProperties,t=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,n=(s,t,a)=>t in s?e(s,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):s[t]=a,l=(e,s)=>{for(var t in s||(s={}))r.call(s,t)&&n(e,t,s[t]);if(a)for(var t of a(s))i.call(s,t)&&n(e,t,s[t]);return e},c=(e,s,t)=>n(e,"symbol"!=typeof s?s+"":s,t),o=(e,s,t)=>new Promise((a,r)=>{var i=e=>{try{l(t.next(e))}catch(s){r(s)}},n=e=>{try{l(t.throw(e))}catch(s){r(s)}},l=e=>e.done?a(e.value):Promise.resolve(e.value).then(i,n);l((t=t.apply(e,s)).next())});import{r as d,j as m,G as x,ah as u,I as h,a4 as g,O as p,U as y,J as j,k as f,ai as N,T as v,S as b,t as w,e as _,R as S,a2 as E,a3 as P,_ as R,F as C,M as I,$ as q,Q as T,aj as D,V as k,P as A,ak as U,al as M,m as O}from"./vendor-BqMjyOVw.js";import{C as H,a as L,B as $,b as z,s as G}from"./index-Bdl1jgS_.js";import{SessionControlService as V}from"./SessionControlService-CcWSYZik.js";import{u as Q,P as F}from"./PinRechargePrompt-B8It1Ju9.js";import{EnhancedNotificationService as W}from"./EnhancedNotificationService-heJqBBu8.js";import{P as J,a as B}from"./PinLogger-C2v3yGM1.js";import{P as Y}from"./pinRechargeRequests-BUqDaw89.js";import"./PinValidationService-Ki4hIVgd.js";import"./pinNotifications-BMRChPcj.js";const K=({psychologistId:e,className:s=""})=>{const[t,a]=d.useState([]),[r,i]=d.useState([]),[n,l]=d.useState(null),[c,g]=d.useState(!1),[p,y]=d.useState(null),[j,f]=d.useState("history"),w=d.useCallback(()=>o(null,null,function*(){if(e)try{g(!0),y(null);const[s,t,r]=yield Promise.all([V.getConsumptionHistory(e,{limit:20}),V.getPendingSessions(e),V.getSessionStatistics(e)]);a(s),i(t),l(r)}catch(s){y(s.message)}finally{g(!1)}}),[e]);d.useEffect(()=>{w()},[w]);const _=e=>{try{return b(new Date(e),"dd/MM/yyyy HH:mm",{locale:v})}catch(s){return"Fecha inválida"}},S=e=>{try{return N(new Date(e),{addSuffix:!0,locale:v})}catch(s){return"Hace un momento"}};return c&&!t.length?m.jsx(H,{className:s,children:m.jsxs(L,{className:"text-center py-8",children:[m.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),m.jsx("p",{className:"text-gray-600",children:"Cargando historial de sesiones..."})]})}):p?m.jsx(H,{className:s,children:m.jsxs(L,{className:"text-center py-8",children:[m.jsx(x,{className:"h-8 w-8 text-red-500 mx-auto mb-4"}),m.jsxs("p",{className:"text-red-600 mb-4",children:["Error al cargar el historial: ",p]}),m.jsxs($,{onClick:w,size:"sm",children:[m.jsx(u,{className:"mr-2"}),"Reintentar"]})]})}):m.jsxs(H,{className:s,children:[m.jsxs(z,{className:"bg-gray-50 border-b",children:[m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsxs("div",{className:"flex items-center",children:[m.jsx(h,{className:"text-gray-600 mr-2"}),m.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Historial de Sesiones y Pines"})]}),m.jsxs($,{onClick:w,size:"sm",variant:"outline",children:[m.jsx(u,{className:"mr-1"}),"Actualizar"]})]}),m.jsxs("div",{className:"flex space-x-1 mt-4",children:[m.jsxs("button",{onClick:()=>f("history"),className:"px-3 py-2 text-sm font-medium rounded-lg transition-colors "+("history"===j?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:["Historial (",t.length,")"]}),m.jsxs("button",{onClick:()=>f("pending"),className:"px-3 py-2 text-sm font-medium rounded-lg transition-colors "+("pending"===j?"bg-yellow-100 text-yellow-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:["Pendientes (",r.length,")"]}),m.jsx("button",{onClick:()=>f("stats"),className:"px-3 py-2 text-sm font-medium rounded-lg transition-colors "+("stats"===j?"bg-green-100 text-green-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:"Estadísticas"})]})]}),m.jsxs(L,{className:"p-0",children:["history"===j&&m.jsx(X,{history:t,formatDate:_,formatRelativeTime:S}),"pending"===j&&m.jsx(Z,{pending:r,formatDate:_,formatRelativeTime:S}),"stats"===j&&m.jsx(ee,{statistics:n})]})]})},X=({history:e,formatDate:s,formatRelativeTime:t})=>0===e.length?m.jsxs("div",{className:"text-center py-8",children:[m.jsx(h,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),m.jsx("p",{className:"text-gray-500",children:"No hay historial de consumo de pines"})]}):m.jsx("div",{className:"divide-y divide-gray-200",children:e.map(e=>m.jsx("div",{className:"p-4 hover:bg-gray-50",children:m.jsxs("div",{className:"flex items-start justify-between",children:[m.jsxs("div",{className:"flex items-start space-x-3",children:[m.jsx("div",{className:"flex-shrink-0 mt-1",children:m.jsx(g,{className:"h-4 w-4 text-green-500"})}),m.jsxs("div",{className:"flex-1",children:[m.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[m.jsx(p,{className:"h-3 w-3 text-gray-400"}),m.jsxs("span",{className:"text-sm font-medium text-gray-900",children:[e.pacientes.nombre," ",e.pacientes.apellido]})]}),m.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-500 mb-2",children:[m.jsxs("div",{className:"flex items-center",children:[m.jsx(y,{className:"h-3 w-3 mr-1"}),m.jsxs("span",{children:["Sesión: ",s(e.fecha_fin)]})]}),m.jsxs("div",{className:"flex items-center",children:[m.jsx(j,{className:"h-3 w-3 mr-1"}),m.jsxs("span",{children:["Pin: ",t(e.pin_consumed_at)]})]})]}),e.informes&&m.jsxs("div",{className:"flex items-center text-xs text-blue-600",children:[m.jsx(f,{className:"h-3 w-3 mr-1"}),m.jsx("span",{children:e.informes.titulo})]})]})]}),m.jsxs("div",{className:"text-xs text-gray-400",children:["ID: ",e.id.slice(-8)]})]})},e.id))}),Z=({pending:e,formatDate:s,formatRelativeTime:t})=>0===e.length?m.jsxs("div",{className:"text-center py-8",children:[m.jsx(g,{className:"h-12 w-12 text-green-300 mx-auto mb-4"}),m.jsx("p",{className:"text-gray-500",children:"No hay sesiones pendientes de consumo"}),m.jsx("p",{className:"text-xs text-gray-400 mt-2",children:"Todas las sesiones finalizadas ya han consumido su pin correspondiente"})]}):m.jsx("div",{className:"divide-y divide-gray-200",children:e.map(e=>m.jsx("div",{className:"p-4 hover:bg-yellow-50",children:m.jsxs("div",{className:"flex items-start justify-between",children:[m.jsxs("div",{className:"flex items-start space-x-3",children:[m.jsx("div",{className:"flex-shrink-0 mt-1",children:m.jsx(w,{className:"h-4 w-4 text-yellow-500"})}),m.jsxs("div",{className:"flex-1",children:[m.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[m.jsx(p,{className:"h-3 w-3 text-gray-400"}),m.jsxs("span",{className:"text-sm font-medium text-gray-900",children:[e.pacientes.nombre," ",e.pacientes.apellido]})]}),m.jsxs("div",{className:"flex items-center text-xs text-gray-500",children:[m.jsx(y,{className:"h-3 w-3 mr-1"}),m.jsxs("span",{children:["Finalizada: ",t(e.fecha_fin)]})]})]})]}),m.jsx("div",{className:"text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full",children:"Pendiente"})]})},e.id))}),ee=({statistics:e})=>e?m.jsxs("div",{className:"p-6",children:[m.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[m.jsxs("div",{className:"text-center",children:[m.jsx("div",{className:"text-2xl font-bold text-blue-600",children:e.totalCompleted}),m.jsx("div",{className:"text-sm text-gray-600",children:"Sesiones Completadas"})]}),m.jsxs("div",{className:"text-center",children:[m.jsx("div",{className:"text-2xl font-bold text-green-600",children:e.alreadyConsumed}),m.jsx("div",{className:"text-sm text-gray-600",children:"Pines Consumidos"})]}),m.jsxs("div",{className:"text-center",children:[m.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:e.pendingConsumption}),m.jsx("div",{className:"text-sm text-gray-600",children:"Pendientes"})]}),m.jsxs("div",{className:"text-center",children:[m.jsxs("div",{className:"text-2xl font-bold text-purple-600",children:[e.consumptionRate,"%"]}),m.jsx("div",{className:"text-sm text-gray-600",children:"Tasa de Consumo"})]})]}),m.jsxs("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[m.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Resumen de Actividad"}),m.jsxs("div",{className:"text-sm text-blue-800 space-y-1",children:[m.jsxs("p",{children:["• ",e.alreadyConsumed," de ",e.totalCompleted," sesiones han consumido pines"]}),m.jsxs("p",{children:["• ",e.pendingConsumption," sesiones están pendientes de consumo"]}),m.jsxs("p",{children:["• Tasa de consumo del ",e.consumptionRate,"%"]})]})]})]}):m.jsx("div",{className:"text-center py-8",children:m.jsxs("div",{className:"animate-pulse",children:[m.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"}),m.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/2 mx-auto"})]})}),se=({psychologistId:e,className:s=""})=>{const[t,a]=d.useState([]),[r,i]=d.useState(0),[n,l]=d.useState(!1),[c,u]=d.useState(!1),h=d.useCallback(()=>o(null,null,function*(){if(e)try{u(!0);const s=yield W.getUnreadNotifications(e);a(s),i(s.length)}catch(s){}finally{u(!1)}}),[e]);d.useEffect(()=>{h();const e=setInterval(h,3e4);return()=>clearInterval(e)},[h]);const p=e=>o(null,null,function*(){try{yield W.markAsRead(e),a(s=>s.filter(s=>s.id!==e)),i(e=>Math.max(0,e-1))}catch(s){}}),y=e=>{switch(e){case"error":case"critical":return m.jsx(P,{className:"text-red-500"});case"warning":return m.jsx(x,{className:"text-yellow-500"});case"success":return m.jsx(g,{className:"text-green-500"});default:return m.jsx(E,{className:"text-blue-500"})}},j=e=>{switch(e){case"error":case"critical":return"border-red-200 bg-red-50";case"warning":return"border-yellow-200 bg-yellow-50";case"success":return"border-green-200 bg-green-50";default:return"border-blue-200 bg-blue-50"}};return m.jsxs("div",{className:`relative ${s}`,children:[m.jsxs("button",{onClick:()=>l(!n),className:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg transition-colors",title:"Notificaciones de pines",children:[m.jsx(_,{className:"h-5 w-5"}),r>0&&m.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium",children:r>9?"9+":r})]}),n&&m.jsxs("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50",children:[m.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[m.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Notificaciones"}),m.jsxs("div",{className:"flex items-center space-x-2",children:[r>0&&m.jsx("button",{onClick:()=>o(null,null,function*(){try{const e=t.map(e=>W.markAsRead(e.id));yield Promise.all(e),a([]),i(0)}catch(e){}}),className:"text-sm text-blue-600 hover:text-blue-800",children:"Marcar todas como leídas"}),m.jsx("button",{onClick:()=>l(!1),className:"text-gray-400 hover:text-gray-600",children:m.jsx(S,{className:"h-4 w-4"})})]})]}),m.jsx("div",{className:"max-h-96 overflow-y-auto",children:c?m.jsxs("div",{className:"p-4 text-center",children:[m.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"}),m.jsx("p",{className:"text-sm text-gray-500",children:"Cargando notificaciones..."})]}):0===t.length?m.jsxs("div",{className:"p-6 text-center",children:[m.jsx(_,{className:"h-8 w-8 text-gray-300 mx-auto mb-2"}),m.jsx("p",{className:"text-sm text-gray-500",children:"No hay notificaciones nuevas"})]}):m.jsx("div",{className:"divide-y divide-gray-200",children:t.map(e=>m.jsx(te,{notification:e,onMarkAsRead:p,getSeverityIcon:y,getSeverityColors:j},e.id))})}),t.length>0&&m.jsx("div",{className:"p-3 border-t border-gray-200 bg-gray-50 rounded-b-lg",children:m.jsx("button",{onClick:h,className:"w-full text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Actualizar notificaciones"})})]}),n&&m.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>l(!1)})]})},te=({notification:e,onMarkAsRead:s,getSeverityIcon:t,getSeverityColors:a})=>{const{id:r,title:i,message:n,severity:l,created_at:c,metadata:o}=e;return m.jsx("div",{className:`p-4 border-l-4 ${a(l)} hover:bg-opacity-75 transition-colors`,children:m.jsxs("div",{className:"flex items-start space-x-3",children:[m.jsx("div",{className:"flex-shrink-0 mt-0.5",children:t(l)}),m.jsxs("div",{className:"flex-1 min-w-0",children:[m.jsxs("div",{className:"flex items-center justify-between mb-1",children:[m.jsx("h4",{className:"text-sm font-medium text-gray-900 truncate",children:i}),m.jsx("button",{onClick:()=>{s(r)},className:"text-gray-400 hover:text-gray-600 ml-2",title:"Marcar como leída",children:m.jsx(S,{className:"h-3 w-3"})})]}),m.jsx("p",{className:"text-sm text-gray-700 mb-2",children:n}),m.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[m.jsxs("div",{className:"flex items-center",children:[m.jsx(w,{className:"h-3 w-3 mr-1"}),m.jsx("span",{children:(e=>{try{return N(new Date(e),{addSuffix:!0,locale:v})}catch(s){return"Hace un momento"}})(c)})]}),void 0!==(null==o?void 0:o.remainingPins)&&m.jsxs("div",{className:"flex items-center",children:[m.jsx(j,{className:"h-3 w-3 mr-1"}),m.jsxs("span",{children:[o.remainingPins," pines"]})]})]})]})]})})};class ae{static createRechargeRequest(e,s){return o(this,null,function*(){const{requestedPins:t,urgency:a=this.URGENCY_LEVELS.NORMAL,reason:r,contactMethod:i="email",additionalInfo:n={}}=s;try{if(J.logInfo("Creating recharge request",{psychologistId:e,requestedPins:t,urgency:a}),!e||!t||!r)throw new Error("Datos de solicitud incompletos");if(t<=0||t>1e3)throw new Error("Cantidad de pines inválida (1-1000)");const{data:s,error:l}=yield G.from("psicologos").select("id, email, nombre, apellido").eq("id",e).single();if(l||!s)throw new Error("Psicólogo no encontrado");const{data:c}=yield G.from("pin_recharge_requests").select("id, status").eq("psychologist_id",e).eq("status",this.REQUEST_STATUS.PENDING).single();if(c)throw new Error("Ya tienes una solicitud pendiente. Espera a que sea procesada.");const o=yield this._getPsychologistUsageStats(e),{data:d,error:m}=yield G.from("pin_recharge_requests").insert({psychologist_id:e,psychologist_email:s.email,psychologist_name:`${s.nombre} ${s.apellido}`,requested_pins:t,urgency:a,reason:r,contact_method:i,status:this.REQUEST_STATUS.PENDING,metadata:{usage_stats:o,additional_info:n,created_from:"web_app"}}).select().single();if(m)throw m;return yield this._notifyAdministrators(d),yield W.createPinAssignmentNotification(e,0,0,{type:"request_created",message:`Tu solicitud de ${t} pines ha sido enviada y está siendo revisada.`}),J.logSuccess("Recharge request created successfully",{requestId:d.id}),{success:!0,request:d,message:"Solicitud enviada exitosamente"}}catch(l){throw J.logError("Error creating recharge request",l),l}})}static getPendingRequests(){return o(this,arguments,function*(e={}){var s,t;const{urgency:a=null,dateFrom:r=null,dateTo:i=null,limit:n=50,offset:l=0}=e;try{const c=yield Y.getRequests({status:"pending",urgency:a,date_from:r,date_to:i,limit:n,offset:l});if(!c.success)throw new Error(c.error||"Error al obtener solicitudes");return J.logInfo("Recharge requests loaded from API",{total:(null==(s=c.pagination)?void 0:s.total)||0,returned:(null==(t=c.data)?void 0:t.length)||0,filters:e}),c.data||[]}catch(c){throw J.logError("Error getting pending requests",c),c}})}static approveRequest(e,s,t,a=""){return o(this,null,function*(){try{J.logInfo("Approving recharge request",{requestId:e,approvedPins:s,adminId:t});const{data:r,error:i}=yield G.from("pin_recharge_requests").select("*").eq("id",e).single();if(i||!r)throw new Error("Solicitud no encontrada");if(r.status!==this.REQUEST_STATUS.PENDING)throw new Error("La solicitud ya fue procesada");const{error:n}=yield G.from("pin_recharge_requests").update({status:this.REQUEST_STATUS.APPROVED,approved_pins:s,processed_by:t,processed_at:(new Date).toISOString(),admin_notes:a}).eq("id",e);if(n)throw n;const l=(yield R(()=>o(this,null,function*(){const{default:e}=yield import("./PinControlService-Cx97559i.js");return{default:e}}),__vite__mapDeps([0,1,2,3,4,5,6]))).default;return yield l.assignPins(r.psychologist_id,s,!1,"standard"),yield W.createPinAssignmentNotification(r.psychologist_id,s,s),J.logSuccess("Recharge request approved",{requestId:e,approvedPins:s}),{success:!0,message:`Solicitud aprobada: ${s} pines asignados`}}catch(r){throw J.logError("Error approving recharge request",r),r}})}static rejectRequest(e,s,t){return o(this,null,function*(){try{J.logInfo("Rejecting recharge request",{requestId:e,adminId:s});const{data:a,error:r}=yield G.from("pin_recharge_requests").select("*").eq("id",e).single();if(r||!a)throw new Error("Solicitud no encontrada");if(a.status!==this.REQUEST_STATUS.PENDING)throw new Error("La solicitud ya fue procesada");const{error:i}=yield G.from("pin_recharge_requests").update({status:this.REQUEST_STATUS.REJECTED,processed_by:s,processed_at:(new Date).toISOString(),rejection_reason:t}).eq("id",e);if(i)throw i;return yield W.createLowPinNotification(a.psychologist_id,0,{channels:[W.NOTIFICATION_CHANNELS.EMAIL],customMessage:`Tu solicitud de ${a.requested_pins} pines fue rechazada. Motivo: ${t}`}),J.logSuccess("Recharge request rejected",{requestId:e}),{success:!0,message:"Solicitud rechazada"}}catch(a){throw J.logError("Error rejecting recharge request",a),a}})}static getPsychologistRequestHistory(e,s=20){return o(this,null,function*(){try{const{data:t,error:a}=yield G.from("pin_recharge_requests").select("*").eq("psychologist_id",e).order("created_at",{ascending:!1}).limit(s);if(a)throw a;return t||[]}catch(t){throw J.logError("Error getting psychologist request history",t),t}})}static getRequestStatistics(){return o(this,arguments,function*(e={}){try{const{dateFrom:s,dateTo:t}=e;let a=G.from("pin_recharge_requests").select("status, requested_pins, approved_pins, created_at");s&&(a=a.gte("created_at",s)),t&&(a=a.lte("created_at",t));const{data:r,error:i}=yield a;if(i)throw i;return{total:r.length,pending:r.filter(e=>e.status===this.REQUEST_STATUS.PENDING).length,approved:r.filter(e=>e.status===this.REQUEST_STATUS.APPROVED).length,rejected:r.filter(e=>e.status===this.REQUEST_STATUS.REJECTED).length,totalRequested:r.reduce((e,s)=>e+(s.requested_pins||0),0),totalApproved:r.reduce((e,s)=>e+(s.approved_pins||0),0),approvalRate:r.length>0?(r.filter(e=>e.status===this.REQUEST_STATUS.APPROVED).length/r.length*100).toFixed(1):0}}catch(s){throw J.logError("Error getting request statistics",s),s}})}static _getPsychologistUsageStats(e){return o(this,null,function*(){var s;try{const{data:t}=yield G.from("psychologist_usage_control").select("total_uses, used_uses, is_unlimited").eq("psychologist_id",e).eq("is_active",!0).single(),{data:a}=yield G.from("informes").select("created_at").eq("psicologo_id",e).gte("created_at",new Date(Date.now()-2592e6).toISOString()).order("created_at",{ascending:!1});return{current_pins:t?t.total_uses-t.used_uses:0,is_unlimited:(null==t?void 0:t.is_unlimited)||!1,recent_reports_count:(null==a?void 0:a.length)||0,last_activity:(null==(s=null==a?void 0:a[0])?void 0:s.created_at)||null}}catch(t){return J.logError("Error getting psychologist usage stats",t),{current_pins:0,is_unlimited:!1,recent_reports_count:0,last_activity:null}}})}static _notifyAdministrators(e){return o(this,null,function*(){try{J.logInfo("Notifying administrators about new recharge request",{requestId:e.id,urgency:e.urgency})}catch(s){J.logError("Error notifying administrators",s)}})}}c(ae,"REQUEST_STATUS",{PENDING:"pending",APPROVED:"approved",REJECTED:"rejected",CANCELLED:"cancelled"}),c(ae,"URGENCY_LEVELS",{LOW:"low",NORMAL:"normal",HIGH:"high",URGENT:"urgent"});const re=({psychologistId:e,className:s="",showNotifications:t=!0,showRechargePrompt:a=!0})=>{const[r,i]=d.useState(!1),[n,l]=d.useState(!1),{validationResult:c,isValidating:u,isBlocked:h,validateSingleReport:g}=Q(e,{showToastAlerts:!1,autoValidate:!0});d.useEffect(()=>{if(e){const e=setInterval(()=>{g(1)},3e5);return()=>clearInterval(e)}},[e,g]);if(!e||u)return m.jsxs("div",{className:`flex items-center space-x-2 ${s}`,children:[m.jsx(C,{className:"h-4 w-4 text-gray-400 animate-spin"}),m.jsx("span",{className:"text-sm text-gray-500",children:"Cargando..."})]});if(!c)return null;const{remainingPins:p,isUnlimited:y}=c,f=y?{icon:q,color:"text-green-600",bgColor:"bg-green-100",borderColor:"border-green-200",text:"Ilimitado",severity:"success"}:0===p?{icon:x,color:"text-red-600",bgColor:"bg-red-100",borderColor:"border-red-200",text:"0 pines",severity:"error"}:p<=B.THRESHOLDS.CRITICAL_PIN_WARNING?{icon:x,color:"text-red-600",bgColor:"bg-red-100",borderColor:"border-red-200",text:`${p} pines`,severity:"critical"}:p<=B.THRESHOLDS.LOW_PIN_WARNING?{icon:x,color:"text-yellow-600",bgColor:"bg-yellow-100",borderColor:"border-yellow-200",text:`${p} pines`,severity:"warning"}:{icon:j,color:"text-blue-600",bgColor:"bg-blue-100",borderColor:"border-blue-200",text:`${p} pines`,severity:"normal"},N=f.icon;return m.jsxs(m.Fragment,{children:[m.jsxs("div",{className:`flex items-center space-x-3 ${s}`,children:[m.jsxs("div",{className:`flex items-center space-x-2 px-3 py-1.5 rounded-full border cursor-pointer transition-all hover:shadow-sm ${f.bgColor} ${f.borderColor}`,onClick:()=>{h&&a&&i(!0)},title:y?"Plan ilimitado activo":h?"Sin pines disponibles - Click para recargar":`${p} pines disponibles`,children:[m.jsx(N,{className:`h-4 w-4 ${f.color}`}),m.jsx("span",{className:`text-sm font-medium ${f.color}`,children:f.text}),"error"===f.severity&&m.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),"critical"===f.severity&&m.jsx("div",{className:"w-2 h-2 bg-red-400 rounded-full animate-pulse"})]}),t&&m.jsx(se,{psychologistId:e,className:"relative"}),(p<=B.THRESHOLDS.LOW_PIN_WARNING||0===p)&&m.jsxs("button",{onClick:()=>o(null,null,function*(){if(!n)try{l(!0);const s={requestedPins:p<=5?50:25,urgency:0===p?"urgent":p<=5?"high":"normal",reason:0===p?"Sin pines disponibles - Necesito generar informes urgentemente":p<=5?"Pines muy bajos - Necesito recarga para continuar con mis pacientes":"Solicito recarga de pines para mis próximos pacientes"};yield ae.createRechargeRequest(e,s),T.success("Solicitud de recarga enviada exitosamente. Será revisada por un administrador.")}catch(s){T.error("Error al enviar la solicitud de recarga. Intente nuevamente.")}finally{l(!1)}}),disabled:n,className:"flex items-center space-x-1 px-3 py-1.5 rounded-full text-xs font-medium transition-all "+(n?"bg-gray-100 text-gray-400 cursor-not-allowed":0===p?"bg-red-600 hover:bg-red-700 text-white":"bg-yellow-600 hover:bg-yellow-700 text-white"),title:"Solicitar recarga de pines",children:[n?m.jsx(C,{className:"h-3 w-3 animate-spin"}):m.jsx(I,{className:"h-3 w-3"}),m.jsx("span",{children:n?"Enviando...":"Solicitar"})]})]}),r&&m.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:m.jsx("div",{className:"max-w-2xl w-full max-h-screen overflow-y-auto",children:m.jsx(F,{psychologistId:e,currentPins:p||0,requiredPins:1,onClose:()=>i(!1),variant:"modal"})})})]})},ie=({psychologistId:e})=>{const[a,r]=d.useState({startDate:b(D(new Date,30),"yyyy-MM-dd"),endDate:b(new Date,"yyyy-MM-dd")}),[i,n]=d.useState(null),[c,x]=d.useState([]),[u,h]=d.useState(!1),[g,p]=d.useState("overview"),{validationResult:j}=Q(e,{showToastAlerts:!1,autoValidate:!0});d.useEffect(()=>{o(null,null,function*(){if(e)try{h(!0);const[s,t]=yield Promise.all([V.getSessionStatistics(e),V.getConsumptionHistory(e,{limit:100,startDate:a.startDate,endDate:a.endDate})]);n(s),x(t)}catch(s){}finally{h(!1)}})},[e,a]);const f=(e,a)=>{r(r=>{return i=l({},r),s(i,t({[e]:a}));var i})},N=e=>{const s=new Date;let t,a;switch(e){case"today":t=a=b(s,"yyyy-MM-dd");break;case"week":t=b(D(s,7),"yyyy-MM-dd"),a=b(s,"yyyy-MM-dd");break;case"month":t=b(U(s),"yyyy-MM-dd"),a=b(M(s),"yyyy-MM-dd");break;case"quarter":t=b(D(s,90),"yyyy-MM-dd"),a=b(s,"yyyy-MM-dd");break;default:return}r({startDate:t,endDate:a})};return m.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-7xl",children:[m.jsxs("div",{className:"mb-8",children:[m.jsxs("div",{className:"flex items-center justify-between mb-4",children:[m.jsxs("div",{children:[m.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Reportes de Uso de Pines"}),m.jsx("p",{className:"text-gray-600",children:"Historial detallado y transparente de tu consumo de pines"})]}),m.jsxs("div",{className:"flex items-center space-x-4",children:[m.jsx(re,{psychologistId:e,showNotifications:!1}),m.jsx(se,{psychologistId:e})]})]}),m.jsxs("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg",children:[m.jsxs("button",{onClick:()=>p("overview"),className:"px-4 py-2 text-sm font-medium rounded-md transition-colors "+("overview"===g?"bg-white text-blue-700 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[m.jsx(k,{className:"inline mr-2"}),"Resumen"]}),m.jsxs("button",{onClick:()=>p("history"),className:"px-4 py-2 text-sm font-medium rounded-md transition-colors "+("history"===g?"bg-white text-blue-700 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[m.jsx(w,{className:"inline mr-2"}),"Historial"]}),m.jsxs("button",{onClick:()=>p("analytics"),className:"px-4 py-2 text-sm font-medium rounded-md transition-colors "+("analytics"===g?"bg-white text-blue-700 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[m.jsx(k,{className:"inline mr-2"}),"Análisis"]})]})]}),m.jsx(H,{className:"mb-6",children:m.jsx(L,{className:"py-4",children:m.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[m.jsxs("div",{className:"flex items-center space-x-4",children:[m.jsxs("div",{className:"flex items-center space-x-2",children:[m.jsx(y,{className:"text-gray-400"}),m.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Período:"})]}),m.jsx("input",{type:"date",value:a.startDate,onChange:e=>f("startDate",e.target.value),className:"px-3 py-1 border border-gray-300 rounded text-sm"}),m.jsx("span",{className:"text-gray-500",children:"hasta"}),m.jsx("input",{type:"date",value:a.endDate,onChange:e=>f("endDate",e.target.value),className:"px-3 py-1 border border-gray-300 rounded text-sm"})]}),m.jsxs("div",{className:"flex items-center space-x-2",children:[m.jsxs("div",{className:"flex space-x-1",children:[m.jsx($,{size:"sm",variant:"outline",onClick:()=>N("today"),children:"Hoy"}),m.jsx($,{size:"sm",variant:"outline",onClick:()=>N("week"),children:"7 días"}),m.jsx($,{size:"sm",variant:"outline",onClick:()=>N("month"),children:"Este mes"}),m.jsx($,{size:"sm",variant:"outline",onClick:()=>N("quarter"),children:"90 días"})]}),m.jsxs($,{size:"sm",onClick:()=>{const e=[["Fecha","Paciente","Acción","Pines"],...c.map(e=>[b(new Date(e.pin_consumed_at),"dd/MM/yyyy HH:mm"),`${e.pacientes.nombre} ${e.pacientes.apellido}`,"Pin Consumido","1"])].map(e=>e.join(",")).join("\n"),s=new Blob([e],{type:"text/csv"}),t=window.URL.createObjectURL(s),a=document.createElement("a");a.href=t,a.download=`reporte-pines-${b(new Date,"yyyy-MM-dd")}.csv`,a.click(),window.URL.revokeObjectURL(t)},children:[m.jsx(A,{className:"mr-1"}),"Exportar"]})]})]})})}),"overview"===g&&m.jsx(ne,{statistics:i,validationResult:j,consumptionHistory:c,loading:u}),"history"===g&&m.jsx(le,{psychologistId:e,dateRange:a}),"analytics"===g&&m.jsx(ce,{statistics:i,consumptionHistory:c,loading:u})]})},ne=({statistics:e,validationResult:s,consumptionHistory:t,loading:a})=>{if(a)return m.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[...Array(4)].map((e,s)=>m.jsx(H,{children:m.jsxs(L,{className:"animate-pulse",children:[m.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),m.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]})},s))});const r=(null==s?void 0:s.remainingPins)||0,i=(null==s?void 0:s.isUnlimited)||!1;return m.jsxs("div",{className:"space-y-6",children:[m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[m.jsx(oe,{title:"Pines Actuales",value:i?"Ilimitado":r,icon:j,color:"blue",subtitle:i?"Plan ilimitado":"Disponibles"}),m.jsx(oe,{title:"Sesiones Completadas",value:(null==e?void 0:e.totalCompleted)||0,icon:O,color:"green",subtitle:"Total histórico"}),m.jsx(oe,{title:"Informes Generados",value:(null==e?void 0:e.alreadyConsumed)||0,icon:f,color:"purple",subtitle:"Pines consumidos"}),m.jsx(oe,{title:"Tasa de Uso",value:`${(null==e?void 0:e.consumptionRate)||0}%`,icon:k,color:"orange",subtitle:"Eficiencia"})]}),m.jsxs(H,{children:[m.jsx(z,{children:m.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Actividad Reciente"})}),m.jsx(L,{children:0===t.length?m.jsx("div",{className:"text-center py-8 text-gray-500",children:"No hay actividad reciente"}):m.jsx("div",{className:"space-y-3",children:t.slice(0,5).map(e=>m.jsxs("div",{className:"flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0",children:[m.jsxs("div",{className:"flex items-center space-x-3",children:[m.jsx(g,{className:"text-green-500 h-4 w-4"}),m.jsxs("div",{children:[m.jsxs("p",{className:"text-sm font-medium text-gray-900",children:["Informe generado para ",e.pacientes.nombre," ",e.pacientes.apellido]}),m.jsx("p",{className:"text-xs text-gray-500",children:b(new Date(e.pin_consumed_at),"dd/MM/yyyy HH:mm",{locale:v})})]})]}),m.jsx("div",{className:"text-sm text-blue-600 font-medium",children:"-1 pin"})]},e.id))})})]})]})},le=({psychologistId:e,dateRange:s})=>m.jsx(K,{psychologistId:e,className:"w-full"}),ce=({statistics:e,consumptionHistory:s,loading:t})=>t?m.jsx("div",{className:"space-y-6",children:m.jsx(H,{children:m.jsx(L,{className:"animate-pulse",children:m.jsx("div",{className:"h-64 bg-gray-200 rounded"})})})}):m.jsx("div",{className:"space-y-6",children:m.jsxs(H,{children:[m.jsx(z,{children:m.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Análisis de Uso"})}),m.jsx(L,{children:m.jsxs("div",{className:"text-center py-8 text-gray-500",children:[m.jsx(k,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),m.jsx("p",{children:"Gráficos de análisis en desarrollo"}),m.jsx("p",{className:"text-sm",children:"Próximamente: tendencias, patrones de uso y predicciones"})]})})]})}),oe=({title:e,value:s,icon:t,color:a,subtitle:r})=>m.jsx(H,{children:m.jsx(L,{children:m.jsxs("div",{className:"flex items-center",children:[m.jsx("div",{className:`p-3 rounded-lg ${{blue:"text-blue-600 bg-blue-100",green:"text-green-600 bg-green-100",purple:"text-purple-600 bg-purple-100",orange:"text-orange-600 bg-orange-100"}[a]}`,children:m.jsx(t,{className:"h-6 w-6"})}),m.jsxs("div",{className:"ml-4",children:[m.jsx("p",{className:"text-sm font-medium text-gray-600",children:e}),m.jsx("p",{className:"text-2xl font-bold text-gray-900",children:s}),r&&m.jsx("p",{className:"text-xs text-gray-500",children:r})]})]})})}),de=({psychologistIds:e=[]})=>{const[s,t]=d.useState([]),[a,r]=d.useState(!1);return d.useEffect(()=>{o(null,null,function*(){if(0!==e.length)try{r(!0);const s=yield Promise.all(e.map(e=>o(null,null,function*(){try{const s=yield V.getSessionStatistics(e);return l({psychologistId:e},s)}catch(s){return{psychologistId:e,error:s.message}}})));t(s)}catch(s){}finally{r(!1)}})},[e]),a?m.jsx(H,{children:m.jsxs(L,{className:"text-center py-8",children:[m.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),m.jsx("p",{className:"text-gray-600",children:"Cargando resumen administrativo..."})]})}):m.jsxs(H,{children:[m.jsx(z,{children:m.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Resumen Ejecutivo - Uso de Pines"})}),m.jsx(L,{children:m.jsx("div",{className:"overflow-x-auto",children:m.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsx("thead",{className:"bg-gray-50",children:m.jsxs("tr",{children:[m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Psicólogo"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Sesiones"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pines Usados"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pendientes"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Eficiencia"})]})}),m.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>m.jsxs("tr",{children:[m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.psychologistId.slice(-8)}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.totalCompleted||0}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.alreadyConsumed||0}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.pendingConsumption||0}),m.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.consumptionRate||0,"%"]})]},e.psychologistId))})]})})})]})};export{de as AdminPinSummary,ie as default};
