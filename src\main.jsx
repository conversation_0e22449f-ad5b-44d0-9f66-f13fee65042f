import React from 'react';
import ReactDOM from 'react-dom/client';
// import SimpleApp from './SimpleApp.jsx';
// import TestApp from './TestApp.jsx';
import { Provider } from 'react-redux';
import { store } from './store';
import { SimpleAuthProviderTemp as AuthProvider } from './context/SimpleAuthContextTemp';
import App from './App.jsx';
import './index.css';
import './styles/page-effects.css';
import './styles/informe-print.css';

// Aplicación con autenticación real
ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <Provider store={store}>
      <AuthProvider>
        <App />
      </AuthProvider>
    </Provider>
  </React.StrictMode>,
);

// Aplicación completa (usar cuando Supabase esté configurado)
/*
ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <Provider store={store}>
      <SafeAuthProvider>
        <App />
      </SafeAuthProvider>
    </Provider>
  </React.StrictMode>,
);
*/

// Aplicación completa (comentada temporalmente)
/*
ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <Provider store={store}>
      <AuthProvider>
        <App />
      </AuthProvider>
    </Provider>
  </React.StrictMode>,
);
*/

// Versión completa (comentada temporalmente)
/*
ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <Provider store={store}>
      <AuthProvider>
        <App />
      </AuthProvider>
    </Provider>
  </React.StrictMode>,
);
*/
