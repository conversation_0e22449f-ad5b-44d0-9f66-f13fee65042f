import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { Button } from '../ui/Button';
import SessionControlService from '../../services/pin/SessionControlService';
import { 
  FaHistory, 
  FaClock, 
  FaCheckCircle, 
  FaExclamationTriangle,
  FaUser,
  FaFileAlt,
  FaCoins,
  FaCalendarAlt,
  FaSyncAlt
} from 'react-icons/fa';
import { formatDistanceToNow, format } from 'date-fns';
import { es } from 'date-fns/locale';

/**
 * Panel para mostrar el historial de sesiones y consumo de pines
 * Proporciona transparencia total sobre el uso de pines
 */
const SessionHistoryPanel = ({ psychologistId, className = '' }) => {
  const [consumptionHistory, setConsumptionHistory] = useState([]);
  const [pendingSessions, setPendingSessions] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('history'); // 'history', 'pending', 'stats'

  // Cargar datos
  const loadData = useCallback(async () => {
    if (!psychologistId) return;

    try {
      setLoading(true);
      setError(null);

      const [history, pending, stats] = await Promise.all([
        SessionControlService.getConsumptionHistory(psychologistId, { limit: 20 }),
        SessionControlService.getPendingSessions(psychologistId),
        SessionControlService.getSessionStatistics(psychologistId)
      ]);

      setConsumptionHistory(history);
      setPendingSessions(pending);
      setStatistics(stats);

    } catch (err) {
      console.error('Error cargando datos de sesiones:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [psychologistId]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: es });
    } catch (error) {
      return 'Fecha inválida';
    }
  };

  const formatRelativeTime = (dateString) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: es });
    } catch (error) {
      return 'Hace un momento';
    }
  };

  if (loading && !consumptionHistory.length) {
    return (
      <Card className={className}>
        <CardBody className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando historial de sesiones...</p>
        </CardBody>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardBody className="text-center py-8">
          <FaExclamationTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">Error al cargar el historial: {error}</p>
          <Button onClick={loadData} size="sm">
            <FaSyncAlt className="mr-2" />
            Reintentar
          </Button>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="bg-gray-50 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FaHistory className="text-gray-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">
              Historial de Sesiones y Pines
            </h3>
          </div>
          <Button onClick={loadData} size="sm" variant="outline">
            <FaSyncAlt className="mr-1" />
            Actualizar
          </Button>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mt-4">
          <button
            onClick={() => setActiveTab('history')}
            className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeTab === 'history'
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            Historial ({consumptionHistory.length})
          </button>
          <button
            onClick={() => setActiveTab('pending')}
            className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeTab === 'pending'
                ? 'bg-yellow-100 text-yellow-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            Pendientes ({pendingSessions.length})
          </button>
          <button
            onClick={() => setActiveTab('stats')}
            className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeTab === 'stats'
                ? 'bg-green-100 text-green-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            Estadísticas
          </button>
        </div>
      </CardHeader>

      <CardBody className="p-0">
        {/* Tab Content */}
        {activeTab === 'history' && (
          <HistoryTab 
            history={consumptionHistory} 
            formatDate={formatDate}
            formatRelativeTime={formatRelativeTime}
          />
        )}

        {activeTab === 'pending' && (
          <PendingTab 
            pending={pendingSessions}
            formatDate={formatDate}
            formatRelativeTime={formatRelativeTime}
          />
        )}

        {activeTab === 'stats' && (
          <StatisticsTab statistics={statistics} />
        )}
      </CardBody>
    </Card>
  );
};

/**
 * Tab de historial de consumo
 */
const HistoryTab = ({ history, formatDate, formatRelativeTime }) => {
  if (history.length === 0) {
    return (
      <div className="text-center py-8">
        <FaHistory className="h-12 w-12 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500">No hay historial de consumo de pines</p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-gray-200">
      {history.map((item) => (
        <div key={item.id} className="p-4 hover:bg-gray-50">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-1">
                <FaCheckCircle className="h-4 w-4 text-green-500" />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <FaUser className="h-3 w-3 text-gray-400" />
                  <span className="text-sm font-medium text-gray-900">
                    {item.pacientes.nombre} {item.pacientes.apellido}
                  </span>
                </div>
                
                <div className="flex items-center space-x-4 text-xs text-gray-500 mb-2">
                  <div className="flex items-center">
                    <FaCalendarAlt className="h-3 w-3 mr-1" />
                    <span>Sesión: {formatDate(item.fecha_fin)}</span>
                  </div>
                  <div className="flex items-center">
                    <FaCoins className="h-3 w-3 mr-1" />
                    <span>Pin: {formatRelativeTime(item.pin_consumed_at)}</span>
                  </div>
                </div>

                {item.informes && (
                  <div className="flex items-center text-xs text-blue-600">
                    <FaFileAlt className="h-3 w-3 mr-1" />
                    <span>{item.informes.titulo}</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="text-xs text-gray-400">
              ID: {item.id.slice(-8)}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * Tab de sesiones pendientes
 */
const PendingTab = ({ pending, formatDate, formatRelativeTime }) => {
  if (pending.length === 0) {
    return (
      <div className="text-center py-8">
        <FaCheckCircle className="h-12 w-12 text-green-300 mx-auto mb-4" />
        <p className="text-gray-500">No hay sesiones pendientes de consumo</p>
        <p className="text-xs text-gray-400 mt-2">
          Todas las sesiones finalizadas ya han consumido su pin correspondiente
        </p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-gray-200">
      {pending.map((item) => (
        <div key={item.id} className="p-4 hover:bg-yellow-50">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-1">
                <FaClock className="h-4 w-4 text-yellow-500" />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <FaUser className="h-3 w-3 text-gray-400" />
                  <span className="text-sm font-medium text-gray-900">
                    {item.pacientes.nombre} {item.pacientes.apellido}
                  </span>
                </div>
                
                <div className="flex items-center text-xs text-gray-500">
                  <FaCalendarAlt className="h-3 w-3 mr-1" />
                  <span>Finalizada: {formatRelativeTime(item.fecha_fin)}</span>
                </div>
              </div>
            </div>
            
            <div className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
              Pendiente
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * Tab de estadísticas
 */
const StatisticsTab = ({ statistics }) => {
  if (!statistics) {
    return (
      <div className="text-center py-8">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {statistics.totalCompleted}
          </div>
          <div className="text-sm text-gray-600">
            Sesiones Completadas
          </div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {statistics.alreadyConsumed}
          </div>
          <div className="text-sm text-gray-600">
            Pines Consumidos
          </div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-yellow-600">
            {statistics.pendingConsumption}
          </div>
          <div className="text-sm text-gray-600">
            Pendientes
          </div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {statistics.consumptionRate}%
          </div>
          <div className="text-sm text-gray-600">
            Tasa de Consumo
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          Resumen de Actividad
        </h4>
        <div className="text-sm text-blue-800 space-y-1">
          <p>• {statistics.alreadyConsumed} de {statistics.totalCompleted} sesiones han consumido pines</p>
          <p>• {statistics.pendingConsumption} sesiones están pendientes de consumo</p>
          <p>• Tasa de consumo del {statistics.consumptionRate}%</p>
        </div>
      </div>
    </div>
  );
};

export default SessionHistoryPanel;
