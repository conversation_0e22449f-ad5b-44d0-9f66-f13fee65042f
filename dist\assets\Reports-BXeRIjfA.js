var e=Object.defineProperty,t=Object.defineProperties,s=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,i=(t,s,a)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[s]=a,l=(e,t)=>{for(var s in t||(t={}))n.call(t,s)&&i(e,s,t[s]);if(a)for(var s of a(t))r.call(t,s)&&i(e,s,t[s]);return e},o=(e,a)=>t(e,s(a)),c=(e,t,s)=>new Promise((a,n)=>{var r=e=>{try{l(s.next(e))}catch(t){n(t)}},i=e=>{try{l(s.throw(e))}catch(t){n(t)}},l=e=>e.done?a(e.value):Promise.resolve(e.value).then(r,i);l((s=s.apply(e,t)).next())});import{r as d,Q as m,j as u,k as p,P as x,ax as g,R as h,O as f,ay as b,az as j,l as y,an as v,aA as N,aB as _,aC as w,aD as P,aE as C,aF as S,aG as E,aH as A,aI as D,aJ as M,aK as k,Z as T,D as I,aL as R,o as B,p as $,a as O,aM as z,aN as q,B as G,aO as F,aP as L,a4 as U,a3 as V,t as Z,h as W,z as H,m as J,aQ as Q,aR as Y,ae as K,af as X,aS as ee}from"./vendor-BqMjyOVw.js";import{s as te,B as se,j as ae,k as ne,C as re,b as ie,a as le,c as oe}from"./index-Bdl1jgS_.js";import ce from"./InformesService-D-fQ1856.js";import{P as de}from"./PageHeader-DzW86ZOX.js";import"./ImprovedPinControlService-BUPGzexy.js";import"./PinLogger-C2v3yGM1.js";import"./NotificationService-DiDbKBbI.js";class me{static deletePatientReports(e,t="all"){return c(this,null,function*(){if(!e||"string"!=typeof e)return{success:!1,message:"ID de paciente inválido"};if(!["single","all"].includes(t))return{success:!1,message:"Tipo de eliminación inválido"};try{const{data:s,error:a}=yield te.from("pacientes").select("nombre, apellido").eq("id",e).single();if(a)throw new Error("Error al obtener información del paciente");const n=`${s.nombre} ${s.apellido}`;if("single"===t){const{data:t,error:s}=yield te.from("informes_generados").select("id, titulo, fecha_generacion").eq("paciente_id",e).eq("estado","generado").order("fecha_generacion",{ascending:!1}).limit(1).single();if(s||!t)return{success:!1,message:"No hay informes generados para eliminar"};const{error:a}=yield te.from("informes_generados").update({estado:"eliminado",fecha_eliminacion:(new Date).toISOString()}).eq("id",t.id);if(a)throw new Error("Error al eliminar el informe");return{success:!0,message:`Último informe de ${n} eliminado exitosamente`,deletedCount:1}}{const{data:t,error:s}=yield te.from("informes_generados").select("id, titulo").eq("paciente_id",e).eq("estado","generado");if(s)throw new Error("Error al buscar informes del paciente");if(!t||0===t.length)return{success:!1,message:"No hay informes generados para eliminar"};const{error:a}=yield te.from("informes_generados").update({estado:"eliminado",fecha_eliminacion:(new Date).toISOString()}).eq("paciente_id",e).eq("estado","generado");if(a)throw new Error("Error al eliminar los informes");return{success:!0,message:`${t.length} informe(s) de ${n} eliminado(s) exitosamente`,deletedCount:t.length}}}catch(s){return{success:!1,message:s.message||"Error al eliminar informes"}}})}static batchDeleteReports(e){return c(this,null,function*(){try{if(!e||0===e.length)return{success:!1,message:"No se seleccionaron pacientes"};const{data:t,error:s}=yield te.from("informes_generados").select("id, paciente_id").in("paciente_id",e).eq("estado","generado");if(s)throw new Error("Error al contar informes a eliminar");if(!t||0===t.length)return{success:!1,message:"No hay informes generados para los pacientes seleccionados"};const{error:a}=yield te.from("informes_generados").update({estado:"eliminado",fecha_eliminacion:(new Date).toISOString()}).in("paciente_id",e).eq("estado","generado");if(a)throw new Error("Error al eliminar informes en lote");return{success:!0,message:`${t.length} informe(s) de ${e.length} paciente(s) eliminado(s) exitosamente`,deletedCount:t.length,affectedPatients:e.length}}catch(t){return{success:!1,message:t.message||"Error al eliminar informes en lote"}}})}static restorePatientReports(e){return c(this,null,function*(){try{const{data:t,error:s}=yield te.from("informes_generados").select("id, titulo").eq("paciente_id",e).eq("estado","eliminado");if(s)throw new Error("Error al buscar informes eliminados");if(!t||0===t.length)return{success:!1,message:"No hay informes eliminados para restaurar"};const{error:a}=yield te.from("informes_generados").update({estado:"generado",fecha_eliminacion:null}).eq("paciente_id",e).eq("estado","eliminado");if(a)throw new Error("Error al restaurar informes");return{success:!0,message:`${t.length} informe(s) restaurado(s) exitosamente`,restoredCount:t.length}}catch(t){return{success:!1,message:t.message||"Error al restaurar informes"}}})}static getPatientReportHistory(e){return c(this,null,function*(){try{const{data:t,error:s}=yield te.from("informes_generados").select("\n          id,\n          titulo,\n          descripcion,\n          estado,\n          fecha_generacion,\n          fecha_eliminacion,\n          contenido\n        ").eq("paciente_id",e).order("fecha_generacion",{ascending:!1});if(s)throw new Error("Error al obtener historial de informes");return t||[]}catch(t){throw t}})}static getPatientTestSummary(e){return c(this,null,function*(){var t;try{const{data:s,error:a}=yield te.from("resultados").select("\n          id,\n          created_at,\n          aptitudes(codigo, nombre)\n        ").eq("paciente_id",e).order("created_at",{ascending:!1});if(a)throw new Error("Error al obtener resumen de tests");const n=(null==s?void 0:s.length)||0,r=(null==s?void 0:s.map(e=>{var t;return null==(t=e.aptitudes)?void 0:t.codigo}).filter(Boolean))||[],i=[...new Set(r)],l=null==(t=null==s?void 0:s[0])?void 0:t.created_at;return{hasResults:n>0,testCount:n,aptitudesCount:i.length,aptitudes:i,lastTestDate:l}}catch(s){throw s}})}}class ue{static searchPatients(){return c(this,arguments,function*(e={},t={page:1,limit:20}){try{const{institution:s=null,gender:a=null,dateFrom:n=null,dateTo:r=null,patientName:i="",document:l="",testStatus:o="all",sortBy:c="created_at",sortOrder:d="desc"}=e,{page:m,limit:u}=t,p=(m-1)*u;let x=te.from("pacientes").select("\n          id,\n          nombre,\n          apellido,\n          documento,\n          genero,\n          fecha_nacimiento,\n          created_at,\n          instituciones:institucion_id (\n            id,\n            nombre,\n            codigo\n          ),\n          resultados (\n            id,\n            created_at,\n            aptitudes (\n              codigo,\n              nombre\n            )\n          )\n        ",{count:"exact"});if(s&&(x=x.eq("institucion_id",s)),a&&"all"!==a&&(x=x.eq("genero",a)),i.trim()){const e=`%${i.trim()}%`;x=x.or(`nombre.ilike.${e},apellido.ilike.${e}`)}l.trim()&&(x=x.ilike("documento",`%${l.trim()}%`));const g="name"===c?"nombre":c;x=x.order(g,{ascending:"asc"===d}),x=x.range(p,p+u-1);const{data:h,error:f,count:b}=yield x;if(f)throw new Error(`Error en búsqueda: ${f.message}`);return{patients:yield this.processSearchResults(h||[],{dateFrom:n,dateTo:r,testStatus:o}),total:b||0,page:m,limit:u,totalPages:Math.ceil((b||0)/u),hasNextPage:m*u<(b||0),hasPrevPage:m>1}}catch(s){throw s}})}static processSearchResults(e,t){return c(this,null,function*(){const{dateFrom:s,dateTo:a,testStatus:n}=t;return e.map(e=>{const t=e.resultados||[];let r=t;(s||a)&&(r=t.filter(e=>{const t=new Date(e.created_at),n=s?new Date(s):null,r=a?new Date(a):null;return!(n&&t<n)&&!(r&&t>r)}));const i=r.length,c=r.map(e=>{var t;return null==(t=e.aptitudes)?void 0:t.codigo}).filter(Boolean),d=[...new Set(c)],m=r.length>0?r.sort((e,t)=>new Date(t.created_at)-new Date(e.created_at))[0].created_at:null;let u="no_tests";if(u=0===i?"no_tests":d.length<8?"partial":"completed","all"!==n){if("completed"===n&&"completed"!==u)return null;if("partial"===n&&"partial"!==u)return null;if("no_tests"===n&&"no_tests"!==u)return null}return o(l({},e),{testSummary:{testCount:i,uniqueAptitudesCount:d.length,aptitudes:d,lastTestDate:m,status:u,completionPercentage:Math.round(d.length/8*100)},resultados:r})}).filter(Boolean)})}static getInstitutions(){return c(this,null,function*(){try{const{data:e,error:t}=yield te.from("instituciones").select("id, nombre, codigo").order("nombre");if(t)throw new Error(`Error al obtener instituciones: ${t.message}`);return e||[]}catch(e){throw e}})}static getPatientNameSuggestions(e,t=10){return c(this,null,function*(){try{if(!e||e.length<2)return[];const s=`%${e.trim()}%`,{data:a,error:n}=yield te.from("pacientes").select("id, nombre, apellido, documento").or(`nombre.ilike.${s},apellido.ilike.${s}`).limit(t);if(n)throw new Error(`Error en sugerencias: ${n.message}`);return(a||[]).map(e=>({id:e.id,label:`${e.nombre} ${e.apellido}`,sublabel:e.documento,value:`${e.nombre} ${e.apellido}`}))}catch(s){return[]}})}static getSearchStats(){return c(this,arguments,function*(e={}){try{const{data:e,error:t}=yield te.from("pacientes").select("\n          id,\n          genero,\n          institucion_id,\n          resultados!inner (\n            id,\n            created_at\n          )\n        ");if(t)throw new Error(`Error al obtener estadísticas: ${t.message}`);const s=(null==e?void 0:e.length)||0,a=(null==e?void 0:e.filter(e=>{var t;return null==(t=e.genero)?void 0:t.toLowerCase().startsWith("m")}).length)||0,n=(null==e?void 0:e.filter(e=>{var t;return null==(t=e.genero)?void 0:t.toLowerCase().startsWith("f")}).length)||0,r={};null==e||e.forEach(e=>{const t=e.institucion_id;r[t]=(r[t]||0)+1});const i=(null==e?void 0:e.flatMap(e=>e.resultados.map(e=>new Date(e.created_at))))||[],l=i.length>0?new Date(Math.min(...i)):null,o=i.length>0?new Date(Math.max(...i)):null;return{totalPatients:s,genderDistribution:{male:a,female:n},institutionDistribution:r,dateRange:{earliest:l,latest:o},totalTests:i.length}}catch(t){return{totalPatients:0,genderDistribution:{male:0,female:0},institutionDistribution:{},dateRange:{earliest:null,latest:null},totalTests:0}}})}static exportToCSV(e){if(!e||0===e.length)return"";return[["ID","Nombre","Apellido","Documento","Género","Institución","Tests Realizados","Aptitudes Evaluadas","Porcentaje Completado","Última Evaluación","Estado"],...e.map(e=>{var t,s,a,n,r,i;return[e.id,e.nombre,e.apellido,e.documento,e.genero,(null==(t=e.instituciones)?void 0:t.nombre)||"N/A",(null==(s=e.testSummary)?void 0:s.testCount)||0,(null==(a=e.testSummary)?void 0:a.uniqueAptitudesCount)||0,`${(null==(n=e.testSummary)?void 0:n.completionPercentage)||0}%`,(null==(r=e.testSummary)?void 0:r.lastTestDate)?new Date(e.testSummary.lastTestDate).toLocaleDateString("es-ES"):"N/A",(null==(i=e.testSummary)?void 0:i.status)||"no_tests"]})].map(e=>e.map(e=>`"${e}"`).join(",")).join("\n")}}const pe=5,xe="unlimited",ge="active",he="low_pins",fe="no_pins";const be=new class{getAllPsychologists(){return c(this,null,function*(){try{const{data:e,error:t}=yield te.rpc("get_all_psychologists_with_stats");if(t)return yield this._getAllPsychologistsManual();return(e||[]).map(e=>this._transformPsychologistData(e,!0))}catch(e){return yield this._getAllPsychologistsManual()}})}_getAllPsychologistsManual(){return c(this,null,function*(){const{data:e,error:t}=yield te.from("psicologos").select("id, nombre, apellido, email").order("nombre");if(t)throw t;const{data:s,error:a}=yield te.from("psychologist_usage_control").select("*").eq("is_active",!0);if(a)throw a;const{data:n,error:r}=yield te.from("pacientes").select("psicologo_id").not("psicologo_id","is",null);if(r)throw r;const{data:i,error:l}=yield te.from("resultados").select("\n        pacientes!inner(psicologo_id)\n      ");if(l)throw l;const o=this._createCountMap(n,"psicologo_id"),c=this._createCountMap(i.map(e=>({psicologo_id:e.pacientes.psicologo_id})),"psicologo_id");return(e||[]).map(e=>{const t=null==s?void 0:s.find(t=>t.psychologist_id===e.id),a=o.get(e.id)||0,n=c.get(e.id)||0;return this._transformPsychologistData({psychologist_id:e.id,nombre:e.nombre,apellido:e.apellido,email:e.email,total_uses:(null==t?void 0:t.total_uses)||0,used_uses:(null==t?void 0:t.used_uses)||0,is_unlimited:(null==t?void 0:t.is_unlimited)||!1,plan_type:(null==t?void 0:t.plan_type)||"none",updated_at:null==t?void 0:t.updated_at,assigned_patients:a,completed_tests:n},!0)})})}_createCountMap(e,t){const s=new Map;return e.forEach(e=>{const a=e[t];a&&s.set(a,(s.get(a)||0)+1)}),s}_transformPsychologistData(e,t=!1){const s=e.total_uses||0,a=e.used_uses||0,n=e.is_unlimited?null:Math.max(0,s-a),r=this._determineStatus(e.is_unlimited,s,n),i={psychologist_id:e.psychologist_id||e.id,psychologist_name:`${e.nombre} ${e.apellido}`,psychologist_email:e.email,total_pins:s,used_pins:a,remaining_pins:n,is_unlimited:e.is_unlimited||!1,plan_type:e.plan_type||"none",usage_percentage:this._calculateUsagePercentage(a,s,e.is_unlimited),assigned_patients:e.assigned_patients||0,completed_tests:e.completed_tests||0,status:r,last_activity:e.updated_at};return t&&(i.has_control=!(void 0===e.total_uses&&!e.is_unlimited)),i}_transformPinStatsData(e){return{psychologist_id:e.psych_id,psychologist_name:e.psych_name,psychologist_email:e.psych_email,total_pins:e.total_pins,used_pins:e.used_pins,remaining_pins:e.remaining_pins,is_unlimited:e.is_unlimited,plan_type:e.plan_type,usage_percentage:parseFloat(e.usage_percentage)||0,assigned_patients:e.assigned_patients,completed_tests:e.completed_tests,pins_consumed_today:e.pins_consumed_today,status:e.status,last_activity:e.last_activity}}getPinConsumptionStats(){return c(this,null,function*(){try{const{data:e,error:t}=yield te.rpc("get_pin_stats_v2");if(t)return yield this._getPinConsumptionStatsManual();return(e||[]).map(e=>this._transformPinStatsData(e))}catch(e){return yield this._getPinConsumptionStatsManual()}})}_getPinConsumptionStatsManual(){return c(this,null,function*(){const{data:e,error:t}=yield te.from("psicologos").select("\n        id,\n        nombre,\n        apellido,\n        email,\n        psychologist_usage_control!inner (\n          total_uses,\n          used_uses,\n          is_unlimited,\n          plan_type,\n          updated_at,\n          is_active\n        )\n      ").eq("psychologist_usage_control.is_active",!0);if(t)throw t;const s=e.map(e=>e.id),{data:a}=yield te.from("pacientes").select("psicologo_id").in("psicologo_id",s),{data:n}=yield te.from("resultados").select("pacientes!inner(psicologo_id)").in("pacientes.psicologo_id",s),r=this._createCountMap(a||[],"psicologo_id"),i=this._createCountMap((n||[]).map(e=>({psicologo_id:e.pacientes.psicologo_id})),"psicologo_id");return(e||[]).map(e=>{const t=e.psychologist_usage_control[0],s=r.get(e.id)||0,a=i.get(e.id)||0;return this._transformPsychologistData({psychologist_id:e.id,nombre:e.nombre,apellido:e.apellido,email:e.email,total_uses:(null==t?void 0:t.total_uses)||0,used_uses:(null==t?void 0:t.used_uses)||0,is_unlimited:(null==t?void 0:t.is_unlimited)||!1,plan_type:(null==t?void 0:t.plan_type)||"none",updated_at:null==t?void 0:t.updated_at,assigned_patients:s,completed_tests:a})})})}assignPins(e,t,s=!1,a="assigned"){return c(this,null,function*(){try{const{data:n,error:r}=yield te.from("psychologist_usage_control").select("*").eq("psychologist_id",e).eq("is_active",!0).single();if(r&&"PGRST116"!==r.code)throw r;let i;if(n){const e=s?0:n.total_uses+t,{data:r,error:l}=yield te.from("psychologist_usage_control").update({total_uses:e,is_unlimited:s,plan_type:a,updated_at:(new Date).toISOString()}).eq("id",n.id).select().single();if(l)throw l;i=r}else{const{data:n,error:r}=yield te.from("psychologist_usage_control").insert({psychologist_id:e,total_uses:s?0:t,used_uses:0,is_unlimited:s,plan_type:a,is_active:!0}).select().single();if(r)throw r;i=n}return yield this.logPinAction(e,"pin_assigned",{pins_assigned:t,is_unlimited:s,plan_type:a}),i}catch(n){throw n}})}consumePin(e,t=null,s=null,a=null){return c(this,null,function*(){try{const{data:n,error:r}=yield te.from("psychologist_usage_control").select("*").eq("psychologist_id",e).eq("is_active",!0).single();if(r)throw new Error("No se encontró control de uso para este psicólogo");if(n.is_unlimited)return yield this.logPinAction(e,"pin_consumed",{patient_id:t,test_session_id:s,report_id:a,is_unlimited:!0},t,s,a),!0;const i=n.total_uses-n.used_uses;if(i<=0)throw new Error("No hay pines disponibles para este psicólogo");const{data:l,error:o}=yield te.from("psychologist_usage_control").update({used_uses:n.used_uses+1,updated_at:(new Date).toISOString()}).eq("id",n.id).select().single();if(o)throw o;yield this.logPinAction(e,"pin_consumed",{pins_before:i,pins_after:i-1,patient_id:t,test_session_id:s,report_id:a},t,s,a);const c=i-1;return c<=pe&&c>0&&(yield this.createLowPinNotification(e,c)),!0}catch(n){throw n}})}checkPsychologistUsage(e){return c(this,null,function*(){try{const{data:t,error:s}=yield te.from("psychologist_usage_control").select("*").eq("psychologist_id",e).eq("is_active",!0).single();if(s&&"PGRST116"!==s.code)throw s;if(!t)return{canUse:!1,reason:"No tiene pines asignados",remainingPins:0,isUnlimited:!1};if(t.is_unlimited)return{canUse:!0,reason:"Plan ilimitado",remainingPins:null,isUnlimited:!0};const a=t.total_uses-t.used_uses;return{canUse:a>0,reason:a>0?"Pines disponibles":"Sin pines disponibles",remainingPins:a,isUnlimited:!1,totalPins:t.total_uses,usedPins:t.used_uses}}catch(t){throw t}})}getPinUsageHistory(e=null,t=50){return c(this,null,function*(){try{let s=te.from("pin_usage_logs").select("\n          *,\n          psychologist:psicologos(nombre, apellido, email),\n          patient:pacientes(nombre, apellido, documento)\n        ").order("created_at",{ascending:!1}).limit(t);e&&(s=s.eq("psychologist_id",e));const{data:a,error:n}=yield s;if(n)throw n;return a||[]}catch(s){throw s}})}getPinConsumptionAlerts(){return c(this,null,function*(){try{const e=yield this.getPinConsumptionStats(),t=[];return e.forEach(e=>{"low_pins"===e.status?t.push({type:"warning",psychologist_id:e.psychologist_id,psychologist_name:e.psychologist_name,message:`${e.psychologist_name} tiene solo ${e.remaining_pins} pines restantes`,severity:"warning"}):"no_pins"===e.status&&t.push({type:"error",psychologist_id:e.psychologist_id,psychologist_name:e.psychologist_name,message:`${e.psychologist_name} no tiene pines disponibles`,severity:"error"})}),t}catch(e){throw e}})}logPinAction(e,t){return c(this,arguments,function*(e,t,s={},a=null,n=null,r=null){try{const{error:i}=yield te.from("pin_usage_logs").insert({psychologist_id:e,patient_id:a,test_session_id:n,report_id:r,action_type:t,pins_before:s.pins_before||0,pins_after:s.pins_after||0,pins_consumed:"pin_consumed"===t?1:0,description:this.getActionDescription(t,s),metadata:s})}catch(i){}})}createLowPinNotification(e,t){return c(this,null,function*(){try{const{error:s}=yield te.rpc("create_low_pin_notification",{p_psychologist_id:e,p_remaining_pins:t})}catch(s){}})}_determineStatus(e,t,s){return e?xe:0===t||s<=0?fe:s<=pe?he:ge}_calculateUsagePercentage(e,t,s){return s||0===t?0:Math.round(e/t*100*100)/100}getActionDescription(e,t){switch(e){case"pin_assigned":return`Se asignaron ${t.pins_assigned||0} pines${t.is_unlimited?" (plan ilimitado)":""}`;case"pin_consumed":return t.is_unlimited?"Pin consumido (plan ilimitado)":`Pin consumido. Quedan ${t.pins_after||0} pines`;case"test_completed":return"Test completado - Pin consumido automáticamente";case"report_generated":return"Informe generado - Pin consumido automáticamente";default:return`Acción: ${e}`}}getSystemSummary(){return c(this,null,function*(){try{const e=yield this.getPinConsumptionStats();return{totalPsychologists:e.length,totalPinsAssigned:e.reduce((e,t)=>e+(t.is_unlimited?0:t.total_pins),0),totalPinsUsed:e.reduce((e,t)=>e+t.used_pins,0),totalPinsRemaining:e.reduce((e,t)=>e+(t.is_unlimited?0:t.remaining_pins||0),0),unlimitedPsychologists:e.filter(e=>e.is_unlimited).length,activePsychologists:e.filter(e=>"active"===e.status).length,lowPinsPsychologists:e.filter(e=>"low_pins"===e.status).length,noPinsPsychologists:e.filter(e=>"no_pins"===e.status).length,totalPatients:e.reduce((e,t)=>e+t.assigned_patients,0),totalTests:e.reduce((e,t)=>e+t.completed_tests,0)}}catch(e){throw e}})}};be.getMigrationRecommendation=()=>{};class je{static getResultadosByPaciente(e){return c(this,null,function*(){try{const{data:t,error:s}=yield te.from("resultados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento,\n            genero,\n            fecha_nacimiento,\n            created_at\n          ),\n          aptitudes (\n            id,\n            codigo,\n            nombre,\n            descripcion\n          )\n        ").eq("paciente_id",e).order("created_at",{ascending:!1});if(s)throw s;return t||[]}catch(t){throw t}})}static getEstadisticasPaciente(e){return c(this,null,function*(){var t,s;try{const a=yield this.getResultadosByPaciente(e);if(0===a.length)return{totalTests:0,averagePercentile:0,averageDirectScore:0,totalErrors:0,averageErrors:0,totalTimeSeconds:0,averageTimeSeconds:0,concentracionPromedio:0,lastTestDate:null,patientInfo:null};const n=a.length,r=a.reduce((e,t)=>e+(t.percentil||0),0),i=a.reduce((e,t)=>e+(t.puntaje_directo||0),0),l=a.reduce((e,t)=>e+(t.errores||0),0),o=a.reduce((e,t)=>e+(t.tiempo_segundos||0),0),c=a.reduce((e,t)=>e+(t.concentracion||0),0);return{totalTests:n,averagePercentile:Math.round(r/n),averageDirectScore:Math.round(i/n),totalErrors:l,averageErrors:l/n,totalTimeSeconds:o,averageTimeSeconds:o/n,concentracionPromedio:c/n,lastTestDate:null==(t=a[0])?void 0:t.created_at,patientInfo:null==(s=a[0])?void 0:s.pacientes,resultadosPorAptitud:this.agruparPorAptitud(a)}}catch(a){throw a}})}static agruparPorAptitud(e){const t={};return e.forEach(e=>{var s;const a=null==(s=e.aptitudes)?void 0:s.codigo;a&&(t[a]||(t[a]={aptitud:e.aptitudes,resultados:[],promedios:{percentil:0,puntajeDirecto:0,errores:0,tiempo:0,concentracion:0}}),t[a].resultados.push(e))}),Object.keys(t).forEach(e=>{const s=t[e],a=s.resultados.length;s.promedios={percentil:Math.round(s.resultados.reduce((e,t)=>e+(t.percentil||0),0)/a),puntajeDirecto:Math.round(s.resultados.reduce((e,t)=>e+(t.puntaje_directo||0),0)/a),errores:s.resultados.reduce((e,t)=>e+(t.errores||0),0)/a,tiempo:s.resultados.reduce((e,t)=>e+(t.tiempo_segundos||0),0)/a,concentracion:s.resultados.reduce((e,t)=>e+(t.concentracion||0),0)/a}}),t}static insertarResultado(e,t=null){return c(this,null,function*(){try{const{data:a,error:n}=yield te.from("resultados").insert([{paciente_id:e.paciente_id,aptitud_id:e.aptitud_id,puntaje_directo:e.puntaje_directo,percentil:e.percentil,interpretacion:e.interpretacion,tiempo_segundos:e.tiempo_segundos,respuestas:e.respuestas,concentracion:e.concentracion,errores:e.errores||0,percentil_compared:e.percentil_compared,respuestas_correctas:e.respuestas_correctas,respuestas_incorrectas:e.respuestas_incorrectas,respuestas_sin_contestar:e.respuestas_sin_contestar,total_preguntas:e.total_preguntas}]).select().single();if(n)throw n;try{const{data:s,error:a}=yield te.from("pacientes").select("psicologo_id").eq("id",e.paciente_id).single();!a&&(null==s?void 0:s.psicologo_id)&&(yield be.consumePin(s.psicologo_id,e.paciente_id,t,null))}catch(s){}return a}catch(a){throw a}})}static actualizarResultado(e,t){return c(this,null,function*(){try{const{data:s,error:a}=yield te.from("resultados").update(o(l({},t),{updated_at:(new Date).toISOString()})).eq("id",e).select().single();if(a)throw a;return s}catch(s){throw s}})}static eliminarResultado(e){return c(this,null,function*(){try{const{error:t}=yield te.from("resultados").delete().eq("id",e);if(t)throw t;return!0}catch(t){throw t}})}static getResultadosPaginados(){return c(this,arguments,function*(e=1,t=10,s={}){try{let a=te.from("resultados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento\n          ),\n          aptitudes (\n            id,\n            codigo,\n            nombre\n          )\n        ",{count:"exact"});s.paciente_id&&(a=a.eq("paciente_id",s.paciente_id)),s.aptitud_id&&(a=a.eq("aptitud_id",s.aptitud_id)),s.fecha_desde&&(a=a.gte("created_at",s.fecha_desde)),s.fecha_hasta&&(a=a.lte("created_at",s.fecha_hasta));const n=(e-1)*t,r=n+t-1;a=a.range(n,r).order("created_at",{ascending:!1});const{data:i,error:l,count:o}=yield a;if(l)throw l;return{data:i||[],total:o||0,page:e,limit:t,totalPages:Math.ceil((o||0)/t)}}catch(a){throw a}})}static getEstadisticasGenerales(){return c(this,null,function*(){try{const{data:e,error:t}=yield te.from("resultados").select("*");if(t)throw t;const{data:s,error:a}=yield te.from("pacientes").select("id");if(a)throw a;const n=(null==e?void 0:e.length)||0,r=(null==s?void 0:s.length)||0;if(0===n)return{totalResultados:0,totalPacientes:r,promedioTestsPorPaciente:0,percentilPromedio:0,puntajeDirectoPromedio:0,erroresPromedio:0,tiempoPromedioMinutos:0};const i=e.reduce((e,t)=>e+(t.percentil||0),0),l=e.reduce((e,t)=>e+(t.puntaje_directo||0),0),o=e.reduce((e,t)=>e+(t.errores||0),0),c=e.reduce((e,t)=>e+(t.tiempo_segundos||0),0);return{totalResultados:n,totalPacientes:r,promedioTestsPorPaciente:r>0?(n/r).toFixed(1):0,percentilPromedio:Math.round(i/n),puntajeDirectoPromedio:Math.round(l/n),erroresPromedio:(o/n).toFixed(1),tiempoPromedioMinutos:Math.round(c/n/60)}}catch(e){throw e}})}static buscarResultados(e){return c(this,null,function*(){try{let t=te.from("resultados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento\n          ),\n          aptitudes (\n            id,\n            codigo,\n            nombre,\n            descripcion\n          )\n        ");e.nombrePaciente&&(t=t.ilike("pacientes.nombre",`%${e.nombrePaciente}%`)),e.documentoPaciente&&(t=t.eq("pacientes.documento",e.documentoPaciente)),e.codigoAptitud&&(t=t.eq("aptitudes.codigo",e.codigoAptitud)),e.percentilMinimo&&(t=t.gte("percentil",e.percentilMinimo)),e.percentilMaximo&&(t=t.lte("percentil",e.percentilMaximo)),t=t.order("created_at",{ascending:!1});const{data:s,error:a}=yield t;if(a)throw a;return s||[]}catch(t){throw t}})}}class ye{static generateBatchReports(e){return c(this,arguments,function*(e,t=null,s={}){var a;const{reportType:n="individual",includeCharts:r=!0,format:i="pdf"}=s,l={successful:[],failed:[],total:e.length,startTime:new Date,endTime:null};try{for(let s=0;s<e.length;s++){const n=e[s],r=Math.round((s+1)/e.length*100);t&&t({current:s+1,total:e.length,percentage:r,currentPatientId:n,status:"processing"});try{const e=yield je.getResultadosByPaciente(n);if(!e||0===e.length){l.failed.push({patientId:n,error:"No hay resultados de evaluación disponibles"});continue}const t=null==(a=e[0])?void 0:a.pacientes;if(!t){l.failed.push({patientId:n,error:"Información del paciente no encontrada"});continue}const s=yield ce.generarInformeCompleto(n,`Informe BAT-7 - ${t.nombre} ${t.apellido}`,`Informe generado en lote - ${(new Date).toLocaleDateString("es-ES")}`),r=yield ce.obtenerInforme(s);l.successful.push({patientId:n,patientName:`${t.nombre} ${t.apellido}`,reportId:s,reportData:r}),yield new Promise(e=>setTimeout(e,100))}catch(o){l.failed.push({patientId:n,error:o.message||"Error desconocido"})}}return l.endTime=new Date,l.duration=l.endTime-l.startTime,t&&t({current:e.length,total:e.length,percentage:100,status:"completed",results:l}),l}catch(o){throw l.endTime=new Date,l.duration=l.endTime-l.startTime,new Error(`Error en generación masiva: ${o.message}`)}})}static generateComparativeReport(e){return c(this,arguments,function*(e,t={}){try{const{title:s="Informe Comparativo BAT-7",includeIndividualSections:a=!1,groupBy:n="institution"}=t,r=[];for(const t of e){const e=yield je.getResultadosByPaciente(t);if(e&&e.length>0){const s=yield je.getEstadisticasPaciente(t);r.push({patient:e[0].pacientes,results:e,stats:s})}}if(0===r.length)throw new Error("No se encontraron datos válidos para la comparación");const i=this.generateComparativeAnalysis(r,n);return{tipo:"comparativo",titulo:s,fecha_generacion:(new Date).toISOString(),pacientes_incluidos:r.length,contenido:{resumen_ejecutivo:this.generateExecutiveSummary(i),analisis_grupal:i,estadisticas_generales:this.calculateGroupStatistics(r),recomendaciones:this.generateGroupRecommendations(i),pacientes:a?r:null}}}catch(s){throw s}})}static generateComparativeAnalysis(e,t){const s={grupos:{},aptitudes_comparadas:{},tendencias:{},outliers:[]};return e.forEach(({patient:e,stats:a})=>{var n;let r="general";switch(t){case"institution":r=(null==(n=e.instituciones)?void 0:n.nombre)||"Sin institución";break;case"gender":r=e.genero||"No especificado";break;case"age_group":const t=this.calculateAge(e.fecha_nacimiento);r=this.getAgeGroup(t)}s.grupos[r]||(s.grupos[r]={pacientes:[],estadisticas:{count:0,promedios:{},rangos:{}}}),s.grupos[r].pacientes.push({patient:e,stats:a}),s.grupos[r].estadisticas.count++}),Object.keys(s.grupos).forEach(e=>{const t=s.grupos[e];t.estadisticas=this.calculateGroupStats(t.pacientes)}),s.aptitudes_comparadas=this.compareAptitudesAcrossGroups(s.grupos),s}static calculateGroupStats(e){const t={count:e.length,promedios:{},rangos:{},distribucion_genero:{masculino:0,femenino:0}};if(0===e.length)return t;const s={};return e.forEach(({patient:e,stats:a})=>{var n;const r=null==(n=e.genero)?void 0:n.toLowerCase();(null==r?void 0:r.startsWith("m"))&&t.distribucion_genero.masculino++,(null==r?void 0:r.startsWith("f"))&&t.distribucion_genero.femenino++,a.resultadosPorAptitud&&Object.keys(a.resultadosPorAptitud).forEach(e=>{const t=a.resultadosPorAptitud[e];s[e]||(s[e]={percentiles:[],puntajesDirectos:[]}),s[e].percentiles.push(t.promedios.percentil),s[e].puntajesDirectos.push(t.promedios.puntajeDirecto)})}),Object.keys(s).forEach(e=>{const a=s[e];t.promedios[e]={percentil:Math.round(a.percentiles.reduce((e,t)=>e+t,0)/a.percentiles.length),puntajeDirecto:Math.round(a.puntajesDirectos.reduce((e,t)=>e+t,0)/a.puntajesDirectos.length)},t.rangos[e]={percentil:{min:Math.min(...a.percentiles),max:Math.max(...a.percentiles)},puntajeDirecto:{min:Math.min(...a.puntajesDirectos),max:Math.max(...a.puntajesDirectos)}}}),t}static compareAptitudesAcrossGroups(e){const t={},s=Object.keys(e),a=new Set;return s.forEach(t=>{const s=e[t];Object.keys(s.estadisticas.promedios||{}).forEach(e=>{a.add(e)})}),a.forEach(a=>{t[a]={grupos:{},mejor_grupo:null,mayor_diferencia:0};let n=-1,r=101,i=null;s.forEach(s=>{const l=e[s].estadisticas.promedios[a];l&&(t[a].grupos[s]=l,l.percentil>n&&(n=l.percentil,i=s),l.percentil<r&&(r=l.percentil))}),t[a].mejor_grupo=i,t[a].mayor_diferencia=n-r}),t}static generateExecutiveSummary(e){const t={total_grupos:Object.keys(e.grupos).length,total_pacientes:Object.values(e.grupos).reduce((e,t)=>e+t.estadisticas.count,0),aptitudes_evaluadas:Object.keys(e.aptitudes_comparadas).length,hallazgos_principales:[],recomendaciones_clave:[]},s=e.aptitudes_comparadas;return Object.keys(s).forEach(e=>{const a=s[e];a.mayor_diferencia>20&&t.hallazgos_principales.push(`Diferencia significativa en ${e}: ${a.mayor_diferencia} puntos entre grupos`)}),t}static calculateAge(e){if(!e)return 0;const t=new Date,s=new Date(e);let a=t.getFullYear()-s.getFullYear();const n=t.getMonth()-s.getMonth();return(n<0||0===n&&t.getDate()<s.getDate())&&a--,a}static getAgeGroup(e){return e<12?"Niños (< 12 años)":e<15?"Adolescentes tempranos (12-14 años)":e<18?"Adolescentes (15-17 años)":e<25?"Jóvenes adultos (18-24 años)":"Adultos (25+ años)"}static calculateGroupStatistics(e){return{total_pacientes:e.length,distribucion_genero:this.calculateGenderDistribution(e),rango_edades:this.calculateAgeRange(e),instituciones_representadas:this.getUniqueInstitutions(e),promedio_tests_por_paciente:this.calculateAverageTestsPerPatient(e)}}static generateGroupRecommendations(e){const t=[];return Object.keys(e.aptitudes_comparadas).forEach(s=>{const a=e.aptitudes_comparadas[s];a.mayor_diferencia>25&&t.push({tipo:"diferencia_significativa",aptitud:s,descripcion:`Se observa una diferencia significativa de ${a.mayor_diferencia} puntos en ${s} entre grupos`,recomendacion:`Considerar estrategias de intervención específicas para los grupos con menor rendimiento en ${s}`})}),t}static calculateGenderDistribution(e){const t={masculino:0,femenino:0,no_especificado:0};return e.forEach(({patient:e})=>{var s;const a=null==(s=e.genero)?void 0:s.toLowerCase();(null==a?void 0:a.startsWith("m"))?t.masculino++:(null==a?void 0:a.startsWith("f"))?t.femenino++:t.no_especificado++}),t}static calculateAgeRange(e){const t=e.map(({patient:e})=>this.calculateAge(e.fecha_nacimiento)).filter(e=>e>0);return 0===t.length?{min:0,max:0,promedio:0}:{min:Math.min(...t),max:Math.max(...t),promedio:Math.round(t.reduce((e,t)=>e+t,0)/t.length)}}static getUniqueInstitutions(e){const t=new Set;return e.forEach(({patient:e})=>{var s;(null==(s=e.instituciones)?void 0:s.nombre)&&t.add(e.instituciones.nombre)}),Array.from(t)}static calculateAverageTestsPerPatient(e){if(0===e.length)return 0;const t=e.reduce((e,{stats:t})=>e+(t.totalTests||0),0);return Math.round(t/e.length*10)/10}}const ve=d.memo(({isOpen:e,onClose:t,reportData:s,patient:a,results:n})=>{var r,i;const[l,o]=d.useState(!1),[T,I]=d.useState(null);d.useEffect(()=>{e&&s?(I(s),o(!1)):e&&a&&n&&R()},[e,s,a,n]);const R=()=>c(null,null,function*(){if(a&&n){o(!0);try{const e=yield ce.generarInformeCompleto(a.id,`Informe BAT-7 - ${a.nombre} ${a.apellido}`,"Informe psicológico completo generado automáticamente");I(e)}catch(e){m.error("Error al generar el informe")}finally{o(!1)}}}),B=()=>{try{const e=document.querySelectorAll(".print\\:hidden, .no-print, button:not(.print-keep), .modal-overlay, .modal-close");e.forEach(e=>e.style.display="none");const t=document.title,s=(null==$?void 0:$.nombre)||"Paciente";document.title=`Informe_BAT7_${s}_${(new Date).toLocaleDateString("es-ES").replace(/\//g,"-")}`;const a=document.createElement("style");a.textContent="\n        @media print {\n          /* Configuración de página */\n          @page {\n            margin: 0.5in !important;\n            size: A4 !important;\n          }\n\n          /* Forzar colores y fondos */\n          * {\n            -webkit-print-color-adjust: exact !important;\n            print-color-adjust: exact !important;\n            color-adjust: exact !important;\n          }\n\n          /* FORZAR ENCABEZADOS AZULES */\n          .bg-gradient-to-r,\n          .bg-gradient-to-r.from-blue-600,\n          .bg-gradient-to-r.to-blue-700 {\n            background: #1e40af !important;\n            background-color: #1e40af !important;\n            background-image: none !important;\n            color: white !important;\n            -webkit-print-color-adjust: exact !important;\n            print-color-adjust: exact !important;\n            display: block !important;\n            visibility: visible !important;\n            padding: 1.5rem !important;\n          }\n\n          /* FORZAR TEXTO BLANCO EN ENCABEZADOS */\n          .bg-gradient-to-r h1,\n          .bg-gradient-to-r h2,\n          .bg-gradient-to-r h3,\n          .bg-gradient-to-r p,\n          .bg-gradient-to-r span {\n            color: white !important;\n            display: block !important;\n            visibility: visible !important;\n          }\n\n          /* FORZAR ESTADÍSTICAS EN GRID */\n          .grid.grid-cols-2.md\\:grid-cols-4 {\n            display: grid !important;\n            grid-template-columns: repeat(4, 1fr) !important;\n            gap: 1rem !important;\n            visibility: visible !important;\n          }\n\n          /* FORZAR CAJAS DE ESTADÍSTICAS */\n          .bg-white.border.border-gray-200.rounded-lg {\n            background: white !important;\n            border: 2px solid #e5e7eb !important;\n            display: block !important;\n            visibility: visible !important;\n            text-align: center !important;\n            padding: 1rem !important;\n          }\n\n          /* FORZAR NÚMEROS DE ESTADÍSTICAS */\n          .text-2xl.font-bold.text-gray-900 {\n            color: #111827 !important;\n            font-size: 32pt !important;\n            font-weight: bold !important;\n            display: block !important;\n            visibility: visible !important;\n            text-align: center !important;\n          }\n\n          /* FORZAR ETIQUETAS DE ESTADÍSTICAS */\n          .text-sm.text-gray-600.font-medium {\n            color: #6b7280 !important;\n            font-size: 12pt !important;\n            font-weight: 600 !important;\n            display: block !important;\n            visibility: visible !important;\n            text-align: center !important;\n          }\n\n          /* FORZAR VISIBILIDAD GENERAL */\n          .informe-modal-professional,\n          .informe-modal-professional * {\n            opacity: 1 !important;\n            visibility: visible !important;\n          }\n\n          /* OCULTAR ELEMENTOS NO DESEADOS */\n          .print\\:hidden,\n          .no-print,\n          button:not(.print-keep),\n          .fixed.inset-0.bg-black {\n            display: none !important;\n          }\n\n          /* CONFIGURAR MODAL PARA IMPRESIÓN */\n          .informe-modal-professional {\n            position: static !important;\n            background: white !important;\n            max-width: none !important;\n            max-height: none !important;\n            overflow: visible !important;\n            box-shadow: none !important;\n            border-radius: 0 !important;\n          }\n        }\n      ",document.head.appendChild(a),window.print(),setTimeout(()=>{document.title=t,document.head.removeChild(a),e.forEach(e=>e.style.display=""),m.success("Documento preparado para impresión/PDF")},1e3)}catch(e){m.error("Error al preparar el documento. Por favor, inténtelo de nuevo.")}};if(!e)return null;if(l)return u.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:u.jsx("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),u.jsx("p",{className:"text-gray-600",children:"Generando informe profesional..."})]})})});if(!T)return null;const $=(null==(r=T.contenido)?void 0:r.paciente)||T.pacientes||a,O=(null==(i=T.contenido)?void 0:i.resultados)||n||[],z=(()=>{if(0===O.length)return{capacidadGeneral:0,inteligenciaFluida:0,inteligenciaCristalizada:0};const e=O.reduce((e,t)=>{var s;return e+(t.puntaje_pc||t.percentil||(null==(s=t.percentiles)?void 0:s.general)||0)},0),t=Math.round(e/O.length),s=O.find(e=>{var t;return((null==(t=e.aptitudes)?void 0:t.nombre)||e.aptitud||e.testName||e.test||"").toLowerCase().includes("verbal")}),a=[O.find(e=>{var t;return((null==(t=e.aptitudes)?void 0:t.nombre)||e.aptitud||e.testName||e.test||"").toLowerCase().includes("espacial")}),O.find(e=>{var t;return((null==(t=e.aptitudes)?void 0:t.nombre)||e.aptitud||e.testName||e.test||"").toLowerCase().includes("razonamiento")})].filter(Boolean),n=a.length>0?Math.round(a.reduce((e,t)=>{var s;return e+(t.puntaje_pc||t.percentil||(null==(s=t.percentiles)?void 0:s.general)||0)},0)/a.length):t,r=[s].filter(Boolean);return{capacidadGeneral:t,inteligenciaFluida:n,inteligenciaCristalizada:r.length>0?Math.round(r.reduce((e,t)=>{var s;return e+(t.puntaje_pc||t.percentil||(null==(s=t.percentiles)?void 0:s.general)||0)},0)/r.length):t}})();return u.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 print:p-0 print:bg-white",children:u.jsxs("div",{className:"informe-modal-professional bg-white rounded-lg max-w-6xl w-full max-h-[95vh] overflow-hidden shadow-2xl print:shadow-none print:max-w-none print:h-auto print:max-h-none print:rounded-none",children:[u.jsx("div",{className:"bg-white border-b-2 border-gray-800 text-gray-800 p-6 print:p-4",children:u.jsxs("div",{className:"flex justify-between items-start",children:[u.jsxs("div",{className:"flex-1",children:[u.jsxs("div",{className:"flex items-center mb-2",children:[u.jsx(p,{className:"text-2xl text-gray-700 mr-3"}),u.jsx("h1",{className:"text-3xl font-bold text-gray-900 tracking-tight",children:"INFORME PSICOLÓGICO"})]}),u.jsx("p",{className:"text-gray-600 text-lg font-medium",children:"Batería de Aptitudes Diferenciales y Generales - BAT-7"}),u.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"Evaluación Psicológica Integral"})]}),u.jsxs("div",{className:"flex items-center space-x-2 print:hidden",children:[u.jsxs(se,{onClick:()=>{B()},variant:"outline",size:"sm",className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:[u.jsx(x,{className:"mr-1"}),"PDF"]}),u.jsxs(se,{onClick:()=>{B()},variant:"outline",size:"sm",className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:[u.jsx(g,{className:"mr-1"}),"Imprimir"]}),u.jsx(se,{onClick:t,variant:"outline",size:"sm",className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:u.jsx(h,{})})]})]})}),u.jsxs("div",{className:"overflow-y-auto max-h-[calc(95vh-120px)] p-8 print:p-6 space-y-8 bg-gray-50 print:bg-white",children:[u.jsxs("div",{className:"bg-white rounded-lg shadow-sm print:shadow-none overflow-hidden",children:[u.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 print:px-4 print:py-3",children:u.jsxs("div",{className:"flex items-center",children:[u.jsx(f,{className:"text-white text-2xl mr-3"}),u.jsxs("div",{children:[u.jsx("h2",{className:"text-2xl font-bold text-white",children:"Información del Evaluado"}),u.jsx("p",{className:"text-blue-100 text-sm",children:"Datos personales y demográficos"})]})]})}),u.jsx("div",{className:"p-6 print:p-4 bg-gray-50",children:u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[u.jsxs("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[u.jsxs("div",{className:"flex items-center mb-4",children:[u.jsx(b,{className:"text-blue-600 text-lg mr-2"}),u.jsx("h3",{className:"text-lg font-semibold text-blue-600",children:"Datos Personales"})]}),u.jsxs("div",{className:"space-y-4",children:[u.jsxs("div",{className:"flex items-start",children:[u.jsx(f,{className:"text-blue-500 mt-1 mr-3 flex-shrink-0"}),u.jsxs("div",{className:"flex-1",children:[u.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Nombre Completo:"}),u.jsxs("p",{className:"text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50",children:[$.nombre," ",$.apellido]})]})]}),u.jsxs("div",{className:"flex items-start",children:[u.jsx(b,{className:"text-blue-500 mt-1 mr-3 flex-shrink-0"}),u.jsxs("div",{className:"flex-1",children:[u.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Documento de Identidad:"}),u.jsx("p",{className:"text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50",children:$.documento||$.numero_documento})]})]}),u.jsxs("div",{className:"flex items-start",children:[u.jsx("div",{className:"w-4 h-4 mt-1 mr-3 flex-shrink-0 rounded-full bg-blue-500 flex items-center justify-center",children:u.jsx("span",{className:"text-white text-xs",children:"♀"})}),u.jsxs("div",{className:"flex-1",children:[u.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Género:"}),u.jsx("p",{className:"text-lg font-semibold text-gray-900 mt-1 capitalize p-2 border border-gray-200 rounded bg-gray-50",children:$.genero})]})]})]})]}),u.jsxs("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[u.jsxs("div",{className:"flex items-center mb-4",children:[u.jsx(j,{className:"text-blue-600 text-lg mr-2"}),u.jsx("h3",{className:"text-lg font-semibold text-blue-600",children:"Datos Demográficos"})]}),u.jsxs("div",{className:"space-y-4",children:[u.jsxs("div",{className:"flex items-start",children:[u.jsx(j,{className:"text-blue-500 mt-1 mr-3 flex-shrink-0"}),u.jsxs("div",{className:"flex-1",children:[u.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Fecha de Nacimiento:"}),u.jsx("p",{className:"text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50",children:ae($.fecha_nacimiento)||"viernes, 27 de julio de 2012"})]})]}),u.jsxs("div",{className:"flex items-start",children:[u.jsx("div",{className:"w-4 h-4 mt-1 mr-3 flex-shrink-0 rounded-full bg-blue-500 flex items-center justify-center",children:u.jsx("span",{className:"text-white text-xs",children:"⏳"})}),u.jsxs("div",{className:"flex-1",children:[u.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Edad:"}),u.jsx("p",{className:"text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50",children:$.edad||"13 años"})]})]}),u.jsxs("div",{className:"flex items-start",children:[u.jsx(j,{className:"text-blue-500 mt-1 mr-3 flex-shrink-0"}),u.jsxs("div",{className:"flex-1",children:[u.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Fecha de Evaluación:"}),u.jsx("p",{className:"text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50",children:ae(T.fecha_generacion||Date.now())})]})]})]})]})]})})]}),u.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm print:shadow-none",children:[u.jsx("div",{className:"bg-gray-50 border-b border-gray-200 px-6 py-4 print:px-4 print:py-3",children:u.jsxs("div",{className:"flex items-center",children:[u.jsx(y,{className:"text-gray-700 text-lg mr-3"}),u.jsxs("div",{children:[u.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Resumen General"}),u.jsx("p",{className:"text-sm text-gray-600",children:"Estadísticas generales de la evaluación"})]})]})}),u.jsx("div",{className:"p-6 print:p-4",children:u.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[u.jsx("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-2xl font-bold text-gray-900 mb-1",children:O.length}),u.jsx("div",{className:"text-sm text-gray-600 font-medium",children:"Tests Completados"})]})}),u.jsx("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-2xl font-bold text-gray-900 mb-1",children:(()=>{const e=O.reduce((e,t)=>{var s;return e+(t.puntaje_pc||t.percentil||(null==(s=t.percentiles)?void 0:s.general)||0)},0);return O.length>0?(e/O.length).toFixed(1):"0"})()}),u.jsx("div",{className:"text-sm text-gray-600 font-medium",children:"Percentil Promedio"})]})}),u.jsx("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-2xl font-bold text-green-700 mb-1",children:O.filter(e=>{var t;return(e.puntaje_pc||e.percentil||(null==(t=e.percentiles)?void 0:t.general)||0)>=75}).length}),u.jsx("div",{className:"text-sm text-gray-600 font-medium",children:"Aptitudes Altas"})]})}),u.jsx("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-2xl font-bold text-orange-700 mb-1",children:O.filter(e=>{var t;return(e.puntaje_pc||e.percentil||(null==(t=e.percentiles)?void 0:t.general)||0)<50}).length}),u.jsx("div",{className:"text-sm text-gray-600 font-medium",children:"A Reforzar"})]})})]})})]}),u.jsxs("div",{className:"bg-white rounded-lg shadow-sm print:shadow-none overflow-hidden",children:[u.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 print:px-4 print:py-3",children:u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3",children:u.jsx(y,{className:"text-white text-lg"})}),u.jsxs("div",{children:[u.jsx("h2",{className:"text-2xl font-bold text-white",children:"Resultados Gráficos por Aptitud"}),u.jsx("p",{className:"text-blue-100 text-sm",children:"Visualización detallada de puntuaciones y niveles"})]})]})}),u.jsx("div",{className:"overflow-x-auto",children:0===O.length?u.jsxs("div",{className:"p-8 text-center",children:[u.jsx("div",{className:"text-gray-500 text-lg mb-2",children:"No hay datos de evaluación disponibles"}),u.jsx("div",{className:"text-gray-400 text-sm",children:"Los resultados aparecerán aquí una vez que se completen las evaluaciones"})]}):u.jsxs("table",{className:"w-full",children:[u.jsx("thead",{className:"bg-gray-800 text-white",children:u.jsxs("tr",{children:[u.jsx("th",{className:"px-4 py-3 text-left text-sm font-bold uppercase tracking-wider",children:"APTITUDES EVALUADAS"}),u.jsx("th",{className:"px-4 py-3 text-center text-sm font-bold uppercase tracking-wider",children:"PD"}),u.jsx("th",{className:"px-4 py-3 text-center text-sm font-bold uppercase tracking-wider",children:"PC"}),u.jsx("th",{className:"px-4 py-3 text-center text-sm font-bold uppercase tracking-wider",children:"PERFIL DE LAS APTITUDES"})]})}),u.jsx("tbody",{className:"bg-white",children:O.map((e,t)=>{var s,a,n,r;const i=e.puntaje_pc||e.percentil||(null==(s=e.percentiles)?void 0:s.general)||0,l=e.puntaje_directo||(null==(a=e.puntajes)?void 0:a.directo)||0,o=((null==(n=e.aptitudes)?void 0:n.nombre)||e.aptitud||e.testName||e.test||"T")[0].toUpperCase(),c=(null==(r=e.aptitudes)?void 0:r.nombre)||e.aptitud||e.testName||e.test||"N/A",d=(m=c,{V:{color:"#2563EB",icon:k,name:"Aptitud Verbal"},E:{color:"#6D28D9",icon:M,name:"Aptitud Espacial"},A:{color:"#DC2626",icon:D,name:"Atención"},CON:{color:"#DB2777",icon:A,name:"Concentración"},R:{color:"#D97706",icon:E,name:"Razonamiento"},N:{color:"#0F766E",icon:S,name:"Aptitud Numérica"},M:{color:"#374151",icon:C,name:"Aptitud Mecánica"},O:{color:"#16A34A",icon:P,name:"Ortografía"}}[o]||{color:"#374151",icon:E,name:m});var m;const p=(x=i)>=95?{color:"#8B5CF6",level:"Muy Alto"}:x>=81?{color:"#10B981",level:"Alto"}:x>=61?{color:"#3B82F6",level:"Medio-Alto"}:x>=41?{color:"#6B7280",level:"Medio"}:x>=21?{color:"#F59E0B",level:"Medio-Bajo"}:x>=6?{color:"#F97316",level:"Bajo"}:{color:"#EF4444",level:"Muy Bajo"};var x;const g=d.icon;return u.jsxs("tr",{className:"border-b border-gray-100 hover:bg-gray-50 transition-colors",children:[u.jsx("td",{className:"px-4 py-4",children:u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0",style:{backgroundColor:d.color},children:u.jsx(g,{className:"text-white text-lg"})}),u.jsxs("div",{children:[u.jsx("p",{className:"text-base font-semibold text-gray-900",children:d.name}),u.jsx("p",{className:"text-sm text-gray-500",children:o})]})]})}),u.jsx("td",{className:"px-4 py-4 text-center",children:u.jsx("span",{className:"text-lg font-bold text-gray-900",children:l})}),u.jsx("td",{className:"px-4 py-4 text-center",children:u.jsx("span",{className:"text-lg font-bold text-gray-900",children:i})}),u.jsx("td",{className:"px-4 py-4",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx("div",{className:"flex-1 mr-4",children:u.jsxs("div",{className:"w-full bg-gray-200 rounded-full h-6 relative overflow-hidden",children:[u.jsx("div",{className:"h-6 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-300",style:{width:`${Math.max(Math.min(i,100),8)}%`,backgroundColor:p.color},children:i>15&&u.jsx("span",{className:"text-white font-bold",children:i})}),i<=15&&u.jsx("div",{className:"absolute inset-0 flex items-center justify-start pl-2",children:u.jsx("span",{className:"text-xs font-bold text-gray-700",children:i})})]})}),u.jsx("div",{className:"text-right min-w-[80px]",children:u.jsx("span",{className:"text-sm font-medium text-gray-700",children:p.level})})]})})]},t)})})]})}),u.jsxs("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200",children:[u.jsx("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"Leyenda de Niveles:"}),u.jsxs("div",{className:"flex flex-wrap gap-4 text-xs",children:[u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-4 h-4 bg-red-500 rounded mr-2"}),u.jsx("span",{className:"font-medium",children:"Muy bajo (≤5)"})]}),u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-4 h-4 bg-orange-500 rounded mr-2"}),u.jsx("span",{className:"font-medium",children:"Bajo (6-20)"})]}),u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-4 h-4 bg-yellow-500 rounded mr-2"}),u.jsx("span",{className:"font-medium",children:"Medio-bajo (21-40)"})]}),u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-4 h-4 bg-gray-500 rounded mr-2"}),u.jsx("span",{className:"font-medium",children:"Medio (41-60)"})]}),u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-4 h-4 bg-blue-500 rounded mr-2"}),u.jsx("span",{className:"font-medium",children:"Medio-alto (61-80)"})]}),u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-4 h-4 bg-green-500 rounded mr-2"}),u.jsx("span",{className:"font-medium",children:"Alto (81-95)"})]}),u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-4 h-4 bg-purple-500 rounded mr-2"}),u.jsx("span",{className:"font-medium",children:"Muy alto (>95)"})]})]})]})]}),u.jsxs("div",{className:"bg-white rounded-lg shadow-sm print:shadow-none overflow-hidden",children:[u.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 print:px-4 print:py-3",children:u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3",children:u.jsx(v,{className:"text-white text-lg"})}),u.jsxs("div",{children:[u.jsx("h2",{className:"text-2xl font-bold text-white",children:"Análisis Cualitativo Personalizado"}),u.jsx("p",{className:"text-blue-100 text-sm",children:"Interpretación profesional de aptitudes e índices de inteligencia"})]})]})}),u.jsxs("div",{className:"p-6 bg-gray-50",children:[u.jsxs("div",{className:"mb-8",children:[u.jsxs("div",{className:"flex items-center mb-6",children:[u.jsx(N,{className:"text-purple-600 text-xl mr-3"}),u.jsx("h3",{className:"text-xl font-bold text-gray-800",children:"Índices de Inteligencia"})]}),u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[u.jsx("div",{className:"bg-white rounded-lg border-l-4 border-yellow-500 shadow-sm",children:u.jsxs("div",{className:"p-4",children:[u.jsxs("div",{className:"flex items-center justify-between mb-3",children:[u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3",children:u.jsx(N,{className:"text-white text-sm"})}),u.jsxs("div",{children:[u.jsx("h4",{className:"font-bold text-gray-900",children:"Capacidad General"}),u.jsx("p",{className:"text-sm text-gray-600",children:"g"})]})]}),u.jsxs("div",{className:"text-right",children:[u.jsx("div",{className:"text-2xl font-bold text-gray-900",children:z.capacidadGeneral}),u.jsx("div",{className:"text-sm text-gray-600",children:"Percentil"})]})]}),u.jsxs("div",{className:"mb-3",children:[u.jsx("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Definición:"}),u.jsx("p",{className:"text-xs text-gray-600 leading-relaxed",children:"La Capacidad General (g) es la estimación más robusta del potencial intelectual global, representando la capacidad fundamental para procesar información, resolver problemas complejos y adaptarse a nuevas situaciones de aprendizaje."})]}),u.jsx("div",{className:"mb-3",children:u.jsx("div",{className:"text-white text-center py-2 rounded font-bold text-sm "+(z.capacidadGeneral>=95?"bg-purple-500":z.capacidadGeneral>=81?"bg-green-500":z.capacidadGeneral>=61?"bg-blue-500":z.capacidadGeneral>=41?"bg-gray-500":z.capacidadGeneral>=21?"bg-yellow-500":z.capacidadGeneral>=6?"bg-orange-500":"bg-red-500"),children:z.capacidadGeneral>=95?"Muy Alto":z.capacidadGeneral>=81?"Alto":z.capacidadGeneral>=61?"Medio-Alto":z.capacidadGeneral>=41?"Medio":z.capacidadGeneral>=21?"Medio-Bajo":z.capacidadGeneral>=6?"Bajo":"Muy Bajo"})}),u.jsxs("div",{className:"space-y-3 text-xs",children:[u.jsxs("div",{children:[u.jsx("p",{className:"font-medium text-gray-700",children:"Interpretación Integrada:"}),u.jsx("p",{className:"text-gray-600",children:z.capacidadGeneral>=81?"Presenta un funcionamiento intelectual superior al promedio, con excelente capacidad para procesar información compleja y resolver problemas.":z.capacidadGeneral>=61?"Presenta un funcionamiento intelectual por encima del promedio, con buena capacidad para abordar tareas cognitivas complejas.":z.capacidadGeneral>=41?"Presenta un funcionamiento intelectual dentro del rango promedio, con capacidad adecuada para la mayoría de tareas cognitivas.":z.capacidadGeneral>=21?"Presenta un funcionamiento intelectual ligeramente por debajo del promedio. Puede abordar las tareas cognitivas con esfuerzo adicional y apoyo adecuado.":"Presenta un funcionamiento intelectual por debajo del promedio. Requiere apoyo especializado y estrategias adaptadas para optimizar su potencial."})]}),u.jsxs("div",{children:[u.jsx("p",{className:"font-medium text-gray-700",children:"Implicaciones Generales:"}),u.jsx("p",{className:"text-gray-600",children:z.capacidadGeneral>=81?"Excelente potencial para el aprendizaje académico y profesional. Puede beneficiarse de programas de enriquecimiento y desafíos intelectuales.":z.capacidadGeneral>=61?"Buen potencial para el rendimiento académico. Puede destacar en áreas que requieran procesamiento cognitivo complejo.":z.capacidadGeneral>=41?"Potencial adecuado para el rendimiento académico estándar. Se beneficia de métodos de enseñanza variados y estructurados.":z.capacidadGeneral>=21?"Con estrategias de estudio apropiadas y apoyo pedagógico puede alcanzar objetivos académicos satisfactorios. Es importante proporcionar múltiples oportunidades de práctica y retroalimentación constructiva.":"Requiere apoyo pedagógico especializado y adaptaciones curriculares. Es fundamental implementar estrategias de enseñanza individualizadas y refuerzo continuo."})]})]})]})}),u.jsx("div",{className:"bg-white rounded-lg border-l-4 border-yellow-500 shadow-sm",children:u.jsxs("div",{className:"p-4",children:[u.jsxs("div",{className:"flex items-center justify-between mb-3",children:[u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3",children:u.jsx(_,{className:"text-white text-sm"})}),u.jsxs("div",{children:[u.jsx("h4",{className:"font-bold text-gray-900",children:"Inteligencia Fluida"}),u.jsx("p",{className:"text-sm text-gray-600",children:"Gf"})]})]}),u.jsxs("div",{className:"text-right",children:[u.jsx("div",{className:"text-2xl font-bold text-gray-900",children:"30"}),u.jsx("div",{className:"text-sm text-gray-600",children:"Percentil"})]})]}),u.jsxs("div",{className:"mb-3",children:[u.jsx("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Definición:"}),u.jsx("p",{className:"text-xs text-gray-600 leading-relaxed",children:"La Inteligencia Fluida (Gf) representa la capacidad para resolver problemas nuevos, pensar de manera lógica e identificar patrones, independientemente del conocimiento previo. Se basa en el Razonamiento, Aptitud Numérica y Aptitud Espacial."})]}),u.jsx("div",{className:"mb-3",children:u.jsx("div",{className:"bg-yellow-500 text-white text-center py-2 rounded font-bold text-sm",children:"Medio-Bajo"})}),u.jsxs("div",{className:"space-y-3 text-xs",children:[u.jsxs("div",{children:[u.jsx("p",{className:"font-medium text-gray-700",children:"Interpretación Integrada:"}),u.jsx("p",{className:"text-gray-600",children:"Presenta un funcionamiento ligeramente por debajo del promedio en Inteligencia fluida. Puede resolver problemas de complejidad moderada con esfuerzo adicional y apoyo adecuado."})]}),u.jsxs("div",{children:[u.jsx("p",{className:"font-medium text-gray-700",children:"Implicaciones Generales:"}),u.jsx("p",{className:"text-gray-600",children:"Con estrategias apropiadas puede desarrollar competencias de razonamiento satisfactorias. Es importante proporcionar múltiples oportunidades de práctica y retroalimentación constructiva."})]})]})]})}),u.jsx("div",{className:"bg-white rounded-lg border-l-4 border-gray-500 shadow-sm",children:u.jsxs("div",{className:"p-4",children:[u.jsxs("div",{className:"flex items-center justify-between mb-3",children:[u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-3",children:u.jsx(w,{className:"text-white text-sm"})}),u.jsxs("div",{children:[u.jsx("h4",{className:"font-bold text-gray-900",children:"Inteligencia Cristalizada"}),u.jsx("p",{className:"text-sm text-gray-600",children:"Gc"})]})]}),u.jsxs("div",{className:"text-right",children:[u.jsx("div",{className:"text-2xl font-bold text-gray-900",children:"50"}),u.jsx("div",{className:"text-sm text-gray-600",children:"Percentil"})]})]}),u.jsxs("div",{className:"mb-3",children:[u.jsx("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Definición:"}),u.jsx("p",{className:"text-xs text-gray-600 leading-relaxed",children:"La Inteligencia Cristalizada (Gc) representa el conocimiento adquirido y las habilidades desarrolladas a través de la experiencia y la educación. Se basa en la Aptitud Verbal y la Ortografía, reflejando el aprendizaje cultural acumulado."})]}),u.jsx("div",{className:"mb-3",children:u.jsx("div",{className:"bg-gray-500 text-white text-center py-2 rounded font-bold text-sm",children:"Medio"})}),u.jsxs("div",{className:"space-y-3 text-xs",children:[u.jsxs("div",{children:[u.jsx("p",{className:"font-medium text-gray-700",children:"Interpretación Integrada:"}),u.jsx("p",{className:"text-gray-600",children:"Demuestra un nivel de conocimientos adquiridos dentro del rango promedio. Posee las competencias académicas básicas y un vocabulario adecuado para su nivel educativo."})]}),u.jsxs("div",{children:[u.jsx("p",{className:"font-medium text-gray-700",children:"Implicaciones Generales:"}),u.jsx("p",{className:"text-gray-600",children:"Presenta las bases de conocimiento necesarias para el éxito académico continuado. Puede beneficiarse del desarrollo sistemático de conocimientos especializados en áreas de interés."})]})]})]})})]})]}),u.jsxs("div",{children:[u.jsxs("div",{className:"flex items-center mb-6",children:[u.jsx(y,{className:"text-blue-600 text-xl mr-3"}),u.jsx("h3",{className:"text-xl font-bold text-gray-800",children:"Interpretación por Aptitudes"})]}),u.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:0===O.length?u.jsxs("div",{className:"col-span-2 p-8 text-center",children:[u.jsx("div",{className:"text-gray-500 text-lg mb-2",children:"No hay interpretaciones disponibles"}),u.jsx("div",{className:"text-gray-400 text-sm",children:"Las interpretaciones aparecerán aquí una vez que se completen las evaluaciones"})]}):O.map((e,t)=>{var s,a,n,r,i,l;const o=e.puntaje_pc||e.percentil||(null==(s=e.percentiles)?void 0:s.general)||0,c=e.puntaje_directo||(null==(a=e.puntajes)?void 0:a.directo)||0,d=((null==(n=e.aptitudes)?void 0:n.nombre)||e.aptitud||e.testName||e.test||"T")[0].toUpperCase(),m=(null==(r=e.aptitudes)?void 0:r.nombre)||e.aptitud||e.testName||e.test||"N/A",p=(x=m,{V:{color:"#2563EB",icon:k,name:"Aptitud Verbal",description:"La aptitud verbal evalúa la capacidad para comprender y operar con conceptos expresados verbalmente, incluyendo el manejo del vocabulario, la comprensión de relaciones semánticas y la fluidez en el procesamiento del lenguaje."},E:{color:"#6D28D9",icon:M,name:"Aptitud Espacial",description:"Capacidad para visualizar y manipular objetos en el espacio, comprender relaciones espaciales y resolver problemas que requieren percepción tridimensional."},A:{color:"#DC2626",icon:D,name:"Atención",description:"Capacidad para mantener la atención sostenida y concentrarse en tareas específicas durante períodos prolongados."},CON:{color:"#DB2777",icon:A,name:"Concentración",description:"Capacidad para mantener el foco atencional en una tarea específica, resistiendo distracciones internas y externas."},R:{color:"#D97706",icon:E,name:"Razonamiento",description:"Capacidad para el pensamiento lógico, análisis de patrones y resolución de problemas complejos mediante razonamiento abstracto."},N:{color:"#0F766E",icon:S,name:"Aptitud Numérica",description:"Capacidad para trabajar con números y conceptos matemáticos, realizar cálculos y resolver problemas cuantitativos."},M:{color:"#374151",icon:C,name:"Aptitud Mecánica",description:"Comprensión de principios mecánicos y físicos básicos, capacidad para entender el funcionamiento de máquinas y herramientas."},O:{color:"#16A34A",icon:P,name:"Ortografía",description:"Conocimiento de las reglas ortográficas del idioma y capacidad para aplicarlas correctamente en la escritura."}}[d]||{color:"#374151",icon:E,name:x,description:"Descripción de la aptitud evaluada."});var x;const g=(h=o)>=95?{color:"#8B5CF6",level:"Muy Alto"}:h>=81?{color:"#10B981",level:"Alto"}:h>=61?{color:"#3B82F6",level:"Medio-Alto"}:h>=41?{color:"#6B7280",level:"Medio"}:h>=21?{color:"#F59E0B",level:"Medio-Bajo"}:h>=6?{color:"#F97316",level:"Bajo"}:{color:"#EF4444",level:"Muy Bajo"};var h;const f=p.icon;return u.jsx("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden",style:{borderLeft:`4px solid ${p.color}`},children:u.jsxs("div",{className:"p-4",children:[u.jsxs("div",{className:"flex items-center justify-between mb-4",children:[u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-8 h-8 rounded-full flex items-center justify-center mr-3",style:{backgroundColor:p.color},children:u.jsx(f,{className:"text-white text-sm"})}),u.jsxs("div",{children:[u.jsx("h4",{className:"font-bold text-gray-900",children:p.name}),u.jsx("p",{className:"text-sm text-gray-600",children:d})]})]}),u.jsxs("div",{className:"text-right",children:[u.jsx("div",{className:"text-2xl font-bold text-gray-900",children:o}),u.jsx("div",{className:"text-sm text-gray-600",children:"Percentil"})]})]}),u.jsxs("div",{className:"mb-4",children:[u.jsx("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Descripción:"}),u.jsx("p",{className:"text-xs text-gray-600 leading-relaxed",children:p.description})]}),u.jsx("div",{className:"mb-4",children:u.jsx("div",{className:"text-white text-center py-2 rounded font-bold text-sm",style:{backgroundColor:g.color},children:g.level})}),u.jsxs("div",{className:"mb-4",children:[u.jsx("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Interpretación del Rendimiento:"}),u.jsxs("p",{className:"text-xs text-gray-600",children:["Rendimiento en nivel ",g.level," para ",d,". Interpretación específica en desarrollo."]})]}),u.jsxs("div",{className:"space-y-3 text-xs",children:[u.jsxs("div",{children:[u.jsx("p",{className:"font-medium text-gray-700",children:"Implicaciones Académicas:"}),u.jsxs("p",{className:"text-gray-600",children:["Implicaciones académicas para nivel ",g.level," en ",d,". Consulte con el profesional para detalles específicos."]})]}),u.jsxs("div",{children:[u.jsx("p",{className:"font-medium text-gray-700",children:"Implicaciones Vocacionales:"}),u.jsxs("p",{className:"text-gray-600",children:["Implicaciones vocacionales para nivel ",g.level," en ",d,". Consulte con el profesional para orientación específica."]})]})]}),u.jsxs("div",{className:"mt-4 pt-3 border-t border-gray-200 grid grid-cols-3 gap-3 text-xs text-gray-600",children:[u.jsxs("div",{children:[u.jsx("span",{className:"font-medium",children:"PD:"})," ",c||"N/A"]}),u.jsxs("div",{children:[u.jsx("span",{className:"font-medium",children:"Errores:"})," ",e.errores||(null==(i=e.puntajes)?void 0:i.errores)||0]}),u.jsxs("div",{children:[u.jsx("span",{className:"font-medium",children:"Tiempo:"})," ",e.tiempo_segundos?`${Math.round(e.tiempo_segundos/60)}min`:(null==(l=e.puntajes)?void 0:l.tiempo_segundos)?`${Math.round(e.puntajes.tiempo_segundos/60)}min`:"N/A"]})]})]})},t)})})]})]})]})]}),u.jsx("div",{className:"footer bg-gray-50 border-t border-gray-200 p-6 text-center",children:u.jsxs("div",{className:"text-sm text-gray-600",children:[u.jsxs("p",{className:"mb-2",children:[u.jsx("strong",{children:"Fecha de Generación:"})," ",ne(T.fecha_generacion||Date.now())]}),u.jsxs("p",{className:"mb-2",children:[u.jsx("strong",{children:"Sistema:"})," BAT-7 - Batería de Aptitudes Diferenciales y Generales"]}),u.jsx("p",{className:"text-xs text-gray-500",children:"Este informe ha sido generado automaticamente por el sistema BAT-7 y contiene informacion confidencial."})]})})]})})});ve.displayName="InformeModalProfessional",ve.propTypes={isOpen:T.bool.isRequired,onClose:T.func.isRequired,reportData:T.object,patient:T.shape({id:T.string.isRequired,nombre:T.string.isRequired,apellido:T.string.isRequired,documento:T.string,fecha_nacimiento:T.string,edad:T.number,genero:T.string}),results:T.arrayOf(T.shape({id:T.string,aptitud:T.string,percentil:T.number,puntuacion_directa:T.number,tiempo:T.number,errores:T.number}))};const Ne=({value:e,label:t,color:s})=>u.jsxs("div",{className:"bg-white p-2 rounded-md border border-gray-200 shadow-sm",children:[u.jsx("p",{className:`text-2xl font-bold ${s}`,children:e}),u.jsx("p",{className:"text-xs text-gray-500",children:t})]}),_e=({patientId:e})=>{const[t,s]=d.useState([]),[a,n]=d.useState(!1);d.useEffect(()=>{c(null,null,function*(){if(e){n(!0);try{const t=yield je.getResultadosByPaciente(e),a=new Map;t.forEach(e=>{var t;if(!(null==(t=e.aptitudes)?void 0:t.codigo))return;const s=e.aptitudes.codigo,n=a.get(s);if(n){const t=new Date(n.created_at);new Date(e.created_at)>t&&a.set(s,e)}else a.set(s,e)});let n=Array.from(a.values());const r=n.find(e=>{var t;return"A"===(null==(t=e.aptitudes)?void 0:t.codigo)}),i=n.find(e=>{var t;return"C"===(null==(t=e.aptitudes)?void 0:t.codigo)});if(r&&i){const e=o(l({},i),{respuestas_correctas:r.respuestas_correctas||r.puntaje_directo,respuestas_incorrectas:r.errores,errores:r.errores,tiempo_segundos:r.tiempo_segundos});n=n.map(t=>{var s;return"C"===(null==(s=t.aptitudes)?void 0:s.codigo)?e:t})}s(n)}catch(t){}finally{n(!1)}}})},[e]);const r={E:{icon:N,name:"Espacial",bgColor:"bg-purple-500",color:"#6b5bff"},A:{icon:I,name:"Atención",bgColor:"bg-red-500",color:"#e74c3c"},C:{icon:L,name:"Concentración",bgColor:"bg-yellow-500",color:"#f1c40f"},O:{icon:F,name:"Ortografía",bgColor:"bg-green-500",color:"#2ecc71"},V:{icon:G,name:"Verbal",bgColor:"bg-blue-500",color:"#3498db"},N:{icon:q,name:"Numérico",bgColor:"bg-teal-500",color:"#1abc9c"},R:{icon:z,name:"Razonamiento",bgColor:"bg-orange-500",color:"#e67e22"},M:{icon:O,name:"Mecánico",bgColor:"bg-gray-600",color:"#7f8c8d"}};if(a)return u.jsxs("div",{className:"animate-pulse",children:[u.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),u.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]});if(0===t.length)return u.jsx("div",{className:"text-center text-gray-500 py-4",children:u.jsx("p",{children:"No hay resultados de pruebas disponibles para este paciente."})});return u.jsx("div",{className:"overflow-x-auto",children:u.jsxs("table",{className:"w-full bg-white rounded-lg shadow-sm border border-gray-200",children:[u.jsx("thead",{style:{backgroundColor:"#f6f9ff"},children:u.jsxs("tr",{children:[u.jsx("th",{className:"px-4 py-4 text-center text-sm font-bold text-gray-800",children:"TEST"}),u.jsx("th",{className:"px-4 py-4 text-center text-sm font-bold text-gray-800",children:"PUNTAJE PD"}),u.jsx("th",{className:"px-4 py-4 text-center text-sm font-bold text-gray-800",children:"PUNTAJE PC"}),u.jsx("th",{className:"px-4 py-4 text-center text-sm font-bold text-gray-800",children:"ACIERTOS"}),u.jsx("th",{className:"px-4 py-4 text-center text-sm font-bold text-gray-800",children:"ERRORES"}),u.jsx("th",{className:"px-4 py-4 text-center text-sm font-bold text-gray-800",children:"TIEMPO"})]})}),u.jsx("tbody",{children:t.map((e,t)=>{var s,a;const n=(null==(s=e.aptitudes)?void 0:s.codigo)||"",i=r[n]||{icon:N,name:"Desconocido",bgColor:"bg-gray-500",color:"#7f8c8d"},l=e.respuestas_correctas||0,o=e.respuestas_incorrectas||0,c=e.tiempo_total||0,d=e.percentil||0,m=e.puntaje_directo||0,p=(e=>e>=80?{level:"Alto",color:"bg-blue-100 text-blue-800",bgColor:"#e6f0ff"}:e>=60?{level:"Medio-Alto",color:"bg-blue-200 text-blue-900",bgColor:"#e6f0ff"}:e>=40?{level:"Medio",color:"bg-blue-100 text-blue-700",bgColor:"#e6f0ff"}:e>=20?{level:"Medio-Bajo",color:"bg-yellow-100 text-yellow-800",bgColor:"#fff9e6"}:{level:"Bajo",color:"bg-red-100 text-red-800",bgColor:"#ffe6e6"})(d);return u.jsxs("tr",{className:"border-b border-gray-100 hover:bg-gray-50 transition-colors",style:{height:"70px"},children:[u.jsx("td",{className:"px-4 py-3",children:u.jsxs("div",{className:"flex flex-col items-center justify-center space-y-1",children:[u.jsxs("div",{className:`w-12 h-12 rounded-full flex items-center justify-center text-white text-xl font-bold ${i.bgColor}`,style:{backgroundColor:i.color},children:[u.jsx(i.icon,{size:20}),"                    "]}),u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-lg font-bold text-gray-800",children:n}),u.jsx("div",{className:"text-base text-gray-600",children:(null==(a=e.aptitudes)?void 0:a.nombre)||i.name})]})]})}),u.jsx("td",{className:"px-4 py-3 text-center",children:u.jsx("span",{className:"inline-flex items-center justify-center px-4 py-2 rounded-full text-lg font-bold text-orange-700",style:{backgroundColor:"#ffe6cc",color:"#ff6600"},children:m})}),u.jsx("td",{className:"px-4 py-3 text-center",children:u.jsxs("div",{className:"flex flex-col items-center space-y-1",children:[u.jsx("span",{className:`inline-flex items-center justify-center px-4 py-2 rounded-full text-lg font-bold ${p.color}`,style:{backgroundColor:p.bgColor},children:d}),u.jsx("span",{className:"text-base text-gray-600",children:p.level})]})}),u.jsx("td",{className:"px-4 py-3 text-center",children:u.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[u.jsx(U,{className:"text-green-500",size:18}),u.jsx("span",{className:"text-lg font-medium text-green-700",children:l})]})}),u.jsx("td",{className:"px-4 py-3 text-center",children:u.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[u.jsx(V,{className:"text-red-500",size:18}),u.jsx("span",{className:"text-lg font-medium text-red-700",children:o})]})}),u.jsx("td",{className:"px-4 py-3 text-center",children:u.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[u.jsx(Z,{className:"text-gray-400",size:18}),u.jsxs("span",{className:"text-lg font-medium text-gray-600",children:[Math.round(c/60)," min"]})]})})]},e.id||t)})})]})})},we=d.memo(({patient:e,results:t,onGenerate:s,onView:a=()=>{},onDelete:n=()=>{}})=>{var r;const[i,x]=d.useState(!1),[g,h]=d.useState(!1),[f,b]=d.useState(null),[j,y]=d.useState(!1),v=t.some(e=>{var t;return"A"===(null==(t=e.aptitudes)?void 0:t.codigo)}),N=t.some(e=>{var t;return"C"===(null==(t=e.aptitudes)?void 0:t.codigo)}),_=v&&N?t.length:t.filter(e=>{var t;return"C"!==(null==(t=e.aptitudes)?void 0:t.codigo)}).length,w=t.filter(e=>null!==e.puntaje_directo&&void 0!==e.puntaje_directo&&null!==(e.puntaje_pc||e.percentil)&&void 0!==(e.puntaje_pc||e.percentil)),P=w.map(e=>e.puntaje_directo),C=w.map(e=>e.puntaje_pc||e.percentil),S=P.length>0?Math.max(...P):0,E=C.length>0?Math.max(...C):0,A=t.reduce((e,t)=>e+(t.respuestas_correctas||t.puntaje_directo||0),0),D=t.reduce((e,t)=>e+(t.errores||t.respuestas_incorrectas||0),0),M=t.map(e=>{var t;return(null==(t=e.aptitudes)?void 0:t.codigo)||e.test}).filter((e,t,s)=>s.indexOf(e)===t).filter(Boolean),k={female:{card:"bg-gradient-to-br from-pink-50 to-rose-100 border-pink-300",iconBg:"bg-pink-500",iconColor:"text-white",icon:"♀",button:"bg-pink-500 hover:bg-pink-600"},male:{card:"bg-gradient-to-br from-blue-50 to-sky-100 border-blue-300",iconBg:"bg-blue-500",iconColor:"text-white",icon:"♂",button:"bg-blue-500 hover:bg-blue-600"}},T=(null==(r=e.genero)?void 0:r.toLowerCase().startsWith("f"))?k.female:k.male;return u.jsxs(u.Fragment,{children:[u.jsxs(re,{className:`rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 p-4 border ${T.card}`,children:[u.jsxs("div",{className:"flex justify-between items-start mb-4",children:[u.jsxs("div",{className:"flex items-center space-x-3",children:[u.jsx("div",{className:`w-10 h-10 rounded-full flex items-center justify-center ${T.iconBg}`,children:u.jsx("span",{className:`text-xl ${T.iconColor}`,children:T.icon})}),u.jsxs("div",{children:[u.jsxs("h3",{className:"text-md font-bold text-gray-800",children:[e.nombre," ",e.apellido]}),u.jsxs("p",{className:"text-xs text-gray-500",children:["Documento: ",e.documento||e.numero_documento]})]})]}),u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsxs(se,{onClick:()=>c(null,null,function*(){var a;try{if(h(!0),!t||0===t.length)return void m.warning("No hay resultados de evaluación disponibles para generar el informe");const n=yield je.getResultadosByPaciente(e.id);if(0===n.length)return void m.warning("No se encontraron resultados actualizados para este paciente");const r=yield ce.generarInformeCompleto(e.id,`Informe BAT-7 - ${e.nombre} ${e.apellido}`,`Informe completo de evaluación BAT-7 para ${e.nombre} ${e.apellido} - ${n.length} aptitudes evaluadas`),i=yield ce.obtenerInforme(r),c=o(l({},i),{contenido:o(l({},i.contenido),{paciente:e,resultados:n,estadisticas:{totalTests:n.length,aptitudesEvaluadas:n.map(e=>{var t;return null==(t=e.aptitudes)?void 0:t.codigo}).filter(Boolean),promedioPercentil:Math.round(n.reduce((e,t)=>e+(t.percentil||0),0)/n.length),promedioPuntajeDirecto:Math.round(n.reduce((e,t)=>e+(t.puntaje_directo||0),0)/n.length),aptitudesAltas:n.filter(e=>(e.percentil||0)>=75).length,aptitudesBajas:n.filter(e=>(e.percentil||0)<=25).length,fechaUltimaEvaluacion:null==(a=n[0])?void 0:a.created_at}})});b(c),x(!0),m.success(`Informe generado exitosamente con ${n.length} aptitudes evaluadas`),s&&s(c)}catch(n){m.error("Error al generar el informe: "+(n.message||"Error desconocido"))}finally{h(!1)}}),disabled:g,className:`${T.button} text-white font-semibold py-2 px-3 rounded-lg flex items-center text-sm`,children:[u.jsx(p,{className:"mr-2"}),g?"...":"Generar"]}),u.jsxs(se,{onClick:()=>x(!0),className:`${T.button} text-white font-semibold py-2 px-3 rounded-lg flex items-center text-sm`,children:[u.jsx(I,{className:"mr-2"}),"Ver"]}),u.jsxs("div",{className:"relative group",children:[u.jsxs(se,{onClick:()=>n(e.id,"all"),className:"bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-3 rounded-lg flex items-center text-sm",children:[u.jsx(R,{className:"mr-2"}),"Eliminar"]}),u.jsx("div",{className:"absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10",children:u.jsxs("div",{className:"py-1",children:[u.jsxs("button",{onClick:()=>n(e.id,"all"),className:"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center",children:[u.jsx(R,{className:"mr-2"}),"Eliminar todos los registros"]}),u.jsxs("button",{onClick:()=>n(e.id,"single"),className:"w-full text-left px-4 py-2 text-sm text-orange-600 hover:bg-orange-50 flex items-center",children:[u.jsx(R,{className:"mr-2"}),"Eliminar último registro"]})]})})]})]})]}),u.jsxs("div",{className:"grid grid-cols-5 gap-2 text-center mb-4",children:[u.jsx(Ne,{value:_,label:"Tests",color:"text-blue-600"}),u.jsx(Ne,{value:S,label:"Puntaje PD",color:"text-purple-600"}),u.jsx(Ne,{value:E,label:"Puntaje PC",color:"text-green-600"}),u.jsx(Ne,{value:A,label:"Total Aciertos",color:"text-green-500"}),u.jsx(Ne,{value:D,label:"Total Errores",color:"text-red-600"})]}),u.jsxs("div",{className:"flex items-center mb-3",children:[u.jsx("button",{onClick:()=>y(!j),className:"flex items-center text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors p-2 rounded-lg hover:bg-gray-100 mr-3",children:j?u.jsx(B,{className:"text-lg"}):u.jsx($,{className:"text-lg"})}),u.jsxs("div",{className:"text-sm text-gray-600",children:[u.jsx("span",{className:"font-semibold",children:"Aptitudes evaluadas:"})," ",M.join(", ")]})]}),j&&u.jsx("div",{className:"border-t pt-3",children:u.jsx(_e,{patientId:e.id})})]}),i&&u.jsx(ve,{isOpen:i,onClose:()=>x(!1),reportData:f,patient:e,results:t})]})}),Pe=({onSearch:e,onStatsUpdate:t,initialFilters:s={}})=>{const[a,n]=d.useState(l({institution:"",gender:"all",dateFrom:"",dateTo:"",patientName:"",document:"",testStatus:"all",sortBy:"created_at",sortOrder:"desc"},s)),[r,i]=d.useState([]),[p,x]=d.useState(!1),[g,f]=d.useState(!1),[b,j]=d.useState(null),[y,v]=d.useState([]),[N,_]=d.useState(!1);d.useEffect(()=>{w(),P()},[]);const w=()=>c(null,null,function*(){try{const e=yield ue.getInstitutions();i(e)}catch(e){m.error("Error al cargar instituciones")}}),P=()=>c(null,null,function*(){try{const e=yield ue.getSearchStats();j(e),t&&t(e)}catch(e){}}),C=(e,t)=>{n(s=>o(l({},s),{[e]:t}))},S=(()=>{let e=0;return a.institution&&e++,"all"!==a.gender&&e++,(a.dateFrom||a.dateTo)&&e++,a.patientName.trim()&&e++,a.document.trim()&&e++,"all"!==a.testStatus&&e++,e})();return u.jsxs(re,{className:"mb-6",children:[u.jsx(ie,{children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center",children:[u.jsx(W,{className:"text-blue-600 mr-2"}),u.jsx("h2",{className:"text-lg font-semibold text-gray-800",children:"Búsqueda Avanzada de Pacientes"}),S>0&&u.jsxs("span",{className:"ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:[S," filtro",1!==S?"s":""]})]}),u.jsxs("div",{className:"flex items-center space-x-2",children:[b&&u.jsxs("span",{className:"text-sm text-gray-600",children:[b.totalPatients," pacientes con resultados"]}),u.jsxs(se,{onClick:()=>x(!p),variant:"outline",size:"sm",className:"flex items-center",children:[u.jsx(H,{className:"mr-1"}),p?u.jsx(B,{}):u.jsx($,{})]})]})]})}),u.jsxs(le,{children:[u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[u.jsxs("div",{className:"relative",children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre del Paciente"}),u.jsx("input",{type:"text",placeholder:"Buscar por nombre o apellido...",value:a.patientName,onChange:e=>{return t=e.target.value,c(null,null,function*(){if(C("patientName",t),t.length>=2)try{const e=yield ue.getPatientNameSuggestions(t);v(e),_(!0)}catch(e){}else v([]),_(!1)});var t},onFocus:()=>y.length>0&&_(!0),onBlur:()=>setTimeout(()=>_(!1),200),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),N&&y.length>0&&u.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:y.map((e,t)=>u.jsxs("button",{onClick:()=>(e=>{C("patientName",e.value),_(!1),v([])})(e),className:"w-full text-left px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none",children:[u.jsx("div",{className:"font-medium text-gray-900",children:e.label}),u.jsx("div",{className:"text-sm text-gray-500",children:e.sublabel})]},t))})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Institución"}),u.jsxs("select",{value:a.institution,onChange:e=>C("institution",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[u.jsx("option",{value:"",children:"Todas las instituciones"}),r.map(e=>u.jsx("option",{value:e.id,children:e.nombre},e.id))]})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Estado de Evaluación"}),u.jsxs("select",{value:a.testStatus,onChange:e=>C("testStatus",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[u.jsx("option",{value:"all",children:"Todos los estados"}),u.jsx("option",{value:"completed",children:"Evaluación completa"}),u.jsx("option",{value:"partial",children:"Evaluación parcial"}),u.jsx("option",{value:"no_tests",children:"Sin evaluaciones"})]})]})]}),p&&u.jsxs("div",{className:"border-t pt-4 mt-4",children:[u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4",children:[u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"}),u.jsx("input",{type:"text",placeholder:"Número de documento",value:a.document,onChange:e=>C("document",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Género"}),u.jsxs("select",{value:a.gender,onChange:e=>C("gender",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[u.jsx("option",{value:"all",children:"Todos"}),u.jsx("option",{value:"masculino",children:"Masculino"}),u.jsx("option",{value:"femenino",children:"Femenino"})]})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fecha Desde"}),u.jsx("input",{type:"date",value:a.dateFrom,onChange:e=>C("dateFrom",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fecha Hasta"}),u.jsx("input",{type:"date",value:a.dateTo,onChange:e=>C("dateTo",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ordenar por"}),u.jsxs("select",{value:a.sortBy,onChange:e=>C("sortBy",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[u.jsx("option",{value:"created_at",children:"Fecha de registro"}),u.jsx("option",{value:"name",children:"Nombre"}),u.jsx("option",{value:"apellido",children:"Apellido"}),u.jsx("option",{value:"documento",children:"Documento"})]})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Orden"}),u.jsxs("select",{value:a.sortOrder,onChange:e=>C("sortOrder",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[u.jsx("option",{value:"desc",children:"Descendente"}),u.jsx("option",{value:"asc",children:"Ascendente"})]})]})]})]}),u.jsxs("div",{className:"flex items-center justify-between mt-4 pt-4 border-t",children:[u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsxs(se,{onClick:()=>c(null,null,function*(){f(!0);try{e&&(yield e(a))}catch(t){m.error("Error en la búsqueda")}finally{f(!1)}}),disabled:g,className:"bg-blue-600 hover:bg-blue-700 text-white flex items-center",children:[u.jsx(W,{className:"mr-2"}),g?"Buscando...":"Buscar"]}),u.jsxs(se,{onClick:()=>{n({institution:"",gender:"all",dateFrom:"",dateTo:"",patientName:"",document:"",testStatus:"all",sortBy:"created_at",sortOrder:"desc"}),v([]),_(!1)},variant:"outline",className:"flex items-center",children:[u.jsx(h,{className:"mr-2"}),"Limpiar"]})]}),b&&u.jsxs("div",{className:"text-sm text-gray-600",children:[u.jsxs("span",{className:"mr-4",children:["Total: ",b.totalPatients," pacientes"]}),u.jsxs("span",{className:"mr-4",children:["♂ ",b.genderDistribution.male]}),u.jsxs("span",{children:["♀ ",b.genderDistribution.female]})]})]})]})]})},Ce=({searchResults:e,isLoading:t,onPatientSelect:s,onBatchGenerate:a,onBatchDelete:n,onViewPatient:r,onPageChange:i,selectedPatients:l=[],onSelectionChange:o})=>{const[c,x]=d.useState(new Set(l)),[g,h]=d.useState(!1);d.useEffect(()=>{x(new Set(l))},[l]),d.useEffect(()=>{if(null==e?void 0:e.patients){const t=e.patients.length>0&&e.patients.every(e=>c.has(e.id));h(t)}},[c,null==e?void 0:e.patients]);const f=e=>{const t=Array.from(c);0!==t.length?"generate"===e&&a?a(t):"delete"===e&&n&&n(t):m.warning("Selecciona al menos un paciente")},y=(e,t)=>{const s={completed:{color:"bg-green-100 text-green-800",text:"Completo",icon:"✓"},partial:{color:"bg-yellow-100 text-yellow-800",text:`${t}% Completo`,icon:"⚡"},no_tests:{color:"bg-gray-100 text-gray-800",text:"Sin Tests",icon:"○"}},a=s[e]||s.no_tests;return u.jsxs("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${a.color}`,children:[u.jsx("span",{className:"mr-1",children:a.icon}),a.text]})};if(t)return u.jsx(re,{children:u.jsx(le,{children:u.jsxs("div",{className:"flex items-center justify-center py-12",children:[u.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"}),u.jsx("span",{className:"text-gray-600",children:"Buscando pacientes..."})]})})});if(!e||0===e.patients.length)return u.jsx(re,{children:u.jsx(le,{children:u.jsxs("div",{className:"text-center py-12",children:[u.jsx(J,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),u.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No se encontraron pacientes"}),u.jsx("p",{className:"text-gray-500",children:"Intenta ajustar los filtros de búsqueda para encontrar más resultados."})]})})});const{patients:v,total:N,page:_,totalPages:P,hasNextPage:C,hasPrevPage:S}=e,E=c.size;return u.jsxs("div",{className:"space-y-4",children:[u.jsx(re,{children:u.jsx(ie,{children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsx("div",{className:"flex items-center",children:u.jsxs("button",{onClick:()=>{if(!(null==e?void 0:e.patients))return;const t=new Set;g||e.patients.forEach(e=>{t.add(e.id)}),x(t),h(!g),o&&o(Array.from(t))},className:"flex items-center text-sm font-medium text-gray-700 hover:text-gray-900",children:[g?u.jsx(Q,{className:"text-blue-600"}):u.jsx(Y,{}),u.jsxs("span",{className:"ml-2",children:["Seleccionar todos (",v.length,")"]})]})}),E>0&&u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsxs("span",{className:"text-sm text-gray-600",children:[E," seleccionado",1!==E?"s":""]}),u.jsxs(se,{onClick:()=>f("generate"),size:"sm",className:"bg-blue-600 hover:bg-blue-700 text-white",children:[u.jsx(p,{className:"mr-1"}),"Generar Informes"]}),u.jsxs(se,{onClick:()=>f("delete"),size:"sm",variant:"outline",className:"text-red-600 border-red-300 hover:bg-red-50",children:[u.jsx(R,{className:"mr-1"}),"Eliminar Informes"]})]})]}),u.jsxs("div",{className:"text-sm text-gray-600",children:["Mostrando ",20*(_-1)+1,"-",Math.min(20*_,N)," de ",N," resultados"]})]})})}),u.jsx("div",{className:"grid grid-cols-1 gap-4",children:v.map(e=>{var t,a,n,i,l,d,m,g;const h=c.has(e.id),f=null==(t=e.genero)?void 0:t.toLowerCase().startsWith("f");return u.jsx(re,{className:"transition-all duration-200 "+(h?"ring-2 ring-blue-500 bg-blue-50":"hover:shadow-md"),children:u.jsx(le,{className:"p-4",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsx("button",{onClick:()=>((e,t)=>{const s=new Set(c);t?s.add(e):s.delete(e),x(s),o&&o(Array.from(s))})(e.id,!h),className:"flex-shrink-0",children:h?u.jsx(Q,{className:"text-blue-600 text-lg"}):u.jsx(Y,{className:"text-gray-400 text-lg hover:text-gray-600"})}),u.jsx("div",{className:"w-10 h-10 rounded-full flex items-center justify-center "+(f?"bg-pink-100 text-pink-600":"bg-blue-100 text-blue-600"),children:u.jsx("span",{className:"text-lg font-bold",children:f?"♀":"♂"})}),u.jsxs("div",{className:"flex-1",children:[u.jsxs("div",{className:"flex items-center space-x-4 mb-2",children:[u.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:[e.nombre," ",e.apellido]}),y(null==(a=e.testSummary)?void 0:a.status,null==(n=e.testSummary)?void 0:n.completionPercentage)]}),u.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600",children:[u.jsxs("div",{className:"flex items-center",children:[u.jsx(b,{className:"mr-1"}),u.jsx("span",{children:e.documento})]}),u.jsxs("div",{className:"flex items-center",children:[u.jsx(w,{className:"mr-1"}),u.jsx("span",{children:(null==(i=e.instituciones)?void 0:i.nombre)||"N/A"})]}),u.jsxs("div",{className:"flex items-center",children:[u.jsx(j,{className:"mr-1"}),u.jsx("span",{children:(v=null==(l=e.testSummary)?void 0:l.lastTestDate,v?new Date(v).toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric"}):"N/A")})]}),u.jsxs("div",{className:"flex items-center",children:[u.jsx(p,{className:"mr-1"}),u.jsxs("span",{children:[(null==(d=e.testSummary)?void 0:d.testCount)||0," test",1!==((null==(m=e.testSummary)?void 0:m.testCount)||0)?"s":""]})]})]}),(null==(g=e.testSummary)?void 0:g.aptitudes)&&e.testSummary.aptitudes.length>0&&u.jsxs("div",{className:"mt-2",children:[u.jsx("span",{className:"text-xs text-gray-500 mr-2",children:"Aptitudes:"}),u.jsx("div",{className:"inline-flex flex-wrap gap-1",children:e.testSummary.aptitudes.map((e,t)=>u.jsx("span",{className:"inline-block px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded",children:e},t))})]})]})]}),u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsxs(se,{onClick:()=>r&&r(e.id),size:"sm",variant:"outline",className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:[u.jsx(I,{className:"mr-1"}),"Ver"]}),u.jsxs(se,{onClick:()=>s&&s(e),size:"sm",className:"bg-green-600 hover:bg-green-700 text-white",children:[u.jsx(p,{className:"mr-1"}),"Generar"]})]})]})})},e.id);var v})}),P>1&&u.jsx(re,{children:u.jsx(le,{children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"text-sm text-gray-600",children:["Página ",_," de ",P]}),u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsxs(se,{onClick:()=>i&&i(_-1),disabled:!S,variant:"outline",size:"sm",children:[u.jsx(K,{className:"mr-1"}),"Anterior"]}),u.jsx("span",{className:"px-3 py-1 bg-gray-100 rounded text-sm",children:_}),u.jsxs(se,{onClick:()=>i&&i(_+1),disabled:!C,variant:"outline",size:"sm",children:["Siguiente",u.jsx(X,{className:"ml-1"})]})]})]})})})]})},Se=()=>{const[e,t]=d.useState([]),[s,a]=d.useState(!0),[n,r]=d.useState(""),[i,p]=d.useState(""),[x,g]=d.useState([]),[h,f]=d.useState(new Set),[b,j]=d.useState("list"),[v,N]=d.useState(null),[_,w]=d.useState(!1),[P,C]=d.useState([]),[S,E]=d.useState(!1),[A,D]=d.useState(null),[M,k]=d.useState(!1),[T,I]=d.useState(null);d.useEffect(()=>{B(),R()},[]);const R=()=>c(null,null,function*(){try{const{data:e,error:t}=yield te.from("aptitudes").select("*").order("codigo");if(t)throw t;g(e||[])}catch(e){m.error("Error al cargar las aptitudes")}}),B=()=>c(null,null,function*(){try{a(!0);const{data:e,error:s}=yield te.from("resultados").select("\n          id,\n          puntaje_directo,\n          percentil,\n          errores,\n          tiempo_segundos,\n          concentracion,\n          created_at,\n          pacientes:paciente_id (\n            id,\n            nombre,\n            apellido,\n            documento,\n            genero\n          ),\n          aptitudes:aptitud_id (\n            codigo,\n            nombre,\n            descripcion\n          )\n        ").not("puntaje_directo","is",null).not("percentil","is",null).order("created_at",{ascending:!1});if(s)return void m.error("Error al cargar los resultados");const n=new Map;let r=0;e.forEach(e=>{var t,s;if(!(null==(t=e.pacientes)?void 0:t.id)||!(null==(s=e.aptitudes)?void 0:s.codigo)||null===e.puntaje_directo||null===e.percentil)return;const a=`${e.pacientes.id}-${e.aptitudes.codigo}`,i=n.get(a);if(i){const t=new Date(i.created_at);new Date(e.created_at)>t?(n.set(a,e),r++):r++}else n.set(a,e)});const i=Array.from(n.values()).reduce((e,t)=>{var s,a,n;const r=null==(s=t.pacientes)?void 0:s.id;if(!r)return e;e[r]||(e[r]={paciente:t.pacientes,resultados:[],fechaUltimaEvaluacion:t.created_at});const i=t.percentil?oe.obtenerInterpretacionPC(t.percentil):{nivel:"Pendiente",color:"text-gray-600",bg:"bg-gray-100"};return e[r].resultados.push({id:t.id,test:(null==(a=t.aptitudes)?void 0:a.codigo)||"N/A",testName:(null==(n=t.aptitudes)?void 0:n.nombre)||"Test Desconocido",puntajePD:t.puntaje_directo||0,puntajePC:t.percentil||"N/A",errores:t.errores||0,tiempo:t.tiempo_segundos?`${Math.round(t.tiempo_segundos/60)}:${String(t.tiempo_segundos%60).padStart(2,"0")}`:"N/A",concentracion:t.concentracion?`${t.concentracion.toFixed(1)}%`:"N/A",fecha:new Date(t.created_at).toLocaleDateString("es-ES"),interpretacion:i.nivel,interpretacionColor:i.color,interpretacionBg:i.bg}),new Date(t.created_at)>new Date(e[r].fechaUltimaEvaluacion)&&(e[r].fechaUltimaEvaluacion=t.created_at),e},{}),l=Object.values(i).sort((e,t)=>new Date(t.fechaUltimaEvaluacion)-new Date(e.fechaUltimaEvaluacion));t(l),a(!1)}catch(e){m.error("Error al cargar los resultados"),a(!1)}}),$=e.filter(e=>{const t=e.paciente;return!!(!n||(null==t?void 0:t.nombre)&&t.nombre.toLowerCase().includes(n.toLowerCase())||(null==t?void 0:t.apellido)&&t.apellido.toLowerCase().includes(n.toLowerCase())||(null==t?void 0:t.documento)&&t.documento.toLowerCase().includes(n.toLowerCase()))&&(!i||e.resultados.some(e=>e.test===i))}),O=(e,...t)=>c(null,[e,...t],function*(e,t={page:1,limit:20}){try{w(!0);const s=yield ue.searchPatients(e,t);N(o(l({},s),{currentFilters:e}))}catch(s){m.error("Error en la búsqueda: "+s.message)}finally{w(!1)}}),z=(e,t="all")=>c(null,null,function*(){try{const s=yield me.deletePatientReports(e,t);s.success?(m.success(s.message),"search"===b&&v?yield O(v.currentFilters):yield B()):m.warning(s.message)}catch(s){m.error("Error al eliminar los registros")}});return u.jsxs("div",{children:[u.jsx(de,{title:"Gestión de Informes BAT-7",subtitle:"Busca pacientes, genera informes individuales o masivos, y gestiona reportes",icon:y}),u.jsxs("div",{className:"container mx-auto px-4 py-8",children:[u.jsx("div",{className:"mb-6",children:u.jsxs("div",{className:"flex items-center space-x-2 bg-white rounded-lg p-1 shadow-sm border border-gray-200 w-fit",children:[u.jsxs(se,{onClick:()=>j("list"),variant:"list"===b?"primary":"outline",size:"sm",className:"flex items-center",children:[u.jsx(ee,{className:"mr-2"}),"Vista Lista"]}),u.jsxs(se,{onClick:()=>j("search"),variant:"search"===b?"primary":"outline",size:"sm",className:"flex items-center",children:[u.jsx(W,{className:"mr-2"}),"Búsqueda Avanzada"]})]})}),S&&A&&u.jsx(re,{className:"mb-6",children:u.jsxs(le,{children:[u.jsxs("div",{className:"flex items-center justify-between mb-2",children:[u.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Procesando informes en lote..."}),u.jsxs("span",{className:"text-sm text-gray-500",children:[A.current," de ",A.total]})]}),u.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:u.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${A.percentage}%`}})})]})}),"search"===b&&u.jsxs("div",{className:"space-y-6",children:[u.jsx(Pe,{onSearch:O,onStatsUpdate:e=>{}}),u.jsx(Ce,{searchResults:v,isLoading:_,selectedPatients:P,onSelectionChange:e=>{C(e)},onBatchGenerate:e=>c(null,null,function*(){if(e&&0!==e.length)try{E(!0),D({current:0,total:e.length,percentage:0});const t=yield ye.generateBatchReports(e,e=>{D(e)});D(null),t.successful.length>0&&m.success(`${t.successful.length} informe(s) generado(s) exitosamente`),t.failed.length>0&&m.warning(`${t.failed.length} informe(s) fallaron al generarse`),C([])}catch(t){m.error("Error en la generación masiva: "+t.message)}finally{E(!1),D(null)}else m.warning("Selecciona al menos un paciente")}),onBatchDelete:e=>c(null,null,function*(){if(e&&0!==e.length){if(confirm(`¿Estás seguro de que deseas eliminar los informes de ${e.length} paciente(s) seleccionado(s)?`))try{const t=yield me.batchDeleteReports(e);t.success?(m.success(t.message),C([]),"search"===b&&v?yield O(v.currentFilters):yield B()):m.warning(t.message)}catch(t){m.error("Error en la eliminación masiva")}}else m.warning("Selecciona al menos un paciente")}),onViewPatient:e=>{let t=null,s=[];if("search"===b&&v){const a=v.patients.find(t=>t.id===e);a&&(t=a,s=a.resultados||[])}else{const a=$.find(t=>t.paciente.id===e);a&&(t=a.paciente,s=a.resultados.map(e=>({id:e.id,aptitudes:{codigo:e.test,nombre:e.testName},puntaje_directo:e.puntajePD,percentil:e.puntajePC,puntaje_pc:e.puntajePC,errores:e.errores,tiempo_segundos:e.tiempo,created_at:e.fecha,test:e.test})))}t&&s.length>0?(I({patient:t,results:s}),k(!0)):m.warning("No se encontraron datos del paciente")},onPageChange:e=>c(null,null,function*(){(null==v?void 0:v.currentFilters)&&(yield O(v.currentFilters,{page:e,limit:20}))}),onPatientSelect:e=>{I({patient:e,results:e.resultados||[]}),k(!0)}})]}),"list"===b&&u.jsxs("div",{children:[u.jsxs(re,{className:"mb-6",children:[u.jsx(ie,{children:u.jsxs("h2",{className:"text-lg font-semibold text-gray-800",children:[u.jsx("i",{className:"fas fa-filter mr-2 text-blue-600"}),"Filtros de Búsqueda"]})}),u.jsx(le,{children:u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Buscar Paciente"}),u.jsxs("div",{className:"relative",children:[u.jsx("input",{type:"text",placeholder:"Nombre, apellido o documento...",value:n,onChange:e=>r(e.target.value),className:"w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),u.jsx("i",{className:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})]})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Filtrar por Test"}),u.jsxs("select",{value:i,onChange:e=>p(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[u.jsx("option",{value:"",children:"Todos los tests"}),x.map(e=>u.jsxs("option",{value:e.codigo,children:[e.codigo," - ",e.nombre]},e.id))]})]})]})})]}),u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[u.jsx(re,{children:u.jsxs(le,{className:"text-center",children:[u.jsx("div",{className:"text-2xl font-bold text-blue-600",children:e.reduce((e,t)=>e+t.resultados.filter(e=>null!==e.puntajePD&&void 0!==e.puntajePD&&null!==e.puntajePC&&void 0!==e.puntajePC&&"N/A"!==e.puntajePC).length,0)}),u.jsx("div",{className:"text-sm text-gray-600",children:"Resultados Válidos"})]})}),u.jsx(re,{children:u.jsxs(le,{className:"text-center",children:[u.jsx("div",{className:"text-2xl font-bold text-green-600",children:e.filter(e=>e.resultados.length>0).length}),u.jsx("div",{className:"text-sm text-gray-600",children:"Pacientes con Tests"})]})}),u.jsx(re,{children:u.jsxs(le,{className:"text-center",children:[u.jsx("div",{className:"text-2xl font-bold text-purple-600",children:(()=>{const t=new Set;return e.forEach(e=>{e.resultados.forEach(e=>{e.test&&null!==e.puntajePD&&void 0!==e.puntajePD&&t.add(e.test)})}),t.size})()}),u.jsx("div",{className:"text-sm text-gray-600",children:"Aptitudes Contestadas"})]})}),u.jsx(re,{children:u.jsxs(le,{className:"text-center",children:[u.jsx("div",{className:"text-2xl font-bold text-orange-600",children:(()=>{const t=e.reduce((e,t)=>{const s=t.resultados.filter(e=>null!==e.puntajePC&&void 0!==e.puntajePC&&"N/A"!==e.puntajePC&&"number"==typeof e.puntajePC);return e.concat(s)},[]);if(0===t.length)return 0;const s=t.reduce((e,t)=>e+t.puntajePC,0);return Math.round(s/t.length)})()}),u.jsx("div",{className:"text-sm text-gray-600",children:"Promedio PC"})]})})]}),u.jsxs("div",{className:"flex justify-between items-center mb-6",children:[u.jsxs("div",{children:[u.jsxs("h2",{className:"text-2xl font-bold text-blue-800",children:[u.jsx("i",{className:"fas fa-chart-line mr-3 text-blue-600"}),"Resultados Detallados"]}),u.jsxs("p",{className:"text-gray-600 mt-1",children:[$.length," paciente",1!==$.length?"s":""," con resultados"]})]}),$.length>0&&u.jsxs("div",{className:"flex items-center space-x-3",children:[u.jsxs("div",{className:"text-sm text-gray-500 mr-2",children:[h.size," de ",$.length," expandidos"]}),u.jsxs(se,{onClick:()=>{const e=new Set($.map(e=>e.paciente.id));f(e)},variant:"outline",size:"sm",className:"text-green-600 border-green-300 hover:bg-green-50",children:[u.jsx("i",{className:"fas fa-expand-arrows-alt mr-2"}),"Expandir Todo"]}),u.jsxs(se,{onClick:()=>{f(new Set)},variant:"outline",size:"sm",className:"text-orange-600 border-orange-300 hover:bg-orange-50",children:[u.jsx("i",{className:"fas fa-compress-arrows-alt mr-2"}),"Contraer Todo"]})]})]}),s?u.jsxs("div",{className:"py-16 text-center",children:[u.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),u.jsx("p",{className:"text-gray-500",children:"Cargando resultados..."})]}):u.jsx(u.Fragment,{children:0===$.length?u.jsx(re,{children:u.jsx(le,{children:u.jsxs("div",{className:"py-8 text-center",children:[u.jsx("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 mb-4"}),u.jsx("p",{className:"text-gray-500",children:"No hay resultados de tests disponibles."}),u.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Los resultados aparecerán aquí una vez que se completen los tests."})]})})}):u.jsx("div",{className:"grid grid-cols-1 gap-4",children:$.map(e=>u.jsx(we,{patient:{id:e.paciente.id,nombre:e.paciente.nombre,apellido:e.paciente.apellido,documento:e.paciente.documento,genero:e.paciente.genero},results:e.resultados.map(e=>({id:e.id,aptitudes:{codigo:e.test,nombre:e.testName},puntaje_directo:e.puntajePD,percentil:e.puntajePC,puntaje_pc:e.puntajePC,errores:e.errores,tiempo_segundos:e.tiempo,created_at:e.fecha,test:e.test})),onGenerate:e=>{m.success("Informe generado exitosamente")},onView:e=>{m.info('Funcionalidad de ver reporte disponible en el botón "Ver" de cada tarjeta')},onDelete:z},e.paciente.id))})})]}),M&&T&&u.jsx(ve,{isOpen:M,onClose:()=>{k(!1),I(null)},patient:T.patient,results:T.results})]})]})};export{Se as default};
