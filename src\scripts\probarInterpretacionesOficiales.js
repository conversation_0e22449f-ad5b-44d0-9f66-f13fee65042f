/**
 * @file probarInterpretacionesOficiales.js
 * @description Script para probar que las interpretaciones oficiales se están usando correctamente
 */

import { InterpretacionCualitativaService } from '../services/interpretacionCualitativaService.js';
import { InterpretacionesService } from '../services/InterpretacionesService.js';
import { INTERPRETACIONES_OFICIALES_CONSOLIDADAS } from '../utils/interpretacionesOficialesConsolidadas.js';

/**
 * Función para probar las interpretaciones oficiales
 */
async function probarInterpretacionesOficiales() {
  console.log('🧪 Probando interpretaciones oficiales del BAT-7...\n');

  // 1. Probar interpretaciones consolidadas locales
  console.log('📋 1. Probando interpretaciones consolidadas locales:');
  
  const pruebasLocales = [
    { aptitud: 'V', percentil: 90 },
    { aptitud: 'E', percentil: 25 },
    { aptitud: 'A', percentil: 75 },
    { aptitud: 'R', percentil: 50 }
  ];

  pruebasLocales.forEach(prueba => {
    const interpretacion = INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerInterpretacionAptitud(
      prueba.aptitud, 
      prueba.percentil
    );
    
    console.log(`✅ ${prueba.aptitud}-${prueba.percentil}: ${interpretacion.nivel_nombre}`);
    console.log(`   Rendimiento: ${interpretacion.rendimiento.substring(0, 80)}...`);
    console.log(`   Académico: ${interpretacion.academico.substring(0, 80)}...`);
    console.log(`   Vocacional: ${interpretacion.vocacional.substring(0, 80)}...\n`);
  });

  // 2. Probar InterpretacionesService
  console.log('🔧 2. Probando InterpretacionesService:');
  
  for (const prueba of pruebasLocales) {
    try {
      const interpretacion = await InterpretacionesService.obtenerInterpretacionAptitud(
        prueba.aptitud, 
        prueba.percentil
      );
      
      console.log(`✅ ${prueba.aptitud}-${prueba.percentil}: ${interpretacion.nivel_nombre || 'Sin nivel'}`);
      console.log(`   Fuente: ${interpretacion.fuente || 'No especificada'}`);
      
      // Verificar que contiene textos oficiales
      if (interpretacion.rendimiento && interpretacion.rendimiento.length > 100) {
        console.log(`   ✅ Contiene texto oficial de rendimiento (${interpretacion.rendimiento.length} caracteres)`);
      } else {
        console.log(`   ⚠️ Texto de rendimiento parece corto (${interpretacion.rendimiento?.length || 0} caracteres)`);
      }
      
    } catch (error) {
      console.log(`❌ ${prueba.aptitud}-${prueba.percentil}: Error - ${error.message}`);
    }
    console.log('');
  }

  // 3. Probar InterpretacionCualitativaService
  console.log('🎯 3. Probando InterpretacionCualitativaService:');
  
  const resultadosPrueba = [
    {
      aptitud: { codigo: 'V', nombre: 'Aptitud Verbal' },
      percentil: 85
    },
    {
      aptitud: { codigo: 'E', nombre: 'Aptitud Espacial' },
      percentil: 30
    },
    {
      aptitud: { codigo: 'R', nombre: 'Razonamiento' },
      percentil: 70
    }
  ];

  const pacientePrueba = {
    nombre: 'Paciente de Prueba',
    edad: 16
  };

  try {
    console.log('   Generando interpretación personalizada...');
    const interpretacionPersonalizada = await InterpretacionCualitativaService.generarInterpretacionPersonalizada(
      resultadosPrueba, 
      pacientePrueba
    );

    console.log('✅ Interpretación personalizada generada exitosamente');
    console.log(`   Resumen general: Nivel ${interpretacionPersonalizada.resumenGeneral.nivelGeneral}`);
    console.log(`   Aptitudes específicas: ${interpretacionPersonalizada.aptitudesEspecificas.length} interpretaciones`);
    
    // Verificar que las interpretaciones específicas contienen textos oficiales
    interpretacionPersonalizada.aptitudesEspecificas.forEach((apt, index) => {
      console.log(`\n   📋 Aptitud ${apt.codigo} (${apt.nombre}):`);
      console.log(`      Nivel: ${apt.nivel}`);
      console.log(`      Percentil: ${apt.percentil}`);
      
      if (apt.interpretacion.rendimiento && apt.interpretacion.rendimiento.length > 100) {
        console.log(`      ✅ Texto oficial de rendimiento (${apt.interpretacion.rendimiento.length} caracteres)`);
        console.log(`      Inicio: "${apt.interpretacion.rendimiento.substring(0, 100)}..."`);
      } else {
        console.log(`      ⚠️ Texto de rendimiento parece corto o faltante`);
      }
      
      if (apt.interpretacion.academico && apt.interpretacion.academico.length > 50) {
        console.log(`      ✅ Texto oficial académico (${apt.interpretacion.academico.length} caracteres)`);
      } else {
        console.log(`      ⚠️ Texto académico parece corto o faltante`);
      }
      
      if (apt.interpretacion.vocacional && apt.interpretacion.vocacional.length > 50) {
        console.log(`      ✅ Texto oficial vocacional (${apt.interpretacion.vocacional.length} caracteres)`);
      } else {
        console.log(`      ⚠️ Texto vocacional parece corto o faltante`);
      }
    });

  } catch (error) {
    console.log(`❌ Error generando interpretación personalizada: ${error.message}`);
    console.error(error);
  }

  // 4. Verificar completitud de interpretaciones oficiales
  console.log('\n📊 4. Verificando completitud de interpretaciones oficiales:');
  
  const resumen = INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerResumenCompletitud();
  
  Object.keys(resumen).forEach(aptitud => {
    const info = resumen[aptitud];
    const status = info.porcentaje === 100 ? '✅' : '⚠️';
    console.log(`${status} ${aptitud}: ${info.disponibles}/${info.total} (${info.porcentaje}%)`);
    
    if (info.faltantes.length > 0) {
      console.log(`   Faltantes: ${info.faltantes.join(', ')}`);
    }
  });

  // 5. Comparar con interpretaciones anteriores
  console.log('\n🔄 5. Verificando que se usan interpretaciones oficiales:');
  
  const pruebaComparacion = { aptitud: 'V', percentil: 90 };
  const interpretacionOficial = INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerInterpretacionAptitud(
    pruebaComparacion.aptitud, 
    pruebaComparacion.percentil
  );

  // Verificar que contiene palabras clave del documento oficial
  const palabrasClaveOficiales = [
    'evaluado',
    'rendimiento académico',
    'ámbito vocacional',
    'capacidad para',
    'habilidad para'
  ];

  let palabrasEncontradas = 0;
  palabrasClaveOficiales.forEach(palabra => {
    const textoCompleto = `${interpretacionOficial.rendimiento} ${interpretacionOficial.academico} ${interpretacionOficial.vocacional}`.toLowerCase();
    if (textoCompleto.includes(palabra.toLowerCase())) {
      palabrasEncontradas++;
      console.log(`✅ Encontrada palabra clave oficial: "${palabra}"`);
    }
  });

  if (palabrasEncontradas >= 3) {
    console.log(`\n🎉 ¡Confirmado! Se están usando interpretaciones oficiales (${palabrasEncontradas}/${palabrasClaveOficiales.length} palabras clave encontradas)`);
  } else {
    console.log(`\n⚠️ Posible problema: Solo se encontraron ${palabrasEncontradas}/${palabrasClaveOficiales.length} palabras clave oficiales`);
  }

  console.log('\n✅ Prueba de interpretaciones oficiales completada!');
}

// Ejecutar prueba si se ejecuta directamente
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🎯 Prueba de Interpretaciones Oficiales BAT-7');
  console.log('📄 Verificando que se usen textos del documento oficial\n');
  
  await probarInterpretacionesOficiales();
}

export { probarInterpretacionesOficiales };
