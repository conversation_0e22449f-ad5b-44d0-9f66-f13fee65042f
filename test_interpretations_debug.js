/**
 * Debug script to test interpretation loading for the report issue
 */

import { InterpretacionCualitativaService } from './src/services/interpretacionCualitativaService.js';
import { INTERPRETACIONES_OFICIALES_CONSOLIDADAS } from './src/utils/interpretacionesOficialesConsolidadas.js';

console.log('🔍 Testing Interpretation System for Report Issue\n');

// Test data similar to what would come from a real patient
const testResults = [
  {
    aptitud: { codigo: 'V', nombre: 'Aptitud Verbal' },
    percentil: 85
  },
  {
    aptitud: { codigo: 'R', nombre: 'Razonamiento' },
    percentil: 5
  },
  {
    aptitud: { codigo: 'N', nombre: 'Aptitud Numérica' },
    percentil: 50
  }
];

const testPatient = {
  nombre: 'Test Patient',
  apellido: 'Debug',
  edad: 16
};

async function testInterpretations() {
  console.log('1. Testing direct INTERPRETACIONES_OFICIALES_CONSOLIDADAS:');
  
  testResults.forEach(result => {
    const interpretation = INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerInterpretacionAptitud(
      result.aptitud.codigo, 
      result.percentil
    );
    
    console.log(`\n📋 ${result.aptitud.codigo} - Percentil ${result.percentil}:`);
    console.log(`   Nivel: ${interpretation.nivel_nombre}`);
    console.log(`   Rendimiento length: ${interpretation.rendimiento?.length || 0}`);
    console.log(`   Rendimiento preview: "${interpretation.rendimiento?.substring(0, 100)}..."`);
    console.log(`   Has academico: ${!!interpretation.academico}`);
    console.log(`   Has vocacional: ${!!interpretation.vocacional}`);
  });

  console.log('\n\n2. Testing InterpretacionCualitativaService:');
  
  try {
    const personalizedInterpretation = await InterpretacionCualitativaService.generarInterpretacionPersonalizada(
      testResults,
      testPatient
    );

    console.log('\n✅ Personalized interpretation generated successfully');
    console.log(`   aptitudesEspecificas count: ${personalizedInterpretation.aptitudesEspecificas?.length || 0}`);
    
    personalizedInterpretation.aptitudesEspecificas?.forEach((apt, index) => {
      console.log(`\n📋 Aptitude ${index + 1}: ${apt.codigo} - ${apt.nombre}`);
      console.log(`   Percentil: ${apt.percentil}`);
      console.log(`   Nivel: ${apt.nivel}`);
      console.log(`   Fuente: ${apt.fuente}`);
      console.log(`   Has interpretacion: ${!!apt.interpretacion}`);
      
      if (apt.interpretacion) {
        console.log(`   Rendimiento length: ${apt.interpretacion.rendimiento?.length || 0}`);
        console.log(`   Rendimiento preview: "${apt.interpretacion.rendimiento?.substring(0, 100)}..."`);
        console.log(`   Has academico: ${!!apt.interpretacion.academico}`);
        console.log(`   Has vocacional: ${!!apt.interpretacion.vocacional}`);
      }
    });

  } catch (error) {
    console.error('❌ Error generating personalized interpretation:', error);
  }
}

// Run the test
testInterpretations().then(() => {
  console.log('\n🎯 Test completed');
}).catch(error => {
  console.error('❌ Test failed:', error);
});
