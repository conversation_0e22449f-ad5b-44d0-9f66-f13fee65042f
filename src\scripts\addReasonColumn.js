import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjMxMjg0MSwiZXhwIjoyMDYxODg4ODQxfQ.zwk3Wiay5jjeYOrg8B1M6T98B2esCqRFI43At-AFV3A';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function addReasonColumn() {
  console.log('🔧 Agregando columna "reason" a psychologist_usage_control...\n');

  try {
    // Ejecutar SQL para agregar la columna
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Agregar columna reason si no existe
        DO $$ 
        BEGIN 
          IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'psychologist_usage_control' 
            AND column_name = 'reason'
          ) THEN
            ALTER TABLE psychologist_usage_control 
            ADD COLUMN reason TEXT;
            
            RAISE NOTICE 'Columna reason agregada exitosamente';
          ELSE
            RAISE NOTICE 'La columna reason ya existe';
          END IF;
        END $$;
      `
    });

    if (error) {
      console.log('❌ Error ejecutando SQL:', error.message);
      
      // Método alternativo: usar una consulta directa
      console.log('🔄 Intentando método alternativo...');
      
      const { error: altError } = await supabase
        .from('psychologist_usage_control')
        .insert([{
          psychologist_id: '00000000-0000-0000-0000-000000000000',
          total_uses: 0,
          used_uses: 0,
          is_unlimited: false,
          is_active: false,
          reason: 'Test column'
        }]);

      if (altError && altError.message.includes("reason")) {
        console.log('❌ La columna reason no existe. Necesita ser agregada manualmente.');
        console.log('\n📋 SQL para ejecutar en Supabase:');
        console.log('ALTER TABLE psychologist_usage_control ADD COLUMN reason TEXT;');
      } else {
        console.log('✅ La columna reason ya existe o fue agregada');
        
        // Limpiar registro de prueba
        await supabase
          .from('psychologist_usage_control')
          .delete()
          .eq('psychologist_id', '00000000-0000-0000-0000-000000000000');
      }
    } else {
      console.log('✅ Operación completada exitosamente');
    }

    // Verificar la estructura actualizada
    console.log('\n🔍 Verificando estructura actualizada...');
    const { error: testError } = await supabase
      .from('psychologist_usage_control')
      .insert([{
        psychologist_id: '00000000-0000-0000-0000-000000000000',
        total_uses: 10,
        used_uses: 0,
        is_unlimited: false,
        is_active: true,
        reason: 'Asignación inicial de prueba'
      }]);

    if (testError) {
      console.log('❌ Error en prueba:', testError.message);
    } else {
      console.log('✅ Columna reason funciona correctamente');
      
      // Limpiar registro de prueba
      await supabase
        .from('psychologist_usage_control')
        .delete()
        .eq('psychologist_id', '00000000-0000-0000-0000-000000000000');
    }

  } catch (error) {
    console.error('❌ Error inesperado:', error);
  }
}

addReasonColumn();
