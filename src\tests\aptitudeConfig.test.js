/**
 * @file aptitudeConfig.test.js
 * @description Pruebas para verificar la configuración centralizada de aptitudes
 */

import { 
  APTITUDE_CONFIG, 
  PERCENTIL_LEVELS, 
  getAptitudeConfig, 
  getPercentilLevel,
  TEST_COLORS 
} from '../constants/aptitudeConstants';

/**
 * Pruebas para verificar que la configuración de aptitudes esté completa y correcta
 */
describe('Configuración de Aptitudes', () => {
  
  test('Todas las aptitudes tienen configuración completa', () => {
    const expectedAptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
    
    expectedAptitudes.forEach(code => {
      const config = APTITUDE_CONFIG[code];
      
      expect(config).toBeDefined();
      expect(config.code).toBe(code);
      expect(config.name).toBeDefined();
      expect(config.shortName).toBeDefined();
      expect(config.description).toBeDefined();
      expect(config.color).toBeDefined();
      expect(config.bgColor).toBeDefined();
      expect(config.textColor).toBeDefined();
      expect(config.borderColor).toBeDefined();
      expect(config.lightBg).toBeDefined();
      expect(config.icon).toBeDefined();
      expect(config.iconClass).toBeDefined();
      expect(config.badge).toBeDefined();
      expect(config.button).toBeDefined();
    });
  });

  test('Los colores siguen el estándar de las imágenes de referencia', () => {
    // Verificar colores específicos según las imágenes de referencia
    expect(APTITUDE_CONFIG.V.color).toBe('#2563EB'); // Azul para Verbal
    expect(APTITUDE_CONFIG.E.color).toBe('#6D28D9'); // Morado para Espacial
    expect(APTITUDE_CONFIG.A.color).toBe('#DC2626'); // Rojo para Atención
    expect(APTITUDE_CONFIG.R.color).toBe('#D97706'); // Naranja para Razonamiento
    expect(APTITUDE_CONFIG.N.color).toBe('#0F766E'); // Teal para Numérica
    expect(APTITUDE_CONFIG.M.color).toBe('#374151'); // Gris para Mecánica
    expect(APTITUDE_CONFIG.O.color).toBe('#16A34A'); // Verde para Ortografía
  });

  test('getAptitudeConfig funciona correctamente', () => {
    const configV = getAptitudeConfig('V');
    expect(configV.name).toBe('Aptitud Verbal');
    expect(configV.color).toBe('#2563EB');

    const configInvalid = getAptitudeConfig('X');
    expect(configInvalid.name).toBe('Desconocido');
    expect(configInvalid.color).toBe('#374151');
  });

  test('TEST_COLORS mapea correctamente', () => {
    expect(TEST_COLORS.V).toBe(APTITUDE_CONFIG.V.color);
    expect(TEST_COLORS.E).toBe(APTITUDE_CONFIG.E.color);
    expect(TEST_COLORS.A).toBe(APTITUDE_CONFIG.A.color);
    expect(TEST_COLORS.R).toBe(APTITUDE_CONFIG.R.color);
    expect(TEST_COLORS.N).toBe(APTITUDE_CONFIG.N.color);
    expect(TEST_COLORS.M).toBe(APTITUDE_CONFIG.M.color);
    expect(TEST_COLORS.O).toBe(APTITUDE_CONFIG.O.color);
  });
});

/**
 * Pruebas para verificar que la configuración de percentiles esté correcta
 */
describe('Configuración de Percentiles', () => {
  
  test('Todos los niveles de percentil están definidos', () => {
    const expectedLevels = ['VERY_HIGH', 'HIGH', 'MEDIUM_HIGH', 'MEDIUM', 'MEDIUM_LOW', 'LOW', 'VERY_LOW'];
    
    expectedLevels.forEach(level => {
      const config = PERCENTIL_LEVELS[level];
      
      expect(config).toBeDefined();
      expect(config.min).toBeDefined();
      expect(config.max).toBeDefined();
      expect(config.level).toBeDefined();
      expect(config.color).toBeDefined();
      expect(config.textColor).toBeDefined();
      expect(config.bgColor).toBeDefined();
      expect(config.bgClass).toBeDefined();
    });
  });

  test('getPercentilLevel funciona correctamente', () => {
    expect(getPercentilLevel(98).level).toBe('Muy Alto');
    expect(getPercentilLevel(85).level).toBe('Alto');
    expect(getPercentilLevel(70).level).toBe('Medio-Alto');
    expect(getPercentilLevel(50).level).toBe('Medio');
    expect(getPercentilLevel(30).level).toBe('Medio-Bajo');
    expect(getPercentilLevel(15).level).toBe('Bajo');
    expect(getPercentilLevel(3).level).toBe('Muy Bajo');
  });

  test('Los rangos de percentiles no se superponen', () => {
    const levels = Object.values(PERCENTIL_LEVELS);
    
    for (let i = 0; i < levels.length - 1; i++) {
      for (let j = i + 1; j < levels.length; j++) {
        const level1 = levels[i];
        const level2 = levels[j];
        
        // Verificar que no hay superposición
        const overlap = !(level1.max < level2.min || level2.max < level1.min);
        if (overlap) {
          // Solo permitir superposición en los límites exactos
          const boundaryOverlap = (level1.max === level2.min) || (level2.max === level1.min);
          expect(boundaryOverlap).toBe(true);
        }
      }
    }
  });
});

/**
 * Pruebas de integración para verificar consistencia
 */
describe('Integración y Consistencia', () => {
  
  test('Todas las clases CSS están definidas correctamente', () => {
    Object.values(APTITUDE_CONFIG).forEach(config => {
      expect(config.bgColor).toMatch(/^bg-\w+-\d+$/);
      expect(config.textColor).toMatch(/^text-\w+-\d+$/);
      expect(config.borderColor).toMatch(/^border-\w+-\d+$/);
      expect(config.lightBg).toMatch(/^bg-\w+-\d+$/);
    });
  });

  test('Los iconos son componentes válidos', () => {
    Object.values(APTITUDE_CONFIG).forEach(config => {
      expect(typeof config.icon).toBe('function');
      expect(typeof config.iconClass).toBe('string');
    });
  });

  test('Las configuraciones de badge y button están completas', () => {
    Object.values(APTITUDE_CONFIG).forEach(config => {
      expect(config.badge.bg).toBeDefined();
      expect(config.badge.text).toBeDefined();
      expect(config.button.bg).toBeDefined();
      expect(config.button.hover).toBeDefined();
      expect(config.button.text).toBeDefined();
    });
  });
});

console.log('✅ Todas las pruebas de configuración de aptitudes pasaron correctamente');
console.log('🎨 Colores estandarizados según las imágenes de referencia');
console.log('📊 Configuración centralizada funcionando correctamente');
