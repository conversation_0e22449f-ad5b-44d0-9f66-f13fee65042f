#!/usr/bin/env node

/**
 * Script simplificado para probar la conexión a Supabase
 */

// Cargar variables de entorno
import { config } from 'dotenv';
config();

import { createClient } from '@supabase/supabase-js';

console.log('🔍 Verificando variables de entorno...');
console.log('VITE_SUPABASE_URL:', process.env.VITE_SUPABASE_URL ? 'DEFINIDA' : 'NO DEFINIDA');
console.log('VITE_SUPABASE_ANON_KEY:', process.env.VITE_SUPABASE_ANON_KEY ? 'DEFINIDA' : 'NO DEFINIDA');
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'DEFINIDA' : 'NO DEFINIDA');
console.log('SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? 'DEFINIDA' : 'NO DEFINIDA');

// Obtener las variables con fallbacks
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY;

console.log('\n📋 Variables finales:');
console.log('URL:', supabaseUrl);
console.log('KEY:', supabaseKey ? 'PRESENTE' : 'AUSENTE');

if (!supabaseUrl || !supabaseKey) {
  console.error('\n❌ Error: Variables de Supabase no están definidas correctamente');
  process.exit(1);
}

try {
  console.log('\n🔌 Creando cliente de Supabase...');
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  console.log('✅ Cliente creado exitosamente');
  
  console.log('\n🧪 Probando conexión a la base de datos...');
  
  // Probar una consulta simple
  const { data, error } = await supabase
    .from('usuarios')
    .select('id')
    .limit(1);
  
  if (error) {
    console.log('⚠️  Error en consulta (esto puede ser normal si no hay datos):', error.message);
  } else {
    console.log('✅ Conexión exitosa a la base de datos');
    console.log('📊 Datos obtenidos:', data?.length || 0, 'registros');
  }
  
  // Probar si las tablas del sistema de pines existen
  console.log('\n🔍 Verificando tablas del sistema de pines...');
  
  const tablesToCheck = [
    'pin_recharge_requests',
    'pin_notifications',
    'pin_consumption_history',
    'pin_statistics_cache'
  ];
  
  for (const tableName of tablesToCheck) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ Tabla ${tableName}: ${error.message}`);
      } else {
        console.log(`✅ Tabla ${tableName}: OK (${data?.length || 0} registros)`);
      }
    } catch (err) {
      console.log(`❌ Tabla ${tableName}: Error - ${err.message}`);
    }
  }
  
  console.log('\n🎉 Prueba de conexión completada');
  
} catch (error) {
  console.error('\n❌ Error fatal:', error.message);
  process.exit(1);
}
