import { createClient } from '@supabase/supabase-js';

// Configuración directa de Supabase para el script
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Importar las funciones del servicio
import CompletePinControlService from '../services/pin/CompletePinControlService.js';

async function testNewFeatures() {
  console.log('🧪 Probando nuevas funcionalidades del sistema de pines...\n');

  try {
    // 1. Obtener psicólogos actuales
    console.log('1️⃣ Obteniendo estado actual...');
    const psychologists = await CompletePinControlService.getPsychologistsWithPinInfo();
    
    console.log(`✅ ${psychologists.length} psicólogos encontrados:`);
    psychologists.forEach((psy, index) => {
      console.log(`   ${index + 1}. ${psy.fullName}`);
      console.log(`      Pines: ${psy.isUnlimited ? '∞' : psy.totalPins} total, ${psy.usedPins} usados, ${psy.isUnlimited ? '∞' : psy.remainingPins} restantes`);
      console.log(`      Estado: ${psy.status} | Control: ${psy.hasControl ? 'Sí' : 'No'}`);
    });

    // Buscar un psicólogo con pines para probar
    const psychWithPins = psychologists.find(p => p.hasControl && !p.isUnlimited && p.totalPins > 0);
    
    if (!psychWithPins) {
      console.log('\n⚠️ No hay psicólogos con pines asignados para probar. Asignando pines primero...');
      
      const testPsy = psychologists[0];
      await CompletePinControlService.assignPins(testPsy.id, 100, false);
      console.log(`✅ Asignados 100 pines a ${testPsy.fullName}`);
      
      // Actualizar lista
      const updatedPsychologists = await CompletePinControlService.getPsychologistsWithPinInfo();
      const updatedTestPsy = updatedPsychologists.find(p => p.id === testPsy.id);
      
      if (updatedTestPsy) {
        console.log(`   Estado actualizado: ${updatedTestPsy.totalPins} total, ${updatedTestPsy.usedPins} usados, ${updatedTestPsy.remainingPins} restantes`);
      }
    }

    // 2. Probar resta de pines
    console.log('\n2️⃣ Probando resta de pines...');
    const finalPsychologists = await CompletePinControlService.getPsychologistsWithPinInfo();
    const testPsychologist = finalPsychologists.find(p => p.hasControl && !p.isUnlimited && p.totalPins > 10);
    
    if (testPsychologist) {
      console.log(`Restando 15 pines de ${testPsychologist.fullName}...`);
      console.log(`Estado antes: ${testPsychologist.totalPins} total, ${testPsychologist.usedPins} usados, ${testPsychologist.remainingPins} restantes`);
      
      const subtractResult = await CompletePinControlService.subtractPins(
        testPsychologist.id,
        15
      );
      
      console.log('✅ Resultado de resta:', subtractResult.message);
      console.log(`   Pines anteriores: ${subtractResult.data.previousTotal}`);
      console.log(`   Pines nuevos: ${subtractResult.data.newTotal}`);
      console.log(`   Pines usados: ${subtractResult.data.usedPins}`);
      console.log(`   Pines restantes: ${subtractResult.data.remainingPins}`);

      // Verificar la resta
      console.log('\n3️⃣ Verificando resta...');
      const verifyPsychologists = await CompletePinControlService.getPsychologistsWithPinInfo();
      const verifyPsy = verifyPsychologists.find(p => p.id === testPsychologist.id);
      
      if (verifyPsy) {
        console.log(`✅ ${verifyPsy.fullName} después de la resta:`);
        console.log(`   - Pines totales: ${verifyPsy.totalPins}`);
        console.log(`   - Pines usados: ${verifyPsy.usedPins}`);
        console.log(`   - Pines restantes: ${verifyPsy.remainingPins}`);
        console.log(`   - Estado: ${verifyPsy.status}`);
      }

      // 4. Probar eliminación de psicólogo
      console.log('\n4️⃣ Probando eliminación de control de pines...');
      
      const removeResult = await CompletePinControlService.removePsychologistPins(
        testPsychologist.id
      );
      
      console.log('✅ Resultado de eliminación:', removeResult.message);
      console.log(`   Psicólogo: ${removeResult.data.psychologist.nombre} ${removeResult.data.psychologist.apellido}`);
      console.log(`   Control eliminado:`, removeResult.data.removedControl);

      // Verificar la eliminación
      console.log('\n5️⃣ Verificando eliminación...');
      const finalVerifyPsychologists = await CompletePinControlService.getPsychologistsWithPinInfo();
      const finalVerifyPsy = finalVerifyPsychologists.find(p => p.id === testPsychologist.id);
      
      if (finalVerifyPsy) {
        console.log(`✅ ${finalVerifyPsy.fullName} después de la eliminación:`);
        console.log(`   - Tiene control: ${finalVerifyPsy.hasControl ? 'Sí' : 'No'}`);
        console.log(`   - Pines totales: ${finalVerifyPsy.totalPins}`);
        console.log(`   - Estado: ${finalVerifyPsy.status}`);
      }

    } else {
      console.log('❌ No se encontró un psicólogo adecuado para probar la resta');
    }

    // 6. Probar casos extremos
    console.log('\n6️⃣ Probando casos extremos...');
    
    // Intentar restar más pines de los que tiene
    const anotherPsy = finalPsychologists.find(p => p.hasControl && !p.isUnlimited);
    if (anotherPsy) {
      try {
        console.log(`Intentando restar 1000 pines de ${anotherPsy.fullName} (tiene ${anotherPsy.totalPins})...`);
        await CompletePinControlService.subtractPins(anotherPsy.id, 1000);
        console.log('⚠️ Esto no debería haber funcionado');
      } catch (error) {
        console.log('✅ Error esperado capturado:', error.message);
      }
    }

    // Intentar restar pines de plan ilimitado
    const unlimitedPsy = finalPsychologists.find(p => p.isUnlimited);
    if (unlimitedPsy) {
      try {
        console.log(`Intentando restar pines de plan ilimitado (${unlimitedPsy.fullName})...`);
        await CompletePinControlService.subtractPins(unlimitedPsy.id, 10);
        console.log('⚠️ Esto no debería haber funcionado');
      } catch (error) {
        console.log('✅ Error esperado capturado:', error.message);
      }
    }

    console.log('\n🎉 ¡Todas las pruebas de nuevas funcionalidades completadas!');
    console.log('\n📋 Resumen de funcionalidades probadas:');
    console.log('✅ Resta de pines: OK');
    console.log('✅ Eliminación de control de pines: OK');
    console.log('✅ Validaciones de casos extremos: OK');
    console.log('✅ Logs de acciones: OK');

  } catch (error) {
    console.error('❌ Error en las pruebas:', error);
    console.error('Stack:', error.stack);
  }
}

// Ejecutar pruebas
testNewFeatures();
