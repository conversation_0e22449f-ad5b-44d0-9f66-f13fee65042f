import React from 'react';
import { FaBell, FaExclamationTriangle, FaCheckCircle } from 'react-icons/fa';

/**
 * Componente de prueba simple para el centro de notificaciones
 */
const TestNotificationCenter = ({ psychologistId, className = '' }) => {
  // Datos de prueba
  const mockNotifications = [
    {
      id: 1,
      type: 'warning',
      title: 'Pines bajos',
      message: 'Te quedan solo 3 pines disponibles',
      timestamp: new Date(),
      read: false
    },
    {
      id: 2,
      type: 'success',
      title: 'Pines asignados',
      message: 'Se han asignado 50 pines a tu cuenta',
      timestamp: new Date(Date.now() - 3600000), // 1 hora atrás
      read: true
    },
    {
      id: 3,
      type: 'info',
      title: 'Informe generado',
      message: 'Se ha generado un informe exitosamente',
      timestamp: new Date(Date.now() - 7200000), // 2 horas atrás
      read: false
    }
  ];

  const getIcon = (type) => {
    switch (type) {
      case 'warning':
        return <FaExclamationTriangle className="text-yellow-500" />;
      case 'success':
        return <FaCheckCircle className="text-green-500" />;
      case 'info':
      default:
        return <FaBell className="text-blue-500" />;
    }
  };

  const getTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (hours === 0) {
      return 'Hace unos minutos';
    } else if (hours === 1) {
      return 'Hace 1 hora';
    } else {
      return `Hace ${hours} horas`;
    }
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      <div className="px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-900">Centro de Notificaciones</h3>
          <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
            {mockNotifications.filter(n => !n.read).length} nuevas
          </span>
        </div>
      </div>
      
      <div className="max-h-64 overflow-y-auto">
        {mockNotifications.length === 0 ? (
          <div className="text-center py-8">
            <FaBell className="text-gray-400 text-2xl mb-2 mx-auto" />
            <p className="text-gray-500 text-sm">No hay notificaciones</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {mockNotifications.map((notification) => (
              <div 
                key={notification.id}
                className={`p-4 hover:bg-gray-50 transition-colors ${
                  !notification.read ? 'bg-blue-50' : ''
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    {getIcon(notification.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className={`text-sm font-medium ${
                        !notification.read ? 'text-gray-900' : 'text-gray-700'
                      }`}>
                        {notification.title}
                      </p>
                      {!notification.read && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      {notification.message}
                    </p>
                    <p className="text-xs text-gray-500 mt-2">
                      {getTimeAgo(notification.timestamp)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
        <button className="text-xs text-blue-600 hover:text-blue-800 font-medium">
          Ver todas las notificaciones
        </button>
      </div>
    </div>
  );
};

export default TestNotificationCenter;
