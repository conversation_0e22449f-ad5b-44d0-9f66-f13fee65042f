import React from 'react';
import { Card, CardHeader, CardBody } from '../../../components/ui/Card';

const ResumenGeneral = ({ estadisticas, resultados }) => {
  // Calcular estadísticas si no vienen del servicio
  // Incluir concentración en el conteo solo si existe atención
  const hasAtencion = resultados?.some(r => r.aptitudes?.codigo === 'A' || r.aptitud?.codigo === 'A');
  const hasConcentracion = resultados?.some(r => r.aptitudes?.codigo === 'C' || r.aptitud?.codigo === 'C');

  // Filtrar resultados para el conteo correcto
  const resultadosParaConteo = resultados?.filter(r => {
    const codigo = r.aptitudes?.codigo || r.aptitud?.codigo;
    // Incluir concentración solo si existe atención
    if (codigo === 'C') {
      return hasAtencion;
    }
    return true;
  }) || [];

  const stats = estadisticas || {
    total_tests: resultadosParaConteo.length,
    percentil_promedio: resultadosParaConteo.length > 0 ?
      Math.round(resultadosParaConteo.reduce((sum, r) => sum + (r.percentil || 0), 0) / resultadosParaConteo.length) : 0,
    aptitudes_altas: resultadosParaConteo.filter(r => (r.percentil || 0) >= 75).length,
    aptitudes_bajas: resultadosParaConteo.filter(r => (r.percentil || 0) <= 25).length
  };

  return (
    <Card className="mb-6 print-summary-card">
      <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white border-b-0 print-header">
        <div className="flex items-center">
          <div className="bg-white bg-opacity-20 p-3 rounded-full mr-4 print-icon">
            <i className="fas fa-chart-pie text-2xl text-white print-icon-color"></i>
          </div>
          <div>
            <h3 className="text-xl font-bold print-title">Resumen General</h3>
            <p className="text-blue-100 text-sm print-subtitle">Estadísticas generales de la evaluación</p>
          </div>
        </div>
      </CardHeader>
      <CardBody className="print-stats-body">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 print-stats-grid">
          <div className="text-center p-4 bg-blue-50 rounded-lg print-stat-box print-stat-blue">
            <div className="text-3xl font-bold text-blue-600 print-stat-number">
              {stats.total_tests || 0}
            </div>
            <div className="text-sm text-gray-600 mt-1 print-stat-label">Tests Completados</div>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg print-stat-box print-stat-green">
            <div className="text-3xl font-bold text-green-600 print-stat-number">
              {stats.percentil_promedio || 0}
            </div>
            <div className="text-sm text-gray-600 mt-1 print-stat-label">Percentil Promedio</div>
          </div>

          <div className="text-center p-4 bg-purple-50 rounded-lg print-stat-box print-stat-purple">
            <div className="text-3xl font-bold text-purple-600 print-stat-number">
              {stats.aptitudes_altas || 0}
            </div>
            <div className="text-sm text-gray-600 mt-1 print-stat-label">Aptitudes Altas</div>
          </div>

          <div className="text-center p-4 bg-orange-50 rounded-lg print-stat-box print-stat-orange">
            <div className="text-3xl font-bold text-orange-600 print-stat-number">
              {stats.aptitudes_bajas || 0}
            </div>
            <div className="text-sm text-gray-600 mt-1 print-stat-label">A Reforzar</div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default ResumenGeneral;