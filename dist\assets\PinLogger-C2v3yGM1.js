import{s as _}from"./index-Bdl1jgS_.js";const E={development:{CACHE_TTL:6e4,BATCH_SIZE:10,LOG_LEVEL:"debug"},production:{CACHE_TTL:3e5,BATCH_SIZE:100,LOG_LEVEL:"info"}},e=E.production||E.development,T={ENV:e,RPC_FUNCTIONS:{GET_ALL_PSYCHOLOGISTS_PIN_BALANCE:"get_all_psychologists_pin_balance",GET_PIN_CONSUMPTION_STATS:"get_pin_consumption_stats",CREATE_LOW_PIN_NOTIFICATION:"create_low_pin_notification",CREATE_PIN_EXHAUSTED_NOTIFICATION:"create_pin_exhausted_notification"},DEFAULTS:{ASSIGNED_PATIENTS:0,COMPLETED_TESTS:0,PLAN_TYPE:"none",TOTAL_PINS:0,USED_PINS:0},THRESHOLDS:{LOW_PIN_WARNING:5,CRITICAL_PIN_WARNING:2,NO_PINS:0,BULK_ASSIGNMENT_MIN:10,BULK_ASSIGNMENT_MAX:1e3},STATUS:{UNLIMITED:"unlimited",ACTIVE:"active",LOW_PINS:"low_pins",NO_PINS:"no_pins",INACTIVE:"inactive"},PLAN_TYPES:{UNLIMITED:"unlimited",ASSIGNED:"assigned",TRIAL:"trial",NONE:"none"},ACTION_TYPES:{PIN_ASSIGNED:"pin_assigned",PIN_CONSUMED:"pin_consumed",TEST_COMPLETED:"test_completed",REPORT_GENERATED:"report_generated",NOTIFICATION_CREATED:"notification_created"},ERROR_CODES:{PSYCHOLOGIST_NOT_FOUND:"PSYCHOLOGIST_NOT_FOUND",NO_PINS_AVAILABLE:"NO_PINS_AVAILABLE",INVALID_PARAMETERS:"INVALID_PARAMETERS",SERVICE_UNAVAILABLE:"SERVICE_UNAVAILABLE"},DEFAULTS:{HISTORY_LIMIT:50,CACHE_TTL:e.CACHE_TTL,RETRY_ATTEMPTS:3,BATCH_SIZE:e.BATCH_SIZE,LOG_LEVEL:e.LOG_LEVEL,CONNECTION_TIMEOUT:3e4,QUERY_TIMEOUT:1e4},PERFORMANCE:{ENABLE_CACHING:!0,ENABLE_BATCH_OPERATIONS:!0,MAX_CONCURRENT_OPERATIONS:10,DEBOUNCE_DELAY:300,THROTTLE_LIMIT:100},MONITORING:{HEALTH_CHECK_INTERVAL:6e4,METRICS_COLLECTION_INTERVAL:3e5,ERROR_THRESHOLD:.05,RESPONSE_TIME_THRESHOLD:2e3}};class t{static logAction(E,e){return t=this,n=arguments,i=function*(E,e,t={},n=null,i=null,N=null){try{const{error:I}=yield _.from("pin_usage_logs").insert({psychologist_id:E,patient_id:n,test_session_id:i,report_id:N,action_type:e,pins_before:t.pins_before||0,pins_after:t.pins_after||0,pins_consumed:e===T.ACTION_TYPES.PIN_CONSUMED?1:0,description:this.getActionDescription(e,t),metadata:t});if(I)throw I}catch(I){throw I}},new Promise((_,E)=>{var e=_=>{try{N(i.next(_))}catch(e){E(e)}},T=_=>{try{N(i.throw(_))}catch(e){E(e)}},N=E=>E.done?_(E.value):Promise.resolve(E.value).then(e,T);N((i=i.apply(t,n)).next())});var t,n,i}static getActionDescription(_,E){switch(_){case T.ACTION_TYPES.PIN_ASSIGNED:return`Assigned ${E.pins_assigned||0} pins${E.is_unlimited?" (unlimited plan)":""}`;case T.ACTION_TYPES.PIN_CONSUMED:return E.is_unlimited?"Pin consumed (unlimited plan)":`Pin consumed. ${E.pins_after||0} pins remaining`;case T.ACTION_TYPES.TEST_COMPLETED:return"Test completed - Pin consumed automatically";case T.ACTION_TYPES.REPORT_GENERATED:return"Report generated - Pin consumed automatically";default:return`Action: ${_}`}}static logInfo(_,E=null){}static logError(_,E=null){}static logSuccess(_,E=null){}}export{t as P,T as a};
