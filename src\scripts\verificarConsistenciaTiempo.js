/**
 * @file verificarConsistenciaTiempo.js
 * @description Script para verificar la consistencia de datos de tiempo entre 
 * el módulo "Resultados de Tests Aplicados" y el informe PDF
 */

import supabase from '../api/supabaseClient.js';

/**
 * Función para formatear tiempo como en TestResultsCharts (ACTUALIZADA: solo minutos)
 */
const formatTimeTestResults = (seconds) => {
  const minutes = Math.round(seconds / 60);
  return `${minutes} min`;
};

/**
 * Función para formatear tiempo como en el informe PDF
 */
const formatTimeInforme = (seconds) => {
  return `${Math.round(seconds / 60)} min`;
};

/**
 * Verificar consistencia de datos de tiempo
 */
const verificarConsistenciaTiempo = async () => {
  try {
    console.log('🔍 VERIFICACIÓN DE CONSISTENCIA DE TIEMPO');
    console.log('==========================================');

    // 1. Obtener datos de la tabla resultados
    const { data: resultados, error } = await supabase
      .from('resultados')
      .select(`
        id,
        puntaje_directo,
        percentil,
        errores,
        tiempo_segundos,
        concentracion,
        created_at,
        pacientes:paciente_id (
          id,
          nombre,
          apellido,
          documento
        ),
        aptitudes:aptitud_id (
          codigo,
          nombre,
          descripcion
        )
      `)
      .not('puntaje_directo', 'is', null)
      .not('percentil', 'is', null)
      .order('created_at', { ascending: false })
      .limit(10); // Limitar a 10 para prueba

    if (error) {
      console.error('❌ Error al obtener datos:', error);
      return;
    }

    console.log(`✅ Datos obtenidos: ${resultados.length} resultados`);
    console.log('');

    // 2. Verificar estructura de datos
    console.log('📊 ESTRUCTURA DE DATOS:');
    console.log('======================');
    
    resultados.forEach((resultado, index) => {
      const paciente = resultado.pacientes;
      const aptitud = resultado.aptitudes;
      
      console.log(`\n${index + 1}. ${paciente?.nombre} ${paciente?.apellido} - ${aptitud?.codigo} (${aptitud?.nombre})`);
      console.log(`   📋 ID: ${resultado.id}`);
      console.log(`   📊 PD: ${resultado.puntaje_directo} | PC: ${resultado.percentil}`);
      console.log(`   ❌ Errores: ${resultado.errores || 0}`);
      console.log(`   ⏱️  Tiempo (segundos): ${resultado.tiempo_segundos || 0}`);
      console.log(`   📅 Fecha: ${new Date(resultado.created_at).toLocaleDateString('es-ES')}`);
      
      // Verificar campo de tiempo
      if (resultado.tiempo_segundos === null || resultado.tiempo_segundos === undefined) {
        console.log(`   ⚠️  ADVERTENCIA: tiempo_segundos es NULL o undefined`);
      } else if (resultado.tiempo_segundos === 0) {
        console.log(`   ⚠️  ADVERTENCIA: tiempo_segundos es 0`);
      } else {
        console.log(`   ✅ tiempo_segundos tiene valor válido`);
      }
    });

    // 3. Comparar formatos de tiempo
    console.log('\n\n🕒 COMPARACIÓN DE FORMATOS DE TIEMPO:');
    console.log('====================================');
    
    resultados.forEach((resultado, index) => {
      const tiempoSegundos = resultado.tiempo_segundos || 0;
      const paciente = resultado.pacientes;
      const aptitud = resultado.aptitudes;
      
      console.log(`\n${index + 1}. ${paciente?.nombre} - ${aptitud?.codigo}:`);
      console.log(`   🔢 Tiempo original (segundos): ${tiempoSegundos}`);
      console.log(`   📊 Formato TestResultsCharts: ${formatTimeTestResults(tiempoSegundos)}`);
      console.log(`   📄 Formato Informe PDF: ${formatTimeInforme(tiempoSegundos)}`);

      // Verificar consistencia (ahora ambos usan el mismo formato)
      const formatoTestResults = formatTimeTestResults(tiempoSegundos);
      const formatoInforme = formatTimeInforme(tiempoSegundos);

      if (formatoTestResults === formatoInforme) {
        console.log(`   ✅ CONSISTENTE: Ambos muestran ${formatoTestResults}`);
      } else {
        console.log(`   ❌ INCONSISTENTE: TestResults=${formatoTestResults}, Informe=${formatoInforme}`);
      }
    });

    // 4. Verificar campos disponibles en la tabla
    console.log('\n\n🗃️  CAMPOS DISPONIBLES EN LA TABLA:');
    console.log('==================================');
    
    if (resultados.length > 0) {
      const primerResultado = resultados[0];
      const campos = Object.keys(primerResultado);
      
      console.log('Campos encontrados:');
      campos.forEach(campo => {
        const valor = primerResultado[campo];
        const tipo = typeof valor;
        console.log(`   • ${campo}: ${tipo} = ${valor}`);
      });
      
      // Verificar campos relacionados con tiempo
      const camposTiempo = campos.filter(campo => 
        campo.toLowerCase().includes('tiempo') || 
        campo.toLowerCase().includes('duracion') ||
        campo.toLowerCase().includes('time')
      );
      
      console.log('\nCampos relacionados con tiempo:');
      if (camposTiempo.length > 0) {
        camposTiempo.forEach(campo => {
          console.log(`   ⏱️  ${campo}: ${primerResultado[campo]}`);
        });
      } else {
        console.log('   ⚠️  No se encontraron otros campos relacionados con tiempo');
      }
    }

    // 5. Resumen y recomendaciones
    console.log('\n\n📋 RESUMEN Y RECOMENDACIONES:');
    console.log('=============================');
    
    const resultadosConTiempo = resultados.filter(r => r.tiempo_segundos > 0);
    const resultadosSinTiempo = resultados.filter(r => !r.tiempo_segundos || r.tiempo_segundos === 0);
    
    console.log(`✅ Resultados con tiempo válido: ${resultadosConTiempo.length}/${resultados.length}`);
    console.log(`⚠️  Resultados sin tiempo: ${resultadosSinTiempo.length}/${resultados.length}`);
    
    if (resultadosSinTiempo.length > 0) {
      console.log('\n❌ PROBLEMA IDENTIFICADO:');
      console.log('   Algunos resultados no tienen tiempo registrado');
      console.log('   Esto causará que el informe PDF muestre 0 minutos');
      
      console.log('\n🔧 SOLUCIÓN RECOMENDADA:');
      console.log('   1. Verificar que el campo tiempo_segundos se esté guardando correctamente');
      console.log('   2. Revisar el proceso de inserción de datos en testResultsService.js');
      console.log('   3. Asegurar que el tiempo se capture durante la evaluación');
    } else {
      console.log('\n✅ ESTADO: Todos los resultados tienen tiempo registrado');
    }
    
    console.log('\n🎯 CAMPO CONFIRMADO:');
    console.log('   • Tabla: resultados');
    console.log('   • Campo: tiempo_segundos');
    console.log('   • Tipo: integer (segundos)');
    console.log('   • Uso en TestResultsCharts: formatTime(result.tiempo_segundos || 0) → "X min"');
    console.log('   • Uso en Informe PDF: Math.round(result.tiempo_segundos / 60) → "X min"');
    console.log('   • FORMATO UNIFICADO: Ambos muestran solo minutos redondeados');

  } catch (error) {
    console.error('❌ Error durante la verificación:', error);
  }
};

// Ejecutar verificación si se llama directamente
if (import.meta.url === `file://${process.argv[1]}`) {
  verificarConsistenciaTiempo();
}

export default verificarConsistenciaTiempo;
