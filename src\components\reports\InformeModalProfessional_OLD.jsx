/**
 * @file InformeModalProfessional.jsx
 * @description Professional modal component for displaying psychological evaluation reports
 * with clean, clinical design following psychological assessment standards
 */

import React, { memo, useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { FaTimes, FaDownload, FaPrint, FaFileAlt } from 'react-icons/fa';
import { generateInformePDF } from '../../utils/pdfGenerator';
import { Button } from '../ui/Button';
import InformesService from '../../services/InformesService';
import { toast } from 'react-toastify';
import { useInformeData } from '../../hooks/useInformeData';
import { useInterpretacionesReales } from '../../hooks/useInterpretacionesReales';
import InformePrintableContent from './InformePrintableContent';
import { testPrintInDevelopment, applyTestPrintStyles } from '../../utils/printTestUtils';
import { applyAllInlineStyles } from '../../utils/printStylesInline';
import './InformeModalProfessional.css';
import '../../styles/informe-print-ultra-robust.css';

/**
 * Professional Modal component for displaying patient reports with formal design
 */
const InformeModalProfessional = memo(({ isOpen, onClose, reportData, patient, results }) => {
  const [loading, setLoading] = useState(false);
  const [reportContent, setReportContent] = useState(null);
  const printRef = useRef();

  // Usar el custom hook para procesar los datos
  const {
    normalizedResultsData,
    intelligenceIndices,
    generalStats,
    getAptitudeConfig,
    getPercentilLevel
  } = useInformeData(results);

  // Hook para obtener interpretaciones reales
  const { interpretaciones, loading: interpretacionesLoading, obtenerInterpretacion } = useInterpretacionesReales(normalizedResultsData);

  // Definir patientData temprano para evitar errores de inicialización
  const patientData = reportContent?.contenido?.paciente || reportContent?.pacientes || patient;

  useEffect(() => {
    if (isOpen && reportData) {
      setReportContent(reportData);
      setLoading(false);
    } else if (isOpen && patient && results) {
      generateReportFromPatientData();
    }
  }, [isOpen, reportData, patient, results]);

  const generateReportFromPatientData = async () => {
    if (!patient || !results) return;

    setLoading(true);
    try {
      const report = await InformesService.generarInformeCompleto(
        patient.id,
        `Informe BAT-7 - ${patient.nombre} ${patient.apellido}`,
        'Informe psicológico completo generado automáticamente'
      );
      setReportContent(report);
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('Error al generar el informe');
    } finally {
      setLoading(false);
    }
  };

  // Estado para controlar la generación de PDF
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  // Función para generar PDF con html2canvas + jsPDF
  const handleGeneratePDF = async () => {
    if (!printRef.current || isGeneratingPDF) {
      console.warn('⚠️ [InformeModal] No hay contenido para generar PDF o ya se está generando');
      return;
    }

    setIsGeneratingPDF(true);

    try {
      console.log('🎯 [InformeModal] Iniciando generación de PDF ultra-preciso...');

      // Aplicar todos los estilos inline antes de la captura
      console.log('🔧 [InformeModal] Aplicando estilos ultra-robustos...');
      if (applyAllInlineStyles) {
        applyAllInlineStyles(printRef.current);
      }

      // Generar PDF con máxima fidelidad visual
      await generateInformePDF(printRef.current, patientData, {
        margin: 10,
        html2canvasOptions: {
          scale: 2, // Alta resolución
          useCORS: true,
          backgroundColor: '#ffffff',
          logging: process.env.NODE_ENV === 'development'
        },
        onBeforeCapture: async (element) => {
          console.log('📸 [InformeModal] Preparando captura...');

          // Diagnóstico final antes de captura
          const diagnostics = {
            blueHeaders: element.querySelectorAll('.print-header, .bg-blue-600, .bg-gradient-to-r'),
            aptitudeIcons: element.querySelectorAll('.bg-orange-500, .bg-red-500, .bg-green-500, .bg-yellow-500, .bg-gray-600'),
            percentileBars: element.querySelectorAll('.h-6[style*="backgroundColor"]'),
            allElements: element.querySelectorAll('*')
          };

          console.log('🔍 [InformeModal] DIAGNÓSTICO PRE-CAPTURA:');
          console.log(`📊 Headers azules: ${diagnostics.blueHeaders.length}`);
          console.log(`🎨 Iconos de aptitudes: ${diagnostics.aptitudeIcons.length}`);
          console.log(`📈 Barras de percentiles: ${diagnostics.percentileBars.length}`);
          console.log(`🌐 Total elementos: ${diagnostics.allElements.length}`);

          // Verificar algunos elementos críticos
          diagnostics.blueHeaders.forEach((header, index) => {
            const styles = window.getComputedStyle(header);
            console.log(`🔵 Header ${index + 1}: bg=${styles.backgroundColor}, color=${styles.color}`);
          });
        }
      });

      toast.success('✅ PDF generado exitosamente', {
        position: "top-right",
        autoClose: 3000,
      });

    } catch (error) {
      console.error('❌ [InformeModal] Error generando PDF:', error);
      toast.error(`❌ Error generando PDF: ${error.message}`, {
        position: "top-right",
        autoClose: 5000,
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // Función legacy para compatibilidad (ahora usa la nueva implementación)
  const handlePrint = () => {
    console.log('🖨️ [InformeModal] Redirigiendo a generación de PDF...');
    handleGeneratePDF();
  };

  // Función para descargar PDF (alias de handleGeneratePDF)
  const handleDownload = () => {
    console.log('💾 [InformeModal] Descargando PDF...');
    handleGeneratePDF();
  };

        /* ========================================
           RESULTADOS GRÁFICOS POR APTITUD
           ======================================== */

        /* Tablas de resultados */
        table, thead, tbody, tr, td, th {
          border-collapse: collapse !important;
          width: 100% !important;
          visibility: visible !important;
        }

        thead th {
          background: #1f2937 !important;
          background-color: #1f2937 !important;
          color: white !important;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
          font-weight: 700 !important;
          text-align: center !important;
          padding: 0.75rem 1rem !important;
        }

        tbody td {
          padding: 1rem !important;
          border-bottom: 1px solid #e5e7eb !important;
          vertical-align: middle !important;
        }

        /* Barras de percentiles */
        .bg-gray-200 {
          background: #e5e7eb !important;
          background-color: #e5e7eb !important;
          border-radius: 9999px !important;
          height: 1.5rem !important;
          width: 100% !important;
          position: relative !important;
          overflow: hidden !important;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        .h-6[style*="backgroundColor"] {
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
          color: white !important;
          font-weight: 700 !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          border-radius: 9999px !important;
          height: 1.5rem !important;
        }

        .h-6[style*="backgroundColor"] span {
          color: white !important;
          font-weight: 700 !important;
          font-size: 0.875rem !important;
        }

        /* Leyenda de niveles */
        .w-4.h-4 {
          width: 1rem !important;
          height: 1rem !important;
          border-radius: 0.25rem !important;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
          display: inline-block !important;
          margin-right: 0.5rem !important;
        }

        /* Iconos circulares de aptitudes */
        .w-12.h-12 {
          width: 3rem !important;
          height: 3rem !important;
          border-radius: 50% !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        /* Números de percentiles grandes */
        .text-4xl {
          font-size: 2.25rem !important;
          font-weight: 700 !important;
          color: black !important;
        }

        /* Evitar saltos de página */
        .print-keep-together, .bg-white.border.border-gray-200.rounded-lg {
          page-break-inside: avoid !important;
        }

        /* Ocultar elementos no deseados */
        .print-hide, .no-print, button:not(.print-keep) {
          display: none !important;
        }
      }
    `,
    onBeforeGetContent: () => {
      console.log('🎯 [ULTRA-ROBUST] Preparando contenido para impresión con sistema ultra-robusto...');

      if (printRef.current) {
        const printElement = printRef.current;

        // SISTEMA ULTRA-ROBUSTO: Aplicar estilos inline con máxima compatibilidad
        console.log('🔧 [ULTRA-ROBUST] Aplicando sistema de estilos ultra-robusto...');
        applyAllInlineStyles(printElement);

        // DIAGNÓSTICO ULTRA-DETALLADO
        const diagnostics = {
          blueHeaders: printElement.querySelectorAll('.print-header, .bg-blue-600, .bg-gradient-to-r'),
          aptitudeIcons: printElement.querySelectorAll('.bg-orange-500, .bg-red-500, .bg-green-500, .bg-yellow-500, .bg-gray-600'),
          boldTexts: printElement.querySelectorAll('.font-bold'),
          normalText: printElement.querySelectorAll('p:not(.print-header p):not(.bg-blue-600 p)'),
          allElements: printElement.querySelectorAll('*')
        };

        console.log('🔍 [ULTRA-ROBUST] DIAGNÓSTICO ULTRA-DETALLADO:');
        console.log(`📊 Headers azules: ${diagnostics.blueHeaders.length}`);
        console.log(`🎨 Iconos de aptitudes: ${diagnostics.aptitudeIcons.length}`);
        console.log(`📝 Textos en negrita: ${diagnostics.boldTexts.length}`);
        console.log(`📄 Texto normal: ${diagnostics.normalText.length}`);
        console.log(`🌐 Total elementos: ${diagnostics.allElements.length}`);

        // Verificar colores aplicados
        diagnostics.blueHeaders.forEach((header, index) => {
          const computedStyle = window.getComputedStyle(header);
          const inlineStyle = header.style;
          console.log(`🔵 Header ${index + 1}:`);
          console.log(`  - Computed Background: ${computedStyle.backgroundColor}`);
          console.log(`  - Inline Background: ${inlineStyle.backgroundColor}`);
          console.log(`  - Computed Color: ${computedStyle.color}`);
          console.log(`  - Inline Color: ${inlineStyle.color}`);
          console.log(`  - Print Color Adjust: ${inlineStyle.WebkitPrintColorAdjust || inlineStyle.printColorAdjust}`);
        });

        // Verificar iconos de aptitudes
        diagnostics.aptitudeIcons.forEach((icon, index) => {
          const computedStyle = window.getComputedStyle(icon);
          const inlineStyle = icon.style;
          console.log(`🎨 Icono ${index + 1}:`);
          console.log(`  - Computed Background: ${computedStyle.backgroundColor}`);
          console.log(`  - Inline Background: ${inlineStyle.backgroundColor}`);
          console.log(`  - Print Color Adjust: ${inlineStyle.WebkitPrintColorAdjust || inlineStyle.printColorAdjust}`);
        });

        // Verificar texto normal
        const sampleNormalText = Array.from(diagnostics.normalText).slice(0, 3);
        sampleNormalText.forEach((text, index) => {
          const computedStyle = window.getComputedStyle(text);
          const inlineStyle = text.style;
          console.log(`📄 Texto normal ${index + 1}:`);
          console.log(`  - Computed Color: ${computedStyle.color}`);
          console.log(`  - Inline Color: ${inlineStyle.color}`);
          console.log(`  - Content: "${text.textContent?.substring(0, 50)}..."`);
        });

        // En desarrollo, ejecutar diagnóstico adicional
        if (process.env.NODE_ENV === 'development') {
          testPrintInDevelopment(printElement);
          console.log('🧪 [ULTRA-ROBUST] Diagnóstico de desarrollo ejecutado');
        }

        console.log('✅ [ULTRA-ROBUST] Sistema ultra-robusto aplicado exitosamente');
      }
      return Promise.resolve();
    },
    onAfterPrint: () => {
      console.log('✅ [react-to-print] Impresión completada');
      toast.success('Documento preparado para impresión/PDF');
    },
    onPrintError: (errorLocation, error) => {
      console.error('❌ [react-to-print] Error en impresión:', errorLocation, error);
      toast.error('Error al preparar el documento para impresión');
    }
  });

  const handleDownload = () => {
    // Para PDF, usamos la misma función de impresión
    // El usuario puede elegir "Guardar como PDF" en el diálogo de impresión
    handlePrint();
  };

  const getAptitudeLevel = (percentil) => {
    if (percentil >= 90) return { level: 'Superior', color: 'text-green-800', bg: 'bg-green-100' };
    if (percentil >= 75) return { level: 'Alto', color: 'text-blue-800', bg: 'bg-blue-100' };
    if (percentil >= 50) return { level: 'Promedio', color: 'text-gray-800', bg: 'bg-gray-100' };
    if (percentil >= 25) return { level: 'Bajo', color: 'text-orange-800', bg: 'bg-orange-100' };
    return { level: 'Muy Bajo', color: 'text-red-800', bg: 'bg-red-100' };
  };

  if (!isOpen) return null;

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Generando informe profesional...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!reportContent) return null;





  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[95vh] overflow-hidden shadow-2xl">
        {/* Header con botones de acción */}
        <div className="bg-white border-b-2 border-gray-800 text-gray-800 p-6">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="flex items-center mb-2">
                <FaFileAlt className="text-2xl text-gray-700 mr-3" />
                <h1 className="text-3xl font-bold text-gray-900 tracking-tight">INFORME PSICOLÓGICO</h1>
              </div>
              <p className="text-gray-600 text-lg font-medium">Batería de Aptitudes Diferenciales y Generales - BAT-7</p>
              <p className="text-gray-500 text-sm mt-1">Evaluación Psicológica Integral</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={handleDownload}
                variant="outline"
                size="sm"
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                <FaDownload className="mr-1" />
                PDF
              </Button>
              <Button
                onClick={handlePrint}
                variant="outline"
                size="sm"
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                <FaPrint className="mr-1" />
                Imprimir
              </Button>
              <Button
                onClick={onClose}
                variant="outline"
                size="sm"
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                <FaTimes />
              </Button>
            </div>
          </div>
        </div>

        {/* Contenido scrolleable con vista previa */}
        <div className="overflow-y-auto max-h-[calc(95vh-120px)]">
          <InformePrintableContent
            ref={printRef}
            patientData={patientData}
            normalizedResultsData={normalizedResultsData}
            intelligenceIndices={intelligenceIndices}
            generalStats={generalStats}
            getAptitudeConfig={getAptitudeConfig}
            getPercentilLevel={getPercentilLevel}
            reportContent={reportContent}
            interpretacionesLoading={interpretacionesLoading}
            obtenerInterpretacion={obtenerInterpretacion}
          />
        </div>
      </div>
    </div>
  );
});

InformeModalProfessional.displayName = 'InformeModalProfessional';

// PropTypes para validación de tipos
InformeModalProfessional.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  reportData: PropTypes.object,
  patient: PropTypes.shape({
    id: PropTypes.string.isRequired,
    nombre: PropTypes.string.isRequired,
    apellido: PropTypes.string.isRequired,
    documento: PropTypes.string,
    fecha_nacimiento: PropTypes.string,
    edad: PropTypes.number,
    genero: PropTypes.string
  }),
  results: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      aptitud: PropTypes.string,
      percentil: PropTypes.number,
      puntuacion_directa: PropTypes.number,
      tiempo: PropTypes.number,
      errores: PropTypes.number
    })
  )
};

export default InformeModalProfessional;
