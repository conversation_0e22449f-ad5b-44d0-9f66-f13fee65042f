import{r as e,u as s,j as a,O as r,W as t,C as l,D as i,F as n,Q as d}from"./vendor-BqMjyOVw.js";import{U as c}from"./UnifiedAuthService-Dpry20MH.js";import"./index-Bdl1jgS_.js";const o=()=>{const[o,m]=e.useState(""),[x,u]=e.useState(""),[h,g]=e.useState("candidato"),[j,b]=e.useState(!1),[f,v]=e.useState(!1),N=s();return a.jsxs("div",{className:"min-h-screen flex",children:[a.jsxs("div",{className:"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-blue-800 relative overflow-hidden",children:[a.jsxs("div",{className:"absolute inset-0 opacity-10",children:[a.jsx("div",{className:"absolute top-20 left-20 w-32 h-32 border border-white rounded-full"}),a.jsx("div",{className:"absolute bottom-20 right-20 w-24 h-24 border border-white rounded-full"}),a.jsx("div",{className:"absolute top-1/2 left-10 w-16 h-16 border border-white rounded-full"})]}),a.jsxs("div",{className:"relative z-10 flex flex-col justify-center px-12 text-white",children:[a.jsx("div",{className:"mb-12",children:a.jsxs("div",{className:"flex items-center mb-4",children:[a.jsx("div",{className:"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:a.jsx("span",{className:"text-white font-bold text-xl",children:"B"})}),a.jsxs("div",{children:[a.jsx("h1",{className:"text-3xl font-bold",children:"BAT-7"}),a.jsx("p",{className:"text-blue-200",children:"Sistema de Evaluación"})]})]})}),a.jsxs("div",{className:"mb-8",children:[a.jsxs("h2",{className:"text-4xl font-bold mb-4",children:["Bienvenido al",a.jsx("br",{}),a.jsx("span",{className:"text-orange-400",children:"Futuro de la Evaluación"}),a.jsx("br",{}),a.jsx("span",{className:"text-blue-200",children:"Psicométrica"})]}),a.jsx("p",{className:"text-blue-200 text-lg leading-relaxed",children:"Plataforma de nueva generación para evaluaciones psicológicas inteligentes y análisis avanzado"})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:a.jsx("span",{className:"text-white text-sm",children:"✓"})}),a.jsxs("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Evaluaciones Inteligentes"}),a.jsx("p",{className:"text-blue-200 text-sm",children:"Adaptativas y avanzadas"})]})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:a.jsx("span",{className:"text-white text-sm",children:"✓"})}),a.jsxs("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Dashboard Avanzado"}),a.jsx("p",{className:"text-blue-200 text-sm",children:"Análisis en tiempo real"})]})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:a.jsx("span",{className:"text-white text-sm",children:"✓"})}),a.jsxs("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Seguridad Total"}),a.jsx("p",{className:"text-blue-200 text-sm",children:"Protección de datos garantizada"})]})]})]})]})]}),a.jsx("div",{className:"w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50",children:a.jsxs("div",{className:"w-full max-w-md",children:[a.jsxs("div",{className:"lg:hidden text-center mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"BAT-7"}),a.jsx("p",{className:"text-gray-600",children:"Sistema de Evaluación"})]}),a.jsx("div",{className:"bg-white rounded-lg shadow-lg p-8",children:a.jsxs("form",{onSubmit:e=>{return s=null,a=null,r=function*(){e.preventDefault(),b(!0);try{if(!o.trim()||!x.trim())return d.error("Por favor complete todos los campos"),void b(!1);const e=yield c.login(o,x);if(e.success){const s=e.user;if(d.success(`¡Bienvenido ${s.nombre}!`),e.requirePasswordChange)return d.info("Debe cambiar su contraseña temporal"),void N("/student/change-password");switch(s.rol){case"administrador":N("/admin/administration");break;case"psicologo":N("/admin/candidates");break;case"paciente":case"estudiante":N("/student/questionnaire");break;default:N("/home")}}else d.error(e.error||"Error en la autenticación")}catch(s){d.error("Error interno del servidor")}finally{b(!1)}},new Promise((t,l)=>{var i=s=>{try{d(r.next(s))}catch(e){l(e)}},n=s=>{try{d(r.throw(s))}catch(e){l(e)}},d=e=>e.done?t(e.value):Promise.resolve(e.value).then(i,n);d((r=r.apply(s,a)).next())});var s,a,r},className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"TIPO DE USUARIO"}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-orange-50 transition-colors",children:[a.jsx("input",{type:"radio",name:"userType",value:"candidato",checked:"candidato"===h,onChange:e=>g(e.target.value),className:"sr-only"}),a.jsx("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center "+("candidato"===h?"border-gray-500 bg-gray-500":"border-gray-300"),children:"candidato"===h&&a.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-3",children:a.jsx(r,{className:"text-white text-sm"})}),a.jsxs("div",{children:[a.jsx("div",{className:"font-medium text-gray-800",children:"Paciente"}),a.jsx("div",{className:"text-sm text-gray-500",children:"Acceso para realizar evaluaciones psicométricas"})]})]})]}),a.jsxs("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors",children:[a.jsx("input",{type:"radio",name:"userType",value:"psicologo",checked:"psicologo"===h,onChange:e=>g(e.target.value),className:"sr-only"}),a.jsx("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center "+("psicologo"===h?"border-gray-500 bg-gray-500":"border-gray-300"),children:"psicologo"===h&&a.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-3",children:a.jsx(r,{className:"text-white text-sm"})}),a.jsxs("div",{children:[a.jsx("div",{className:"font-medium text-gray-800",children:"Psicólogo"}),a.jsx("div",{className:"text-sm text-gray-500",children:"Acceso para gestionar candidatos y resultados"})]})]})]}),a.jsxs("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors",children:[a.jsx("input",{type:"radio",name:"userType",value:"administrador",checked:"administrador"===h,onChange:e=>g(e.target.value),className:"sr-only"}),a.jsx("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center "+("administrador"===h?"border-orange-500 bg-orange-500":"border-gray-300"),children:"administrador"===h&&a.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-3",children:a.jsx(r,{className:"text-white text-sm"})}),a.jsxs("div",{children:[a.jsx("div",{className:"font-medium text-gray-800",children:"Administrador"}),a.jsx("div",{className:"text-sm text-gray-500",children:"Acceso completo al sistema"})]})]})]})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"EMAIL O DOCUMENTO"}),a.jsxs("div",{className:"relative",children:[a.jsx(r,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),a.jsx("input",{type:"text",value:o,onChange:e=>m(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent",placeholder:"Número de documento",required:!0})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CONTRASEÑA"}),a.jsxs("div",{className:"relative",children:[a.jsx(t,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),a.jsx("input",{type:f?"text":"password",value:x,onChange:e=>u(e.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent",placeholder:"Ingresa tu contraseña",required:!0}),a.jsx("button",{type:"button",onClick:()=>v(!f),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors","aria-label":f?"Ocultar contraseña":"Mostrar contraseña",children:f?a.jsx(l,{className:"h-5 w-5"}):a.jsx(i,{className:"h-5 w-5"})})]})]}),a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",className:"rounded border-gray-300 text-orange-500 focus:ring-orange-500"}),a.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"Recordarme"})]}),a.jsx("a",{href:"#",className:"text-sm text-orange-500 hover:text-orange-600",children:"¿Olvidaste tu contraseña?"})]}),a.jsx("button",{type:"submit",disabled:j,className:"w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center",children:j?a.jsxs(a.Fragment,{children:[a.jsx(n,{className:"animate-spin mr-2"}),"Iniciando sesión..."]}):a.jsxs(a.Fragment,{children:[a.jsx(r,{className:"mr-2"}),"Iniciar Sesión"]})})]})})]})})]})};export{o as default};
