import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjMxMjg0MSwiZXhwIjoyMDYxODg4ODQxfQ.zwk3Wiay5jjeYOrg8B1M6T98B2esCqRFI43At-AFV3A';

// Crear cliente con service key para operaciones administrativas
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createAdminUser() {
  console.log('🔧 Creando usuario administrador...');

  try {
    // 1. Crear usuario en auth.users
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'admin123',
      email_confirm: true,
      user_metadata: {
        nombre: 'Administrador',
        apellido: 'Siste<PERSON>',
        tipo_usuario: 'administrador'
      }
    });

    if (authError) {
      console.error('❌ Error creando usuario en auth:', authError);
      return;
    }

    console.log('✅ Usuario creado en auth.users:', authData.user.id);

    // 2. Crear perfil en tabla usuarios
    const { data: profileData, error: profileError } = await supabase
      .from('usuarios')
      .insert([
        {
          id: authData.user.id,
          documento: '13716261',
          nombre: 'Administrador',
          apellido: 'Sistema',
          tipo_usuario: 'administrador',
          rol: 'administrador',
          activo: true
        }
      ])
      .select()
      .single();

    if (profileError) {
      console.error('❌ Error creando perfil:', profileError);
      return;
    }

    console.log('✅ Perfil creado en usuarios:', profileData);

    console.log('\n🎉 Usuario administrador creado exitosamente!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('👑 Rol: administrador');

  } catch (error) {
    console.error('❌ Error inesperado:', error);
  }
}

// Función para crear psicólogos de prueba
async function createTestPsychologists() {
  console.log('\n👨‍⚕️ Creando psicólogos de prueba...');

  const psychologists = [
    {
      email: '<EMAIL>',
      password: 'psi123',
      nombre: 'Dr. Juan',
      apellido: 'Pérez',
      documento: '12345678'
    },
    {
      email: '<EMAIL>',
      password: 'psi123',
      nombre: 'Dra. María',
      apellido: 'González',
      documento: '87654321'
    },
    {
      email: '<EMAIL>',
      password: 'psi123',
      nombre: 'Dr. Carlos',
      apellido: 'Rodríguez',
      documento: '11223344'
    }
  ];

  for (const psy of psychologists) {
    try {
      // Crear usuario en auth
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: psy.email,
        password: psy.password,
        email_confirm: true,
        user_metadata: {
          nombre: psy.nombre,
          apellido: psy.apellido,
          tipo_usuario: 'psicologo'
        }
      });

      if (authError) {
        console.error(`❌ Error creando ${psy.email}:`, authError);
        continue;
      }

      // Crear perfil en usuarios
      const { error: profileError } = await supabase
        .from('usuarios')
        .insert([
          {
            id: authData.user.id,
            documento: psy.documento,
            nombre: psy.nombre,
            apellido: psy.apellido,
            tipo_usuario: 'psicologo',
            rol: 'psicologo',
            activo: true
          }
        ]);

      if (profileError) {
        console.error(`❌ Error creando perfil para ${psy.email}:`, profileError);
        continue;
      }

      console.log(`✅ Psicólogo creado: ${psy.nombre} ${psy.apellido} (${psy.email})`);

    } catch (error) {
      console.error(`❌ Error inesperado con ${psy.email}:`, error);
    }
  }
}

async function main() {
  await createAdminUser();
  await createTestPsychologists();
  
  console.log('\n🏁 Proceso completado. Ahora puedes:');
  console.log('1. Ir a http://localhost:5173');
  console.log('2. Iniciar sesió<NAME_EMAIL> / admin123');
  console.log('3. Probar la asignación de pines');
}

main().catch(console.error);
