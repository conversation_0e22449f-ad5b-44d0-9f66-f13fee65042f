import { createClient } from '@supabase/supabase-js';

// Función para obtener variables de entorno compatibles con Vite y Node.js
function getEnvVar(name) {
  // En el navegador con Vite
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return import.meta.env[name];
  }

  // En Node.js
  if (typeof process !== 'undefined' && process.env) {
    return process.env[name];
  }

  return undefined;
}

// Función para crear el cliente de Supabase de forma lazy
function createSupabaseClient() {
  // Obtener las variables de entorno
  const supabaseUrl = getEnvVar('VITE_SUPABASE_URL') || getEnvVar('SUPABASE_URL');
  const supabaseAnonKey = getEnvVar('VITE_SUPABASE_ANON_KEY') || getEnvVar('SUPABASE_ANON_KEY');

  // Validar que las variables estén definidas
  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Error: Variables de entorno de Supabase no definidas');
    console.error('VITE_SUPABASE_URL:', supabaseUrl || 'undefined');
    console.error('VITE_SUPABASE_ANON_KEY:', supabaseAnonKey ? '[DEFINIDA]' : '[NO DEFINIDA]');
    console.error('Asegúrate de tener un archivo .env con VITE_SUPABASE_URL y VITE_SUPABASE_ANON_KEY');
    throw new Error('Variables de entorno de Supabase no definidas');
  }

  return createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    }
  });
}

// Variable para almacenar el cliente una vez creado
let _supabaseClient = null;

// Función para obtener el cliente (lazy loading)
function getSupabaseClient() {
  if (!_supabaseClient) {
    _supabaseClient = createSupabaseClient();
  }
  return _supabaseClient;
}

// Exportar el cliente usando un Proxy para lazy loading
export const supabase = new Proxy({}, {
  get(target, prop) {
    const client = getSupabaseClient();
    const value = client[prop];

    // Si es una función, bindearla al cliente
    if (typeof value === 'function') {
      return value.bind(client);
    }

    return value;
  }
});

export default supabase;
