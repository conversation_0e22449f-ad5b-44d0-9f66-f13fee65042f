import React, { useState, useEffect, useRef } from 'react';
import InformesService from '../../services/InformesService';
import InterpretacionesSupabaseService from '../../services/InterpretacionesSupabaseService';
import { toast } from 'react-toastify';
import { useReactToPrint } from 'react-to-print';

// Componentes modulares
import InformeHeader from './sections/InformeHeader';
import InformacionPaciente from './sections/InformacionPaciente';
import ResumenGeneral from './sections/ResumenGeneral';
import ResultadosDetallados from './sections/ResultadosDetallados';
import AnalisisCualitativo from './sections/AnalisisCualitativo';
import InformePrintableContent from '../../components/reports/InformePrintableContent';
import { getAptitudeConfig, getPercentilLevel } from '../../constants/aptitudeConstants';

const InformeViewer = ({ informeId, onClose, mockData }) => {
  // Estados principales
  const [informe, setInforme] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [generandoPDF, setGenerandoPDF] = useState(false);
  const printComponentRef = useRef(null);

  // Estados para la carga completa de datos
  const [interpretaciones, setInterpretaciones] = useState({});
  const [interpretacionesLoading, setInterpretacionesLoading] = useState(false);
  const [isReportReady, setIsReportReady] = useState(false);

  // Datos procesados del informe
  const [normalizedResultsData, setNormalizedResultsData] = useState([]);
  const [intelligenceIndices, setIntelligenceIndices] = useState({});
  const [generalStats, setGeneralStats] = useState({});

  // Función para cargar interpretaciones de todas las aptitudes
  const cargarInterpretaciones = async (resultados) => {
    if (!resultados || resultados.length === 0) return {};

    console.log('🔍 [InformeViewer] Cargando interpretaciones para todas las aptitudes...');
    setInterpretacionesLoading(true);

    const interpretacionesMap = {};

    try {
      for (const resultado of resultados) {
        const aptitudCodigo = resultado.aptitud_codigo || resultado.aptitud;
        const percentil = resultado.percentil || resultado.pc;

        if (aptitudCodigo && percentil !== undefined) {
          console.log(`📖 [InformeViewer] Cargando interpretación para ${aptitudCodigo}-${percentil}`);

          try {
            const interpretacion = await InterpretacionesSupabaseService.obtenerInterpretacionOficial(aptitudCodigo, percentil);
            if (interpretacion) {
              interpretacionesMap[aptitudCodigo] = interpretacion;
              console.log(`✅ [InformeViewer] Interpretación cargada para ${aptitudCodigo}`);
            }
          } catch (error) {
            console.warn(`⚠️ [InformeViewer] Error cargando interpretación para ${aptitudCodigo}:`, error);
          }
        }
      }

      console.log('✅ [InformeViewer] Todas las interpretaciones cargadas:', interpretacionesMap);
      setInterpretaciones(interpretacionesMap);
      return interpretacionesMap;

    } catch (error) {
      console.error('❌ [InformeViewer] Error general cargando interpretaciones:', error);
      return {};
    } finally {
      setInterpretacionesLoading(false);
    }
  };

  // Función para procesar datos del informe
  const procesarDatosInforme = (resultados) => {
    if (!resultados || resultados.length === 0) return;

    console.log('🔄 [InformeViewer] Procesando datos del informe...');

    // Normalizar resultados
    const normalized = resultados.map(resultado => ({
      aptitud: resultado.aptitud_codigo || resultado.aptitud,
      aptitud_codigo: resultado.aptitud_codigo || resultado.aptitud,
      pd: resultado.puntuacion_directa || resultado.pd,
      pc: resultado.percentil || resultado.pc,
      percentil: resultado.percentil || resultado.pc,
      puntuacion_directa: resultado.puntuacion_directa || resultado.pd,
      nivel: getPercentilLevel(resultado.percentil || resultado.pc),
      config: getAptitudeConfig(resultado.aptitud_codigo || resultado.aptitud)
    }));

    // Calcular índices de inteligencia básicos
    const indices = {
      capacidadGeneral: {
        percentil: Math.round(normalized.reduce((sum, r) => sum + r.percentil, 0) / normalized.length),
        nivel: 'Medio'
      },
      inteligenciaFluida: {
        percentil: normalized.find(r => r.aptitud === 'R')?.percentil || 0,
        nivel: 'Medio'
      },
      inteligenciaCristalizada: {
        percentil: normalized.find(r => r.aptitud === 'V')?.percentil || 0,
        nivel: 'Medio'
      }
    };

    // Estadísticas generales
    const stats = {
      totalAptitudes: normalized.length,
      promedioPercentil: Math.round(normalized.reduce((sum, r) => sum + r.percentil, 0) / normalized.length),
      aptitudesEvaluadas: normalized.map(r => r.aptitud)
    };

    setNormalizedResultsData(normalized);
    setIntelligenceIndices(indices);
    setGeneralStats(stats);

    console.log('✅ [InformeViewer] Datos del informe procesados');
  };

  useEffect(() => {
    // Si hay datos mock, usarlos directamente
    if (mockData) {
      console.log('🧪 [InformeViewer] Usando datos mock para pruebas:', mockData);
      setInforme(mockData);
      procesarDatosInforme(mockData.resultados);
      cargarInforme(mockData);
      return;
    }

    // Si no hay datos mock, cargar normalmente
    if (informeId) {
      cargarInforme();
    }
  }, [informeId, mockData]);

  const cargarInforme = async (datosInforme = null) => {
    try {
      setLoading(true);
      setIsReportReady(false);

      let informeData;

      if (datosInforme) {
        // Usar datos proporcionados (mock data)
        informeData = datosInforme;
        console.log('🧪 [InformeViewer] Usando datos proporcionados');
      } else {
        // Cargar desde el servicio
        console.log('🔍 [InformeViewer] Cargando informe desde servicio:', informeId);
        informeData = await InformesService.obtenerInforme(informeId);
        console.log('📄 [InformeViewer] Datos del informe cargados:', informeData);
      }

      // Procesar y estructurar los datos correctamente
      const informeProcesado = {
        id: informeData.id,
        titulo: informeData.titulo,
        tipo: informeData.tipo_informe || informeData.tipo,
        fechaGeneracion: informeData.fecha_generacion || informeData.fechaGeneracion,
        paciente: informeData.contenido?.paciente || informeData.paciente || {},
        resultados: informeData.contenido?.resultados || informeData.resultados || [],
        estadisticas: informeData.contenido?.estadisticas || informeData.estadisticas || {},
        evaluacion: informeData.contenido?.evaluacion || informeData.evaluacion || {},
        metadatos: informeData.metadatos || {}
      };

      console.log('✅ [InformeViewer] Informe procesado:', informeProcesado);
      setInforme(informeProcesado);

      // FASE 1: Procesar datos del informe
      procesarDatosInforme(informeProcesado.resultados);

      // FASE 2: Cargar interpretaciones de todas las aptitudes
      console.log('🔄 [InformeViewer] Iniciando carga completa de interpretaciones...');
      const interpretacionesCargadas = await cargarInterpretaciones(informeProcesado.resultados);

      // FASE 3: Marcar como listo cuando todo esté cargado
      console.log('🎉 [InformeViewer] ¡Informe completamente cargado y listo!');
      setIsReportReady(true);
      setError(null);

    } catch (error) {
      console.error('❌ [InformeViewer] Error cargando informe:', error);
      setError(error.message);
      setIsReportReady(false);
    } finally {
      setLoading(false);
    }
  };

  // Hook de react-to-print para una impresión robusta
  const handlePrint = useReactToPrint({
    content: () => printComponentRef.current,
    documentTitle: `Informe_BAT7_${informe?.paciente?.nombre || 'Paciente'}_${new Date().toLocaleDateString('es-ES').replace(/\//g, '-')}`,
    pageStyle: `
      @media print {
        body {
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }
      }
    `,
    onBeforeGetContent: () => {
      console.log('🖨️ [InformeViewer] Preparando impresión - Datos listos:', isReportReady);
      setGenerandoPDF(true);

      // Ya no necesitamos setTimeout porque todos los datos están cargados
      if (isReportReady) {
        console.log('✅ [InformeViewer] Todos los datos están listos para impresión');
        return Promise.resolve();
      } else {
        console.warn('⚠️ [InformeViewer] Los datos aún no están completamente cargados');
        return Promise.resolve();
      }
    },
    onAfterPrint: () => {
      setGenerandoPDF(false);
      toast.success('Documento preparado para impresión/PDF');
    },
    onPrintError: (error) => {
      console.error('❌ [InformeViewer] Error de impresión:', error);
      toast.error('Error al preparar el documento. Por favor, inténtelo de nuevo.');
      setGenerandoPDF(false);
    }
  });

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-8 rounded-lg shadow-xl">
          <div className="flex items-center">
            <i className="fas fa-spinner fa-spin text-blue-600 text-2xl mr-4"></i>
            <span className="text-lg font-medium text-gray-700">Cargando informe...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white p-8 rounded-lg shadow-xl max-w-md w-full">
          <div className="text-center">
            <i className="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Error al cargar el informe</h3>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="flex space-x-3">
              <button
                onClick={cargarInforme}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                Reintentar
              </button>
              <button
                onClick={onClose}
                className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
              >
                Cerrar
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!informe) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white p-8 rounded-lg shadow-xl max-w-md w-full text-center">
          <i className="fas fa-file-times text-gray-400 text-4xl mb-4"></i>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Informe no encontrado</h3>
          <p className="text-gray-600 mb-6">No se pudo cargar la información del informe solicitado.</p>
          <button
            onClick={onClose}
            className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400"
          >
            Cerrar
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        
        {/* Header del Modal */}
        <InformeHeader
          informe={informe}
          onClose={onClose}
          onGeneratePDF={handlePrint}
          isGeneratingPDF={generandoPDF}
          isReportReady={isReportReady}
          interpretacionesLoading={interpretacionesLoading}
        />

        {/* Contenido del Informe */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="p-6 bg-gray-50">
            {/* Debug: Mostrar toda la información del informe */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mb-4 p-4 bg-yellow-100 border border-yellow-400 rounded max-h-60 overflow-y-auto">
                <h4 className="font-bold text-yellow-800">🔍 Debug - Estructura Completa del Informe:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  <div>
                    <h5 className="font-semibold text-yellow-800">Paciente:</h5>
                    <pre className="text-xs text-yellow-700 bg-yellow-50 p-2 rounded">
                      {JSON.stringify(informe.paciente, null, 2)}
                    </pre>
                  </div>
                  <div>
                    <h5 className="font-semibold text-yellow-800">Resultados ({informe.resultados?.length || 0}):</h5>
                    <pre className="text-xs text-yellow-700 bg-yellow-50 p-2 rounded">
                      {JSON.stringify(informe.resultados?.slice(0, 2), null, 2)}
                    </pre>
                  </div>
                  <div>
                    <h5 className="font-semibold text-yellow-800">Estados:</h5>
                    <pre className="text-xs text-yellow-700 bg-yellow-50 p-2 rounded">
                      {JSON.stringify({
                        isReportReady,
                        interpretacionesLoading,
                        interpretacionesCount: Object.keys(interpretaciones).length
                      }, null, 2)}
                    </pre>
                  </div>
                  <div>
                    <h5 className="font-semibold text-yellow-800">Datos Procesados:</h5>
                    <pre className="text-xs text-yellow-700 bg-yellow-50 p-2 rounded">
                      {JSON.stringify({
                        normalizedCount: normalizedResultsData.length,
                        intelligenceIndices: Object.keys(intelligenceIndices).length,
                        generalStats: Object.keys(generalStats).length
                      }, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            )}

            {/* Información del Paciente */}
            <InformacionPaciente paciente={informe.paciente} />

            {/* Resumen General */}
            <ResumenGeneral estadisticas={informe.estadisticas} resultados={informe.resultados} />

            {/* Resultados Detallados */}
            <ResultadosDetallados resultados={informe.resultados} />

            {/* Análisis Cualitativo con Índices de Inteligencia */}
            <AnalisisCualitativo
              resultados={informe.resultados}
              paciente={informe.paciente}
              normalizedResultsData={normalizedResultsData}
              intelligenceIndices={intelligenceIndices}
              interpretaciones={interpretaciones}
              isReportReady={isReportReady}
            />
          </div>
        </div>

        {/* Contenido Imprimible (Oculto) - La verdadera fuente para el PDF */}
        <div style={{ position: 'absolute', left: '-9999px', top: '0' }}>
          {informe && isReportReady && (
            <InformePrintableContent
              ref={printComponentRef}
              patientData={informe.paciente}
              normalizedResultsData={normalizedResultsData}
              intelligenceIndices={intelligenceIndices}
              generalStats={generalStats}
              reportContent={informe}
              getAptitudeConfig={getAptitudeConfig}
              getPercentilLevel={getPercentilLevel}
              interpretaciones={interpretaciones}
              interpretacionesLoading={false}
              obtenerInterpretacion={(aptitud, percentil) => interpretaciones[aptitud]}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default InformeViewer;