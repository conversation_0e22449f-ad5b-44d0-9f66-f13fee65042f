var e=Object.defineProperty,a=Object.defineProperties,i=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,n=(a,i,r)=>i in a?e(a,i,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[i]=r;import{j as l,v as t,r as c}from"./vendor-CIyllXGj.js";import{C as d,b as m,a as p,d as u,e as b,s as v,f}from"./index-CrXvaDRr.js";const g=()=>l.jsxDEV("div",{className:"flex flex-col justify-center items-center min-h-[400px]",children:[l.jsxDEV("div",{className:"w-16 h-16 border-t-4 border-b-4 border-blue-500 rounded-full animate-spin mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PageLoader.jsx",lineNumber:9,columnNumber:7},void 0),l.jsxDEV("h2",{className:"text-xl font-semibold text-gray-700",children:"Cargando"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PageLoader.jsx",lineNumber:10,columnNumber:7},void 0),l.jsxDEV("p",{className:"text-gray-500 mt-2",children:"Por favor espere un momento..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PageLoader.jsx",lineNumber:11,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PageLoader.jsx",lineNumber:8,columnNumber:5},void 0),N={V:{descripcion:"V evalúa la destreza para formular y comprobar hipótesis acerca de conceptos e ideas expresados verbalmente. Implica cierto grado de conocimiento léxico y la comprensión semántica de nombres, verbos y adjetivos.",altas:["Facilidad para establecer relaciones entre términos lingüísticos.","Nivel alto de pensamiento lógico aplicado a argumentos verbales.","Habilidad para resolver problemas expresados verbalmente, ya sea de forma escrita u oral.","Agilidad con las conexiones semánticas entre conceptos verbales.","Riqueza de vocabulario.","Buena capacidad para comprender y expresar ideas verbalmente de un modo correcto y fluido.","Buen manejo de la información sobre conceptos culturales."],bajas:["Dificultad para comprender las relaciones entre términos y la lógica de argumentos verbales.","Limitaciones para expresarse por escrito u oralmente de un modo preciso y ágil.","Poca amplitud de vocabulario.","Posiblemente, nivel bajo de pensamiento lógico.","Posibles limitaciones en la memoria operativa al considerar parcialmente los términos de las analogías."]},E:{descripcion:"E evalúa la capacidad para visualizar, recordar y transformar mentalmente imágenes visuales en dos y tres dimensiones.",altas:["Facilidad para analizar, sintetizar y manipular mentalmente formas, figuras y objetos (rotarlos, plegarlos, invertirlos, desarrollarlos...).","Habilidad para generar, retener, recordar y transformar imágenes visuales.","Destreza para emparejar estímulos viso-espaciales de dos y tres dimensiones y para descomponer un modelo en sus partes constitutivas.","Capacidad normal o superior para percibir la forma, los ángulos, el tamaño y la orientación de los objetos."],bajas:["Dificultad para analizar, sintetizar y manipular mentalmente formas, figuras y objetos (rotarlos, plegarlos, invertirlos, desarrollarlos...).","Limitaciones para generar, retener, recordar y transformar imágenes visuales.","Poca capacidad para emparejar estímulos viso-espaciales de dos y tres dimensiones y posibles limitaciones a la hora de descomponer un modelo en partes constitutivas.","Posiblemente, dificultades para percibir la forma, los ángulos, el tamaño y la orientación de los objetos."]},A:{descripcion:"A evalúa la habilidad para identificar rápida y selectivamente los aspectos relevantes de un estímulo y para ignorar los irrelevantes. Puede interpretarse como una medida de la velocidad de procesamiento.",altas:["Un nivel alto en la velocidad del procesamiento de las operaciones mentales simples.","Buena capacidad para atender selectiva y secuencialmente a figuras similares.","Aplicación de estrategias eficaces a la hora de realizar comparaciones visuales.","Habilidad para percibir y discriminar con rapidez la configuración perceptiva de las figuras.","Facilidad para motivarse y mantener la activación ante estímulos repetitivos y monótonos.","Posiblemente, un nivel elevado de memoria de trabajo en relación con patrones visuales.","Aptitud para trabajar deprisa bajo presión de tiempo."],bajas:["Baja velocidad del procesamiento de las operaciones mentales simples.","Déficits para atender selectivamente y discriminar entre figuras similares.","Aplicación de estrategias poco eficaces a las comparaciones visuales.","Un nivel de motivación bajo o fatiga en tareas monótonas o repetitivas.","Posiblemente, limitaciones en la memoria de trabajo aplicada a patrones visuales.","Falta de atención y poca conciencia sobre el trabajo bajo presión de tiempo."]},CON:{descripcion:"CON evalúa la precisión del procesamiento de la información visual independiente de la velocidad. Puede interpretarse como una medida de la calidad del procesamiento.",altas:["Buen nivel de concentración ante estímulos monótonos y repetitivos.","Nivel alto de precisión a la hora de percibir y discriminar la configuración perceptiva de las figuras, al margen de la velocidad.","Buena calidad del procesamiento de la información asociada a las operaciones mentales simples.","Posiblemente, un nivel medio o alto de reflexividad."],bajas:["Falta de concentración ante estímulos monótonos y repetitivos.","Poca precisión a la hora de percibir y discriminar la configuración perceptiva de las figuras, al margen de la velocidad.","Limitaciones en la calidad del procesamiento de la información asociada a las operaciones mentales simples.","Posiblemente, un nivel alto de impulsividad o poca motivación ante la tarea."]},R:{descripcion:"R evalúa la capacidad para resolver problemas novedosos aplicando leyes lógicas de tipo deductivo y estableciendo correlatos entre figuras abstractas.",altas:["Un nivel alto de razonamiento no verbal de tipo deductivo.","Habilidad para solucionar problemas abstractos y razonar con situaciones novedosas, complejas y poco familiares.","Facilidad para establecer y trabajar con secuencias.","Buena capacidad para identificar y deducir las leyes lógicas que rigen las variaciones seriales de tipo abstracto.","Habilidad para usar la mediación verbal en la formulación y comprobación de hipótesis lógicas.","Destreza para almacenar en la memoria visual a corto plazo el resultado de aplicar secuencias lógicas.","Posiblemente, una buena capacidad para analizar patrones viso-espaciales en términos de percepción de la forma, el tamaño relativo y la posición."],bajas:["Un nivel bajo de razonamiento no verbal de tipo hipotético-deductivo.","Limitaciones a la hora de solucionar problemas y enfrentarse a situaciones novedosas, complejas o poco familiares.","Poca capacidad para identificar y deducir secuencias.","Incapacidad para usar estrategias eficaces de mediación verbal en la resolución de problemas simbólicos y abstractos.","Posible dependencia de las instrucciones verbales o falta de flexibilidad para elegir estrategias resolutivas.","Posibles limitaciones en la memoria de trabajo o visual a corto plazo y en el análisis perceptivo de patrones viso-espaciales."]},N:{descripcion:"N evalúa la capacidad para razonar de modo inductivo o deductivo con conceptos matemáticos en términos de relaciones y propiedades.",altas:["Un nivel alto de razonamiento cuantitativo de tipo deductivo e inductivo.","Facilidad para identificar las reglas que gobiernan las relaciones numéricas o formales y la formulación y comprobación de hipótesis sobre esas reglas apoyándose en la mediación verbal.","Buena capacidad analítica para separar un problema en sus partes componentes.","Agilidad para recuperar la información de la memoria a largo plazo.","Buen nivel de conocimiento de cifras, hechos numéricos y operaciones aritméticas."],bajas:["Un nivel bajo de razonamiento cuantitativo de tipo deductivo e inductivo.","Dificultades para identificar las reglas que gobiernan las relaciones numéricas o formales y para apoyarse en la mediación verbal para la resolución de problemas.","Limitaciones para separar un problema en sus partes constituyentes.","Posiblemente, carencias en los conocimientos almacenados sobre cifras, hechos numéricos u operaciones aritméticas."]},M:{descripcion:"M evalúa el grado de comprensión de los principios mecánicos relacionados con el equilibrio y el movimiento de los cuerpos sometidos a cualquier fuerza.",altas:["Nivel alto de comprensión de las leyes físico-mecánicas.","Facilidad para representarse mentalmente la trayectoria de los objetos sometidos a una fuerza.","Habilidad para formular y poner a prueba hipótesis sobre la resistencia de los materiales y el desplazamiento y equilibrio de los cuerpos.","Buena comprensión de los problemas expresados de forma gráfica y verbal.","Posiblemente, agilidad a la hora de recuperar la información de la memoria a largo plazo y de percibir y discriminar la configuración perceptiva de las situaciones."],bajas:["Poca comprensión de las leyes físico-mecánicas.","Dificultades para representarse mentalmente la trayectoria de los objetos sometidos a una fuerza.","Limitaciones para prever la resistencia de los materiales y el desplazamiento y equilibrio de los cuerpos.","Posible nivel bajo de comprensión de los problemas expresados de forma gráfica y verbal.","Posibles limitaciones para recuperar la información de la memoria a largo plazo y para percibir y discriminar la configuración perceptiva de las situaciones."]},O:{descripcion:"O evalúa la habilidad en la aplicación del conocimiento almacenado de las reglas ortográficas.",altas:["Buen conocimiento y aplicación de las reglas de ortografía.","Buena capacidad para segmentar fonológicamente las palabras en componentes.","Habilidad para realizar una grafía correcta y, probablemente, para expresarse con claridad y precisión.","Facilidad para recuperar la información almacenada en la memoria a largo plazo relacionada con la escritura.","Buen nivel de vocabulario.","Posiblemente, destreza para discriminar visualmente entre estímulos verbales escritos."],bajas:["Conocimiento limitado sobre la aplicación de las reglas ortográficas.","Limitaciones para segmentar fonológicamente las palabras en componentes.","Dificultades para recuperar la información almacenada en la memoria a largo plazo relacionada con la escritura.","Dificultad para realizar una grafía correcta y, probablemente, para expresarse con claridad y precisión.","Nivel bajo de vocabulario.","Posibles problemas para discriminar visualmente entre estímulos verbales escritos."]}},j=()=>{const{pacienteId:e}=t(),[j,x]=c.useState(null),[h,y]=c.useState([]),[P,V]=c.useState(!0),[D,B]=c.useState(null);c.useEffect(()=>{var l,t,c;l=null,t=null,c=function*(){if(!e)return B("No se proporcionó ID de paciente."),void V(!1);try{V(!0);const{data:l,error:t}=yield v.from("pacientes").select("*").eq("id",e).single();if(t)throw t;x(l);const{data:c,error:d}=yield v.from("resultados").select("\n            *,\n            aptitudes:aptitud_id (nombre, codigo)\n          ").eq("paciente_id",e);if(d)throw d;const m=c.map(e=>{let t=e.percentil;if(null==t||"pendiente"===String(t).toLowerCase())if(l&&l.fecha_nacimiento&&null!==e.puntaje_directo&&e.aptitudes&&e.aptitudes.codigo){const a=u(l.fecha_nacimiento),i=`${Math.floor(a)}-${Math.floor(a)+1}`;t=f(e.puntaje_directo,e.aptitudes.codigo,i)}else t="N/A por datos incompletos";return c=((e,a)=>{for(var i in a||(a={}))o.call(a,i)&&n(e,i,a[i]);if(r)for(var i of r(a))s.call(a,i)&&n(e,i,a[i]);return e})({},e),a(c,i({pc_calculado:t}));var c});y(m)}catch(l){B(l.message)}finally{V(!1)}},new Promise((e,a)=>{var i=e=>{try{o(c.next(e))}catch(i){a(i)}},r=e=>{try{o(c.throw(e))}catch(i){a(i)}},o=a=>a.done?e(a.value):Promise.resolve(a.value).then(i,r);o((c=c.apply(l,t)).next())})},[e]);return P?l.jsxDEV(g,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:231,columnNumber:23},void 0):D?l.jsxDEV("div",{className:"text-red-500 text-center p-4",children:["Error: ",D]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:232,columnNumber:21},void 0):j?l.jsxDEV("div",{className:"container mx-auto p-4",children:[l.jsxDEV("h1",{className:"text-3xl font-bold text-center mb-8 text-gray-700",children:"Informe Psicopedagógico"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:237,columnNumber:7},void 0),l.jsxDEV(d,{className:"mb-6",children:[l.jsxDEV(m,{children:l.jsxDEV("h2",{className:"text-2xl font-semibold text-gray-600",children:"Datos del Paciente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:241,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:240,columnNumber:9},void 0),l.jsxDEV(p,{children:[l.jsxDEV("p",{children:[l.jsxDEV("strong",{children:"Nombre:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:244,columnNumber:14},void 0)," ",j.nombre," ",j.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:244,columnNumber:11},void 0),l.jsxDEV("p",{children:[l.jsxDEV("strong",{children:"Fecha de Nacimiento:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:245,columnNumber:14},void 0)," ",new Date(j.fecha_nacimiento).toLocaleDateString()]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:245,columnNumber:11},void 0),l.jsxDEV("p",{children:[l.jsxDEV("strong",{children:"Edad:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:246,columnNumber:14},void 0)," ",u(j.fecha_nacimiento)," años"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:246,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:243,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:239,columnNumber:7},void 0),l.jsxDEV("h2",{className:"text-2xl font-semibold text-gray-600 mb-4",children:"Resultados de las Aptitudes Evaluadas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:251,columnNumber:7},void 0),h.length>0?h.map((e,a)=>{var i,r;const o=null==(i=e.aptitudes)?void 0:i.codigo,s=o?((e,a)=>{const i=N[e];return i?a>=70?{descripcion:i.descripcion,puntos:i.altas,nivel:"Alto"}:a<=30?{descripcion:i.descripcion,puntos:i.bajas,nivel:"Bajo"}:{descripcion:i.descripcion,puntos:["Puntuación dentro del promedio."],nivel:"Promedio"}:{descripcion:"",puntos:["No hay interpretación disponible."]}})(o,e.pc_calculado):{descripcion:"",puntos:["Código de aptitud no encontrado."],nivel:""};return l.jsxDEV(d,{className:"mb-6",children:[l.jsxDEV(m,{children:l.jsxDEV("h3",{className:"text-xl font-semibold text-blue-600",children:[(null==(r=e.aptitudes)?void 0:r.nombre)||"Aptitud Desconocida"," (PC: ",null!==e.pc_calculado&&void 0!==e.pc_calculado?e.pc_calculado:"N/A",") - Nivel: ",s.nivel]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:259,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:258,columnNumber:15},void 0),l.jsxDEV(p,{children:[l.jsxDEV("p",{className:"italic text-gray-600 mb-2",children:s.descripcion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:262,columnNumber:17},void 0),l.jsxDEV("h4",{className:"font-semibold mb-1",children:"Puntos Clave:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:263,columnNumber:17},void 0),l.jsxDEV("ul",{className:"list-disc list-inside ml-4 text-sm",children:s.puntos.map((e,a)=>l.jsxDEV("li",{children:e},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:266,columnNumber:21},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:264,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:261,columnNumber:15},void 0),l.jsxDEV(b,{className:"text-xs text-gray-500",children:["Fecha de evaluación: ",new Date(e.created_at).toLocaleDateString()]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:270,columnNumber:15},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:257,columnNumber:13},void 0)}):l.jsxDEV("p",{children:"No hay resultados disponibles para este paciente."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:277,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:236,columnNumber:5},void 0):l.jsxDEV("div",{className:"text-center p-4",children:"No se encontraron datos del paciente."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/InformePaciente.jsx",lineNumber:233,columnNumber:25},void 0)};export{j as default};
