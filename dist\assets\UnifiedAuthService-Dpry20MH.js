var e=(e,r,s)=>new Promise((o,t)=>{var a=e=>{try{n(s.next(e))}catch(r){t(r)}},i=e=>{try{n(s.throw(e))}catch(r){t(r)}},n=e=>e.done?o(e.value):Promise.resolve(e.value).then(a,i);n((s=s.apply(e,r)).next())});import{s as r}from"./index-Bdl1jgS_.js";class s{static login(s,o){return e(this,null,function*(){try{let e,t;if(s.includes("@")){const{data:a,error:i}=yield r.auth.signInWithPassword({email:s.trim(),password:o});if(i)return{success:!1,error:this.getAuthErrorMessage(i)};e=a;const{data:n,error:c}=yield r.from("usuarios").select("*").eq("id",a.user.id).single();if(c)return yield r.auth.signOut(),{success:!1,error:"Usuario no encontrado en el sistema"};t=n}else{const{data:a,error:i}=yield r.from("usuarios").select("*").eq("documento",s.trim()).single();if(i||!a)return{success:!1,error:"Usuario no encontrado con ese documento"};if(!a.activo)return{success:!1,error:"Usuario inactivo. Contacte al administrador"};const{data:n,error:c}=yield r.from("auth.users").select("email").eq("id",a.id).single();let u;if(c||!n){const{data:e,error:s}=yield r.rpc("get_user_email",{user_id:a.id});if(s||!e)return{success:!1,error:"Error al obtener datos de autenticación"};u=e}else u=n.email;const{data:l,error:d}=yield r.auth.signInWithPassword({email:u,password:o});if(d)return{success:!1,error:this.getAuthErrorMessage(d)};e=l,t=a}if(!t.activo)return yield r.auth.signOut(),{success:!1,error:"Usuario inactivo. Contacte al administrador"};yield r.from("usuarios").update({ultimo_acceso:(new Date).toISOString()}).eq("id",t.id);const a={id:t.id,email:e.user.email,documento:t.documento,nombre:t.nombre,apellido:t.apellido,rol:t.rol||"candidato",tipo_usuario:t.rol||"candidato",activo:t.activo};return localStorage.setItem("isLoggedIn","true"),localStorage.setItem("userRole",a.rol),localStorage.setItem("userData",JSON.stringify(a)),localStorage.setItem("userEmail",a.email),{success:!0,user:a,session:e.session,requirePasswordChange:!1}}catch(e){return{success:!1,error:e.message||"Error interno del servidor"}}})}static getAuthErrorMessage(e){switch(e.message){case"Invalid login credentials":return"Credenciales incorrectas. Verifique su email/documento y contraseña";case"Email not confirmed":return"Email no confirmado. Revise su bandeja de entrada";case"Too many requests":return"Demasiados intentos. Intente más tarde";case"User not found":return"Usuario no encontrado";default:return e.message||"Error de autenticación"}}static createUser(s){return e(this,null,function*(){try{const{data:o,error:t}=yield r.from("usuarios").select("id, documento").eq("documento",s.documento).single();if(o)return{success:!1,error:"Ya existe un usuario con ese documento"};const{data:a,error:i}=yield r.auth.signUp({email:s.email,password:s.password,options:{data:{nombre:s.nombre,apellido:s.apellido,documento:s.documento,rol:s.rol}}});if(i)return{success:!1,error:"Error al crear usuario: "+i.message};if(!a.user)return{success:!1,error:"Error al crear usuario en el sistema de autenticación"};const{error:n}=yield r.from("usuarios").insert({id:a.user.id,nombre:s.nombre,apellido:s.apellido,documento:s.documento,email:s.email,rol:s.rol,activo:!0,fecha_creacion:(new Date).toISOString(),ultimo_acceso:(new Date).toISOString()});if(n){try{yield r.auth.admin.deleteUser(a.user.id)}catch(e){}return{success:!1,error:"Error al crear perfil de usuario: "+n.message}}return{success:!0,userId:a.user.id,message:`Usuario ${s.nombre} ${s.apellido} creado exitosamente`}}catch(o){return{success:!1,error:o.message||"Error interno del servidor"}}})}static updateUser(s,o){return e(this,null,function*(){try{const{data:e,error:t}=yield r.from("usuarios").select("id, nombre, apellido, rol").eq("id",s).single();if(t||!e)return{success:!1,error:"Usuario no encontrado"};const a={};void 0!==o.nombre&&(a.nombre=o.nombre),void 0!==o.apellido&&(a.apellido=o.apellido),void 0!==o.documento&&(a.documento=o.documento),void 0!==o.activo&&(a.activo=o.activo),void 0!==o.rol&&(a.rol=o.rol),void 0!==o.email&&(a.email=o.email),a.ultimo_acceso=(new Date).toISOString();const{error:i}=yield r.from("usuarios").update(a).eq("id",s);return i?{success:!1,error:"Error al actualizar usuario: "+i.message}:{success:!0,message:`Usuario ${e.nombre} ${e.apellido} actualizado exitosamente`}}catch(e){return{success:!1,error:e.message||"Error interno del servidor"}}})}static listUsers(){return e(this,arguments,function*(e={}){try{const{data:s,error:o}=yield r.rpc("list_users",{p_limit:e.limit||50,p_offset:e.offset||0,p_rol:e.rol||null,p_activo:void 0!==e.activo?e.activo:null});if(o)throw new Error(o.message);return s.success?{success:!0,users:s.users,total:s.total,limit:s.limit,offset:s.offset}:{success:!1,error:s.error}}catch(s){return{success:!1,error:s.message||"Error interno del servidor"}}})}static getUserById(s){return e(this,null,function*(){try{const{data:e,error:o}=yield r.rpc("get_user_by_id",{p_user_id:s});if(o)throw new Error(o.message);return e.success?{success:!0,user:e.user}:{success:!1,error:e.error}}catch(e){return{success:!1,error:e.message||"Error interno del servidor"}}})}static setTempPassword(s,o){return e(this,null,function*(){try{const{data:e,error:t}=yield r.rpc("set_user_temp_password",{p_user_id:s,temp_password:o});if(t)throw new Error(t.message);return e.success?{success:!0,message:e.message}:{success:!1,error:e.error}}catch(e){return{success:!1,error:e.message||"Error interno del servidor"}}})}static changePassword(s,o,t){return e(this,null,function*(){try{const{data:e,error:a}=yield r.rpc("change_user_password",{p_user_id:s,old_password:o,new_password:t});if(a)throw new Error(a.message);if(!e.success)return{success:!1,error:e.error};const i=this.getCurrentUser();return i&&i.id===s&&(i.require_password_change=!1,i.has_temp_password=!1,localStorage.setItem("userData",JSON.stringify(i))),{success:!0,message:e.message}}catch(e){return{success:!1,error:e.message||"Error interno del servidor"}}})}static getCurrentUser(){try{const e=localStorage.getItem("userData");return e?JSON.parse(e):null}catch(e){return null}}static isLoggedIn(){const e="true"===localStorage.getItem("isLoggedIn"),r=this.getCurrentUser();return e&&null!==r}static hasRole(e){const r=this.getCurrentUser();return r&&r.rol===e}static logout(){localStorage.removeItem("isLoggedIn"),localStorage.removeItem("userRole"),localStorage.removeItem("userData"),localStorage.removeItem("userEmail")}static deleteUser(s){return e(this,null,function*(){try{const{data:e,error:o}=yield r.from("usuarios").select("id, nombre, apellido, rol, activo").eq("id",s).single();if(o||!e)return{success:!1,error:"Usuario no encontrado"};if("administrador"===e.rol){const{data:e,error:o}=yield r.from("usuarios").select("id",{count:"exact"}).eq("rol","administrador").eq("activo",!0).neq("id",s);if(o)return{success:!1,error:"Error verificando permisos"};if(0===e.length)return{success:!1,error:"No se puede eliminar el último administrador activo"}}const{error:t}=yield r.from("usuarios").delete().eq("id",s);return t?{success:!1,error:"Error al eliminar usuario: "+t.message}:{success:!0,message:`Usuario ${e.nombre} ${e.apellido} eliminado exitosamente`}}catch(e){return{success:!1,error:e.message||"Error interno del servidor"}}})}static generateTempPassword(e=8){const r="ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";let s="";for(let o=0;o<e;o++)s+=r.charAt(Math.floor(56*Math.random()));return s}static validatePassword(e){const r=[];return(!e||e.length<6)&&r.push("La contraseña debe tener al menos 6 caracteres"),e.length>50&&r.push("La contraseña no puede tener más de 50 caracteres"),/[A-Za-z]/.test(e)||r.push("La contraseña debe contener al menos una letra"),/[0-9]/.test(e)||r.push("La contraseña debe contener al menos un número"),{isValid:0===r.length,errors:r}}}export{s as U};
