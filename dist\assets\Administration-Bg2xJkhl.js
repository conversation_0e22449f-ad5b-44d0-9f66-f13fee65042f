var e=Object.defineProperty,t=Object.defineProperties,s=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,l=(t,s,i)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[s]=i,n=(e,t)=>{for(var s in t||(t={}))a.call(t,s)&&l(e,s,t[s]);if(i)for(var s of i(t))r.call(t,s)&&l(e,s,t[s]);return e},o=(e,i)=>t(e,s(i)),c=(e,t,s)=>new Promise((i,a)=>{var r=e=>{try{n(s.next(e))}catch(t){a(t)}},l=e=>{try{n(s.throw(e))}catch(t){a(t)}},n=e=>e.done?i(e.value):Promise.resolve(e.value).then(r,l);n((s=s.apply(e,t)).next())});import{r as d,j as m,F as u,Q as x,M as p,a1 as h,B as g,Y as b,a8 as y,a7 as f,am as j,a as v,a0 as N,m as w,aw as C}from"./vendor-BqMjyOVw.js";import{P as _}from"./PageHeader-DzW86ZOX.js";import{s as k,C as S,b as P,a as E,B as D,S as I}from"./index-Bdl1jgS_.js";const q=(e,t,s,i)=>c(null,null,function*(){try{yield k.from("logs").insert({action:e,table_name:t,record_id:s,data:i,created_at:(new Date).toISOString()})}catch(a){}}),O={checkConnection(){return c(this,null,function*(){try{const{data:e,error:t}=yield k.rpc("get_tables");if(t)throw t;return{success:!0,data:e}}catch(e){return{success:!1,error:e}}})},getInstitutions(){return c(this,null,function*(){return yield k.from("instituciones").select("*").order("nombre",{ascending:!0})})},createInstitution(e){return c(this,null,function*(){const t={nombre:e.nombre,direccion:e.direccion||"",telefono:e.telefono||"",email:e.email||"",sitio_web:e.sitio_web||"",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},s=yield k.from("instituciones").insert([t]).select();return s.data&&s.data.length>0&&(yield q("create","instituciones",s.data[0].id,t)),s})},updateInstitution(e,t){return c(this,null,function*(){const s={nombre:t.nombre,direccion:t.direccion||"",telefono:t.telefono||"",email:t.email||"",sitio_web:t.sitio_web||"",updated_at:(new Date).toISOString()},i=yield k.from("instituciones").update(s).eq("id",e).select();return i.data&&i.data.length>0&&(yield q("update","instituciones",e,s)),i})},deleteInstitution(e){return c(this,null,function*(){const{data:t}=yield k.from("instituciones").select("*").eq("id",e).single(),s=yield k.from("instituciones").delete().eq("id",e);return!s.error&&t&&(yield q("delete","instituciones",e,t)),s})},getPsychologists(){return c(this,null,function*(){return yield k.from("psicologos").select("*, instituciones(id, nombre)").order("nombre",{ascending:!0})})},createPsychologist(e){return c(this,null,function*(){const t={usuario_id:e.usuario_id||null,institucion_id:e.institucion_id,nombre:e.nombre,apellido:e.apellido||"",email:e.email||"",telefono:e.telefono||"",genero:e.genero||"",especialidad:e.especialidad||"",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},s=yield k.from("psicologos").insert([t]).select();return s.data&&s.data.length>0&&(yield q("create","psicologos",s.data[0].id,t)),s})},updatePsychologist(e,t){return c(this,null,function*(){const s={institucion_id:t.institucion_id,nombre:t.nombre,apellido:t.apellido||"",email:t.email||"",telefono:t.telefono||"",genero:t.genero||"",especialidad:t.especialidad||"",updated_at:(new Date).toISOString()},i=yield k.from("psicologos").update(s).eq("id",e).select();return i.data&&i.data.length>0&&(yield q("update","psicologos",e,s)),i})},deletePsychologist(e){return c(this,null,function*(){const{data:t}=yield k.from("psicologos").select("*").eq("id",e).single(),s=yield k.from("psicologos").delete().eq("id",e);return!s.error&&t&&(yield q("delete","psicologos",e,t)),s})},getPatients(){return c(this,null,function*(){return yield k.from("pacientes").select("*, instituciones(id, nombre), psicologos(id, nombre, apellido)").order("nombre",{ascending:!0})})},createPatient(e){return c(this,null,function*(){const t={psicologo_id:e.psicologo_id||null,institucion_id:e.institucion_id,nombre:e.nombre,apellido:e.apellido||e.apellidos||"",documento:e.documento||e.documento_identidad||"",email:e.email||"",genero:e.genero||"",fecha_nacimiento:e.fecha_nacimiento||null,nivel_educativo:e.nivel_educativo||"",ocupacion:e.ocupacion||"",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},s=yield k.from("pacientes").insert([t]).select();return s.data&&s.data.length>0&&(yield q("create","pacientes",s.data[0].id,t)),s})},updatePatient(e,t){return c(this,null,function*(){const s={psicologo_id:t.psicologo_id||null,institucion_id:t.institucion_id,nombre:t.nombre,apellido:t.apellido||t.apellidos||"",documento:t.documento||t.documento_identidad||"",email:t.email||"",genero:t.genero||"",fecha_nacimiento:t.fecha_nacimiento||null,nivel_educativo:t.nivel_educativo||"",ocupacion:t.ocupacion||"",updated_at:(new Date).toISOString()},i=yield k.from("pacientes").update(s).eq("id",e).select();return i.data&&i.data.length>0&&(yield q("update","pacientes",e,s)),i})},deletePatient(e){return c(this,null,function*(){const{data:t}=yield k.from("pacientes").select("*").eq("id",e).single(),s=yield k.from("pacientes").delete().eq("id",e);return!s.error&&t&&(yield q("delete","pacientes",e,t)),s})},getPatientsByPsychologist(e){return c(this,null,function*(){return yield k.from("pacientes").select("*").eq("psicologo_id",e).order("nombre",{ascending:!0})})},getPatientsByInstitution(e){return c(this,null,function*(){return yield k.from("pacientes").select("*").eq("institucion_id",e).order("nombre",{ascending:!0})})},getPsychologistsByInstitution(e){return c(this,null,function*(){return yield k.from("psicologos").select("*").eq("institucion_id",e).order("nombre",{ascending:!0})})}},A=({initialData:e,onSubmit:t,onCancel:s})=>{const[i,a]=d.useState({nombre:"",direccion:"",telefono:"",email:"",sitio_web:""}),[r,l]=d.useState(!1);d.useEffect(()=>{e&&a({nombre:e.nombre||"",direccion:e.direccion||"",telefono:e.telefono||"",email:e.email||"",sitio_web:e.sitio_web||""})},[e]);const x=e=>{const{name:t,value:s,type:r,checked:l}=e.target;a(o(n({},i),{[t]:"checkbox"===r?l:s}))};return m.jsxs("form",{onSubmit:e=>c(null,null,function*(){if(e.preventDefault(),i.nombre.trim()||(alert("El nombre de la institución es obligatorio"),0)){l(!0);try{yield t(i)}catch(s){}finally{l(!1)}}}),className:"space-y-4",children:[m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[m.jsxs("div",{className:"md:col-span-2",children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre de la Institución *"}),m.jsx("input",{type:"text",name:"nombre",value:i.nombre,onChange:x,className:"w-full p-2 border border-gray-300 rounded-md",required:!0})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),m.jsx("input",{type:"email",name:"email",value:i.email,onChange:x,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Dirección"}),m.jsx("input",{type:"text",name:"direccion",value:i.direccion,onChange:x,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Teléfono"}),m.jsx("input",{type:"text",name:"telefono",value:i.telefono,onChange:x,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sitio Web"}),m.jsx("input",{type:"url",name:"sitio_web",value:i.sitio_web,onChange:x,className:"w-full p-2 border border-gray-300 rounded-md",placeholder:"https://ejemplo.com"})]})]}),m.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[m.jsx("button",{type:"button",onClick:s,className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300",children:"Cancelar"}),m.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",disabled:r,children:r?m.jsxs(m.Fragment,{children:[m.jsx(u,{className:"animate-spin inline mr-2"}),"Guardando..."]}):e?"Actualizar":"Crear"})]})]})},L=()=>{const[e,t]=d.useState([]),[s,i]=d.useState(!0),[a,r]=d.useState(!1),[l,n]=d.useState(null),[o,y]=d.useState("");d.useEffect(()=>{f()},[]);const f=()=>c(null,null,function*(){i(!0);try{const{data:e,error:s}=yield O.getInstitutions();if(s)throw s;t(e||[])}catch(e){x.error("Error al cargar las instituciones")}finally{i(!1)}}),j=(e=null)=>{n(e),r(!0)},v=()=>{r(!1),n(null)},N=e.filter(e=>{var t,s,i,a;return(null==(t=e.nombre)?void 0:t.toLowerCase().includes(o.toLowerCase()))||(null==(s=e.email)?void 0:s.toLowerCase().includes(o.toLowerCase()))||(null==(i=e.direccion)?void 0:i.toLowerCase().includes(o.toLowerCase()))||(null==(a=e.telefono)?void 0:a.toLowerCase().includes(o.toLowerCase()))});return m.jsxs("div",{className:"space-y-6",children:[m.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[m.jsxs("div",{children:[m.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Gestión de Instituciones"}),m.jsx("p",{className:"text-gray-600",children:"Administre las instituciones registradas en el sistema"})]}),m.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[m.jsxs("div",{className:"relative",children:[m.jsx("input",{type:"text",placeholder:"Buscar institución...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",value:o,onChange:e=>y(e.target.value)}),m.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:m.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:m.jsx("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"})})})]}),m.jsxs("button",{onClick:()=>j(),className:"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",disabled:s,children:[m.jsx(p,{className:"mr-2"}),"Nueva Institución"]})]})]}),m.jsx("div",{className:"bg-white shadow overflow-hidden rounded-lg",children:s?m.jsxs("div",{className:"flex justify-center items-center p-8",children:[m.jsx(u,{className:"animate-spin text-blue-600 text-2xl mr-2"}),m.jsx("span",{children:"Cargando instituciones..."})]}):0===N.length?m.jsx("div",{className:"text-center p-8 text-gray-500",children:o?"No se encontraron instituciones que coincidan con la búsqueda.":"No hay instituciones registradas."}):m.jsx("div",{className:"overflow-x-auto",children:m.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsx("thead",{className:"bg-sky-800",children:m.jsxs("tr",{children:[m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Nombre"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Email"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Dirección"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Teléfono"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Sitio Web"}),m.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider",children:"Acciones"})]})}),m.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:N.map(e=>m.jsxs("tr",{className:"hover:bg-gray-50",children:[m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsxs("div",{className:"flex items-center",children:[m.jsx(h,{className:"text-gray-500 mr-2"}),m.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.nombre})]})}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.direccion}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.telefono}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.sitio_web?m.jsx("a",{href:e.sitio_web,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:e.sitio_web.replace(/^https?:\/\//,"").split("/")[0]}):"-"}),m.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[m.jsx("button",{onClick:()=>j(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:m.jsx(g,{})}),m.jsx("button",{onClick:()=>{return t=e.id,c(null,null,function*(){if(window.confirm("¿Está seguro de que desea eliminar esta institución?"))try{i(!0);const{error:e}=yield O.deleteInstitution(t);if(e)throw e;x.success("Institución eliminada correctamente"),f()}catch(e){x.error("Error al eliminar la institución")}finally{i(!1)}});var t},className:"text-red-600 hover:text-red-900",children:m.jsx(b,{})})]})]},e.id))})]})})}),a&&m.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:m.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-3xl mx-4",children:[m.jsx("h2",{className:"text-xl font-bold mb-4",children:l?"Editar Institución":"Nueva Institución"}),m.jsx(A,{initialData:l,onSubmit:e=>c(null,null,function*(){try{let t;if(i(!0),l){if(t=yield O.updateInstitution(l.id,e),t.error)throw t.error;x.success("Institución actualizada correctamente")}else{if(t=yield O.createInstitution(e),t.error)throw t.error;x.success("Institución creada correctamente")}f(),v()}catch(t){x.error(l?"Error al actualizar la institución":"Error al crear la institución")}finally{i(!1)}}),onCancel:v})]})})]})},z=()=>{const[e,t]=d.useState([]),[s,i]=d.useState([]),[a,r]=d.useState(!0),[l,h]=d.useState(!1),[j,v]=d.useState(null),[N,w]=d.useState(""),[C,_]=d.useState({nombre:"",apellido:"",genero:"masculino",email:"",telefono:"",especialidad:"",institucion_id:""});d.useEffect(()=>{k(),S()},[]);const k=()=>c(null,null,function*(){r(!0);try{const{data:e,error:s}=yield O.getPsychologists();if(s)throw s;t(e||[])}catch(e){x.error("Error al cargar los psicólogos")}finally{r(!1)}}),S=()=>c(null,null,function*(){try{const{data:e,error:t}=yield O.getInstitutions();if(t)throw t;i(e||[])}catch(e){x.error("Error al cargar las instituciones")}}),P=(e=null)=>{v(e),_(e?{nombre:e.nombre||"",apellido:e.apellido||"",genero:e.genero||"masculino",email:e.email||"",telefono:e.telefono||"",especialidad:e.especialidad||"",institucion_id:e.institucion_id||"",activo:!1!==e.activo}:{nombre:"",apellido:"",genero:"masculino",email:"",telefono:"",especialidad:"",institucion_id:"",activo:!0}),h(!0)},E=()=>{h(!1),v(null)},D=e=>{const{name:t,value:s,type:i,checked:a}=e.target;_(o(n({},C),{[t]:"checkbox"===i?a:s}))},I=e.filter(e=>{var t,s,i,a;return(null==(t=e.nombre)?void 0:t.toLowerCase().includes(N.toLowerCase()))||(null==(s=e.apellido)?void 0:s.toLowerCase().includes(N.toLowerCase()))||(null==(i=e.email)?void 0:i.toLowerCase().includes(N.toLowerCase()))||(null==(a=e.especialidad)?void 0:a.toLowerCase().includes(N.toLowerCase()))}),q=e=>{const t=s.find(t=>t.id===e);return t?t.nombre:"No asignada"};return m.jsxs("div",{className:"space-y-6",children:[m.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[m.jsxs("div",{children:[m.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Gestión de Psicólogos"}),m.jsx("p",{className:"text-gray-600",children:"Administre los psicólogos registrados en el sistema"})]}),m.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[m.jsxs("div",{className:"relative",children:[m.jsx("input",{type:"text",placeholder:"Buscar psicólogo...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",value:N,onChange:e=>w(e.target.value)}),m.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:m.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:m.jsx("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"})})})]}),m.jsxs("button",{onClick:()=>P(),className:"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",disabled:a,children:[m.jsx(p,{className:"mr-2"}),"Nuevo Psicólogo"]})]})]}),m.jsx("div",{className:"bg-white shadow overflow-hidden rounded-lg",children:a&&0===e.length?m.jsxs("div",{className:"flex justify-center items-center p-8",children:[m.jsx(u,{className:"animate-spin text-blue-600 text-2xl mr-2"}),m.jsx("span",{children:"Cargando psicólogos..."})]}):0===I.length?m.jsx("div",{className:"text-center p-8 text-gray-500",children:N?"No se encontraron psicólogos que coincidan con la búsqueda.":"No hay psicólogos registrados."}):m.jsx("div",{className:"overflow-x-auto",children:m.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsx("thead",{className:"bg-sky-800",children:m.jsxs("tr",{children:[m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Nombre"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Email"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Teléfono"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Especialidad"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Institución"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Estado"}),m.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider",children:"Acciones"})]})}),m.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:I.map(e=>m.jsxs("tr",{className:"hover:bg-gray-50",children:[m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsxs("div",{className:"flex items-center",children:["femenino"===e.genero?m.jsx(y,{className:"text-pink-500 mr-2"}):m.jsx(f,{className:"text-blue-500 mr-2"}),m.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]})]})}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email||"-"}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.telefono||"-"}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.especialidad||"-"}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:q(e.institucion_id)}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full "+(e.activo?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.activo?"Activo":"Inactivo"})}),m.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[m.jsx("button",{onClick:()=>P(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:m.jsx(g,{})}),m.jsx("button",{onClick:()=>{return t=e.id,c(null,null,function*(){if(window.confirm("¿Está seguro de que desea eliminar este psicólogo?"))try{r(!0);const{error:e}=yield O.deletePsychologist(t);if(e)throw e;x.success("Psicólogo eliminado correctamente"),k()}catch(e){x.error("Error al eliminar el psicólogo")}finally{r(!1)}});var t},className:"text-red-600 hover:text-red-900",children:m.jsx(b,{})})]})]},e.id))})]})})}),l&&m.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:m.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-3xl mx-4",children:[m.jsx("h2",{className:"text-xl font-bold mb-4",children:j?"Editar Psicólogo":"Nuevo Psicólogo"}),m.jsxs("form",{onSubmit:e=>c(null,null,function*(){if(e.preventDefault(),C.nombre.trim()?C.apellido.trim()?C.institucion_id?!C.email||/\S+@\S+\.\S+/.test(C.email)||(x.error("El email no es válido"),0):(x.error("Debe seleccionar una institución"),0):(x.error("El apellido es obligatorio"),0):(x.error("El nombre es obligatorio"),0))try{let e;if(r(!0),j){if(e=yield O.updatePsychologist(j.id,C),e.error)throw e.error;x.success("Psicólogo actualizado correctamente")}else{if(e=yield O.createPsychologist(C),e.error)throw e.error;x.success("Psicólogo creado correctamente")}k(),E()}catch(t){x.error(j?"Error al actualizar el psicólogo":"Error al crear el psicólogo")}finally{r(!1)}}),className:"space-y-4",children:[m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre *"}),m.jsx("input",{type:"text",name:"nombre",value:C.nombre,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md",required:!0})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido *"}),m.jsx("input",{type:"text",name:"apellido",value:C.apellido,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md",required:!0})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Género"}),m.jsxs("select",{name:"genero",value:C.genero,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md",children:[m.jsx("option",{value:"masculino",children:"Masculino"}),m.jsx("option",{value:"femenino",children:"Femenino"}),m.jsx("option",{value:"otro",children:"Otro"})]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),m.jsx("input",{type:"email",name:"email",value:C.email,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Teléfono"}),m.jsx("input",{type:"text",name:"telefono",value:C.telefono,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Especialidad"}),m.jsx("input",{type:"text",name:"especialidad",value:C.especialidad,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Institución *"}),m.jsxs("select",{name:"institucion_id",value:C.institucion_id,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md",required:!0,children:[m.jsx("option",{value:"",children:"Seleccione una institución"}),s.map(e=>m.jsx("option",{value:e.id,children:e.nombre},e.id))]})]})]}),m.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[m.jsx("button",{type:"button",onClick:E,className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300",children:"Cancelar"}),m.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",disabled:a,children:a?m.jsxs(m.Fragment,{children:[m.jsx(u,{className:"animate-spin inline mr-2"}),"Guardando..."]}):j?"Actualizar":"Crear"})]})]})]})})]})},$=()=>{const[e,t]=d.useState([]),[s,i]=d.useState([]),[a,r]=d.useState([]),[l,h]=d.useState(!0),[j,v]=d.useState(!1),[N,w]=d.useState(null),[C,_]=d.useState(""),[k,S]=d.useState("nombre"),[P,E]=d.useState("asc"),[D,I]=d.useState({institucion_id:"",genero:"",psicologo_id:"",edad_min:"",edad_max:""}),[q,A]=d.useState({nombre:"",apellido:"",genero:"masculino",fecha_nacimiento:"",documento:"",email:"",nivel_educativo:"",ocupacion:"",institucion_id:"",psicologo_id:""});d.useEffect(()=>{L(),z(),$()},[]);const L=()=>c(null,null,function*(){h(!0);try{const{data:e,error:s}=yield O.getPatients();if(s)throw s;const i=(Array.isArray(e)?e:[]).map(e=>o(n({},e),{edad:T(e.fecha_nacimiento)}));t(i)}catch(e){x.error("Error: "+(e.message||"Error al cargar los pacientes"))}finally{h(!1)}}),z=()=>c(null,null,function*(){try{const{data:e,error:t}=yield O.getInstitutions();if(t)throw t;i(e||[])}catch(e){x.error("Error: "+(e.message||"Error al cargar las instituciones"))}}),$=()=>c(null,null,function*(){try{const{data:e,error:t}=yield O.getPsychologists();if(t)throw t;r(e||[])}catch(e){x.error("Error: "+(e.message||"Error al cargar los psicólogos"))}}),B=(e=null)=>{w(e),A(e?{nombre:e.nombre||"",apellido:e.apellido||"",genero:e.genero||"masculino",fecha_nacimiento:e.fecha_nacimiento||"",documento:e.documento||"",email:e.email||"",institucion_id:e.institucion_id||"",psicologo_id:e.psicologo_id||"",activo:!1!==e.activo}:{nombre:"",apellido:"",genero:"masculino",fecha_nacimiento:"",documento:"",email:"",institucion_id:"",psicologo_id:"",activo:!0}),v(!0)},M=()=>{v(!1),w(null)},R=e=>{const{name:t,value:s,type:i,checked:a}=e.target;A(o(n({},q),{[t]:"checkbox"===i?a:s}))},T=e=>{if(!e)return"-";const t=new Date,s=new Date(e);let i=t.getFullYear()-s.getFullYear();const a=t.getMonth()-s.getMonth();return(a<0||0===a&&t.getDate()<s.getDate())&&i--,i},F=e=>{const t=s.find(t=>t.id===e);return t?t.nombre:"No asignada"},G=e=>{const t=a.find(t=>t.id===e);return t?`${t.nombre} ${t.apellido}`:"No asignado"},V=e.filter(e=>{var t,i,a,r,l,n;const o=C.toLowerCase(),c=!C||(null==(t=e.nombre)?void 0:t.toLowerCase().includes(o))||(null==(i=e.apellidos)?void 0:i.toLowerCase().includes(o))||(null==(a=e.documento_identidad)?void 0:a.toLowerCase().includes(o))||(null==(r=e.email)?void 0:r.toLowerCase().includes(o))||((null==(l=s.find(t=>t.id===e.institucion_id))?void 0:l.nombre)||"").toLowerCase().includes(o)||(null==(n=e.notas)?void 0:n.toLowerCase().includes(o)),d=!D.institucion_id||e.institucion_id===D.institucion_id,m=!D.genero||e.genero===D.genero,u=!D.psicologo_id||("null"===D.psicologo_id?!e.psicologo_id:e.psicologo_id===D.psicologo_id),x="number"==typeof e.edad?e.edad:-1,p=parseInt(D.edad_min),h=isNaN(p)||-1===x||x>=p,g=parseInt(D.edad_max),b=isNaN(g)||-1===x||x<=g;return c&&d&&m&&u&&h&&b});return s.map(e=>({value:e.id,label:e.nombre})),a.map(e=>({value:e.id,label:`${e.nombre} ${e.apellidos}`})),s.map(e=>({value:e.id,label:e.nombre})),a.map(e=>({value:e.id,label:`${e.nombre} ${e.apellidos}`})),m.jsxs("div",{className:"space-y-6",children:[m.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[m.jsxs("div",{children:[m.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Gestión de Pacientes"}),m.jsx("p",{className:"text-gray-600",children:"Administre los pacientes registrados en el sistema"})]}),m.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[m.jsxs("div",{className:"relative",children:[m.jsx("input",{type:"text",placeholder:"Buscar paciente...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",value:C,onChange:e=>_(e.target.value)}),m.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:m.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:m.jsx("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"})})})]}),m.jsxs("button",{onClick:()=>B(),className:"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",disabled:l,children:[m.jsx(p,{className:"mr-2"}),"Nuevo Paciente"]})]})]}),m.jsx("div",{className:"bg-white shadow overflow-hidden rounded-lg",children:l&&0===e.length?m.jsxs("div",{className:"flex justify-center items-center p-8",children:[m.jsx(u,{className:"animate-spin text-blue-600 text-2xl mr-2"}),m.jsx("span",{children:"Cargando pacientes..."})]}):0===V.length?m.jsx("div",{className:"text-center p-8 text-gray-500",children:C?"No se encontraron pacientes que coincidan con la búsqueda.":"No hay pacientes registrados."}):m.jsx("div",{className:"overflow-x-auto",children:m.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsx("thead",{className:"bg-sky-800",children:m.jsxs("tr",{children:[m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Nombre"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Documento"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Edad"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Contacto"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Institución"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Psicólogo"}),m.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider",children:"Acciones"})]})}),m.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:V.map(e=>m.jsxs("tr",{className:"hover:bg-gray-50",children:[m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsxs("div",{className:"flex items-center",children:["femenino"===e.genero?m.jsx(y,{className:"text-pink-500 mr-2"}):m.jsx(f,{className:"text-blue-500 mr-2"}),m.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]})]})}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.documento||"-"}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:T(e.fecha_nacimiento)}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email?m.jsx("div",{children:m.jsx("div",{children:e.email})}):e.email||"-"}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:F(e.institucion_id)}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:G(e.psicologo_id)}),m.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[m.jsx("button",{onClick:()=>B(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:m.jsx(g,{})}),m.jsx("button",{onClick:()=>{return t=e.id,c(null,null,function*(){if(window.confirm("¿Está seguro de que desea eliminar este paciente?"))try{h(!0);const{error:e}=yield O.deletePatient(t);if(e)throw e;x.success("Paciente eliminado correctamente"),L()}catch(e){x.error("Error: "+(e.message||"Error al eliminar el paciente"))}finally{h(!1)}});var t},className:"text-red-600 hover:text-red-900",children:m.jsx(b,{})})]})]},e.id))})]})})}),j&&m.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:m.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-3xl mx-4",children:[m.jsx("h2",{className:"text-xl font-bold mb-4",children:N?"Editar Paciente":"Nuevo Paciente"}),m.jsxs("form",{onSubmit:e=>c(null,null,function*(){if(e.preventDefault(),q.nombre.trim()?q.apellido.trim()?q.institucion_id?!q.email||/\S+@\S+\.\S+/.test(q.email)||(x.error("El email no es válido"),0):(x.error("Debe seleccionar una institución"),0):(x.error("El apellido es obligatorio"),0):(x.error("El nombre es obligatorio"),0))try{h(!0);const e={nombre:q.nombre,apellido:q.apellido,genero:q.genero,fecha_nacimiento:q.fecha_nacimiento||null,documento:q.documento||"",email:q.email||"",institucion_id:q.institucion_id,psicologo_id:q.psicologo_id||null,activo:q.activo,updated_at:(new Date).toISOString()};let t;if(N||(e.created_at=(new Date).toISOString()),N){if(t=yield O.updatePatient(N.id,e),t.error)throw t.error;x.success(`Paciente "${q.nombre}" actualizado correctamente`)}else{if(t=yield O.createPatient(e),t.error)throw t.error;x.success(`Paciente "${q.nombre}" creado correctamente`)}L(),M()}catch(t){x.error("Error: "+(t.message||(N?"Error al actualizar el paciente":"Error al crear el paciente")))}finally{h(!1)}}),className:"space-y-4",children:[m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre *"}),m.jsx("input",{type:"text",name:"nombre",value:q.nombre,onChange:R,className:"w-full p-2 border border-gray-300 rounded-md",required:!0})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido *"}),m.jsx("input",{type:"text",name:"apellido",value:q.apellido,onChange:R,className:"w-full p-2 border border-gray-300 rounded-md",required:!0})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Género"}),m.jsxs("select",{name:"genero",value:q.genero,onChange:R,className:"w-full p-2 border border-gray-300 rounded-md",children:[m.jsx("option",{value:"masculino",children:"Masculino"}),m.jsx("option",{value:"femenino",children:"Femenino"}),m.jsx("option",{value:"otro",children:"Otro"})]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fecha de Nacimiento"}),m.jsx("input",{type:"date",name:"fecha_nacimiento",value:q.fecha_nacimiento,onChange:R,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"}),m.jsx("input",{type:"text",name:"documento",value:q.documento,onChange:R,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),m.jsx("input",{type:"email",name:"email",value:q.email,onChange:R,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nivel Educativo"}),m.jsx("input",{type:"text",name:"nivel_educativo",value:q.nivel_educativo,onChange:R,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ocupación"}),m.jsx("input",{type:"text",name:"ocupacion",value:q.ocupacion,onChange:R,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Institución *"}),m.jsxs("select",{name:"institucion_id",value:q.institucion_id,onChange:R,className:"w-full p-2 border border-gray-300 rounded-md",required:!0,children:[m.jsx("option",{value:"",children:"Seleccione una institución"}),s.map(e=>m.jsx("option",{value:e.id,children:e.nombre},e.id))]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Psicólogo"}),m.jsxs("select",{name:"psicologo_id",value:q.psicologo_id,onChange:R,className:"w-full p-2 border border-gray-300 rounded-md",children:[m.jsx("option",{value:"",children:"Seleccione un psicólogo"}),a.map(e=>m.jsxs("option",{value:e.id,children:[e.nombre," ",e.apellido]},e.id))]})]})]}),m.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[m.jsx("button",{type:"button",onClick:M,className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300",children:"Cancelar"}),m.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",disabled:l,children:l?m.jsxs(m.Fragment,{children:[m.jsx(u,{className:"animate-spin inline mr-2"}),"Guardando..."]}):N?"Actualizar":"Crear"})]})]})]})})]})},B=()=>{const[e,t]=d.useState(!1),[s,i]=d.useState([{id:1,puntajeDirecto:25,aptitudCodigo:"V",edad:13,resultado:null},{id:2,puntajeDirecto:35,aptitudCodigo:"E",edad:13,resultado:null},{id:3,puntajeDirecto:45,aptitudCodigo:"A",edad:13,resultado:null},{id:4,puntajeDirecto:30,aptitudCodigo:"C",edad:13,resultado:null},{id:5,puntajeDirecto:20,aptitudCodigo:"R",edad:13,resultado:null}]),a=[{codigo:"V",nombre:"Verbal"},{codigo:"E",nombre:"Espacial"},{codigo:"A",nombre:"Atención"},{codigo:"C",nombre:"Concentración"},{codigo:"R",nombre:"Razonamiento"},{codigo:"N",nombre:"Numérica"},{codigo:"M",nombre:"Mecánica"},{codigo:"O",nombre:"Ortografía"}],r=(e,t,s)=>{i(i=>i.map(i=>i.id===e?o(n({},i),{[t]:s,resultado:null}):i))};return m.jsx("div",{className:"space-y-6",children:m.jsxs(S,{children:[m.jsxs(P,{children:[m.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"🔄 Probar Conversión PD → PC"}),m.jsx("p",{className:"text-sm text-gray-600",children:"Convierte puntajes directos (PD) a percentiles (PC) para las 8 aptitudes del BAT-7"})]}),m.jsx(E,{children:m.jsxs("div",{className:"space-y-4",children:[m.jsxs("div",{className:"flex gap-3 mb-6",children:[m.jsx(D,{onClick:()=>c(null,null,function*(){t(!0);try{const e=[];for(const t of s){const s=yield I.probarConversion(t.puntajeDirecto,t.aptitudCodigo,t.edad);s.success?e.push(o(n({},t),{resultado:s.percentil})):e.push(o(n({},t),{resultado:"Error"}))}i(e),x.success("Todas las conversiones completadas")}catch(e){x.error("Error al probar conversiones")}finally{t(!1)}}),disabled:e,className:"bg-blue-600 hover:bg-blue-700",children:e?"Procesando...":"Probar Todas"}),m.jsx(D,{onClick:()=>{const e=Math.max(...s.map(e=>e.id))+1;i(t=>[...t,{id:e,puntajeDirecto:25,aptitudCodigo:"V",edad:13,resultado:null}])},disabled:e,className:"bg-green-600 hover:bg-green-700",children:"+ Agregar Prueba"})]}),m.jsx("div",{className:"overflow-x-auto",children:m.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsx("thead",{className:"bg-gray-50",children:m.jsxs("tr",{children:[m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Aptitud"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"PD"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Edad"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"PC"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acciones"})]})}),m.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(l=>m.jsxs("tr",{children:[m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsx("select",{value:l.aptitudCodigo,onChange:e=>r(l.id,"aptitudCodigo",e.target.value),className:"border border-gray-300 rounded px-3 py-1 text-sm",children:a.map(e=>m.jsxs("option",{value:e.codigo,children:[e.codigo," - ",e.nombre]},e.codigo))})}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsx("input",{type:"number",value:l.puntajeDirecto,onChange:e=>r(l.id,"puntajeDirecto",parseInt(e.target.value)||0),className:"border border-gray-300 rounded px-3 py-1 text-sm w-20",min:"0",max:"100"})}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsx("input",{type:"number",value:l.edad,onChange:e=>r(l.id,"edad",parseInt(e.target.value)||13),className:"border border-gray-300 rounded px-3 py-1 text-sm w-20",min:"12",max:"14"})}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsx("span",{className:"px-2 py-1 text-sm rounded "+(null===l.resultado?"bg-gray-100 text-gray-500":"Error"===l.resultado?"bg-red-100 text-red-800":"bg-green-100 text-green-800"),children:null===l.resultado?"Pendiente":l.resultado})}),m.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[m.jsx("button",{onClick:()=>{return e=l.id,c(null,null,function*(){var a;t(!0);try{const t=s.find(t=>t.id===e);if(!t)return;const r=yield I.probarConversion(t.puntajeDirecto,t.aptitudCodigo,t.edad);r.success?(i(t=>t.map(t=>t.id===e?o(n({},t),{resultado:r.percentil}):t)),x.success(`Conversión exitosa: PD ${t.puntajeDirecto} → PC ${r.percentil}`)):x.error(`Error en conversión: ${(null==(a=r.error)?void 0:a.message)||"Error desconocido"}`)}catch(r){x.error("Error al probar conversión")}finally{t(!1)}});var e},disabled:e,className:"text-blue-600 hover:text-blue-900 disabled:text-gray-400",children:"Probar"}),s.length>1&&m.jsx("button",{onClick:()=>{return e=l.id,void(s.length>1&&i(t=>t.filter(t=>t.id!==e)));var e},disabled:e,className:"text-red-600 hover:text-red-900 disabled:text-gray-400",children:"Eliminar"})]})]},l.id))})]})}),m.jsxs("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[m.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"Aptitudes Disponibles:"}),m.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-blue-800",children:a.map(e=>m.jsxs("div",{children:[m.jsxs("strong",{children:[e.codigo,":"]})," ",e.nombre]},e.codigo))}),m.jsx("p",{className:"text-xs text-blue-700 mt-2",children:"* Edades soportadas: 12-14 años | PD: Puntaje Directo | PC: Percentil"})]})]})})]})})},M=()=>{const[e,t]=d.useState([]),[s,i]=d.useState(!1),[a,r]=d.useState(null),{psychologists:l,loading:n,error:o}=(()=>{const[e,t]=d.useState([]),[s,i]=d.useState(!1),[a,r]=d.useState(null),l=d.useCallback(()=>c(null,null,function*(){try{i(!0),r(null);const{data:e,error:s}=yield O.getPsychologists();if(s)throw s;const a=(e||[]).map(e=>({id:e.id,nombre:e.nombre||"",apellido:e.apellidos||e.apellido||"",email:e.email||"",currentPins:0,hasControl:!1,fullName:`${e.nombre||""} ${e.apellidos||e.apellido||""}`.trim()}));t(a)}catch(e){r("Error al cargar la lista de psicólogos: "+e.message),x.error("Error al cargar la lista de psicólogos: "+e.message)}finally{i(!1)}}),[]);return d.useEffect(()=>{l()},[l]),{psychologists:e,loading:s,error:a,refetch:l}})(),u=()=>c(null,null,function*(){try{i(!0),r(null);const{data:e,error:s}=yield O.getPsychologists();if(s)throw s;t(e||[])}catch(e){r(e.message)}finally{i(!1)}});return d.useEffect(()=>{u()},[]),m.jsxs("div",{className:"p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto",children:[m.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Prueba de Carga de Psicólogos"}),m.jsxs("div",{className:"mb-8",children:[m.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Datos Directos de Supabase"}),s&&m.jsx("p",{className:"text-blue-600",children:"Cargando datos directos..."}),a&&m.jsxs("p",{className:"text-red-600",children:["Error: ",a]}),!s&&!a&&m.jsxs("div",{children:[m.jsxs("p",{className:"mb-2",children:["Total de psicólogos: ",e.length]}),m.jsx("div",{className:"bg-gray-100 p-4 rounded max-h-40 overflow-y-auto",children:m.jsx("pre",{children:JSON.stringify(e,null,2)})})]})]}),m.jsxs("div",{className:"mb-8",children:[m.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Datos del Hook usePsychologists"}),n&&m.jsx("p",{className:"text-blue-600",children:"Cargando datos del hook..."}),o&&m.jsxs("p",{className:"text-red-600",children:["Error: ",o]}),!n&&!o&&m.jsxs("div",{children:[m.jsxs("p",{className:"mb-2",children:["Total de psicólogos transformados: ",l.length]}),m.jsx("div",{className:"bg-gray-100 p-4 rounded max-h-40 overflow-y-auto",children:m.jsx("pre",{children:JSON.stringify(l,null,2)})})]})]}),m.jsxs("div",{className:"mb-8",children:[m.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Lista Desplegable de Prueba"}),m.jsxs("select",{className:"w-full px-4 py-2 border border-gray-300 rounded-lg",children:[m.jsx("option",{value:"",children:"Seleccionar psicólogo..."}),l.map(e=>m.jsxs("option",{value:e.id,children:[e.nombre," ",e.apellido," - ",e.email]},e.id))]}),0===l.length&&!n&&m.jsx("p",{className:"text-red-600 mt-2",children:"⚠️ No hay psicólogos disponibles en la lista"})]}),m.jsx("button",{onClick:u,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Recargar Datos"})]})},R=()=>{const[e,t]=d.useState("testing"),[s,i]=d.useState({loading:!1,data:null,error:null}),[a,r]=d.useState({loading:!1,data:null,error:null}),l=()=>c(null,null,function*(){try{t("testing");const{data:e,error:s}=yield k.from("psicologos").select("count",{count:"exact",head:!0});t(s?"error":"connected")}catch(e){t("error")}}),n=()=>c(null,null,function*(){try{i({loading:!0,data:null,error:null});const{data:e,error:t}=yield k.from("psicologos").select("*").limit(5);i(t?{loading:!1,data:null,error:t.message}:{loading:!1,data:e,error:null})}catch(e){i({loading:!1,data:null,error:e.message})}}),o=()=>c(null,null,function*(){try{r({loading:!0,data:null,error:null});const{data:e,error:t}=yield O.getPsychologists();r(t?{loading:!1,data:null,error:t.message}:{loading:!1,data:e,error:null})}catch(e){r({loading:!1,data:null,error:e.message})}});return d.useEffect(()=>{l(),n(),o()},[]),m.jsxs("div",{className:"p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto",children:[m.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Test de Conexión a Supabase"}),m.jsxs("div",{className:"mb-6",children:[m.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Estado de Conexión"}),m.jsxs("div",{className:"p-3 rounded "+("connected"===e?"bg-green-100 text-green-800":"error"===e?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:["connected"===e&&"✅ Conectado a Supabase","error"===e&&"❌ Error de conexión","testing"===e&&"🔄 Probando conexión..."]})]}),m.jsxs("div",{className:"mb-6",children:[m.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Consulta Directa a Supabase"}),s.loading&&m.jsx("p",{className:"text-blue-600",children:"Cargando..."}),s.error&&m.jsxs("p",{className:"text-red-600",children:["Error: ",s.error]}),s.data&&m.jsxs("div",{children:[m.jsxs("p",{className:"mb-2",children:["Registros encontrados: ",s.data.length]}),m.jsx("div",{className:"bg-gray-100 p-4 rounded max-h-40 overflow-y-auto",children:m.jsx("pre",{children:JSON.stringify(s.data,null,2)})})]})]}),m.jsxs("div",{className:"mb-6",children:[m.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Consulta del Servicio"}),a.loading&&m.jsx("p",{className:"text-blue-600",children:"Cargando..."}),a.error&&m.jsxs("p",{className:"text-red-600",children:["Error: ",a.error]}),a.data&&m.jsxs("div",{children:[m.jsxs("p",{className:"mb-2",children:["Registros encontrados: ",a.data.length]}),m.jsx("div",{className:"bg-gray-100 p-4 rounded max-h-40 overflow-y-auto",children:m.jsx("pre",{children:JSON.stringify(a.data,null,2)})})]})]}),m.jsxs("div",{className:"flex space-x-4",children:[m.jsx("button",{onClick:l,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Probar Conexión"}),m.jsx("button",{onClick:n,className:"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700",children:"Consulta Directa"}),m.jsx("button",{onClick:o,className:"px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700",children:"Consulta del Servicio"})]})]})},T=()=>{const[e,t]=d.useState("institutions"),[s,i]=d.useState(!0),[a,r]=d.useState(!1),l=j(),n=new URLSearchParams(l.search).get("tab");d.useEffect(()=>{n&&["institutions","psychologists","patients","conversion"].includes(n)&&t(n);return(()=>{let e=document.getElementById("modal-root");e||(e=document.createElement("div"),e.id="modal-root",document.body.appendChild(e),e.style.position="relative",e.style.zIndex="9999")})(),setTimeout(()=>{i(!1)},1e3),()=>{const e=document.getElementById("modal-root");e&&0===e.childElementCount&&document.body.removeChild(e)}},[n]);const o=e=>{t(e)};return s?m.jsxs("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gray-100",children:[m.jsx(u,{className:"animate-spin text-blue-600 text-4xl mb-4"}),m.jsx("h2",{className:"text-xl font-semibold text-gray-700",children:"Cargando Panel de Administración..."}),m.jsx("p",{className:"text-gray-500 mt-2",children:"Verificando permisos y cargando datos"})]}):m.jsxs("div",{className:"bg-gray-100 min-h-screen",children:[m.jsx(_,{title:"Panel de Administración",subtitle:"Gestión centralizada de recursos de la plataforma",icon:v}),m.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:m.jsxs("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[m.jsx("div",{className:"border-b border-gray-200",children:m.jsxs("nav",{className:"flex -mb-px",children:[m.jsx("button",{className:`px-2 py-4 text-center text-xs font-medium ${"institutions"===e?"border-b-2 border-blue-500 text-amber-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/7`,onClick:()=>o("institutions"),children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx(h,{className:"mr-1"}),m.jsx("span",{children:"Instituciones"})]})}),m.jsx("button",{className:`px-2 py-4 text-center text-xs font-medium ${"psychologists"===e?"border-b-2 border-blue-500 text-amber-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/7`,onClick:()=>o("psychologists"),children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx(N,{className:"mr-1"}),m.jsx("span",{children:"Psicólogos"})]})}),m.jsx("button",{className:`px-2 py-4 text-center text-xs font-medium ${"patients"===e?"border-b-2 border-blue-500 text-amber-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/7`,onClick:()=>o("patients"),children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx(w,{className:"mr-1"}),m.jsx("span",{children:"Pacientes"})]})}),m.jsx("button",{className:`px-2 py-4 text-center text-xs font-medium ${"test"===e?"border-b-2 border-blue-500 text-red-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/7`,onClick:()=>o("test"),children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx(C,{className:"mr-1"}),m.jsx("span",{children:"Test"})]})}),m.jsx("button",{className:`px-2 py-4 text-center text-xs font-medium ${"supabase"===e?"border-b-2 border-blue-500 text-purple-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/7`,onClick:()=>o("supabase"),children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx(C,{className:"mr-1"}),m.jsx("span",{children:"Supabase"})]})}),m.jsx("button",{className:`px-2 py-4 text-center text-xs font-medium ${"quick"===e?"border-b-2 border-blue-500 text-orange-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/7`,onClick:()=>o("quick"),children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx(C,{className:"mr-1"}),m.jsx("span",{children:"Quick"})]})}),m.jsx("button",{className:`px-2 py-4 text-center text-xs font-medium ${"conversion"===e?"border-b-2 border-blue-500 text-green-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/7`,onClick:()=>o("conversion"),children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx("i",{className:"fas fa-exchange-alt mr-1"}),m.jsx("span",{children:"Conversión"})]})})]})}),m.jsx("div",{className:"p-6",children:(()=>{switch(e){case"institutions":default:return m.jsx(L,{});case"psychologists":return m.jsx(z,{});case"patients":return m.jsx($,{});case"test":case"quick":return m.jsx(M,{});case"supabase":return m.jsx(R,{});case"conversion":return m.jsx(B,{})}})()})]})}),m.jsx("footer",{className:"bg-white border-t border-gray-200 py-8",children:m.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:m.jsxs("p",{className:"text-center text-gray-500 text-sm",children:["© ",(new Date).getFullYear()," Sistema de Gestión Psicológica - Panel de Administración"]})})})]})};export{T as default};
