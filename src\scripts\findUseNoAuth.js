import fs from 'fs';
import path from 'path';

/**
 * <PERSON>ript para encontrar todos los archivos que usan useNoAuth
 */

function findFilesWithUseNoAuth(dir, results = []) {
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // Ignorar node_modules y .git
      if (!file.startsWith('.') && file !== 'node_modules') {
        findFilesWithUseNoAuth(filePath, results);
      }
    } else if (file.endsWith('.jsx') || file.endsWith('.js')) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        if (content.includes('useNoAuth')) {
          const lines = content.split('\n');
          const matchingLines = [];
          
          lines.forEach((line, index) => {
            if (line.includes('useNoAuth')) {
              matchingLines.push({
                lineNumber: index + 1,
                content: line.trim()
              });
            }
          });

          results.push({
            file: filePath,
            matches: matchingLines
          });
        }
      } catch (error) {
        console.error(`Error leyendo ${filePath}:`, error.message);
      }
    }
  }

  return results;
}

function main() {
  console.log('🔍 Buscando archivos que usan useNoAuth...\n');

  const srcDir = path.join(process.cwd(), 'src');
  const results = findFilesWithUseNoAuth(srcDir);

  if (results.length === 0) {
    console.log('✅ No se encontraron archivos que usen useNoAuth');
    return;
  }

  console.log(`❌ Se encontraron ${results.length} archivos que usan useNoAuth:\n`);

  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.file}`);
    result.matches.forEach(match => {
      console.log(`   Línea ${match.lineNumber}: ${match.content}`);
    });
    console.log('');
  });

  console.log('\n🔧 Para corregir estos archivos, reemplaza:');
  console.log('   import { useNoAuth } from \'...NoAuthContext\';');
  console.log('   con:');
  console.log('   import { useAuth } from \'...AuthContext\';');
  console.log('');
  console.log('   Y reemplaza:');
  console.log('   const { ... } = useNoAuth();');
  console.log('   con:');
  console.log('   const { ... } = useAuth();');
}

main();
