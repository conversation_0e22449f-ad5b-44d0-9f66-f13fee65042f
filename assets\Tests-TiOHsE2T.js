import{j as e,L as s}from"./vendor-CIyllXGj.js";import{T as i}from"./TestCard-DWMYHxS2.js";const a=()=>e.jsxDEV("div",{className:"container mx-auto px-4 py-8",children:[e.jsxDEV("div",{className:"mb-12 text-center",children:[e.jsxDEV("div",{className:"flex items-center justify-center mb-4",children:[e.jsxDEV("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mr-3 shadow-lg",children:e.jsxDEV("i",{className:"fas fa-clipboard-list text-white text-lg"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:11,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:10,columnNumber:11},void 0),e.jsxDEV("h1",{className:"text-4xl font-bold text-gray-900",children:"Tests de Aptitud"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:13,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:9,columnNumber:9},void 0),e.jsxDEV("p",{className:"text-lg text-gray-600 whitespace-nowrap",children:"Evaluaciones psicométricas para medir diferentes habilidades y competencias cognitivas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:17,columnNumber:9},void 0),e.jsxDEV("div",{className:"mt-4 w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:20,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:8,columnNumber:7},void 0),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 auto-rows-fr",children:[e.jsxDEV(i,{test:{id:"bat7",title:"Batería Completa BAT-7",description:"Evaluación completa de aptitudes y habilidades cognitivas",time:120,questions:184,path:"/test/bat7"},iconClass:"fas fa-clipboard-list",bgClass:"bg-purple-100",textClass:"text-purple-600",buttonColor:"purple",abbreviation:"BAT",showButton:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:26,columnNumber:9},void 0),e.jsxDEV(i,{test:{id:"verbal",title:"Aptitud Verbal",description:"Evaluación de analogías verbales y comprensión de relaciones entre conceptos",time:12,questions:32,path:"/test/verbal"},iconClass:"fas fa-comments",bgClass:"bg-blue-100",textClass:"text-blue-600",buttonColor:"blue",abbreviation:"V",showButton:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:44,columnNumber:9},void 0),e.jsxDEV(i,{test:{id:"espacial",title:"Aptitud Espacial",description:"Razonamiento espacial con cubos y redes",time:15,questions:28,path:"/test/espacial"},iconClass:"fas fa-cube",bgClass:"bg-indigo-100",textClass:"text-indigo-600",buttonColor:"indigo",abbreviation:"E",showButton:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:62,columnNumber:9},void 0),e.jsxDEV(i,{test:{id:"atencion",title:"Atención",description:"Rapidez y precisión en la localización de símbolos",time:8,questions:80,path:"/test/atencion"},iconClass:"fas fa-eye",bgClass:"bg-red-100",textClass:"text-red-600",buttonColor:"red",abbreviation:"A",showButton:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:80,columnNumber:9},void 0),e.jsxDEV(i,{test:{id:"razonamiento",title:"Razonamiento",description:"Continuar series lógicas de figuras",time:20,questions:32,path:"/test/razonamiento"},iconClass:"fas fa-puzzle-piece",bgClass:"bg-amber-100",textClass:"text-amber-600",buttonColor:"amber",abbreviation:"R",showButton:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:100,columnNumber:9},void 0),e.jsxDEV(i,{test:{id:"numerico",title:"Aptitud Numérica",description:"Resolución de igualdades, series numéricas y análisis de tablas de datos",time:20,questions:32,path:"/test/numerico"},iconClass:"fas fa-calculator",bgClass:"bg-teal-100",textClass:"text-teal-600",buttonColor:"teal",abbreviation:"N",showButton:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:118,columnNumber:9},void 0),e.jsxDEV(i,{test:{id:"mecanico",title:"Aptitud Mecánica",description:"Comprensión de principios físicos y mecánicos básicos",time:12,questions:28,path:"/test/mecanico"},iconClass:"fas fa-cogs",bgClass:"bg-slate-100",textClass:"text-slate-600",buttonColor:"slate",abbreviation:"M",showButton:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:136,columnNumber:9},void 0),e.jsxDEV(i,{test:{id:"ortografia",title:"Ortografía",description:"Identificación de palabras con errores ortográficos",time:10,questions:32,path:"/test/ortografia"},iconClass:"fas fa-spell-check",bgClass:"bg-green-100",textClass:"text-green-600",buttonColor:"green",abbreviation:"O",showButton:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:154,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:24,columnNumber:7},void 0),e.jsxDEV("div",{className:"mt-16",children:[e.jsxDEV("div",{className:"text-center mb-8",children:[e.jsxDEV("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full mb-3 shadow-md",children:e.jsxDEV("i",{className:"fas fa-clipboard-check text-white text-lg"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:176,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:175,columnNumber:11},void 0),e.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Tests Completados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:178,columnNumber:11},void 0),e.jsxDEV("p",{className:"text-gray-600",children:"Revisa tus resultados y progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:179,columnNumber:11},void 0),e.jsxDEV("div",{className:"mt-3 w-16 h-0.5 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mx-auto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:180,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:174,columnNumber:9},void 0),e.jsxDEV("div",{className:"bg-white shadow-sm rounded-lg overflow-hidden border border-gray-100",children:t.length>0?e.jsxDEV("ul",{className:"divide-y divide-gray-200",children:t.map((i,a)=>e.jsxDEV("li",{className:"p-4 hover:bg-gray-50 transition-colors",children:e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:`flex-shrink-0 rounded-md p-2 ${i.iconBg}`,children:e.jsxDEV("i",{className:`fas fa-${i.icon} text-white`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:191,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:190,columnNumber:23},void 0),e.jsxDEV("div",{className:"ml-3",children:[e.jsxDEV("p",{className:"text-sm font-medium text-gray-900",children:i.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:194,columnNumber:25},void 0),e.jsxDEV("div",{className:"flex items-center mt-1",children:[e.jsxDEV("span",{className:"text-xs text-gray-500",children:["Completado: ",i.completedDate]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:196,columnNumber:27},void 0),e.jsxDEV("span",{className:"mx-2 inline-block h-1 w-1 rounded-full bg-gray-300"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:197,columnNumber:27},void 0),e.jsxDEV("span",{className:"text-xs text-gray-500",children:["Puntuación: ",i.score]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:198,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:195,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:193,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:189,columnNumber:21},void 0),e.jsxDEV("div",{children:e.jsxDEV(s,{to:`/test/results/${i.id}`,className:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors",children:[e.jsxDEV("i",{className:"fas fa-chart-bar mr-1.5"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:207,columnNumber:25},void 0),"Ver resultado"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:203,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:202,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:188,columnNumber:19},void 0)},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:187,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:185,columnNumber:13},void 0):e.jsxDEV("div",{className:"py-10 text-center",children:[e.jsxDEV("div",{className:"inline-block p-4 rounded-full bg-gray-100 mb-3",children:e.jsxDEV("i",{className:"fas fa-clipboard-check text-gray-400 text-2xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:218,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:217,columnNumber:15},void 0),e.jsxDEV("p",{className:"text-gray-500",children:"No has completado ningún test todavía."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:220,columnNumber:15},void 0),e.jsxDEV("p",{className:"text-sm text-gray-400 mt-1",children:"Los resultados aparecerán aquí cuando completes tus tests."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:221,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:216,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:183,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:173,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Tests.jsx",lineNumber:7,columnNumber:5},void 0),t=[{id:"1",name:"Aptitud Espacial (E)",completedDate:"25/04/2025",score:"85/100",icon:"cube",iconBg:"bg-indigo-600"},{id:"2",name:"Razonamiento (R)",completedDate:"20/04/2025",score:"78/100",icon:"puzzle-piece",iconBg:"bg-amber-600"},{id:"3",name:"Ortografía (O)",completedDate:"15/04/2025",score:"92/100",icon:"spell-check",iconBg:"bg-green-600"}];export{a as default};
