const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/InformesService-D-fQ1856.js","assets/vendor-BqMjyOVw.js","assets/vendor-B4zyQOk2.css","assets/ImprovedPinControlService-BUPGzexy.js","assets/PinLogger-C2v3yGM1.js","assets/NotificationService-DiDbKBbI.js","assets/Dashboard-CORJVBNr.js","assets/enhancedSupabaseService-D53fSbDl.js","assets/Profile-DQTcor2G.js","assets/Settings-BwazZz_F.js","assets/Home-DdavNFCp.js","assets/Help-Brqj9_nb.js","assets/PageHeader-DzW86ZOX.js","assets/ConfiguracionTemp-BKQHGnpu.js","assets/pinRechargeRequests-BUqDaw89.js","assets/pinNotifications-BMRChPcj.js","assets/Candidates-1amhvm6H.js","assets/useToast-Du4vz6Q_.js","assets/VerbalInfo-TG6K1YOs.js","assets/Users-BPA-1jvj.js","assets/Institutions-DQe7h0iW.js","assets/Reports-BXeRIjfA.js","assets/Reports-DaDNVnb6.css","assets/Patients-CcGkFcaD.js","assets/Administration-Bg2xJkhl.js","assets/TestPage-DLZQfqPn.js","assets/CompleteReport-CuUa_A2s.js","assets/interpretacionesAptitudes-Bt_sak-B.js","assets/SavedReports-D6OwDcM_.js","assets/ViewSavedReport-BhOcD-SZ.js","assets/PinAssignmentPanel-uABOWMMP.js","assets/PinManagementService-DAtjQY2H.js","assets/PinUsageReports-BsptD2em.js","assets/SessionControlService-CcWSYZik.js","assets/PinRechargePrompt-B8It1Ju9.js","assets/PinValidationService-Ki4hIVgd.js","assets/EnhancedNotificationService-heJqBBu8.js","assets/PinRechargeManagement-BjcgmkTM.js","assets/ReportGenerationDemo-BtVTmKZt.js","assets/Students-ZoQ4wGME.js","assets/Tests-dDef3CXv.js","assets/Reports-DFrD-ZUT.js","assets/Tests-dTtTTLPI.js","assets/TestCard-CFSJq7a1.js","assets/TestCard-DBV9lrnR.css","assets/Results-B131fvQB.js","assets/Patients-27jxQMcc.js","assets/Patients-TixZtU9H.css","assets/Questionnaire-BLmNuSfo.js","assets/ChangePassword-CHe3wYuj.js","assets/UnifiedAuthService-Dpry20MH.js","assets/InformePaciente-Cj00uQnj.js","assets/BasicLogin-CdU8_yxW.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,a=Object.defineProperties,s=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,i=(a,s,t)=>s in a?e(a,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[s]=t,o=(e,a)=>{for(var s in a||(a={}))r.call(a,s)&&i(e,s,a[s]);if(t)for(var s of t(a))n.call(a,s)&&i(e,s,a[s]);return e},l=(e,t)=>a(e,s(t)),c=(e,a)=>{var s={};for(var i in e)r.call(e,i)&&a.indexOf(i)<0&&(s[i]=e[i]);if(null!=e&&t)for(var i of t(e))a.indexOf(i)<0&&n.call(e,i)&&(s[i]=e[i]);return s},d=(e,a,s)=>i(e,"symbol"!=typeof a?a+"":a,s),m=(e,a,s)=>new Promise((t,r)=>{var n=e=>{try{o(s.next(e))}catch(a){r(a)}},i=e=>{try{o(s.throw(e))}catch(a){r(a)}},o=e=>e.done?t(e.value):Promise.resolve(e.value).then(n,i);o((s=s.apply(e,a)).next())});import{Q as u,aT as p,aU as x,aV as h,aW as g,r as b,j,u as f,L as y,aX as v,aY as N,am as w,as as C,G as A,H as E,aZ as S,E as T,a_ as q,_ as z,Z as _,a$ as P,N as D,b0 as I,O as k,k as R,U as M,D as O,l as B,Y as $,b1 as L,b2 as V,M as F,h as Q,z as U,P as H,b3 as G,b4 as J,b5 as W,b6 as Y,F as Z,b7 as X,b8 as K,b9 as ee}from"./vendor-BqMjyOVw.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))a(e);new MutationObserver(e=>{for(const s of e)if("childList"===s.type)for(const e of s.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&a(e)}).observe(document,{childList:!0,subtree:!0})}function a(e){if(e.ep)return;e.ep=!0;const a=function(e){const a={};return e.integrity&&(a.integrity=e.integrity),e.referrerPolicy&&(a.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?a.credentials="include":"anonymous"===e.crossOrigin?a.credentials="omit":a.credentials="same-origin",a}(e);fetch(e.href,a)}}();const ae="validation",se="authentication",te="authorization",re="network",ne="server",ie="client",oe="unknown",le="low",ce="medium",de="high",me="critical";class ue extends Error{constructor({message:e,type:a=oe,severity:s=ce,code:t=null,details:r=null,userMessage:n=null,shouldLog:i=!0,shouldNotify:o=!0,retryable:l=!1,originalError:c=null}){super(e),this.name="AppError",this.type=a,this.severity=s,this.code=t,this.details=r,this.userMessage=n||this.generateUserMessage(),this.shouldLog=i,this.shouldNotify=o,this.retryable=l,this.originalError=c,this.timestamp=(new Date).toISOString(),this.stack=(null==c?void 0:c.stack)||this.stack}generateUserMessage(){const e={[ae]:"Por favor, verifique los datos ingresados.",[se]:"Error de autenticación. Verifique sus credenciales.",[te]:"No tiene permisos para realizar esta acción.",[re]:"Error de conexión. Verifique su conexión a internet.",[ne]:"Error del servidor. Intente nuevamente en unos momentos.",[ie]:"Error en la aplicación. Recargue la página.",[oe]:"Ha ocurrido un error inesperado."};return e[this.type]||e[oe]}toJSON(){return{name:this.name,message:this.message,type:this.type,severity:this.severity,code:this.code,details:this.details,userMessage:this.userMessage,timestamp:this.timestamp,stack:this.stack}}}const pe=new class{constructor(){this.errorLog=[],this.maxLogSize=100,this.retryAttempts=new Map,this.maxRetries=3}handle(e,a={}){const s=this.normalizeError(e,a);return s.shouldLog&&this.logError(s,a),s.shouldNotify&&this.notifyUser(s),s}normalizeError(e,a={}){return e instanceof ue?e:(null==e?void 0:e.response)?this.handleHttpError(e,a):(null==e?void 0:e.code)?this.handleServiceError(e,a):e instanceof TypeError&&e.message.includes("fetch")?new ue({message:e.message,type:re,severity:de,userMessage:"Error de conexión. Verifique su conexión a internet.",retryable:!0,originalError:e}):new ue({message:e.message||"Error desconocido",type:oe,severity:ce,originalError:e})}handleHttpError(e,a={}){var s,t;const r=null==(s=e.response)?void 0:s.status,n=null==(t=e.response)?void 0:t.data,i={400:{type:ae,severity:le,userMessage:"Datos inválidos. Verifique la información ingresada."},401:{type:se,severity:ce,userMessage:"Sesión expirada. Por favor, inicie sesión nuevamente."},403:{type:te,severity:ce,userMessage:"No tiene permisos para realizar esta acción."},404:{type:ie,severity:le,userMessage:"Recurso no encontrado."},422:{type:ae,severity:le,userMessage:"Datos inválidos. Verifique la información ingresada."},429:{type:ne,severity:ce,userMessage:"Demasiadas solicitudes. Intente nuevamente en unos momentos.",retryable:!0},500:{type:ne,severity:de,userMessage:"Error del servidor. Intente nuevamente en unos momentos.",retryable:!0},502:{type:ne,severity:de,userMessage:"Servicio temporalmente no disponible.",retryable:!0},503:{type:ne,severity:de,userMessage:"Servicio temporalmente no disponible.",retryable:!0}}[r]||{type:ne,severity:ce,userMessage:"Error del servidor. Intente nuevamente."};return new ue(o({message:(null==n?void 0:n.message)||e.message||`HTTP ${r} Error`,code:r,details:n,originalError:e},i))}handleServiceError(e,a={}){const s={PGRST116:{type:ae,userMessage:"Datos inválidos o faltantes."},PGRST301:{type:te,userMessage:"No tiene permisos para acceder a este recurso."},23505:{type:ae,userMessage:"Ya existe un registro con estos datos."},23503:{type:ae,userMessage:"Referencia inválida en los datos."},invalid_credentials:{type:se,userMessage:"Credenciales inválidas."},email_not_confirmed:{type:se,userMessage:"Email no confirmado. Verifique su correo electrónico."},signup_disabled:{type:te,userMessage:"Registro de usuarios deshabilitado."}}[e.code]||{type:ne,userMessage:"Error del servicio. Intente nuevamente."};return new ue(o({message:e.message||"Service error",code:e.code,details:e.details,originalError:e},s))}logError(e,a={}){const s=l(o({},e.toJSON()),{context:a,url:window.location.href,userAgent:navigator.userAgent,timestamp:(new Date).toISOString()});this.errorLog.unshift(s),this.errorLog.length>this.maxLogSize&&this.errorLog.pop(),e.severity,e.severity!==le&&this.sendToMonitoring(s)}notifyUser(e){const a={position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0};switch(e.severity){case me:case de:u.error(e.userMessage,l(o({},a),{autoClose:8e3}));break;case ce:u.warning(e.userMessage,a);break;case le:u.info(e.userMessage,l(o({},a),{autoClose:3e3}));break;default:u(e.userMessage,a)}}showSuccess(e,a={}){u.success(e,o({position:"top-right",autoClose:3e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0},a))}showInfo(e,a={}){u.info(e,o({position:"top-right",autoClose:4e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0},a))}retry(e,a){return m(this,arguments,function*(e,a,s=this.maxRetries){const t=this.retryAttempts.get(a)||0;if(t>=s)throw this.retryAttempts.delete(a),new ue({message:"Maximum retry attempts exceeded",type:ie,severity:de,userMessage:"Operación fallida después de varios intentos."});try{const s=yield e();return this.retryAttempts.delete(a),s}catch(r){this.retryAttempts.set(a,t+1);const e=Math.min(1e3*Math.pow(2,t),1e4);throw yield new Promise(a=>setTimeout(a,e)),r}})}sendToMonitoring(e){return m(this,null,function*(){})}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[],this.retryAttempts.clear()}createErrorBoundaryHandler(){return(e,a)=>{const s=new ue({message:e.message,type:ie,severity:de,details:a,userMessage:"Ha ocurrido un error en la aplicación. La página se recargará automáticamente.",originalError:e});this.handle(s,{errorBoundary:!0}),setTimeout(()=>{window.location.reload()},3e3)}}},xe=(e,a)=>pe.handle(e,a),he="not_started",ge="in_progress",be="paused",je="completed",fe="expired",ye=p("test/startTestSession",(e,a)=>m(null,[e,a],function*({testId:e,userId:a},{rejectWithValue:s}){try{const s=yield fetch(`/api/tests/${e}/start`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:a})});if(!s.ok)throw new Error("Failed to start test");return yield s.json()}catch(t){return improvedErrorHandler.handleError(t,{context:"startTestSession",userId:a,testId:e}),s(t.message)}})),ve=p("test/submitAnswer",(e,a)=>m(null,[e,a],function*({sessionId:e,questionId:a,answer:s,timeSpent:t},{rejectWithValue:r}){try{const r=yield fetch(`/api/test-sessions/${e}/answers`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({questionId:a,answer:s,timeSpent:t,timestamp:Date.now()})});if(!r.ok)throw new Error("Failed to submit answer");return yield r.json()}catch(n){return xe(n,{context:"submitAnswer",sessionId:e,questionId:a}),r(n.message)}})),Ne=p("test/completeTestSession",(e,a)=>m(null,[e,a],function*({sessionId:e},{getState:a,rejectWithValue:s}){try{const s=a(),{answers:t,startTime:r}=s.test.currentSession,n=yield fetch(`/api/test-sessions/${e}/complete`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({answers:t,completedAt:Date.now(),totalTime:Date.now()-r})});if(!n.ok)throw new Error("Failed to complete test");return yield n.json()}catch(t){return xe(t,{context:"completeTestSession",sessionId:e}),s(t.message)}})),we=p("test/fetchTestHistory",(e,a)=>m(null,[e,a],function*({userId:e,limit:a=10,offset:s=0},{rejectWithValue:t}){try{const t=yield fetch(`/api/users/${e}/test-history?limit=${a}&offset=${s}`);if(!t.ok)throw new Error("Failed to fetch test history");return yield t.json()}catch(r){return xe(r,{context:"fetchTestHistory",userId:e}),t(r.message)}})),Ce=p("test/fetchTestResults",(e,a)=>m(null,[e,a],function*({sessionId:e},{rejectWithValue:a}){try{const a=yield fetch(`/api/test-sessions/${e}/results`);if(!a.ok)throw new Error("Failed to fetch test results");return yield a.json()}catch(s){return xe(s,{context:"fetchTestResults",sessionId:e}),a(s.message)}})),Ae={currentSession:{id:null,testId:null,userId:null,status:he,startTime:null,endTime:null,currentQuestionIndex:0,totalQuestions:0,answers:{},timeSpent:{},progress:0,allowedTime:null,timeRemaining:null,isPaused:!1,pauseStartTime:null,totalPauseTime:0},testConfig:{id:null,name:"",description:"",type:null,version:"1.0",timeLimit:null,questionsPerPage:1,allowBackNavigation:!1,allowPause:!0,randomizeQuestions:!1,randomizeAnswers:!1,showProgress:!0,showTimeRemaining:!0,autoSubmit:!0,passingScore:null},questions:[],currentQuestion:null,results:{sessionId:null,scores:{},percentile:null,interpretation:"",recommendations:[],detailedAnalysis:{},comparisonData:null,generatedAt:null},history:{tests:[],totalCount:0,currentPage:1,hasMore:!1},availableTests:[],loading:{starting:!1,submitting:!1,completing:!1,fetchingHistory:!1,fetchingResults:!1,fetchingTests:!1},errors:{start:null,submit:null,complete:null,history:null,results:null,general:null},ui:{showInstructions:!0,showConfirmation:!1,showResults:!1,highlightedAnswers:[],flaggedQuestions:[],reviewMode:!1,fullscreen:!1},statistics:{totalTestsTaken:0,averageScore:0,bestScore:0,lastTestDate:null,testsByType:{},monthlyProgress:[]},currentTest:null,testResults:null,answeredQuestions:{},timeRemaining:0,testStarted:!1,testCompleted:!1,error:null},Ee=x({name:"test",initialState:Ae,reducers:{initializeSession:(e,a)=>{const{sessionId:s,testConfig:t,questions:r}=a.payload;e.currentSession=l(o({},Ae.currentSession),{id:s,testId:t.id,status:he,totalQuestions:r.length,allowedTime:t.timeLimit,timeRemaining:t.timeLimit}),e.testConfig=t,e.questions=r,e.currentQuestion=r[0]||null,e.currentTest=t,e.timeRemaining=t.timeLimit||0},startSession:e=>{e.currentSession.status=ge,e.currentSession.startTime=Date.now(),e.ui.showInstructions=!1,e.testStarted=!0},pauseSession:e=>{e.testConfig.allowPause&&e.currentSession.status===ge&&(e.currentSession.status=be,e.currentSession.isPaused=!0,e.currentSession.pauseStartTime=Date.now())},resumeSession:e=>{e.currentSession.isPaused&&(e.currentSession.status=ge,e.currentSession.isPaused=!1,e.currentSession.pauseStartTime&&(e.currentSession.totalPauseTime+=Date.now()-e.currentSession.pauseStartTime,e.currentSession.pauseStartTime=null))},goToQuestion:(e,a)=>{const s=a.payload;s>=0&&s<e.questions.length&&(e.currentSession.currentQuestionIndex=s,e.currentQuestion=e.questions[s],e.currentSession.progress=(s+1)/e.questions.length*100)},nextQuestion:e=>{const a=e.currentSession.currentQuestionIndex+1;a<e.questions.length&&(e.currentSession.currentQuestionIndex=a,e.currentQuestion=e.questions[a],e.currentSession.progress=(a+1)/e.questions.length*100)},previousQuestion:e=>{if(e.testConfig.allowBackNavigation){const a=e.currentSession.currentQuestionIndex-1;a>=0&&(e.currentSession.currentQuestionIndex=a,e.currentQuestion=e.questions[a],e.currentSession.progress=(a+1)/e.questions.length*100)}},setAnswer:(e,a)=>{const{questionId:s,answer:t,timeSpent:r}=a.payload;e.currentSession.answers[s]={value:t,timestamp:Date.now(),timeSpent:r||0},r&&(e.currentSession.timeSpent[s]=r),e.answeredQuestions[s]=t},clearAnswer:(e,a)=>{const s=a.payload;delete e.currentSession.answers[s],delete e.currentSession.timeSpent[s],delete e.answeredQuestions[s]},flagQuestion:(e,a)=>{const s=a.payload;e.ui.flaggedQuestions.includes(s)||e.ui.flaggedQuestions.push(s)},unflagQuestion:(e,a)=>{const s=a.payload;e.ui.flaggedQuestions=e.ui.flaggedQuestions.filter(e=>e!==s)},updateTimeRemaining:(e,a)=>{const s=a.payload;e.currentSession.timeRemaining=Math.max(0,s),e.timeRemaining=s,s<=0&&e.testConfig.autoSubmit&&(e.currentSession.status=fe)},toggleInstructions:e=>{e.ui.showInstructions=!e.ui.showInstructions},setShowConfirmation:(e,a)=>{e.ui.showConfirmation=a.payload},setReviewMode:(e,a)=>{e.ui.reviewMode=a.payload},toggleFullscreen:e=>{e.ui.fullscreen=!e.ui.fullscreen},highlightAnswers:(e,a)=>{e.ui.highlightedAnswers=a.payload},setResults:(e,a)=>{e.results=l(o(o({},e.results),a.payload),{generatedAt:Date.now()}),e.ui.showResults=!0,e.testResults=a.payload},clearResults:e=>{e.results=Ae.results,e.ui.showResults=!1,e.testResults=null},updateStatistics:(e,a)=>{e.statistics=o(o({},e.statistics),a.payload)},setError:(e,a)=>{const{type:s,error:t}=a.payload;void 0!==e.errors[s]&&(e.errors[s]=t),e.error=t},clearError:(e,a)=>{const s=a.payload;void 0!==e.errors[s]&&(e.errors[s]=null),"general"===s&&(e.error=null)},clearAllErrors:e=>{e.errors=Ae.errors,e.error=null},setCurrentTest:(e,a)=>{var s;e.currentTest=a.payload,e.answeredQuestions={},e.testStarted=!1,e.testCompleted=!1,e.timeRemaining=(null==(s=a.payload)?void 0:s.duration)?60*a.payload.duration:0,a.payload&&(e.testConfig=l(o(o({},e.testConfig),a.payload),{timeLimit:a.payload.duration?60*a.payload.duration:null}),e.currentSession.timeRemaining=e.timeRemaining)},startTest:e=>{e.testStarted=!0,e.currentSession.status=ge,e.currentSession.startTime=Date.now()},answerQuestion:(e,a)=>{const{questionId:s,answerId:t}=a.payload;e.answeredQuestions[s]=t,e.currentSession.answers[s]={value:t,timestamp:Date.now(),timeSpent:0}},completeTest:e=>{e.testCompleted=!0,e.currentSession.status=je,e.currentSession.endTime=Date.now()},setTestResults:(e,a)=>{e.testResults=a.payload,e.results=l(o(o({},e.results),a.payload),{generatedAt:Date.now()}),e.ui.showResults=!0},setLoading:(e,a)=>{e.loading=a.payload,Object.keys(e.loading).forEach(s=>{e.loading[s]=a.payload})},resetSession:e=>{e.currentSession=Ae.currentSession,e.currentQuestion=null,e.questions=[],e.testConfig=Ae.testConfig,e.ui=Ae.ui,e.errors=Ae.errors},resetTestState:()=>Ae,resetTest:()=>Ae},extraReducers:e=>{e.addCase(ye.pending,e=>{e.loading.starting=!0,e.errors.start=null,e.loading=!0}).addCase(ye.fulfilled,(e,a)=>{e.loading.starting=!1,e.loading=!1;const{session:s,config:t,questions:r}=a.payload;e.currentSession=l(o(o({},e.currentSession),s),{status:ge,startTime:Date.now()}),e.testConfig=t,e.questions=r,e.currentQuestion=r[0]||null,e.currentTest=t,e.testStarted=!0}).addCase(ye.rejected,(e,a)=>{e.loading.starting=!1,e.loading=!1,e.errors.start=a.payload,e.error=a.payload}).addCase(ve.pending,e=>{e.loading.submitting=!0,e.errors.submit=null}).addCase(ve.fulfilled,(e,a)=>{e.loading.submitting=!1}).addCase(ve.rejected,(e,a)=>{e.loading.submitting=!1,e.errors.submit=a.payload}).addCase(Ne.pending,e=>{e.loading.completing=!0,e.errors.complete=null,e.loading=!0}).addCase(Ne.fulfilled,(e,a)=>{e.loading.completing=!1,e.loading=!1,e.currentSession.status=je,e.currentSession.endTime=Date.now(),e.testCompleted=!0,a.payload.results&&(e.results=l(o(o({},e.results),a.payload.results),{sessionId:e.currentSession.id,generatedAt:Date.now()}),e.ui.showResults=!0,e.testResults=a.payload.results)}).addCase(Ne.rejected,(e,a)=>{e.loading.completing=!1,e.loading=!1,e.errors.complete=a.payload,e.error=a.payload}).addCase(we.pending,e=>{e.loading.fetchingHistory=!0,e.errors.history=null}).addCase(we.fulfilled,(e,a)=>{e.loading.fetchingHistory=!1;const{tests:s,totalCount:t,page:r,hasMore:n}=a.payload;1===r?e.history.tests=s:e.history.tests.push(...s),e.history.totalCount=t,e.history.currentPage=r,e.history.hasMore=n}).addCase(we.rejected,(e,a)=>{e.loading.fetchingHistory=!1,e.errors.history=a.payload}).addCase(Ce.pending,e=>{e.loading.fetchingResults=!0,e.errors.results=null}).addCase(Ce.fulfilled,(e,a)=>{e.loading.fetchingResults=!1,e.results=l(o(o({},e.results),a.payload),{generatedAt:Date.now()}),e.ui.showResults=!0,e.testResults=a.payload}).addCase(Ce.rejected,(e,a)=>{e.loading.fetchingResults=!1,e.errors.results=a.payload})}}),{initializeSession:Se,startSession:Te,pauseSession:qe,resumeSession:ze,goToQuestion:_e,nextQuestion:Pe,previousQuestion:De,setAnswer:Ie,clearAnswer:ke,flagQuestion:Re,unflagQuestion:Me,toggleInstructions:Oe,setShowConfirmation:Be,setReviewMode:$e,toggleFullscreen:Le,highlightAnswers:Ve,setResults:Fe,clearResults:Qe,updateStatistics:Ue,clearError:He,clearAllErrors:Ge,resetSession:Je,resetTest:We,setCurrentTest:Ye,startTest:Ze,updateTimeRemaining:Xe,answerQuestion:Ke,completeTest:ea,setTestResults:aa,setLoading:sa,setError:ta,resetTestState:ra}=Ee.actions,na=h({reducer:{test:Ee.reducer},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST"]}}),devTools:!1});g(na.dispatch),na.getState,na.dispatch;const ia=b.createContext(),oa=({children:e})=>{const[a,s]=b.useState({id:"4116c661-bd7d-4af7-8117-966a00d63152",email:"<EMAIL>",nombre:"Henry",apellido:"Rueda",rol:"administrador",tipo_usuario:"administrador"}),[t,r]=b.useState(!1),n={user:a,loading:t,error:null,login:e=>m(null,[e],function*({email:e,password:a}){return{success:!0}}),logout:()=>m(null,null,function*(){return s(null),{success:!0}}),isAuthenticated:!!a,userRole:(null==a?void 0:a.tipo_usuario)||null,isAdmin:"administrador"===(null==a?void 0:a.tipo_usuario),isPsicologo:"psicologo"===(null==a?void 0:a.tipo_usuario),isCandidato:"candidato"===(null==a?void 0:a.tipo_usuario),setUserType:()=>{},userName:a?`${a.nombre||""} ${a.apellido||""}`.trim():"",userFirstName:null==a?void 0:a.nombre,userLastName:null==a?void 0:a.apellido,sessionCreated:null==a?void 0:a.fecha_creacion,lastAccess:null==a?void 0:a.ultimo_acceso};return j.jsx(ia.Provider,{value:n,children:e})},la=()=>{const e=b.useContext(ia);if(!e)throw new Error("useSimpleAuthTemp debe ser usado dentro de SimpleAuthProviderTemp");return e},ca=({children:e})=>j.jsx("div",{className:"page-content opacity-100 transition-opacity duration-200 ease-in-out",children:e}),da=({className:e=""})=>j.jsxs("div",{className:`title-container ${e}`,children:[j.jsx("div",{className:"inline-flex items-center",children:j.jsx("h1",{className:"simple-title text-2xl font-bold text-blue-900",children:j.jsx("span",{children:"BAT-7 Batería de Aptitudes"})})}),j.jsx("style",{jsx:"true",children:"\n        .title-container {\n          display: inline-block;\n        }\n        \n        .simple-title {\n          font-weight: 800;\n          letter-spacing: 1px;\n          color: #1d387a;\n        }\n        \n        /* Responsive */\n        @media (max-width: 768px) {\n          .simple-title {\n            font-size: 1.5rem;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .simple-title {\n            font-size: 1.25rem;\n            text-align: center;\n            line-height: 1.2;\n          }\n        }\n\n        @media (max-width: 360px) {\n          .simple-title {\n            font-size: 1rem;\n          }\n        }\n      "})]}),ma=({isOpen:e,toggleSidebar:a,onLogout:s})=>{const t=w(),[r,n]=b.useState(()=>{const e=localStorage.getItem("sidebarFavorites");return e?JSON.parse(e):{dashboard:!1,home:!1,patients:!1,tests:!1,reports:!1,administration:!1,settings:!1,help:!1}});b.useEffect(()=>{localStorage.setItem("sidebarFavorites",JSON.stringify(r))},[r]);const i=(e,a)=>{a.preventDefault(),a.stopPropagation(),n(a=>l(o({},a),{[e]:!a[e]}))},c=e=>"/"===e?"/"===t.pathname:t.pathname===e||t.pathname.startsWith(e)&&(t.pathname.length===e.length||"/"===t.pathname[e.length]),d=[{title:"Navegación Principal",items:[{name:"Inicio",path:"/home",icon:"home",key:"home"},{name:"Pacientes",path:"/admin/patients",icon:"users",key:"patients"},{name:"Cuestionario",path:"/student/questionnaire",icon:"clipboard-list",key:"tests"},{name:"Resultados",path:"/admin/reports",icon:"chart-bar",key:"reports"}]},{title:"Administración",items:[{name:"Panel Admin",path:"/admin/administration",icon:"shield-alt",key:"administration"},{name:"Configuración",path:"/configuracion",icon:"cog",key:"settings"}]},{title:"Soporte",items:[{name:"Ayuda",path:"/help",icon:"question-circle",key:"help"}]}],m=d.flatMap(e=>e.items).filter(e=>r[e.key]);return j.jsxs("div",{className:"sidebar bg-[#121940] text-[#a4b1cd] fixed top-0 left-0 h-full z-50 transition-all duration-300 ease-in-out\n                     "+(e?"w-64":"w-[70px]"),children:[j.jsxs("div",{className:"sidebar-header p-5 flex justify-between items-center border-b border-opacity-10 border-white",children:[e&&j.jsxs("h1",{className:"sidebar-logo text-3xl font-bold text-white text-center flex-1",children:["Activatu",j.jsx("span",{className:"text-[#ffda0a]",children:"mente"})]}),j.jsx("button",{onClick:a,className:"collapse-button text-[#a4b1cd] cursor-pointer hover:text-white",title:e?"Contraer menú":"Expandir menú","aria-label":e?"Contraer menú":"Expandir menú",children:j.jsx("i",{className:"fas "+(e?"fa-chevron-left":"fa-chevron-right")})})]}),m.length>0&&j.jsxs("div",{className:"sidebar-section py-2 border-b border-opacity-10 border-white",children:[e&&j.jsx("h2",{className:"uppercase text-xs px-5 py-2 tracking-wider font-semibold text-gray-400",children:"FAVORITOS"}),j.jsx("ul",{className:"menu-list",children:m.map(a=>j.jsx("li",{className:"py-3 px-5 hover:bg-opacity-10 hover:bg-white transition-all duration-300 relative transform hover:translate-x-1\n                          "+(c(a.path)?"bg-[#ffda0a] bg-opacity-20 border-l-4 border-[#ffda0a] shadow-lg":""),children:j.jsxs("div",{className:"flex items-center justify-between w-full",children:[j.jsxs(y,{to:a.path,className:"flex items-center flex-grow transition-colors duration-200 "+(c(a.path)?"text-[#ffda0a] font-semibold":"text-[#a4b1cd] hover:text-white"),children:[j.jsx("i",{className:`fas fa-${a.icon} ${e?"mr-3":""} w-5 text-center transition-colors duration-200 ${c(a.path)?"text-[#ffda0a]":""}`}),e&&j.jsx("span",{children:a.name})]}),e&&j.jsx("span",{className:"text-[#ffda0a] cursor-pointer hover:scale-110 transition-transform duration-200",onClick:e=>i(a.key,e),title:"Quitar de favoritos",children:j.jsx("i",{className:"fas fa-star"})})]})},`fav-${a.key}`))})]}),j.jsx("div",{className:"sidebar-content py-2 flex-1 overflow-y-auto",children:d.map((a,s)=>j.jsxs("div",{className:"mb-4",children:[e&&j.jsx("div",{className:"px-5 py-2 mb-2",children:j.jsx("h3",{className:"section-title text-xs font-semibold text-gray-400 uppercase tracking-wider",children:a.title})}),!e&&s>0&&j.jsx("div",{className:"section-separator"}),j.jsx("ul",{className:"menu-list space-y-1",children:a.items.map(a=>j.jsx("li",{className:"menu-item mx-2 rounded-lg transition-all duration-300 relative transform hover:translate-x-1\n                            "+(c(a.path)?"active bg-[#ffda0a] bg-opacity-20 border-l-4 border-[#ffda0a] shadow-lg":"hover:bg-white hover:bg-opacity-10"),children:j.jsxs("div",{className:"flex items-center justify-between w-full px-3 py-3",children:[j.jsxs(y,{to:a.path,className:"flex items-center flex-grow transition-colors duration-200 "+(c(a.path)?"text-[#ffda0a] font-semibold":"text-[#a4b1cd] hover:text-white"),children:[j.jsx("i",{className:`menu-icon fas fa-${a.icon} ${e?"mr-4":"text-center"} w-5 transition-colors duration-200 ${c(a.path)?"text-[#ffda0a]":""}`}),e&&j.jsx("span",{className:"font-medium",children:a.name})]}),e&&j.jsx("span",{className:"favorite-star cursor-pointer hover:text-[#ffda0a] transition-all duration-200 ml-2 "+(r[a.key]?"active text-[#ffda0a]":"text-gray-400"),onClick:e=>i(a.key,e),title:r[a.key]?"Quitar de favoritos":"Añadir a favoritos",children:j.jsx("i",{className:(r[a.key]?"fas":"far")+" fa-star text-sm"})})]})},a.name))})]},a.title))}),j.jsx("div",{className:"mt-auto p-5 border-t border-opacity-10 border-white",children:e?j.jsxs("button",{className:"logout-button flex items-center w-full text-gray-400 hover:text-red-400 cursor-pointer transition-all duration-200 hover:bg-red-500 hover:bg-opacity-10 rounded-lg p-3 border border-transparent hover:border-red-500 hover:border-opacity-30",onClick:s,"aria-label":"Cerrar sesión",children:[j.jsx("i",{className:"fas fa-door-open mr-3 transition-transform duration-200"}),j.jsx("span",{className:"font-medium",children:"Cerrar Sesión"})]}):j.jsx("button",{className:"logout-button flex justify-center w-full text-gray-400 hover:text-red-400 cursor-pointer transition-all duration-200 p-3 rounded-lg border border-transparent hover:border-red-500 hover:border-opacity-30 hover:bg-red-500 hover:bg-opacity-10",onClick:s,title:"Cerrar Sesión","aria-label":"Cerrar sesión",children:j.jsx("i",{className:"fas fa-door-open transition-transform duration-200"})})})]})},ua=()=>{const{user:e,isAdmin:a,isPsicologo:s,isCandidato:t,logout:r}=la(),[n,i]=b.useState(!0),[o,l]=b.useState(!1),c=b.useRef(null),d=f(),u=s,p=e?`${e.nombre||""} ${e.apellido||""}`.trim():"",x=null==e?void 0:e.email,h=()=>m(null,null,function*(){try{yield r(),d("/login")}catch(e){window.location.href="/login"}});return b.useEffect(()=>{const e=e=>{c.current&&!c.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),j.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[j.jsx(ma,{isOpen:n,toggleSidebar:()=>{i(!n)},onLogout:h}),j.jsxs("div",{className:"flex-1 transition-all duration-300 ease-in-out\n                    "+(n?"ml-64":"ml-[70px]"),children:[j.jsx("header",{className:"bg-white shadow-sm",children:j.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:j.jsxs("div",{className:"flex justify-between h-16 items-center",children:[j.jsx("div",{className:"flex items-center",children:j.jsx(da,{})}),j.jsxs("div",{className:"flex items-center relative",ref:c,children:[j.jsxs("button",{className:"flex items-center space-x-3 cursor-pointer hover:bg-gray-50 rounded-lg px-3 py-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",onClick:()=>{l(!o)},id:"user-menu-button","aria-expanded":o,"aria-haspopup":"true","aria-label":"Abrir menú de usuario",children:[j.jsxs("div",{className:"flex flex-col items-end",children:[j.jsx("span",{className:"text-sm font-medium text-gray-800",children:p||x||"Usuario"}),j.jsx("span",{className:"text-xs text-gray-500",children:j.jsxs("span",{className:"inline-flex items-center",children:[j.jsx("span",{className:"w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5 animate-pulse"}),j.jsx("span",{className:"text-green-600 font-semibold",children:"Activo"}),j.jsx("span",{className:"mx-2",children:"•"}),j.jsx("span",{className:"text-amber-600 font-medium",children:a?"Administrador":u?"Psicólogo":"Candidato"})]})})]}),j.jsx("div",{className:"h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-md",children:j.jsx("i",{className:"fas fa-user-shield"})}),j.jsx("div",{className:"text-gray-400",children:j.jsx("i",{className:`fas fa-chevron-${o?"up":"down"} text-xs transition-transform duration-200 ${o?"rotate-180":""}`})})]}),o&&j.jsxs("div",{className:"absolute right-0 top-full mt-2 w-72 bg-white rounded-xl menu-shadow border border-gray-200 z-50 overflow-hidden animate-in user-menu-dropdown",role:"menu","aria-orientation":"vertical","aria-labelledby":"user-menu-button",children:[j.jsx("div",{className:"px-5 py-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-blue-50",children:j.jsxs("div",{className:"flex items-start space-x-4",children:[j.jsx("div",{className:"h-14 w-14 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-lg ring-2 ring-blue-100",children:j.jsx("i",{className:"fas fa-user-shield text-lg"})}),j.jsxs("div",{className:"flex-1 min-w-0",children:[j.jsx("p",{className:"text-base font-semibold text-gray-900 truncate",children:p||x||"Usuario Desarrollo"}),j.jsx("p",{className:"text-sm text-gray-600 truncate mt-0.5",children:x||"<EMAIL>"}),j.jsxs("div",{className:"flex items-center mt-2 space-x-2",children:[j.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800 border border-green-200",children:[j.jsx("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-1.5 animate-pulse"}),"Activo"]}),j.jsx("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 border border-amber-200",children:a?"Administrador":u?"Psicólogo":"Candidato"})]})]})]})}),j.jsxs("div",{className:"py-2",children:[j.jsxs(y,{to:"/profile",className:"flex items-center px-5 py-3 text-sm font-medium text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 focus:outline-none focus:bg-blue-50 focus:text-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-inset",onClick:()=>l(!1),role:"menuitem",tabIndex:0,"aria-label":"Ir a mi perfil",children:[j.jsx("i",{className:"fas fa-user-cog mr-4 text-gray-400 w-4 text-center"}),j.jsx("span",{children:"Mi Perfil"})]}),j.jsxs(y,{to:"/configuracion",className:"flex items-center px-5 py-3 text-sm font-medium text-gray-700 hover:bg-amber-50 hover:text-amber-700 transition-all duration-200 focus:outline-none focus:bg-amber-50 focus:text-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-inset",onClick:()=>l(!1),role:"menuitem",tabIndex:0,"aria-label":"Ir a configuración del sistema",children:[j.jsx("i",{className:"fas fa-cog mr-4 text-gray-400 w-4 text-center"}),j.jsx("span",{children:"Configuración"})]}),j.jsxs(y,{to:"/help",className:"flex items-center px-5 py-3 text-sm font-medium text-gray-700 hover:bg-green-50 hover:text-green-700 transition-all duration-200 focus:outline-none focus:bg-green-50 focus:text-green-700 focus:ring-2 focus:ring-green-500 focus:ring-inset",onClick:()=>l(!1),role:"menuitem",tabIndex:0,"aria-label":"Obtener ayuda y soporte",children:[j.jsx("i",{className:"fas fa-question-circle mr-4 text-gray-400 w-4 text-center"}),j.jsx("span",{children:"Ayuda"})]}),j.jsx("div",{className:"border-t border-gray-200 my-2 mx-2"}),j.jsxs("button",{className:"flex w-full items-center px-5 py-3 text-sm font-medium text-red-700 hover:bg-red-50 hover:text-red-800 transition-all duration-200 focus:outline-none focus:bg-red-50 focus:text-red-800 focus:ring-2 focus:ring-red-500 focus:ring-inset group",onClick:()=>{l(!1),h()},role:"menuitem",tabIndex:0,"aria-label":"Cerrar sesión y salir del sistema",children:[j.jsx("i",{className:"fas fa-door-open mr-4 text-red-500 w-4 text-center group-hover:animate-pulse"}),j.jsx("span",{children:"Cerrar Sesión"})]})]})]})]})]})})}),j.jsx("main",{className:"py-10",children:j.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:j.jsx(ca,{children:j.jsx(v,{})})})}),j.jsx("footer",{className:"bg-white border-t border-gray-200 py-8",children:j.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:j.jsxs("p",{className:"text-center text-gray-500 text-sm",children:["© ",(new Date).getFullYear()," BAT-7 Evaluaciones. Todos los derechos reservados."]})})}),j.jsx(N,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!0,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0})]})]})},pa=({fullScreen:e=!1,message:a="Cargando..."})=>{const s=e?"fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50":"flex flex-col items-center justify-center py-8";return j.jsx("div",{className:s,children:j.jsxs("div",{className:"flex flex-col items-center",children:[j.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),a&&j.jsx("p",{className:"text-gray-600",children:a})]})})};class xa extends C.Component{constructor(e){super(e),d(this,"handleRetry",()=>{this.setState({hasError:!1,error:null,errorInfo:null})}),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,a){this.setState({error:e,errorInfo:a})}render(){return this.state.hasError?j.jsx("div",{className:"min-h-64 flex items-center justify-center bg-red-50 rounded-lg border border-red-200 p-8",children:j.jsxs("div",{className:"text-center",children:[j.jsx(A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),j.jsx("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:this.props.title||"Something went wrong"}),j.jsx("p",{className:"text-red-600 mb-4",children:this.props.message||"An error occurred while loading this component."}),!1,j.jsxs("button",{onClick:this.handleRetry,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 mx-auto transition-colors",children:[j.jsx(E,{}),j.jsx("span",{children:"Try Again"})]})]})}):this.props.children}}const ha=({children:e})=>{const{user:a,isAuthenticated:s,isAdmin:t}=la();return s?t?e:j.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:j.jsxs("div",{className:"max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center",children:[j.jsx("div",{className:"mb-6",children:j.jsx("div",{className:"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100",children:j.jsx("svg",{className:"h-8 w-8 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:j.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"})})})}),j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Acceso Restringido"}),j.jsx("p",{className:"text-sm text-gray-600 mb-6",children:"No tienes permisos para acceder a esta sección. Solo los administradores pueden acceder a esta área."}),j.jsxs("div",{className:"space-y-3",children:[j.jsx("button",{onClick:()=>window.history.back(),className:"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:"Volver Atrás"}),j.jsx("button",{onClick:()=>window.location.href="/home",className:"w-full bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors",children:"Ir al Inicio"})]}),j.jsxs("div",{className:"mt-6 pt-4 border-t border-gray-200",children:[j.jsxs("p",{className:"text-xs text-gray-500",children:["Usuario actual: ",j.jsx("span",{className:"font-medium",children:(null==a?void 0:a.nombre)||(null==a?void 0:a.email)})]}),j.jsxs("p",{className:"text-xs text-gray-500",children:["Rol: ",j.jsx("span",{className:"font-medium",children:(null==a?void 0:a.rol)||"No definido"})]})]})]})}):j.jsx(S,{to:"/login",replace:!0})},ga=(e,a,s={})=>{const{serialize:t=JSON.stringify,deserialize:r=JSON.parse,syncAcrossTabs:n=!0,errorOnFailure:i=!1}=s,[o,l]=b.useState(()=>{if("undefined"==typeof window)return a;try{const s=window.localStorage.getItem(e);return s?r(s):a}catch(s){if(i)throw s;return a}}),c=b.useCallback(a=>{try{const s=a instanceof Function?a(o):a;l(s),"undefined"!=typeof window&&(void 0===s?window.localStorage.removeItem(e):window.localStorage.setItem(e,t(s)))}catch(s){if(i)throw s}},[e,t,o,i]),d=b.useCallback(()=>{try{l(void 0),"undefined"!=typeof window&&window.localStorage.removeItem(e)}catch(a){if(i)throw a}},[e,i]);return b.useEffect(()=>{if(!n||"undefined"==typeof window)return;const a=a=>{if(a.key===e&&a.newValue!==t(o))try{const e=a.newValue?r(a.newValue):void 0;l(e)}catch(s){if(i)throw s}};return window.addEventListener("storage",a),()=>window.removeEventListener("storage",a)},[e,o,t,r,n,i]),[o,c,d]},ba=b.createContext(),ja=()=>{const e=b.useContext(ba);if(!e)throw new Error("usePatientSession must be used within a PatientSessionProvider");return e},fa=({children:e})=>{const[a,s]=ga("bat7_selected_patient",null),[t,r]=b.useState(!1),[n,i]=ga("bat7_selected_level","E"),[o,l]=ga("bat7_completed_tests",[]),[c,d]=ga("bat7_session_start",null);b.useEffect(()=>{a&&c&&r(!0)},[a,c]);const m={selectedPatient:a,isSessionActive:t,selectedLevel:n,completedTests:o,sessionStartTime:c,startPatientSession:(e,a="E")=>{s(e),i(a),r(!0),d((new Date).toISOString()),l([])},endPatientSession:()=>{s(null),r(!1),d(null),l([])},markTestCompleted:e=>{l(a=>a.includes(e)?a:[...a,e])},isTestCompleted:e=>o.includes(e),updateSelectedLevel:e=>{i(e)},getSessionInfo:()=>t&&a?{patient:a,level:n,startTime:c,completedTests:o,duration:c?Math.floor((new Date-new Date(c))/1e3/60):0}:null,clearSessionData:()=>{s(null),r(!1),d(null),l([]),i("E")},hasActiveSession:t&&a,sessionDuration:c?Math.floor((new Date-new Date(c))/1e3/60):0};return j.jsx(ba.Provider,{value:m,children:e})},ya=e=>{var a=e,{children:s,className:t=""}=a,r=c(a,["children","className"]);return j.jsx("div",l(o({className:`bg-white rounded-lg shadow-sm border border-gray-200 ${t}`},r),{children:s}))},va=e=>{var a=e,{children:s,className:t=""}=a,r=c(a,["children","className"]);return j.jsx("div",l(o({className:`px-6 py-4 border-b border-gray-200 ${t}`},r),{children:s}))},Na=e=>{var a=e,{children:s,className:t=""}=a,r=c(a,["children","className"]);return j.jsx("div",l(o({className:`px-6 py-4 ${t}`},r),{children:s}))},wa=e=>{var a=e,{children:s,className:t=""}=a,r=c(a,["children","className"]);return j.jsx("div",l(o({className:`px-6 py-4 border-t border-gray-200 ${t}`},r),{children:s}))},Ca=e=>{var a=e,{children:s,variant:t="primary",size:r="md",className:n="",disabled:i=!1,as:d="button",to:m}=a,u=c(a,["children","variant","size","className","disabled","as","to"]);const p=`inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm",secondary:"bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-400 shadow-sm",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-400 shadow-sm",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm"}[t]} ${{sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-6 py-3"}[r]} ${i?"opacity-50 cursor-not-allowed":"cursor-pointer"} ${n}`;if(d===y||"Link"===d&&m)return j.jsx(y,l(o({to:m,className:p},u),{children:s}));if("function"==typeof d){const e=d;return j.jsx(e,l(o({className:p},u),{children:s}))}return j.jsx("button",l(o({className:p,disabled:i},u),{children:s}))},Aa={verbal:{id:"verbal",name:"Test de Aptitud Verbal",type:"verbal",description:"Test V - Evaluación de analogías verbales. Este test evalúa la capacidad para identificar relaciones entre palabras y conceptos, midiendo el razonamiento verbal y la comprensión de relaciones lógicas.",duration:12,numberOfQuestions:32,instructions:["Lee cada pregunta detenidamente antes de responder.","En cada ejercicio, debes encontrar la palabra que completa la frase dotándola de sentido.","Para las analogías verbales, identifica la relación exacta entre el primer par de palabras.","Entre las cuatro opciones, solo UNA es la correcta.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas.","Trabaja rápidamente, ya que el tiempo es limitado.","Si no estás completamente seguro de una respuesta, elige la opción que creas más correcta; no se penalizan los errores."],additionalInfo:"Este test evalúa tu capacidad para comprender relaciones entre conceptos expresados a través de palabras. Implica el dominio del lenguaje y la habilidad para entender relaciones lógicas entre conceptos verbales.",components:[{name:"Analogías Verbales",description:"Mide tu capacidad para identificar relaciones entre conceptos"},{name:"Razonamiento Verbal",description:"Evalúa tu habilidad para entender relaciones lógicas"},{name:"Comprensión Lingüística",description:"Mide tu dominio del lenguaje y vocabulario"},{name:"Pensamiento Abstracto",description:"Evalúa tu capacidad para identificar patrones conceptuales"}],recommendations:["Fíjate bien en la relación entre el primer par de palabras para identificar el patrón que debes aplicar.","Si no encuentras la respuesta inmediatamente, analiza cada opción eliminando las que claramente no cumplen con la relación buscada.","Recuerda que las relaciones pueden ser de diversos tipos: causa-efecto, parte-todo, función, oposición, etc.","Si terminas antes del tiempo concedido, aprovecha para revisar tus respuestas."]},ortografia:{id:"ortografia",name:"Test de Ortografía",type:"ortografia",description:"Test O - Evaluación de la capacidad para identificar errores ortográficos en palabras.",duration:10,numberOfQuestions:32,instructions:["En cada grupo de cuatro palabras, identificar la única palabra que está mal escrita (intencionadamente).","La falta de ortografía puede ser de cualquier tipo, incluyendo errores en letras o la ausencia/presencia incorrecta de una tilde.","Marcar la letra correspondiente (A, B, C o D) a la palabra mal escrita.","Trabajar rápidamente. Si no se está seguro, elegir la opción que parezca más correcta (no se penaliza el error).","Si se termina antes, repasar las respuestas."],additionalInfo:"Este test evalúa tu dominio de las reglas ortográficas del español, incluyendo acentuación, uso de letras específicas y formación de palabras.",components:[{name:"Ortografía General",description:"Mide tu conocimiento de las reglas básicas de escritura"},{name:"Acentuación",description:"Evalúa tu dominio de las reglas de acentuación"},{name:"Uso de Letras",description:"Mide tu conocimiento del uso correcto de letras que pueden confundirse"},{name:"Atención al Detalle",description:"Evalúa tu capacidad para detectar errores sutiles"}],recommendations:["Revisa visualmente cada palabra con atención.","Recuerda las reglas de acentuación de palabras agudas, llanas y esdrújulas.","Presta especial atención a las letras que suelen causar confusión: b/v, g/j, h, etc.","Observa la presencia o ausencia de tildes en las palabras."],examples:[{question:"A. año, B. berso, C. vuelo, D. campana",answer:"B",explanation:'La grafía correcta es "verso".'},{question:"A. bosque, B. armario, C. telon, D. libro",answer:"C",explanation:'La palabra correcta es "telón", lleva tilde en la "o".'}]},razonamiento:{id:"razonamiento",name:"Test de Razonamiento",type:"razonamiento",description:"Test R - Evaluación de la capacidad para identificar patrones y continuar series lógicas de figuras.",duration:20,numberOfQuestions:32,instructions:["Observar una serie de figuras y determinar qué figura (A, B, C o D) debería ir a continuación, sustituyendo al interrogante, siguiendo la lógica de la serie.","Analiza cuidadosamente cómo evolucionan las figuras en cada serie.","Busca patrones como rotaciones, traslaciones, adiciones o sustracciones de elementos.","Entre las cuatro opciones, solo UNA es la correcta.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas.","Trabaja metódicamente, ya que algunas secuencias pueden tener patrones complejos.","Si no estás completamente seguro de una respuesta, intenta descartar opciones que claramente no siguen el patrón."],additionalInfo:"Este test evalúa tu capacidad para identificar patrones lógicos y aplicarlos para predecir el siguiente elemento en una secuencia. Es una medida del razonamiento inductivo y del pensamiento lógico-abstracto.",components:[{name:"Razonamiento Inductivo",description:"Mide tu capacidad para identificar reglas a partir de ejemplos"},{name:"Pensamiento Lógico",description:"Evalúa tu habilidad para aplicar reglas sistemáticamente"},{name:"Visualización Espacial",description:"Mide tu capacidad para manipular imágenes mentalmente"},{name:"Atención al Detalle",description:"Evalúa tu capacidad para detectar patrones sutiles"}],recommendations:["Intenta identificar más de un patrón en cada serie (puede haber cambios en color, forma, tamaño y posición).","Observa si hay ciclos repetitivos en los patrones.","Analiza cada elemento individualmente si la figura es compleja.","Si encuentras dificultades, intenta verbalizar el patrón para hacerlo más claro."]},atencion:{id:"atencion",name:"Test de Atención",type:"atencion",description:"Test A - Evaluación de la rapidez y precisión en la localización de símbolos.",duration:8,numberOfQuestions:80,instructions:["En cada ejercicio aparece una fila con diferentes símbolos y tu tarea consistirá en localizar cuántas veces aparece uno determinado.","El símbolo que tienes que localizar es siempre el mismo y se presenta en la parte superior de la página.","El símbolo puede aparecer 0, 1, 2 o 3 veces en cada fila, pero nunca más de 3.","Deberás marcar cuántas veces aparece el símbolo en cada fila (0, 1, 2 o 3).","Trabaja con rapidez y precisión, asegurándote de que tu respuesta se corresponda con el número del ejercicio que estás contestando.","Avanza sistemáticamente por cada fila, de izquierda a derecha.","Presta especial atención a símbolos muy similares al modelo pero que no son idénticos."],additionalInfo:"Esta prueba trata de evaluar tu rapidez y tu precisión trabajando con símbolos. Es una medida de la atención selectiva y sostenida, así como de la velocidad y precisión en el procesamiento de información visual.",components:[{name:"Atención Selectiva",description:"Mide tu capacidad para enfocarte en elementos específicos"},{name:"Velocidad Perceptiva",description:"Evalúa tu rapidez para procesar información visual"},{name:"Discriminación Visual",description:"Mide tu habilidad para distinguir detalles visuales"},{name:"Concentración",description:"Evalúa tu capacidad para mantener el foco durante una tarea repetitiva"}],recommendations:["Mantén un ritmo constante, sin detenerte demasiado en ningún elemento.","Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta; no se penalizará el error.","Si terminas antes del tiempo concedido, repasa tus respuestas.","Utiliza el dedo o un marcador para seguir las filas si te ayuda a mantener el enfoque.","Evita distracciones y mantén la concentración en la tarea."]},espacial:{id:"espacial",name:"Test de Aptitud Espacial",type:"espacial",description:"Test E - Evaluación del razonamiento espacial con cubos y redes.",duration:15,numberOfQuestions:28,instructions:["En cada ejercicio encontrarás un cubo junto con su modelo desplegado, al que se le han borrado casi todos los números y letras.","Tu tarea consistirá en averiguar qué número o letra debería aparecer en lugar del interrogante (?) y en qué orientación.","En el cubo se han representado en color gris los números o letras que se encuentran en las caras de atrás (las que no se ven directamente).","Observa cuidadosamente la orientación y posición relativa de las caras en el cubo.","Considera cómo las distintas caras del cubo se conectan entre sí en el modelo desplegado.","Visualiza mentalmente el proceso de plegado del modelo para formar el cubo.","Entre las cuatro opciones, solo UNA es la correcta.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas."],additionalInfo:"Este test evalúa tu capacidad para manipular objetos mentalmente en el espacio tridimensional. Es una medida de la visualización espacial, rotación mental y comprensión de relaciones espaciales.",components:[{name:"Visualización Espacial",description:"Mide tu capacidad para manipular objetos en 3D mentalmente"},{name:"Rotación Mental",description:"Evalúa tu habilidad para rotar figuras en tu mente"},{name:"Relaciones Espaciales",description:"Mide tu comprensión de cómo se conectan las partes de un objeto"},{name:"Razonamiento Geométrico",description:"Evalúa tu entendimiento de principios geométricos básicos"}],recommendations:["Utiliza marcas mentales para orientarte en la ubicación de cada cara del cubo.","Fíjate en detalles específicos de los diseños en cada cara para determinar su orientación correcta.","Si es necesario, utiliza tus manos para ayudarte a visualizar el plegado del modelo.","Si en algún ejercicio no estás completamente seguro de cuál puede ser la respuesta, elige la opción que creas que es más correcta; no se penalizará el error.","Si terminas antes del tiempo concedido, repasa tus respuestas."]},mecanico:{id:"mecanico",name:"Test de Razonamiento Mecánico",type:"mecanico",description:"Test M - Evaluación de la comprensión de principios físicos y mecánicos básicos.",duration:12,numberOfQuestions:28,instructions:["Observar dibujos que representan diversas situaciones físicas o mecánicas y responder a una pregunta sobre cada situación, eligiendo la opción más adecuada.","Analiza los elementos del dibujo y cómo interactúan entre sí.","Aplica principios básicos de física y mecánica como palancas, poleas, engranajes, fuerzas, etc.","Ten en cuenta la dirección de las fuerzas, el movimiento o el equilibrio en cada situación.","Entre las opciones presentadas, selecciona la que mejor explica el fenómeno o predice el resultado.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas.","Si no estás seguro, intenta aplicar el sentido común y los principios básicos que conozcas."],additionalInfo:"Este test evalúa tu comprensión intuitiva de principios físicos y mecánicos, así como tu capacidad para aplicar estos principios a situaciones prácticas. No requiere conocimientos técnicos avanzados, sino una comprensión básica de cómo funcionan los objetos en el mundo físico.",components:[{name:"Comprensión Física",description:"Mide tu entendimiento de principios físicos básicos"},{name:"Razonamiento Mecánico",description:"Evalúa tu capacidad para entender sistemas mecánicos"},{name:"Resolución de Problemas",description:"Mide tu habilidad para aplicar principios a situaciones nuevas"},{name:"Intuición Tecnológica",description:"Evalúa tu comprensión natural de cómo funcionan las máquinas"}],recommendations:["Recuerda principios básicos como la ley de la palanca, la transmisión de fuerzas en poleas y engranajes.","Considera factores como la gravedad, la fricción y la inercia cuando analices cada situación.","Visualiza el movimiento o la acción que ocurriría en la situación presentada.","Si tienes dificultades, intenta simplificar el problema a sus componentes más básicos."]},numerico:{id:"numerico",name:"Test de Aptitud Numérica",type:"numerico",description:"Test N - Resolución de problemas numéricos, series y tablas.",duration:20,numberOfQuestions:32,instructions:["En esta prueba encontrarás distintos ejercicios numéricos que tendrás que resolver.","Para ello tendrás que analizar la información que se presenta y determinar qué debe aparecer en lugar del interrogante.","Cuando lo hayas decidido, deberás marcar la letra de la opción correspondiente en la hoja de respuestas.","Asegúrate de que coincida con el ejercicio que estás contestando.","","El tiempo máximo para su realización es de 20 minutos, por lo que deberás trabajar rápidamente.","Esfuérzate al máximo en encontrar la respuesta correcta.","Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta.","No se penalizará el error.","Si terminas antes del tiempo concedido, repasa tus respuestas, pero NO continúes con las demás pruebas."],additionalInfo:"Este test evalúa tu capacidad para resolver problemas numéricos mediante el análisis de igualdades, series y tablas. Mide el razonamiento matemático, la identificación de patrones numéricos y la habilidad para trabajar con datos organizados.",components:[{name:"Igualdades Numéricas",description:"Resolver ecuaciones con elementos faltantes"},{name:"Series Numéricas",description:"Identificar patrones y continuar secuencias"},{name:"Tablas de Datos",description:"Analizar información organizada y encontrar valores faltantes"},{name:"Cálculo Mental",description:"Realizar operaciones matemáticas con rapidez y precisión"}],recommendations:["Lee cuidadosamente cada problema antes de intentar resolverlo.","En las igualdades, calcula primero el lado conocido de la ecuación.","En las series, busca patrones simples antes de considerar reglas más complejas.","En las tablas, analiza las relaciones entre filas y columnas.","Verifica tus cálculos cuando sea posible.","Si no estás seguro, elige la opción que te parezca más lógica."]},bat7:{id:"bat7",name:"Batería Completa BAT-7",type:"battery",description:"Evaluación completa de aptitudes y habilidades cognitivas.",duration:120,numberOfQuestions:184,instructions:["Lee atentamente cada pregunta antes de responder.","Responde a todas las preguntas, aunque no estés seguro/a de la respuesta.","Administra bien tu tiempo. Si una pregunta te resulta difícil, pasa a la siguiente y vuelve a ella más tarde.","No uses calculadora ni ningún otro dispositivo o material durante el test.","Una vez iniciado el test, no podrás pausarlo. Asegúrate de disponer del tiempo necesario para completarlo.","Responde con honestidad. Este test está diseñado para evaluar tus habilidades actuales.","Cada subtest tiene instrucciones específicas que deberás leer antes de comenzar esa sección."],additionalInfo:"La batería BAT-7 está compuesta por siete pruebas independientes que evalúan diferentes aptitudes: verbal, espacial, numérica, mecánica, razonamiento, atención y ortografía. Cada prueba tiene un tiempo específico de realización y unas instrucciones particulares.",subtests:[{id:"verbal",name:"Test de Aptitud Verbal (V)",duration:12,questions:32,description:"Evaluación de analogías verbales y comprensión de relaciones entre conceptos."},{id:"ortografia",name:"Test de Ortografía (O)",duration:10,questions:32,description:"Identificación de palabras con errores ortográficos."},{id:"razonamiento",name:"Test de Razonamiento (R)",duration:20,questions:32,description:"Continuación de series lógicas de figuras."},{id:"atencion",name:"Test de Atención (A)",duration:8,questions:80,description:"Rapidez y precisión en la localización de símbolos."},{id:"espacial",name:"Test de Visualización Espacial (E)",duration:15,questions:28,description:"Razonamiento espacial con cubos y redes."},{id:"mecanico",name:"Test de Razonamiento Mecánico (M)",duration:12,questions:28,description:"Comprensión de principios físicos y mecánicos básicos."},{id:"numerico",name:"Test de Razonamiento Numérico (N)",duration:20,questions:32,description:"Resolución de problemas numéricos, series y tablas."}],recommendations:["Descansa adecuadamente antes de realizar la batería completa.","Realiza los tests en un ambiente tranquilo y sin distracciones.","Gestiona tu energía a lo largo de toda la batería, ya que algunos tests son más exigentes que otros.","Mantén una actitud positiva y confía en tus capacidades."]}},Ea={bat7:{icon:"fas fa-clipboard-list",color:"text-purple-600"},verbal:{icon:"fas fa-comments",color:"text-blue-600"},espacial:{icon:"fas fa-cube",color:"text-indigo-600"},atencion:{icon:"fas fa-eye",color:"text-red-600"},razonamiento:{icon:"fas fa-puzzle-piece",color:"text-amber-600"},numerico:{icon:"fas fa-calculator",color:"text-teal-600"},mecanico:{icon:"fas fa-cogs",color:"text-slate-600"},ortografia:{icon:"fas fa-spell-check",color:"text-green-600"}},Sa=()=>{var e;const{testId:a}=T(),s=f(),t=w(),[r,n]=b.useState(null),[i,o]=b.useState(!0),[l,c]=b.useState(!1),d=null==(e=t.state)?void 0:e.patientId;b.useEffect(()=>{m(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800)),Aa[a]?n(Aa[a]):u.warning(`No se encontraron instrucciones para el test: ${a}`),o(!1)}catch(e){u.error("Error al cargar la información del test"),o(!1)}})},[a]);const p=Ea[a]||{icon:"fas fa-clipboard-list",color:"text-gray-600"};return j.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[j.jsxs("div",{className:"mb-6 text-center",children:[j.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[j.jsx("i",{className:`${p.icon} ${p.color} mr-2`}),"Instrucciones del Test"]}),!i&&r&&j.jsx("p",{className:"text-gray-600",children:r.name})]}),i?j.jsx("div",{className:"py-16 text-center",children:j.jsxs("div",{className:"flex flex-col items-center justify-center",children:[j.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),j.jsx("p",{className:"text-gray-500",children:"Cargando instrucciones del test..."})]})}):r?j.jsxs(j.Fragment,{children:[j.jsxs(ya,{className:"mb-6",children:[j.jsx(va,{className:"text-center",children:j.jsx("h2",{className:"text-lg font-medium",children:"Información General"})}),j.jsxs(Na,{children:[j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[j.jsxs("div",{children:[j.jsx("h3",{className:"text-md font-medium mb-2 text-center",children:"Descripción"}),j.jsx("p",{className:"text-gray-700",children:r.description})]}),j.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[j.jsxs("div",{children:[j.jsx("h3",{className:"text-md font-medium mb-2 text-center",children:"Duración"}),j.jsxs("p",{className:"text-gray-700 text-center",children:[r.duration," minutos"]})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-md font-medium mb-2 text-center",children:"Preguntas"}),j.jsxs("p",{className:"text-gray-700 text-center",children:[r.numberOfQuestions," preguntas"]})]})]})]}),r.additionalInfo&&j.jsx("div",{className:"bg-blue-50 border-l-4 border-blue-500 p-4 mb-4",children:j.jsx("p",{className:"text-blue-700",children:r.additionalInfo})}),"battery"!==r.type&&r.components&&j.jsxs("div",{className:"mt-6",children:[j.jsx("h3",{className:"text-md font-medium mb-3 text-center",children:"Componentes Evaluados"}),j.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:r.components.map((e,a)=>j.jsxs("div",{className:"border rounded p-3",children:[j.jsx("p",{className:"font-medium",children:e.name}),j.jsx("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]},a))})]}),"battery"===r.type&&r.subtests&&r.subtests.length>0&&j.jsxs("div",{className:"mt-6",children:[j.jsx("h3",{className:"text-md font-medium mb-3 text-center",children:"Subtests"}),j.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:r.subtests.map((e,a)=>j.jsxs("div",{className:"border rounded p-3",children:[j.jsxs("p",{className:"font-medium",children:[a+1,". ",e.name]}),j.jsx("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),j.jsxs("div",{className:"flex justify-between mt-2 text-sm text-gray-600",children:[j.jsxs("span",{children:[e.duration," min"]}),j.jsxs("span",{children:[e.questions," preguntas"]})]})]},e.id))})]})]})]}),j.jsxs(ya,{className:"mb-6",children:[j.jsx(va,{className:"text-center",children:j.jsx("h2",{className:"text-lg font-medium",children:"Instrucciones"})}),j.jsxs(Na,{children:[j.jsx("div",{className:"space-y-3",children:r.instructions.map((e,a)=>{if(""===e)return j.jsx("div",{className:"h-4"},a);if(e.startsWith("**")&&e.endsWith("**"))return j.jsx("h4",{className:"text-lg font-semibold text-gray-800 mt-6 mb-3 border-b border-gray-200 pb-2",children:e.replace(/\*\*/g,"")},a);const s=r.instructions.slice(0,a+1).filter(e=>""!==e&&!e.startsWith("**")).length;return j.jsxs("div",{className:"flex items-start",children:[j.jsx("div",{className:"flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-blue-100 text-blue-600 mr-3 mt-0.5",children:s}),j.jsx("p",{className:"text-gray-700",children:e})]},a)})}),r.recommendations&&j.jsxs("div",{className:"mt-6 bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[j.jsx("h3",{className:"text-md font-medium text-yellow-800 mb-2",children:"Recomendaciones Adicionales"}),j.jsx("ul",{className:"space-y-2 text-yellow-700",children:r.recommendations.map((e,a)=>j.jsxs("li",{children:["• ",e]},a))})]})]})]}),j.jsxs(ya,{children:[j.jsx(Na,{children:j.jsxs("div",{className:"flex items-start mb-4",children:[j.jsx("input",{type:"checkbox",id:"accept-conditions",checked:l,onChange:e=>c(e.target.checked),className:"h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500 mt-1"}),j.jsx("label",{htmlFor:"accept-conditions",className:"ml-3 text-gray-700",children:"He leído y acepto las instrucciones. Entiendo que una vez iniciado el test no podré pausarlo y deberé completarlo en su totalidad."})]})}),j.jsx(wa,{className:"flex justify-end",children:j.jsx(Ca,{variant:l?"primary":"outline",onClick:()=>{l?(u.info("Iniciando test..."),"battery"===r.type?r.subtests&&r.subtests.length>0?s(`/test/${r.subtests[0].id}`,{state:{patientId:d}}):s("/student/tests"):s(`/test/${r.id}`,{state:{patientId:d}})):u.warning("Debes aceptar las condiciones para continuar")},disabled:!l,children:"Iniciar Test"})})]})]}):j.jsx(ya,{children:j.jsx(Na,{children:j.jsx("div",{className:"py-8 text-center",children:j.jsx("p",{className:"text-gray-500",children:"No se encontró información para el test solicitado."})})})})]})},Ta=Object.freeze(Object.defineProperty({__proto__:null,default:Sa},Symbol.toStringTag,{value:"Module"})),qa={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c",VITE_SUPABASE_URL:"https://ydglduxhgwajqdseqzpy.supabase.co"};var za={};function _a(e){return void 0!==import.meta&&qa?qa[e]:"undefined"!=typeof process&&za?za[e]:void 0}let Pa=null;function Da(){return Pa||(Pa=function(){const e=_a("VITE_SUPABASE_URL")||_a("SUPABASE_URL"),a=_a("VITE_SUPABASE_ANON_KEY")||_a("SUPABASE_ANON_KEY");if(!e||!a)throw new Error("Variables de entorno de Supabase no definidas");return q(e,a,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}})}()),Pa}const Ia=new Proxy({},{get(e,a){const s=Da(),t=s[a];return"function"==typeof t?t.bind(s):t}}),ka={"12-13":{V:[{pc:99,pd:[30,32]},{pc:97,pd:[29,29]},{pc:95,pd:[28,28]},{pc:90,pd:[27,27]},{pc:85,pd:[26,26]},{pc:80,pd:[25,25]},{pc:70,pd:[24,24]},{pc:65,pd:[23,23]},{pc:55,pd:[22,22]},{pc:50,pd:[21,21]},{pc:40,pd:[20,20]},{pc:35,pd:[19,19]},{pc:25,pd:[18,18]},{pc:20,pd:[17,17]},{pc:15,pd:[16,16]},{pc:10,pd:[15,15]},{pc:5,pd:[13,14]},{pc:4,pd:[12,12]},{pc:2,pd:[11,11]},{pc:1,pd:[0,10]}],E:[{pc:99,pd:[27,28]},{pc:96,pd:[26,26]},{pc:95,pd:[25,25]},{pc:90,pd:[24,24]},{pc:85,pd:[23,23]},{pc:80,pd:[22,22]},{pc:75,pd:[21,21]},{pc:70,pd:[20,20]},{pc:60,pd:[19,19]},{pc:45,pd:[18,18]},{pc:55,pd:[18,18]},{pc:50,pd:[17,17]},{pc:40,pd:[16,16]},{pc:35,pd:[15,15]},{pc:25,pd:[14,14]},{pc:20,pd:[13,13]},{pc:15,pd:[12,12]},{pc:10,pd:[11,11]},{pc:5,pd:[9,10]},{pc:4,pd:[8,8]},{pc:3,pd:[7,7]},{pc:2,pd:[6,6]},{pc:1,pd:[0,5]}],A:[{pc:99,pd:[49,80]},{pc:98,pd:[48,48]},{pc:97,pd:[46,47]},{pc:96,pd:[44,45]},{pc:95,pd:[43,43]},{pc:90,pd:[39,42]},{pc:85,pd:[36,38]},{pc:80,pd:[35,35]},{pc:75,pd:[34,34]},{pc:70,pd:[33,33]},{pc:65,pd:[31,32]},{pc:60,pd:[29,30]},{pc:55,pd:[28,28]},{pc:50,pd:[27,27]},{pc:45,pd:[26,26]},{pc:40,pd:[25,25]},{pc:35,pd:[24,24]},{pc:30,pd:[23,23]},{pc:25,pd:[22,22]},{pc:20,pd:[21,21]},{pc:15,pd:[19,20]},{pc:10,pd:[17,18]},{pc:5,pd:[15,16]},{pc:4,pd:[13,14]},{pc:2,pd:[12,12]},{pc:1,pd:[0,11]}],CON:[{pc:99,pd:[98,100]},{pc:97,pd:[96,97]},{pc:96,pd:[95,95]},{pc:95,pd:[94,94]},{pc:90,pd:[91,93]},{pc:85,pd:[89,90]},{pc:80,pd:[88,88]},{pc:75,pd:[85,87]},{pc:70,pd:[83,84]},{pc:65,pd:[82,82]},{pc:60,pd:[80,81]},{pc:55,pd:[78,79]},{pc:50,pd:[76,77]},{pc:45,pd:[74,75]},{pc:40,pd:[72,73]},{pc:35,pd:[69,71]},{pc:30,pd:[67,68]},{pc:25,pd:[64,66]},{pc:20,pd:[61,63]},{pc:15,pd:[56,60]},{pc:10,pd:[47,55]},{pc:5,pd:[36,46]},{pc:4,pd:[33,35]},{pc:3,pd:[29,32]},{pc:2,pd:[28,28]},{pc:1,pd:[0,27]}],R:[{pc:99,pd:[29,32]},{pc:98,pd:[28,28]},{pc:96,pd:[27,27]},{pc:95,pd:[26,26]},{pc:90,pd:[25,25]},{pc:85,pd:[24,24]},{pc:80,pd:[23,23]},{pc:70,pd:[22,22]},{pc:65,pd:[21,21]},{pc:60,pd:[20,20]},{pc:50,pd:[19,19]},{pc:45,pd:[18,18]},{pc:40,pd:[17,17]},{pc:30,pd:[16,16]},{pc:25,pd:[15,15]},{pc:20,pd:[14,14]},{pc:15,pd:[13,13]},{pc:10,pd:[11,12]},{pc:5,pd:[8,10]},{pc:3,pd:[7,7]},{pc:2,pd:[6,6]},{pc:1,pd:[0,5]}],N:[{pc:99,pd:[28,32]},{pc:98,pd:[27,27]},{pc:97,pd:[26,26]},{pc:96,pd:[25,25]},{pc:95,pd:[24,24]},{pc:90,pd:[22,23]},{pc:85,pd:[22,22]},{pc:85,pd:[20,21]},{pc:80,pd:[19,19]},{pc:75,pd:[18,18]},{pc:70,pd:[17,17]},{pc:65,pd:[16,16]},{pc:60,pd:[15,15]},{pc:55,pd:[14,14]},{pc:50,pd:[13,13]},{pc:45,pd:[12,12]},{pc:40,pd:[11,11]},{pc:35,pd:[10,10]},{pc:25,pd:[9,9]},{pc:20,pd:[8,8]},{pc:15,pd:[7,7]},{pc:10,pd:[6,6]},{pc:5,pd:[5,5]},{pc:3,pd:[4,4]},{pc:1,pd:[0,3]}],M:[{pc:99,pd:[25,28]},{pc:96,pd:[24,24]},{pc:95,pd:[23,23]},{pc:90,pd:[22,22]},{pc:85,pd:[21,21]},{pc:80,pd:[20,20]},{pc:70,pd:[19,19]},{pc:60,pd:[18,18]},{pc:50,pd:[17,17]},{pc:45,pd:[16,16]},{pc:35,pd:[15,15]},{pc:30,pd:[14,14]},{pc:20,pd:[13,13]},{pc:15,pd:[12,12]},{pc:10,pd:[11,11]},{pc:5,pd:[10,10]},{pc:4,pd:[9,9]},{pc:3,pd:[8,8]},{pc:1,pd:[0,7]}],O:[{pc:99,pd:[31,32]},{pc:98,pd:[30,30]},{pc:95,pd:[29,29]},{pc:90,pd:[27,28]},{pc:85,pd:[26,26]},{pc:80,pd:[25,25]},{pc:70,pd:[24,24]},{pc:65,pd:[23,23]},{pc:60,pd:[22,22]},{pc:55,pd:[21,21]},{pc:50,pd:[20,20]},{pc:45,pd:[19,19]},{pc:40,pd:[18,18]},{pc:35,pd:[17,17]},{pc:30,pd:[16,16]},{pc:25,pd:[15,15]},{pc:20,pd:[14,14]},{pc:15,pd:[13,13]},{pc:10,pd:[11,12]},{pc:5,pd:[9,10]},{pc:4,pd:[8,8]},{pc:3,pd:[7,7]},{pc:2,pd:[6,6]},{pc:1,pd:[0,5]}]},"13-14":{V:[{pc:99,pd:[31,32]},{pc:98,pd:[30,30]},{pc:95,pd:[29,29]},{pc:90,pd:[28,28]},{pc:85,pd:[27,27]},{pc:75,pd:[26,26]},{pc:65,pd:[25,25]},{pc:60,pd:[24,24]},{pc:50,pd:[23,23]},{pc:45,pd:[22,22]},{pc:35,pd:[21,21]},{pc:30,pd:[20,20]},{pc:25,pd:[19,19]},{pc:20,pd:[18,18]},{pc:15,pd:[16,17]},{pc:10,pd:[15,15]},{pc:5,pd:[13,14]},{pc:4,pd:[12,12]},{pc:2,pd:[11,11]},{pc:1,pd:[0,10]}],E:[{pc:99,pd:[28,28]},{pc:97,pd:[27,27]},{pc:95,pd:[26,26]},{pc:90,pd:[25,25]},{pc:85,pd:[24,24]},{pc:80,pd:[23,23]},{pc:75,pd:[22,22]},{pc:65,pd:[21,21]},{pc:60,pd:[20,20]},{pc:50,pd:[19,19]},{pc:45,pd:[18,18]},{pc:40,pd:[17,17]},{pc:35,pd:[16,16]},{pc:25,pd:[15,15]},{pc:20,pd:[14,14]},{pc:15,pd:[13,13]},{pc:10,pd:[12,12]},{pc:5,pd:[10,11]},{pc:4,pd:[9,9]},{pc:2,pd:[8,8]},{pc:1,pd:[0,7]}],A:[{pc:99,pd:[60,80]},{pc:98,pd:[55,59]},{pc:97,pd:[51,54]},{pc:96,pd:[49,50]},{pc:95,pd:[48,48]},{pc:90,pd:[42,47]},{pc:85,pd:[39,41]},{pc:80,pd:[37,38]},{pc:75,pd:[35,36]},{pc:70,pd:[34,34]},{pc:65,pd:[33,33]},{pc:60,pd:[31,32]},{pc:55,pd:[30,30]},{pc:50,pd:[29,29]},{pc:45,pd:[28,28]},{pc:40,pd:[26,27]},{pc:35,pd:[25,25]},{pc:30,pd:[24,24]},{pc:25,pd:[23,23]},{pc:20,pd:[22,22]},{pc:15,pd:[20,21]},{pc:10,pd:[18,19]},{pc:5,pd:[14,17]},{pc:3,pd:[13,13]},{pc:2,pd:[10,12]},{pc:1,pd:[0,9]}],CON:[{pc:99,pd:[100,100]},{pc:98,pd:[97,99]},{pc:97,pd:[96,96]},{pc:96,pd:[95,95]},{pc:95,pd:[94,94]},{pc:90,pd:[91,93]},{pc:85,pd:[89,90]},{pc:80,pd:[87,88]},{pc:75,pd:[85,86]},{pc:70,pd:[83,84]},{pc:65,pd:[82,82]},{pc:60,pd:[80,81]},{pc:55,pd:[78,79]},{pc:50,pd:[76,77]},{pc:45,pd:[75,75]},{pc:40,pd:[72,74]},{pc:35,pd:[70,71]},{pc:30,pd:[68,69]},{pc:25,pd:[66,67]},{pc:20,pd:[61,65]},{pc:15,pd:[57,60]},{pc:10,pd:[49,56]},{pc:5,pd:[37,48]},{pc:4,pd:[35,36]},{pc:3,pd:[31,34]},{pc:2,pd:[29,30]},{pc:1,pd:[0,28]}],R:[{pc:99,pd:[30,32]},{pc:98,pd:[29,29]},{pc:95,pd:[28,28]},{pc:90,pd:[26,27]},{pc:85,pd:[25,25]},{pc:80,pd:[24,24]},{pc:70,pd:[23,23]},{pc:65,pd:[22,22]},{pc:55,pd:[21,21]},{pc:50,pd:[20,20]},{pc:45,pd:[19,19]},{pc:40,pd:[18,18]},{pc:30,pd:[17,17]},{pc:25,pd:[16,16]},{pc:20,pd:[15,15]},{pc:15,pd:[13,14]},{pc:10,pd:[11,12]},{pc:5,pd:[9,10]},{pc:3,pd:[8,8]},{pc:2,pd:[7,7]},{pc:1,pd:[0,6]}],N:[{pc:99,pd:[29,32]},{pc:98,pd:[28,28]},{pc:97,pd:[27,27]},{pc:96,pd:[26,26]},{pc:95,pd:[25,25]},{pc:90,pd:[24,24]},{pc:85,pd:[22,23]},{pc:80,pd:[21,21]},{pc:70,pd:[19,20]},{pc:65,pd:[17,18]},{pc:50,pd:[15,15]},{pc:45,pd:[14,14]},{pc:40,pd:[13,13]},{pc:30,pd:[12,12]},{pc:25,pd:[10,11]},{pc:20,pd:[9,9]},{pc:15,pd:[8,8]},{pc:10,pd:[7,7]},{pc:5,pd:[5,6]},{pc:2,pd:[4,4]},{pc:1,pd:[0,3]}],M:[{pc:99,pd:[26,28]},{pc:97,pd:[25,25]},{pc:95,pd:[24,24]},{pc:90,pd:[23,23]},{pc:85,pd:[22,22]},{pc:75,pd:[21,21]},{pc:70,pd:[20,20]},{pc:60,pd:[19,19]},{pc:50,pd:[18,18]},{pc:45,pd:[17,17]},{pc:40,pd:[16,16]},{pc:30,pd:[15,15]},{pc:25,pd:[14,14]},{pc:20,pd:[13,13]},{pc:15,pd:[12,12]},{pc:10,pd:[11,11]},{pc:5,pd:[10,10]},{pc:4,pd:[9,9]},{pc:3,pd:[8,8]},{pc:1,pd:[0,7]}],O:[{pc:99,pd:[32,32]},{pc:98,pd:[31,31]},{pc:95,pd:[30,30]},{pc:90,pd:[29,29]},{pc:85,pd:[27,28]},{pc:80,pd:[26,26]},{pc:70,pd:[25,25]},{pc:65,pd:[24,24]},{pc:60,pd:[23,23]},{pc:50,pd:[22,22]},{pc:45,pd:[21,21]},{pc:40,pd:[20,20]},{pc:35,pd:[19,19]},{pc:30,pd:[18,18]},{pc:25,pd:[17,17]},{pc:20,pd:[16,16]},{pc:15,pd:[14,15]},{pc:10,pd:[13,13]},{pc:5,pd:[10,12]},{pc:3,pd:[9,9]},{pc:2,pd:[8,8]},{pc:1,pd:[0,7]}]}},Ra=(e,a,s)=>{if(null==a)return null;let t;if("string"!=typeof a&&(a=String(a)),12===s)t="12-13";else{if(13!==s&&14!==s)return null;t="13-14"}const r=ka[t];if(!r)return null;const n=r[a.toUpperCase()];if(!n)return null;for(const i of n)if(e>=i.pd[0]&&e<=i.pd[1])return i.pc;return e<n[n.length-1].pd[0]?n[n.length-1].pc:e>n[0].pd[1]?n[0].pc:null};class Ma{static calcularEdad(e){if(!e)return null;const a=new Date,s=new Date(e);let t=a.getFullYear()-s.getFullYear();const r=a.getMonth()-s.getMonth();return(r<0||0===r&&a.getDate()<s.getDate())&&t--,t}static determinarGrupoEdad(e){return 12===e?"12-13":13===e||14===e?"13-14":null}static convertirPdAPC(e,a,s){return m(this,null,function*(){try{const{data:t,error:r}=yield Ia.from("pacientes").select("fecha_nacimiento").eq("id",s).single();if(r)return null;const n=this.calcularEdad(t.fecha_nacimiento);if(!n)return null;return{pc:Ra(e,a,n),edad:n,grupoEdad:this.determinarGrupoEdad(n),pd:e}}catch(t){return null}})}static actualizarResultadoConPC(e,a){return m(this,null,function*(){try{const{data:s,error:t}=yield Ia.from("resultados").update({percentil:a,updated_at:(new Date).toISOString()}).eq("id",e).select();return t?null:s[0]}catch(s){return null}})}static procesarConversionAutomatica(e,a,s,t){return m(this,null,function*(){try{const r=yield this.convertirPdAPC(a,s,t);if(!r)return null;return yield this.actualizarResultadoConPC(e,r.pc,r.edad,r.grupoEdad)}catch(r){return null}})}static obtenerInterpretacionPC(e){return e>=98?{nivel:"Muy Alto",color:"text-green-700",bg:"bg-green-100"}:e>=85?{nivel:"Alto",color:"text-blue-700",bg:"bg-blue-100"}:e>=70?{nivel:"Medio-Alto",color:"text-indigo-700",bg:"bg-indigo-100"}:e>=31?{nivel:"Medio",color:"text-gray-700",bg:"bg-gray-100"}:e>=16?{nivel:"Medio-Bajo",color:"text-yellow-700",bg:"bg-yellow-100"}:e>=3?{nivel:"Bajo",color:"text-orange-700",bg:"bg-orange-100"}:{nivel:"Muy Bajo",color:"text-red-700",bg:"bg-red-100"}}static recalcularPCPaciente(e){return m(this,null,function*(){var a;try{const{data:s,error:t}=yield Ia.from("resultados").select("\n          id,\n          puntaje_directo,\n          aptitudes:aptitud_id (codigo)\n        ").eq("paciente_id",e).is("percentil",null);if(t)return!1;let r=0;for(const n of s){(yield this.procesarConversionAutomatica(n.id,n.puntaje_directo,null==(a=n.aptitudes)?void 0:a.codigo,e))&&r++}return!0}catch(s){return!1}})}}class Oa{static _validarParametrosConversion(e,a,s){const t=[];return("number"!=typeof e||e<0||e>100)&&t.push("Puntaje directo debe ser un número entre 0 y 100"),a&&"string"==typeof a&&0!==a.length||t.push("Código de aptitud es requerido"),("number"!=typeof s||s<6||s>18)&&t.push("Edad debe ser un número entre 6 y 18 años"),t}static _limpiarCache(){this._cache={baremos:null,funcionesDisponibles:null,lastCheck:null}}static _esCacheValido(){return this._cache.lastCheck&&Date.now()-this._cache.lastCheck<this.CACHE_DURATION}static recalcularTodosLosPercentiles(){return m(this,null,function*(){try{const{data:e,error:a}=yield Ia.rpc("recalcular_todos_los_percentiles");if(a)return u.error("Error al recalcular percentiles en Supabase"),{success:!1,error:a};const s=e||0;return s>0?u.success(`Se actualizaron ${s} resultados con sus percentiles`):u.info("No hay resultados pendientes de conversión"),{success:!0,count:s}}catch(e){return u.error("Error al ejecutar el recálculo de percentiles"),{success:!1,error:e}}})}static probarConversion(e,a,s){return m(this,null,function*(){try{const t=this._validarParametrosConversion(e,a,s);if(t.length>0){const e=`Parámetros inválidos: ${t.join(", ")}`;return u.error(e),{success:!1,error:{message:e},validationErrors:t}}const r=this.convertirPDaPCLocal(e,a.toUpperCase(),Math.round(s));if(null==r){const t=`No se pudo calcular percentil para PD=${e}, aptitud=${a}, edad=${s}`;return u.warning(t),{success:!1,error:{message:t}}}if("number"!=typeof r||r<1||r>99){const e=`Percentil fuera de rango válido (1-99): ${r}`;return u.warning(e),{success:!1,error:{message:e}}}return{success:!0,percentil:r}}catch(t){return u.error(`Error inesperado en conversión: ${t.message}`),{success:!1,error:t}}})}static convertirResultadosEnLote(e,a=!0){return m(this,null,function*(){try{if(!Array.isArray(e)||0===e.length)return u.warning("No se proporcionaron resultados para convertir"),{success:!1,error:"Lista de resultados vacía"};const a={exitosos:[],fallidos:[],total:e.length},s=10,t=[];for(let n=0;n<e.length;n+=s)t.push(e.slice(n,n+s));for(let e=0;e<t.length;e++){const s=t[e].map(e=>m(this,null,function*(){try{const s=yield this.forzarConversionResultado(e);s.success?a.exitosos.push({id:e,resultado:s.resultado}):a.fallidos.push({id:e,error:s.error})}catch(s){a.fallidos.push({id:e,error:s})}}));yield Promise.all(s),e<t.length-1&&(yield new Promise(e=>setTimeout(e,100)))}const r=(a.exitosos.length/a.total*100).toFixed(1);return a.exitosos.length>0&&u.success(`Conversión completada: ${a.exitosos.length}/${a.total} resultados convertidos`),a.fallidos.length>0&&u.warning(`${a.fallidos.length} conversiones fallaron`),{success:a.exitosos.length>0,resultados:a,porcentajeExito:parseFloat(r)}}catch(a){return u.error("Error en conversión en lote"),{success:!1,error:a}}})}static configurarConversionAutomatica(){return m(this,null,function*(){try{return(yield this.recalcularTodosLosPercentiles()).success?(u.success("Conversión automática configurada correctamente"),!0):(u.error("Error al configurar la conversión automática"),!1)}catch(e){return u.error("Error al configurar la conversión automática"),!1}})}static obtenerEstadisticasConversion(){return m(this,null,function*(){try{const{data:e,error:a}=yield Ia.from("resultados").select("id",{count:"exact"}).not("percentil","is",null),{data:s,error:t}=yield Ia.from("resultados").select("id",{count:"exact"}).is("percentil",null);if(a||t)return null;return{totalResultados:((null==e?void 0:e.length)||0)+((null==s?void 0:s.length)||0),conPercentil:(null==e?void 0:e.length)||0,sinPercentil:(null==s?void 0:s.length)||0,porcentajeConvertido:(((null==e?void 0:e.length)||0)/(((null==e?void 0:e.length)||0)+((null==s?void 0:s.length)||0))*100).toFixed(1)}}catch(e){return null}})}static forzarConversionResultado(e){return m(this,null,function*(){try{const{data:a,error:s}=yield Ia.from("resultados").select("\n          id,\n          puntaje_directo,\n          aptitudes:aptitud_id (codigo),\n          pacientes:paciente_id (fecha_nacimiento)\n        ").eq("id",e).single();if(s||!a)return{success:!1,error:s};const t=new Date(a.pacientes.fecha_nacimiento),r=new Date;let n=r.getFullYear()-t.getFullYear();const i=r.getMonth()-t.getMonth();(i<0||0===i&&r.getDate()<t.getDate())&&n--;const o=yield this.probarConversion(a.puntaje_directo,a.aptitudes.codigo,n);if(!o.success)return{success:!1,error:"Error en conversión"};const{data:l,error:c}=yield Ia.from("resultados").update({percentil:o.percentil,updated_at:(new Date).toISOString()}).eq("id",e).select().single();return c?{success:!1,error:c}:(u.success(`Conversión completada: PC ${o.percentil}`),{success:!0,resultado:l})}catch(a){return{success:!1,error:a}}})}static verificarBaremos(){return m(this,null,function*(){try{const{data:e,error:a}=yield Ia.from("baremos").select("factor, baremo_grupo, count(*)").group("factor, baremo_grupo");return a?{success:!1,error:a}:{success:!0,baremos:e}}catch(e){return{success:!1,error:e}}})}static convertirPDaPCLocal(e,a,s){if(s<12||s>14)return null;const t={V:[{min:0,max:5,pc:5},{min:6,max:10,pc:15},{min:11,max:15,pc:25},{min:16,max:20,pc:35},{min:21,max:25,pc:50},{min:26,max:30,pc:65},{min:31,max:35,pc:75},{min:36,max:40,pc:85},{min:41,max:50,pc:95}],E:[{min:0,max:8,pc:5},{min:9,max:16,pc:15},{min:17,max:24,pc:25},{min:25,max:32,pc:35},{min:33,max:40,pc:50},{min:41,max:48,pc:65},{min:49,max:56,pc:75},{min:57,max:64,pc:85},{min:65,max:80,pc:95}],A:[{min:0,max:10,pc:5},{min:11,max:20,pc:15},{min:21,max:30,pc:25},{min:31,max:40,pc:35},{min:41,max:50,pc:50},{min:51,max:60,pc:65},{min:61,max:70,pc:75},{min:71,max:80,pc:85},{min:81,max:100,pc:95}],C:[{min:0,max:8,pc:5},{min:9,max:16,pc:15},{min:17,max:24,pc:25},{min:25,max:32,pc:35},{min:33,max:40,pc:50},{min:41,max:48,pc:65},{min:49,max:56,pc:75},{min:57,max:64,pc:85},{min:65,max:80,pc:95}],R:[{min:0,max:6,pc:5},{min:7,max:12,pc:15},{min:13,max:18,pc:25},{min:19,max:24,pc:35},{min:25,max:30,pc:50},{min:31,max:36,pc:65},{min:37,max:42,pc:75},{min:43,max:48,pc:85},{min:49,max:60,pc:95}],N:[{min:0,max:5,pc:5},{min:6,max:10,pc:15},{min:11,max:15,pc:25},{min:16,max:20,pc:35},{min:21,max:25,pc:50},{min:26,max:30,pc:65},{min:31,max:35,pc:75},{min:36,max:40,pc:85},{min:41,max:50,pc:95}],M:[{min:0,max:8,pc:5},{min:9,max:16,pc:15},{min:17,max:24,pc:25},{min:25,max:32,pc:35},{min:33,max:40,pc:50},{min:41,max:48,pc:65},{min:49,max:56,pc:75},{min:57,max:64,pc:85},{min:65,max:80,pc:95}],O:[{min:0,max:10,pc:5},{min:11,max:20,pc:15},{min:21,max:30,pc:25},{min:31,max:40,pc:35},{min:41,max:50,pc:50},{min:51,max:60,pc:65},{min:61,max:70,pc:75},{min:71,max:80,pc:85},{min:81,max:100,pc:95}]}[a];if(!t)return null;for(const r of t)if(e>=r.min&&e<=r.max)return r.pc;return 50}}d(Oa,"_cache",{baremos:null,funcionesDisponibles:null,lastCheck:null}),d(Oa,"CACHE_DURATION",3e5);class Ba{static getAptitudeId(e){return m(this,null,function*(){try{const a=this.TEST_APTITUDE_MAP[e];if(!a)throw new Error(`Tipo de test no reconocido: ${e}`);const{data:s,error:t}=yield Ia.from("aptitudes").select("id").eq("codigo",a).single();if(t)throw t;return s.id}catch(a){throw a}})}static calculateConcentration(e,a=0){return e&&0!==e?e/(e+a)*100:0}static saveTestResult(e){return m(this,arguments,function*({patientId:e,testType:a,correctCount:s,incorrectCount:t,unansweredCount:r,timeUsed:n,totalQuestions:i,answers:o={},errores:l=0}){try{if(!e)throw new Error("ID del paciente es requerido");if(!a)throw new Error("Tipo de test es requerido");const p=yield this.getAptitudeId(a),x=s||0;let h=null;"atencion"===a&&(h=this.calculateConcentration(x,l));const g={paciente_id:e,aptitud_id:p,puntaje_directo:x,tiempo_segundos:n||0,respuestas:o,errores:l,concentracion:h,respuestas_correctas:s||0,respuestas_incorrectas:t||0,respuestas_sin_contestar:r||0,total_preguntas:i||0,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},{data:b,error:j}=yield Ia.from("resultados").insert([g]).select().single();if(j)throw j;try{const a=(yield z(()=>m(this,null,function*(){const{default:e}=yield import("./InformesService-D-fQ1856.js");return{default:e}}),__vite__mapDeps([0,1,2,3,4,5]))).default;(yield a.generarInformeAutomatico(e,b.id))&&u.success("Resultado guardado e informe generado automáticamente")}catch(c){}try{if(b.percentil)return u.success(`Resultado guardado y convertido automáticamente (PC: ${b.percentil})`),b;const s=this.TEST_APTITUDE_MAP[a];if(s&&b.id){const a=yield Oa.forzarConversionResultado(b.id);if(a.success)return a.resultado;{const a=yield Ma.procesarConversionAutomatica(b.id,x,s,e);if(a)return u.success(`Resultado guardado y convertido (PC: ${a.percentil})`),a}}}catch(d){u.warning("Resultado guardado, pero falló la conversión automática a PC")}return u.success("Resultado guardado correctamente en la base de datos"),b}catch(p){throw u.error(`Error al guardar resultado: ${p.message}`),p}})}static getPatientResults(e){return m(this,null,function*(){try{const{data:a,error:s}=yield Ia.from("resultados").select("\n          *,\n          aptitudes:aptitud_id (\n            codigo,\n            nombre,\n            descripcion\n          )\n        ").eq("paciente_id",e).order("created_at",{ascending:!1});if(s)throw s;return a||[]}catch(a){throw a}})}static hasTestResult(e,a){return m(this,null,function*(){try{const s=yield this.getAptitudeId(a),{data:t,error:r}=yield Ia.from("resultados").select("id").eq("paciente_id",e).eq("aptitud_id",s).limit(1);if(r)throw r;return t&&t.length>0}catch(s){return!1}})}static updateTestResult(e,a){return m(this,null,function*(){try{const{data:s,error:t}=yield Ia.from("resultados").update(l(o({},a),{updated_at:(new Date).toISOString()})).eq("id",e).select().single();if(t)throw t;return u.success("Resultado actualizado correctamente"),s}catch(s){throw u.error(`Error al actualizar resultado: ${s.message}`),s}})}static deleteTestResult(e){return m(this,null,function*(){try{const{error:a}=yield Ia.from("resultados").delete().eq("id",e);if(a)throw a;return u.success("Resultado eliminado correctamente"),!0}catch(a){throw u.error(`Error al eliminar resultado: ${a.message}`),a}})}static getPatientStats(e){return m(this,null,function*(){try{const a=yield this.getPatientResults(e),s={totalTests:a.length,averageScore:0,completedTests:a.map(e=>{var a;return null==(a=e.aptitudes)?void 0:a.codigo}).filter(Boolean),lastTestDate:a.length>0?a[0].created_at:null};if(a.length>0){const e=a.reduce((e,a)=>e+(a.puntaje_directo||0),0);s.averageScore=Math.round(e/a.length)}return s}catch(a){throw a}})}}d(Ba,"TEST_APTITUDE_MAP",{verbal:"V",espacial:"E",atencion:"A",razonamiento:"R",numerico:"N",mecanico:"M",ortografia:"O"});const $a=()=>{var e;const a=f(),s=w(),[t,r]=b.useState(!0),[n,i]=b.useState([]),[c,d]=b.useState(0),[p,x]=b.useState({}),[h,g]=b.useState(720),[y,v]=b.useState(!1),N=null==(e=s.state)?void 0:e.patientId,C={1:"4",2:"1",3:"4",4:"2",5:"2",6:"1",7:"3",8:"2",9:"3",10:"4",11:"3",12:"4",13:"3",14:"4",15:"2",16:"3",17:"3",18:"2",19:"3",20:"2",21:"3",22:"3",23:"4",24:"3",25:"2",26:"1",27:"3",28:"1",29:"2",30:"2",31:"1",32:"1"},A={a:"1",b:"2",c:"3",d:"4"};b.useEffect(()=>{m(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800)),i([{id:1,type:"analogies",text:"Ciudad es a hombre como colmena es a ...",options:[{id:"a",text:"Hormiga"},{id:"b",text:"Mosquito"},{id:"c",text:"Araña"},{id:"d",text:"Abeja"}],correctAnswer:"d"},{id:2,type:"analogies",text:"Batido es a batir como zumo es a ...",options:[{id:"a",text:"Exprimir"},{id:"b",text:"Aplastar"},{id:"c",text:"Machacar"},{id:"d",text:"Succionar"}],correctAnswer:"a"},{id:3,type:"analogies",text:"Consejero es a consejo como cantante es a ...",options:[{id:"a",text:"Fama"},{id:"b",text:"Éxito"},{id:"c",text:"Composición"},{id:"d",text:"Canción"}],correctAnswer:"d"},{id:4,type:"analogies",text:"Estufa es a calor como nevera es a ...",options:[{id:"a",text:"Temperatura"},{id:"b",text:"Frío"},{id:"c",text:"Conservación"},{id:"d",text:"Congelación"}],correctAnswer:"b"},{id:5,type:"analogies",text:"Martillo es a clavo como destornillador es a ...",options:[{id:"a",text:"Hierro"},{id:"b",text:"Tornillo"},{id:"c",text:"Remache"},{id:"d",text:"Herramienta"}],correctAnswer:"b"},{id:6,type:"analogies",text:"Asa es a cesta como pomo es a ...",options:[{id:"a",text:"Puerta"},{id:"b",text:"Tirador"},{id:"c",text:"Envase"},{id:"d",text:"Manillar"}],correctAnswer:"a"},{id:7,type:"analogies",text:"Líquido es a sopa como sólido es a ...",options:[{id:"a",text:"Comer"},{id:"b",text:"Bebida"},{id:"c",text:"Plátano"},{id:"d",text:"Gaseoso"}],correctAnswer:"c"},{id:8,type:"analogies",text:"Ballena es a acuático como león es a ...",options:[{id:"a",text:"Carnívoro"},{id:"b",text:"Terrestre"},{id:"c",text:"Depredador"},{id:"d",text:"Devorador"}],correctAnswer:"b"},{id:9,type:"analogies",text:"Restar es a sumar como arreglar es a ...",options:[{id:"a",text:"Incluir"},{id:"b",text:"Corregir"},{id:"c",text:"Estropear"},{id:"d",text:"Resarcir"}],correctAnswer:"c"},{id:10,type:"analogies",text:"Más es a menos como después es a ...",options:[{id:"a",text:"Tiempo"},{id:"b",text:"Siguiente"},{id:"c",text:"Pronto"},{id:"d",text:"Antes"}],correctAnswer:"d"},{id:11,type:"analogies",text:"Fémur es a hueso como corazón es a ...",options:[{id:"a",text:"Glándula"},{id:"b",text:"Vena"},{id:"c",text:"Músculo"},{id:"d",text:"Arteria"}],correctAnswer:"c"},{id:12,type:"analogies",text:"Cuatro es a cinco como cuadrado es a ...",options:[{id:"a",text:"Triángulo"},{id:"b",text:"Heptágono"},{id:"c",text:"Hexágono"},{id:"d",text:"Pentágono"}],correctAnswer:"d"},{id:13,type:"analogies",text:"Harina es a trigo como cerveza es a ...",options:[{id:"a",text:"Manzana"},{id:"b",text:"Patata"},{id:"c",text:"Cebada"},{id:"d",text:"Alfalfa"}],correctAnswer:"c"},{id:14,type:"analogies",text:"Pie es a cuerpo como bombilla es a ...",options:[{id:"a",text:"Ojos"},{id:"b",text:"Luz"},{id:"c",text:"Vela"},{id:"d",text:"Lámpara"}],correctAnswer:"d"},{id:15,type:"analogies",text:"Excavar es a cavidad como alinear es a ...",options:[{id:"a",text:"Seguido"},{id:"b",text:"Recta"},{id:"c",text:"Acodo"},{id:"d",text:"Ensamblar"}],correctAnswer:"b"},{id:16,type:"analogies",text:"Harina es a pan como leche es a ...",options:[{id:"a",text:"Vaca"},{id:"b",text:"Trigo"},{id:"c",text:"Yogur"},{id:"d",text:"Agua"}],correctAnswer:"c"},{id:17,type:"analogies",text:"Círculo es a cuadrado como esfera es a ...",options:[{id:"a",text:"Cuadrilátero"},{id:"b",text:"Rombo"},{id:"c",text:"Cubo"},{id:"d",text:"Circunferencia"}],correctAnswer:"c"},{id:18,type:"analogies",text:"Bicicleta es a avión como metal es a ...",options:[{id:"a",text:"Solidez"},{id:"b",text:"Madera"},{id:"c",text:"Velocidad"},{id:"d",text:"Fragmento"}],correctAnswer:"b"},{id:19,type:"analogies",text:"Doctora es a doctor como amazona es a ...",options:[{id:"a",text:"Piloto"},{id:"b",text:"Modisto"},{id:"c",text:"Jinete"},{id:"d",text:"Bailarín"}],correctAnswer:"c"},{id:20,type:"analogies",text:"Escultor es a estudio como actor es a ...",options:[{id:"a",text:"Arte"},{id:"b",text:"Escenario"},{id:"c",text:"Drama"},{id:"d",text:"Literatura"}],correctAnswer:"b"},{id:21,type:"analogies",text:"Perder es a ganar como reposo es a ...",options:[{id:"a",text:"Ganancia"},{id:"b",text:"Descanso"},{id:"c",text:"Actividad"},{id:"d",text:"Calma"}],correctAnswer:"c"},{id:22,type:"analogies",text:"Encubierto es a clandestino como endeble es a ...",options:[{id:"a",text:"Doblado"},{id:"b",text:"Simple"},{id:"c",text:"Delicado"},{id:"d",text:"Comprimido"}],correctAnswer:"c"},{id:23,type:"analogies",text:"Apocado es a tímido como arrogante es a ...",options:[{id:"a",text:"Listo"},{id:"b",text:"Humilde"},{id:"c",text:"Virtuoso"},{id:"d",text:"Soberbio"}],correctAnswer:"d"},{id:24,type:"analogies",text:"Rodillo es a masa como torno es a ...",options:[{id:"a",text:"Escayola"},{id:"b",text:"Goma"},{id:"c",text:"Arcilla"},{id:"d",text:"Pintura"}],correctAnswer:"c"},{id:25,type:"analogies",text:"Hora es a tiempo como litro es a ...",options:[{id:"a",text:"Peso"},{id:"b",text:"Capacidad"},{id:"c",text:"Balanza"},{id:"d",text:"Cantidad"}],correctAnswer:"b"},{id:26,type:"analogies",text:"Indefenso es a desvalido como enlazado es a ...",options:[{id:"a",text:"Conexo"},{id:"b",text:"Recorrido"},{id:"c",text:"Torcido"},{id:"d",text:"Explorado"}],correctAnswer:"a"},{id:27,type:"analogies",text:"Reparar es a enmendar como mantener es a ...",options:[{id:"a",text:"Moderar"},{id:"b",text:"Presumir"},{id:"c",text:"Proseguir"},{id:"d",text:"Ayunar"}],correctAnswer:"c"},{id:28,type:"analogies",text:"Adelantar es a demorar como anticipar es a ...",options:[{id:"a",text:"Aplazar"},{id:"b",text:"Desistir"},{id:"c",text:"Proveer"},{id:"d",text:"Achacar"}],correctAnswer:"a"},{id:29,type:"analogies",text:"Infinito es a inagotable como vasto es a ...",options:[{id:"a",text:"Expedito"},{id:"b",text:"Colosal"},{id:"c",text:"Demorado"},{id:"d",text:"Confuso"}],correctAnswer:"b"},{id:30,type:"analogies",text:"Amenazar es a intimidar como articular es a ...",options:[{id:"a",text:"Legislar"},{id:"b",text:"Pronunciar"},{id:"c",text:"Afirmar"},{id:"d",text:"Arquear"}],correctAnswer:"b"},{id:31,type:"analogies",text:"Agua es a embudo como tierra es a ...",options:[{id:"a",text:"Criba"},{id:"b",text:"Fresadora"},{id:"c",text:"Cincel"},{id:"d",text:"Escariador"}],correctAnswer:"a"},{id:32,type:"analogies",text:"Prender es a extinguir como juntar es a ...",options:[{id:"a",text:"Separar"},{id:"b",text:"Unir"},{id:"c",text:"Apagar"},{id:"d",text:"Reducir"}],correctAnswer:"a"}]),r(!1)}catch(e){u.error("Error al cargar las preguntas del test"),r(!1)}})},[]);const E=b.useCallback(()=>m(null,null,function*(){try{const s=Object.keys(p).length,t=n.length,r=t-s;let i=0;Object.entries(p).forEach(([e,a])=>{C[e]===A[a]&&i++});const o=s-i,l=720-h,c={correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"verbal"};if(N)try{yield Ba.saveTestResult({patientId:N,testType:"verbal",correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:p,errores:o}),u.success("Resultado guardado correctamente")}catch(e){u.error(`Error al guardar: ${e.message}`)}u.success(`Test completado. Has respondido ${s} de ${t} preguntas. Respuestas correctas: ${i}`),a("/test/results/verbal",{state:c})}catch(e){u.error("Error al procesar los resultados del test")}}),[p,n.length,h,a,N]);b.useEffect(()=>{if(!y||h<=0)return;const e=setInterval(()=>{g(a=>a<=1?(clearInterval(e),setTimeout(()=>E(),0),0):a-1)},1e3);return()=>clearInterval(e)},[y,h,E]);const S=e=>{const a=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},T=n[c],q=!!T&&p[T.id];return j.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[j.jsxs("div",{className:"mb-6",children:[j.jsxs("div",{className:"text-center mb-4",children:[j.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[j.jsx("i",{className:"fas fa-comments mr-2 text-blue-600"}),"Test de Aptitud Verbal"]}),j.jsx("p",{className:"text-gray-600",children:"Evaluación de analogías verbales y comprensión de relaciones entre conceptos"})]}),y&&j.jsx("div",{className:"text-center",children:j.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:S(h)})})]}),t?j.jsx("div",{className:"py-16 text-center",children:j.jsxs("div",{className:"flex flex-col items-center justify-center",children:[j.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),j.jsx("p",{className:"text-gray-500",children:"Cargando test de razonamiento verbal..."})]})}):y?j.jsx(j.Fragment,{children:j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[j.jsx("div",{className:"md:col-span-3",children:j.jsxs(ya,{className:"mb-6",children:[j.jsxs(va,{className:"flex justify-between items-center",children:[j.jsxs("div",{children:[j.jsxs("h2",{className:"text-lg font-medium",children:["Pregunta ",c+1," de ",n.length]}),j.jsx("p",{className:"text-sm text-gray-500",children:T?(z=T.type,"analogies"===z?"Analogías":z):""})]}),j.jsx("div",{className:"text-sm text-gray-500",children:q?"Respondida":"Sin responder"})]}),j.jsx(Na,{children:T&&j.jsxs(j.Fragment,{children:[j.jsx("div",{className:"text-gray-800 mb-6 whitespace-pre-line font-medium",children:T.text}),j.jsx("div",{className:"space-y-3",children:T.options.map(e=>j.jsx("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(p[T.id]===e.id&&e.id===T.correctAnswer?"bg-green-100 border-green-500":p[T.id]===e.id?"bg-blue-50 border-blue-500":"hover:bg-gray-50"),onClick:()=>{return a=T.id,s=e.id,void x(l(o({},p),{[a]:s}));var a,s},children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(p[T.id]===e.id?"bg-blue-500 text-white":"bg-gray-200 text-gray-700"),children:e.id.toUpperCase()}),j.jsx("div",{className:"text-gray-700",children:e.text})]})},e.id))})]})}),j.jsxs(wa,{className:"flex justify-between",children:[j.jsx(Ca,{variant:"outline",onClick:()=>{c>0&&d(c-1)},disabled:0===c,children:"Anterior"}),c<n.length-1?j.jsx(Ca,{variant:"primary",onClick:()=>{c<n.length-1&&d(c+1)},children:"Siguiente"}):j.jsx(Ca,{variant:"primary",onClick:E,children:"Finalizar Test"})]})]})}),j.jsx("div",{children:j.jsxs(ya,{className:"sticky top-6",children:[j.jsx(va,{children:j.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),j.jsxs(Na,{children:[j.jsx("div",{className:"grid grid-cols-4 gap-2",children:n.map((e,a)=>j.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(c===a?"bg-blue-500 text-white":p[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>d(a),title:`Pregunta ${a+1}`,children:a+1},e.id))}),j.jsxs("div",{className:"mt-6",children:[j.jsxs("div",{className:"flex items-center justify-between mb-2 text-sm",children:[j.jsx("span",{children:"Progreso"}),j.jsxs("span",{children:[Object.keys(p).length," de ",n.length]})]}),j.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:j.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(p).length/n.length*100+"%"}})})]}),j.jsx("div",{className:"mt-6",children:j.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[j.jsx("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"}),j.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",j.jsx("span",{className:"font-medium",children:S(h)})]}),j.jsx("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."})]})}),j.jsx(Ca,{variant:"primary",className:"w-full mt-2",onClick:E,children:"Finalizar Test"})]})]})})]})}):j.jsxs(ya,{children:[j.jsx(va,{children:j.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Razonamiento Verbal: Instrucciones"})}),j.jsx(Na,{children:j.jsxs("div",{className:"space-y-6",children:[j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Razonamiento Verbal?"}),j.jsx("p",{className:"text-gray-600 mb-2",children:"El razonamiento verbal es la capacidad para comprender y establecer relaciones lógicas entre conceptos expresados mediante palabras. Implica entender analogías, relaciones semánticas y encontrar patrones en expresiones verbales."}),j.jsx("p",{className:"text-gray-600",children:"Esta habilidad es fundamental en el ámbito académico y profesional, siendo especialmente relevante para carreras que requieren pensamiento lógico y analítico."})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"}),j.jsx("p",{className:"text-gray-600 mb-3",children:"A continuación encontrarás frases a las que les falta una palabra que ha sido sustituida por puntos suspensivos. Tu tarea consistirá en descubrir qué palabra falta para que la frase resulte verdadera y con sentido."}),j.jsx("p",{className:"text-gray-600 mb-3",children:"En cada ejercicio se proponen cuatro palabras u opciones de respuesta posibles. Entre las cuatro palabras solamente UNA es la opción correcta, la que completa mejor la frase dotándola de sentido."}),j.jsx("p",{className:"text-gray-600",children:'Las frases tienen la siguiente estructura: "A es a B como C es a D". Deberás identificar la relación entre A y B, y aplicar la misma relación entre C y la palabra que falta (D).'})]}),j.jsxs("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[j.jsx("h3",{className:"text-lg font-medium text-orange-700 mb-2",children:"Ejemplos"}),j.jsxs("div",{className:"mb-6",children:[j.jsxs("p",{className:"text-gray-600 mb-3",children:[j.jsx("strong",{className:"text-blue-600",children:"Ejemplo 1:"})," ",j.jsx("strong",{children:"Alto es a bajo como grande es a ..."})]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[j.jsx("div",{className:"bg-white p-3 rounded border border-gray-200",children:"A. Visible"}),j.jsx("div",{className:"bg-white p-3 rounded border border-gray-200",children:"B. Gordo"}),j.jsx("div",{className:"bg-lime-100 p-3 rounded border border-lime-300 font-medium",children:"C. Pequeño"}),j.jsx("div",{className:"bg-white p-3 rounded border border-gray-200",children:"D. Poco"})]}),j.jsxs("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",j.jsx("strong",{children:"C. Pequeño"}),", porque grande y pequeño se relacionan de la misma forma que alto y bajo: son opuestos."]})]}),j.jsxs("div",{children:[j.jsxs("p",{className:"text-gray-600 mb-3",children:[j.jsx("strong",{className:"text-blue-600",children:"Ejemplo 2:"})," ",j.jsx("strong",{children:"...?... es a estrella como tierra es a planeta."})]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[j.jsx("div",{className:"bg-white p-3 rounded border border-gray-200",children:"A. Luz"}),j.jsx("div",{className:"bg-white p-3 rounded border border-gray-200",children:"B. Calor"}),j.jsx("div",{className:"bg-white p-3 rounded border border-gray-200",children:"C. Noche"}),j.jsx("div",{className:"bg-lime-100 p-3 rounded border border-lime-300 font-medium",children:"D. Sol"})]}),j.jsxs("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",j.jsx("strong",{children:"D. Sol"}),', porque sol y estrella guardan entre sí la misma relación que tierra y planeta: el Sol es una estrella y la Tierra es un planeta. Fíjate igualmente en que cualquiera de las otras opciones no sería correcta; por ejemplo, en la opción B, es cierto que las estrellas producen calor, pero no tiene sentido la misma relación en las dos últimas palabras ("planeta" no produce "tierra").']})]})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"}),j.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[j.jsx("li",{children:"El test consta de 32 preguntas de analogías verbales."}),j.jsxs("li",{children:["Dispondrás de ",j.jsx("span",{className:"font-medium",children:"12 minutos"})," para completar todas las preguntas."]}),j.jsx("li",{children:"Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible."}),j.jsx("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'}),j.jsx("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."}),j.jsx("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."}),j.jsx("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas."})]})]}),j.jsxs("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[j.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"}),j.jsx("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."})]})]})}),j.jsx(wa,{className:"flex justify-end",children:j.jsx(Ca,{variant:"primary",onClick:()=>{v(!0),u.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2",children:"Comenzar Test"})})]})]});var z},La=Object.freeze(Object.defineProperty({__proto__:null,default:$a},Symbol.toStringTag,{value:"Module"}));_.number.isRequired,_.number.isRequired,_.object.isRequired,_.func.isRequired,_.number,_.number.isRequired,_.func.isRequired;const Va=()=>{var e;const a=f(),s=w(),[t,r]=b.useState(0),[n,i]=b.useState({}),[c,d]=b.useState(!1),{timeRemaining:p,startTimer:x,stopTimer:h,formatTime:g}=(e=>{const[a,s]=b.useState(e),[t,r]=b.useState(!1),n=b.useCallback(()=>{r(!0)},[]),i=b.useCallback(()=>{r(!1)},[]),o=b.useCallback(()=>{s(e),r(!1)},[e]),l=b.useCallback(e=>{const a=e%60;return`${Math.floor(e/60)}:${a<10?"0":""}${a}`},[]);return b.useEffect(()=>{let e;return t&&a>0?e=setInterval(()=>{s(e=>e-1)},1e3):0===a&&r(!1),()=>{e&&clearInterval(e)}},[t,a]),{timeRemaining:a,isRunning:t,startTimer:n,stopTimer:i,resetTimer:o,formatTime:l}})(600),y=null==(e=s.state)?void 0:e.patientId;b.useEffect(()=>(x(),()=>{h()}),[x,h]),b.useEffect(()=>{0===p&&N()},[p]);const v=[{id:1,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"reloj"},{id:"B",text:"reciclaje"},{id:"C",text:"reyna"},{id:"D",text:"nube"}],correctAnswer:"C"},{id:2,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hola"},{id:"B",text:"Zoo"},{id:"C",text:"ambos"},{id:"D",text:"vallena"}],correctAnswer:"D"},{id:3,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"adibinar"},{id:"B",text:"inmediato"},{id:"C",text:"gestar"},{id:"D",text:"anchoa"}],correctAnswer:"A"},{id:4,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"herrero"},{id:"B",text:"saver"},{id:"C",text:"cerrar"},{id:"D",text:"honrado"}],correctAnswer:"B"},{id:5,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"éxtasis"},{id:"B",text:"cesta"},{id:"C",text:"ademas"},{id:"D",text:"llevar"}],correctAnswer:"C"},{id:6,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"avión"},{id:"B",text:"abrir"},{id:"C",text:"favor"},{id:"D",text:"espionage"}],correctAnswer:"D"},{id:7,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"insecto"},{id:"B",text:"jota"},{id:"C",text:"habrigo"},{id:"D",text:"extraño"}],correctAnswer:"C"},{id:8,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hacha"},{id:"B",text:"oler"},{id:"C",text:"polbo"},{id:"D",text:"abril"}],correctAnswer:"C"},{id:9,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"amartillar"},{id:"B",text:"desacer"},{id:"C",text:"exageración"},{id:"D",text:"humildad"}],correctAnswer:"B"},{id:10,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"bendige"},{id:"B",text:"bifurcación"},{id:"C",text:"amarrar"},{id:"D",text:"país"}],correctAnswer:"A"},{id:11,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"horrible"},{id:"B",text:"llacimiento"},{id:"C",text:"inmóvil"},{id:"D",text:"enredar"}],correctAnswer:"B"},{id:12,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"zebra"},{id:"B",text:"impaciente"},{id:"C",text:"alrededor"},{id:"D",text:"mayor"}],correctAnswer:"A"},{id:13,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hormona"},{id:"B",text:"jirafa"},{id:"C",text:"desván"},{id:"D",text:"enpañar"}],correctAnswer:"D"},{id:14,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"abdicar"},{id:"B",text:"area"},{id:"C",text:"ombligo"},{id:"D",text:"extinguir"}],correctAnswer:"B"},{id:15,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"júbilo"},{id:"B",text:"lúz"},{id:"C",text:"quince"},{id:"D",text:"hilera"}],correctAnswer:"B"},{id:16,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"inexorable"},{id:"B",text:"coraje"},{id:"C",text:"ingerir"},{id:"D",text:"hunir"}],correctAnswer:"D"},{id:17,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"aereo"},{id:"B",text:"conserje"},{id:"C",text:"drástico"},{id:"D",text:"ataviar"}],correctAnswer:"A"},{id:18,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"grave"},{id:"B",text:"abrumar"},{id:"C",text:"contración"},{id:"D",text:"enmienda"}],correctAnswer:"C"},{id:19,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hay"},{id:"B",text:"gemido"},{id:"C",text:"carácter"},{id:"D",text:"harpón"}],correctAnswer:"D"},{id:20,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"embarcar"},{id:"B",text:"ambiguo"},{id:"C",text:"arroyo"},{id:"D",text:"esotérico"}],correctAnswer:"D"},{id:21,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"léntamente"},{id:"B",text:"utopía"},{id:"C",text:"aprensivo"},{id:"D",text:"irascible"}],correctAnswer:"A"},{id:22,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"desahogar"},{id:"B",text:"córnea"},{id:"C",text:"convenido"},{id:"D",text:"azúl"}],correctAnswer:"D"},{id:23,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"próspero"},{id:"B",text:"fué"},{id:"C",text:"regencia"},{id:"D",text:"pelaje"}],correctAnswer:"B"},{id:24,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"savia"},{id:"B",text:"ciénaga"},{id:"C",text:"andamiage"},{id:"D",text:"inmediatamente"}],correctAnswer:"C"},{id:25,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"traspié"},{id:"B",text:"urón"},{id:"C",text:"embellecer"},{id:"D",text:"vasija"}],correctAnswer:"B"},{id:26,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"río"},{id:"B",text:"barar"},{id:"C",text:"hiena"},{id:"D",text:"buhardilla"}],correctAnswer:"B"},{id:27,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"sátira"},{id:"B",text:"crujir"},{id:"C",text:"subrayar"},{id:"D",text:"extrategia"}],correctAnswer:"D"},{id:28,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"dátil"},{id:"B",text:"imágen"},{id:"C",text:"geranio"},{id:"D",text:"anteojo"}],correctAnswer:"B"},{id:29,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"incisivo"},{id:"B",text:"baya"},{id:"C",text:"impío"},{id:"D",text:"arnes"}],correctAnswer:"D"},{id:30,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"jersey"},{id:"B",text:"berengena"},{id:"C",text:"exhibir"},{id:"D",text:"atestar"}],correctAnswer:"B"},{id:31,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"versátil"},{id:"B",text:"hogaza"},{id:"C",text:"vadear"},{id:"D",text:"hurraca"}],correctAnswer:"D"},{id:32,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"exacerbar"},{id:"B",text:"leído"},{id:"C",text:"hayar"},{id:"D",text:"hostil"}],correctAnswer:"C"}],N=()=>m(null,null,function*(){try{h(),d(!0);const s=Object.keys(n).length,t=v.length,r=t-s;let i=0;Object.entries(n).forEach(([e,a])=>{const s=v.find(a=>a.id===parseInt(e));s&&s.correctAnswer===a&&i++});const o=s-i,l=600-p,c={correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"ortografia"};if(y)try{yield Ba.saveTestResult({patientId:y,testType:"ortografia",correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:n,errores:o})}catch(e){}u.success(`Test completado. Has respondido ${s} de ${t} preguntas. Respuestas correctas: ${i}`),a("/test/results/ortografia",{state:c})}catch(e){u.error("Error al procesar los resultados del test")}});Object.keys(n).length,v.length;const C=v[t],A=void 0!==n[C.id];return j.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[j.jsxs("div",{className:"mb-6",children:[j.jsxs("div",{className:"text-center mb-4",children:[j.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[j.jsx("i",{className:"fas fa-spell-check mr-2 text-green-600"}),"Test de Ortografía"]}),j.jsx("p",{className:"text-gray-600",children:"Identificación de palabras con errores ortográficos"})]}),j.jsx("div",{className:"text-center",children:j.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:g(p)})})]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[j.jsx("div",{className:"md:col-span-3",children:j.jsxs("div",{className:"bg-white rounded-lg shadow border border-gray-200",children:[j.jsx("div",{className:"p-4 border-b border-gray-200",children:j.jsxs("div",{className:"flex justify-between items-center",children:[j.jsxs("div",{children:[j.jsxs("h2",{className:"text-lg font-semibold text-gray-800",children:["Pregunta ",t+1," de ",v.length]}),j.jsx("p",{className:"text-sm text-gray-600",children:"Ortografía"})]}),j.jsx("div",{className:"text-sm font-medium text-gray-500",children:A?"Respondida":"Sin responder"})]})}),j.jsxs("div",{className:"p-6",children:[j.jsx("p",{className:"text-lg font-medium text-gray-800 mb-6",children:C.text}),j.jsx("div",{className:"space-y-3",children:C.options.map(e=>j.jsx("button",{className:"w-full text-left p-4 rounded-lg border "+(n[C.id]===e.id?"bg-blue-50 border-blue-500":"bg-white border-gray-200 hover:bg-gray-50"),onClick:()=>{return a=C.id,s=e.id,void i(e=>l(o({},e),{[a]:s}));var a,s},children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"w-8 h-8 flex items-center justify-center rounded-full border mr-3 "+(n[C.id]===e.id?"bg-blue-500 text-white border-blue-500":"text-gray-500 border-gray-300"),children:e.id}),j.jsx("span",{className:"text-gray-800 font-medium",children:e.text})]})},e.id))})]}),j.jsxs("div",{className:"px-6 py-4 border-t border-gray-200 flex justify-between",children:[j.jsx("button",{onClick:()=>{t>0&&r(t-1)},disabled:0===t,className:"px-4 py-2 rounded-md "+(0===t?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"),children:"Anterior"}),j.jsx("button",{onClick:t<v.length-1?()=>{t<v.length-1&&r(t+1)}:N,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:t<v.length-1?"Siguiente":"Finalizar Test"})]})]})}),j.jsx("div",{children:j.jsxs("div",{className:"bg-white rounded-lg shadow border border-gray-200 sticky top-6",children:[j.jsx("div",{className:"p-4 border-b border-gray-200",children:j.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),j.jsxs("div",{className:"p-4",children:[j.jsx("div",{className:"grid grid-cols-4 gap-2",children:v.map((e,a)=>j.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(t===a?"bg-blue-500 text-white":void 0!==n[v[a].id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>r(a),title:`Pregunta ${a+1}`,children:a+1},a))}),j.jsxs("div",{className:"mt-6",children:[j.jsxs("div",{className:"flex items-center justify-between mb-2 text-sm",children:[j.jsx("span",{children:"Progreso"}),j.jsxs("span",{children:[Object.keys(n).length," de ",v.length]})]}),j.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:j.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(n).length/v.length*100+"%"}})})]}),j.jsx("div",{className:"mt-6",children:j.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[j.jsx("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"}),j.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",j.jsx("span",{className:"font-medium",children:g(p)})]}),j.jsx("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."})]})}),j.jsx("button",{onClick:N,className:"w-full mt-2 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium",children:"Finalizar Test"})]})]})})]})]})},Fa=Object.freeze(Object.defineProperty({__proto__:null,default:Va},Symbol.toStringTag,{value:"Module"})),Qa=e=>{const a=e.startsWith("/")?e.slice(1):e;return a.startsWith("assets/")?`/${a}`:`/assets/${a}`},Ua=(e,a)=>(e=>{let a=e.startsWith("/")?e.slice(1):e;return a.startsWith("assets/images/")?a=a.replace("assets/images/",""):a.startsWith("images/")&&(a=a.replace("images/","")),Qa(`images/${a}`)})(`${e}/${a}`),Ha=()=>{var e;const a=f(),s=w(),[t,r]=b.useState(!0),[n,i]=b.useState([]),[c,d]=b.useState(0),[p,x]=b.useState({}),[h,g]=b.useState(1200),[y,v]=b.useState(!1),N=null==(e=s.state)?void 0:e.patientId,C={1:"4",2:"4",3:"4",4:"3",5:"2",6:"4",7:"3",8:"3",9:"1",10:"4",11:"3",12:"1",13:"2",14:"2",15:"3",16:"2",17:"1",18:"3",19:"3",20:"4",21:"3",22:"2",23:"2",24:"1",25:"3",26:"1",27:"1",28:"1",29:"3",30:"4",31:"3",32:"2"},A={1:"a",2:"b",3:"c",4:"d"},E={a:"1",b:"2",c:"3",d:"4"};b.useEffect(()=>{m(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800));const e=Array.from({length:32},(e,a)=>({id:a+1,type:"series",imagePath:Ua("razonamiento",`Racionamiento${a+1}.png`),options:[{id:"a",text:"Opción A"},{id:"b",text:"Opción B"},{id:"c",text:"Opción C"},{id:"d",text:"Opción D"}],correctAnswer:A[C[a+1]]}));i(e),r(!1)}catch(e){u.error("Error al cargar las preguntas del test"),r(!1)}})},[]),b.useEffect(()=>{if(!y||h<=0)return;const e=setInterval(()=>{g(a=>a<=1?(clearInterval(e),S(),0):a-1)},1e3);return()=>clearInterval(e)},[y,h]);const S=()=>m(null,null,function*(){try{const s=Object.keys(p).length,t=n.length,r=t-s;let i=0;Object.entries(p).forEach(([e,a])=>{C[e]===E[a]&&i++});const o=s-i,l=1200-h,c={correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"razonamiento"};if(N)try{yield Ba.saveTestResult({patientId:N,testType:"razonamiento",correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:p,errores:o})}catch(e){}u.success(`Test completado. Has respondido ${s} de ${t} preguntas. Respuestas correctas: ${i}`),a("/test/results/razonamiento",{state:c})}catch(e){u.error("Error al procesar los resultados del test")}}),T=e=>{const a=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},q=n[c],z=!!q&&p[q.id];return j.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[j.jsxs("div",{className:"mb-6",children:[j.jsxs("div",{className:"text-center mb-4",children:[j.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[j.jsx("i",{className:"fas fa-puzzle-piece mr-2 text-amber-600"}),"Test de Razonamiento"]}),j.jsx("p",{className:"text-gray-600",children:"Continuar series lógicas de figuras"})]}),y&&j.jsx("div",{className:"text-center",children:j.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:T(h)})})]}),t?j.jsx("div",{className:"py-16 text-center",children:j.jsxs("div",{className:"flex flex-col items-center justify-center",children:[j.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mb-4"}),j.jsx("p",{className:"text-gray-500",children:"Cargando test de razonamiento..."})]})}):y?j.jsx(j.Fragment,{children:j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[j.jsx("div",{className:"md:col-span-3",children:j.jsxs(ya,{className:"mb-6",children:[j.jsxs(va,{className:"flex justify-between items-center",children:[j.jsxs("div",{children:[j.jsxs("h2",{className:"text-lg font-medium",children:["Pregunta ",c+1," de ",n.length]}),j.jsx("p",{className:"text-sm text-gray-500",children:q?(_=q.type,"series"===_?"Series":_):""})]}),j.jsx("div",{className:"text-sm text-gray-500",children:z?"Respondida":"Sin responder"})]}),j.jsx(Na,{children:q&&j.jsxs(j.Fragment,{children:[j.jsx("div",{className:"flex justify-center mb-6",children:j.jsx("img",{src:q.imagePath,alt:`Pregunta ${q.id}`,className:"max-w-full h-auto"})}),j.jsx("div",{className:"space-y-3",children:q.options.map(e=>j.jsx("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(p[q.id]===e.id?"bg-amber-50 border-amber-500":"hover:bg-gray-50"),onClick:()=>{return a=q.id,s=e.id,void x(l(o({},p),{[a]:s}));var a,s},children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(p[q.id]===e.id?"bg-amber-500 text-white":"bg-gray-200 text-gray-700"),children:e.id.toUpperCase()}),j.jsx("div",{className:"text-gray-700",children:e.text})]})},e.id))})]})}),j.jsxs(wa,{className:"flex justify-between",children:[j.jsx(Ca,{variant:"outline",onClick:()=>{c>0&&d(c-1)},disabled:0===c,children:"Anterior"}),c<n.length-1?j.jsx(Ca,{variant:"primary",onClick:()=>{c<n.length-1&&d(c+1)},className:"bg-amber-600 hover:bg-amber-700",children:"Siguiente"}):j.jsx(Ca,{variant:"primary",onClick:S,className:"bg-amber-600 hover:bg-amber-700",children:"Finalizar Test"})]})]})}),j.jsx("div",{children:j.jsxs(ya,{className:"sticky top-6",children:[j.jsx(va,{children:j.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),j.jsxs(Na,{children:[j.jsx("div",{className:"grid grid-cols-4 gap-2",children:n.map((e,a)=>j.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(c===a?"bg-amber-500 text-white":p[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>d(a),title:`Pregunta ${a+1}`,children:a+1},e.id))}),j.jsxs("div",{className:"mt-6",children:[j.jsxs("div",{className:"flex items-center justify-between mb-2 text-sm",children:[j.jsx("span",{children:"Progreso"}),j.jsxs("span",{children:[Object.keys(p).length," de ",n.length]})]}),j.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:j.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(p).length/n.length*100+"%"}})})]}),j.jsx("div",{className:"mt-6",children:j.jsxs("div",{className:"bg-amber-50 p-3 rounded-lg border border-amber-100 mb-4",children:[j.jsx("h3",{className:"text-sm font-medium text-amber-700 mb-1",children:"Información"}),j.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",j.jsx("span",{className:"font-medium",children:T(h)})]}),j.jsx("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."})]})}),j.jsx(Ca,{variant:"primary",className:"w-full mt-2 bg-amber-600 hover:bg-amber-700",onClick:S,children:"Finalizar Test"})]})]})})]})}):j.jsxs(ya,{children:[j.jsx(va,{children:j.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Razonamiento: Instrucciones"})}),j.jsx(Na,{children:j.jsxs("div",{className:"space-y-6",children:[j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Razonamiento?"}),j.jsx("p",{className:"text-gray-600 mb-2",children:"El razonamiento es la capacidad para identificar patrones, relaciones y reglas lógicas en series de figuras o dibujos. Esta habilidad es fundamental para resolver problemas, tomar decisiones y aprender nuevos conceptos."})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"}),j.jsx("p",{className:"text-gray-600 mb-3",children:"En esta prueba se trabaja con series de figuras o dibujos, ordenados de acuerdo con una ley. Tu tarea consistirá en averiguar la ley que ordena las figuras y elegir entre las opciones de respuesta la que continúa la serie."}),j.jsx("p",{className:"text-gray-600 mb-3",children:"En todos los ejercicios se presenta la serie en la parte superior y las opciones de respuesta en la parte inferior. Cuando hayas decidido qué opción es la única correcta, selecciona la letra correspondiente (A, B, C o D)."})]}),j.jsxs("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[j.jsx("h3",{className:"text-lg font-medium text-orange-700 mb-2",children:"Ejemplos"}),j.jsxs("div",{className:"mb-6",children:[j.jsx("p",{className:"text-gray-600 mb-3",children:j.jsx("strong",{className:"text-blue-600",children:"Ejemplo R1:"})}),j.jsx("div",{className:"flex justify-center mb-4",children:j.jsx("img",{src:"/assets/images/razonamiento/R1.png",alt:"Ejemplo R1",className:"max-w-full h-auto"})}),j.jsx("p",{className:"text-gray-600 mt-3",children:"En este ejemplo se presenta una figura que va girando 90 grados hacia la derecha de una casilla a otra. ¿Cuál debería ser la próxima figura de la serie?"}),j.jsxs("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es la ",j.jsx("strong",{children:"D"}),"."]})]}),j.jsxs("div",{children:[j.jsx("p",{className:"text-gray-600 mb-3",children:j.jsx("strong",{className:"text-blue-600",children:"Ejemplo R2:"})}),j.jsx("div",{className:"flex justify-center mb-4",children:j.jsx("img",{src:"/assets/images/razonamiento/R2.png",alt:"Ejemplo R2",className:"max-w-full h-auto"})})]})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"}),j.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[j.jsx("li",{children:"El test consta de 32 preguntas de series lógicas."}),j.jsxs("li",{children:["Dispondrás de ",j.jsx("span",{className:"font-medium",children:"20 minutos"})," para completar todas las preguntas."]}),j.jsx("li",{children:"Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible."}),j.jsx("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'}),j.jsx("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."}),j.jsx("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."}),j.jsx("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas."})]})]}),j.jsxs("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[j.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"}),j.jsx("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."})]})]})}),j.jsx(wa,{className:"flex justify-end",children:j.jsx(Ca,{variant:"primary",onClick:()=>{v(!0),u.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2 bg-amber-600 hover:bg-amber-700",children:"Comenzar Test"})})]})]});var _},Ga=Object.freeze(Object.defineProperty({__proto__:null,default:Ha},Symbol.toStringTag,{value:"Module"})),Ja=()=>{var e;const a=f(),s=w(),[t,r]=b.useState(!0),[n,i]=b.useState([]),[c,d]=b.useState(0),[p,x]=b.useState({}),[h,g]=b.useState(480),[y,v]=b.useState(!1),N=null==(e=s.state)?void 0:e.patientId,C=10,A={1:3,2:3,3:2,4:1,5:2,6:3,7:2,8:2,9:4,10:2,11:4,12:1,13:4,14:2,15:4,16:2,17:2,18:3,19:2,20:3,21:4,22:2,23:3,24:2,25:3,26:3,27:1,28:2,29:1,30:2,31:3,32:3,33:4,34:1,35:4,36:3,37:1,38:2,39:4,40:1,41:1,42:4,43:2,44:3,45:2,46:1,47:2,48:3,49:1,50:3,51:1,52:4,53:1,54:1,55:1,56:3,57:3,58:2,59:1,60:4,61:4,62:3,63:2,64:3,65:2,66:4,67:3,68:1,69:2,70:4,71:3,72:3,73:3,74:1,75:1,76:2,77:2,78:4,79:1,80:1};b.useEffect(()=>{m(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800));const e=Array.from({length:80},(e,a)=>({id:a+1,type:"attention",imageUrl:Ua("atencion",`Atencion${a+1}.png`),options:[{id:"0",text:"0 veces"},{id:"1",text:"1 vez"},{id:"2",text:"2 veces"},{id:"3",text:"3 veces"},{id:"4",text:"4 veces"}],correctAnswer:A[a+1]?A[a+1].toString():"0"}));i(e),r(!1)}catch(e){u.error("Error al cargar las preguntas del test"),r(!1)}})},[]),b.useEffect(()=>{if(!y||h<=0)return;const e=setInterval(()=>{g(a=>a<=1?(clearInterval(e),E(),0):a-1)},1e3);return()=>clearInterval(e)},[y,h]);const E=()=>m(null,null,function*(){try{const s=Object.keys(p).length,t=n.length,r=t-s;let i=0;Object.entries(p).forEach(([e,a])=>{const s=n.find(a=>a.id.toString()===e);s&&a===s.correctAnswer&&i++});const o=s-i,l=480-h,c={correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"atencion"};if(N)try{yield Ba.saveTestResult({patientId:N,testType:"atencion",correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:p,errores:o})}catch(e){}u.success(`Test completado. Has respondido ${s} de ${t} preguntas. Respuestas correctas: ${i}`),a("/test/results/atencion",{state:c})}catch(e){u.error("Error al procesar los resultados del test")}}),S=e=>{const a=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},T=Math.ceil(n.length/C);return j.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[j.jsxs("div",{className:"mb-6",children:[j.jsxs("div",{className:"text-center mb-4",children:[j.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[j.jsx("i",{className:"fas fa-eye mr-2 text-purple-600"}),"Test de Atención y Concentración"]}),j.jsx("p",{className:"text-gray-600",children:"Localización de símbolos específicos"})]}),y&&j.jsx("div",{className:"text-center",children:j.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:S(h)})})]}),t?j.jsx("div",{className:"py-16 text-center",children:j.jsxs("div",{className:"flex flex-col items-center justify-center",children:[j.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),j.jsx("p",{className:"text-gray-500",children:"Cargando test de atención..."})]})}):y?n.length>0?j.jsx(j.Fragment,{children:j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[j.jsxs("div",{className:"md:col-span-3",children:[j.jsxs(ya,{className:"mb-6",children:[j.jsxs(va,{className:"flex justify-between items-center",children:[j.jsxs("div",{children:[j.jsxs("h2",{className:"text-lg font-medium",children:["Página ",c+1," de ",T]}),j.jsxs("p",{className:"text-sm text-gray-500",children:["Preguntas ",c*C+1," - ",Math.min((c+1)*C,n.length)," de ",n.length]})]}),j.jsxs("div",{className:"text-sm text-gray-500",children:["Tiempo restante: ",S(h)]})]}),j.jsx(Na,{children:j.jsx("div",{className:"space-y-8",children:(()=>{const e=c*C,a=e+C;return n.slice(e,a)})().map(e=>j.jsx("div",{className:"border-b pb-6 mb-6 last:border-b-0 last:pb-0 last:mb-0",children:j.jsxs("div",{className:"flex flex-col md:flex-row md:items-start gap-4",children:[j.jsxs("div",{className:"md:w-3/4",children:[j.jsxs("h3",{className:"text-md font-medium mb-3",children:["Pregunta ",e.id]}),j.jsx("div",{className:"mb-4",children:j.jsx("img",{src:e.imageUrl,alt:`Pregunta ${e.id}`,className:"w-full max-w-2xl h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/600x100?text=Imagen+no+disponible"}})}),j.jsx("p",{className:"text-gray-600 mb-3",children:"¿Cuántas veces aparece el símbolo modelo en esta fila?"})]}),j.jsx("div",{className:"md:w-1/4",children:j.jsx("div",{className:"space-y-2",children:e.options.map(a=>j.jsx("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(p[e.id]===a.id?"bg-indigo-50 border-indigo-500":"hover:bg-gray-50"),onClick:()=>{return s=e.id,t=a.id,void x(l(o({},p),{[s]:t}));var s,t},children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(p[e.id]===a.id?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"),children:a.id}),j.jsx("div",{className:"text-gray-700",children:a.text})]})},a.id))})})]})},e.id))})}),j.jsxs(wa,{className:"flex justify-between",children:[j.jsx(Ca,{variant:"outline",onClick:()=>{c>0&&(d(c-1),window.scrollTo(0,0))},disabled:0===c,children:"Página Anterior"}),j.jsxs("div",{className:"flex space-x-2",children:[Array.from({length:T},(e,a)=>j.jsx("button",{className:"w-8 h-8 rounded-full text-sm "+(c===a?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),onClick:()=>d(a),children:a+1},a)).slice(Math.max(0,c-2),Math.min(T,c+3)),c+3<T&&j.jsx("span",{className:"self-center",children:"..."})]}),c<T-1?j.jsx(Ca,{variant:"primary",onClick:()=>{(c+1)*C<n.length&&(d(c+1),window.scrollTo(0,0))},children:"Página Siguiente"}):j.jsx(Ca,{variant:"primary",onClick:E,children:"Finalizar Test"})]})]}),j.jsxs("div",{className:"flex justify-between items-center",children:[j.jsxs("div",{children:[j.jsxs("p",{className:"text-sm text-gray-600",children:["Has respondido ",Object.keys(p).length," de ",n.length," preguntas (",Math.round(Object.keys(p).length/n.length*100),"%)"]}),j.jsx("div",{className:"w-64 bg-gray-200 rounded-full h-2 mt-1",children:j.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(p).length/n.length*100+"%"}})})]}),j.jsx(Ca,{variant:"primary",onClick:E,children:"Finalizar Test"})]})]}),j.jsx("div",{children:j.jsxs(ya,{className:"sticky top-6",children:[j.jsx(va,{children:j.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),j.jsxs(Na,{children:[j.jsx("div",{className:"grid grid-cols-4 gap-2 max-h-64 overflow-y-auto p-1",children:n.map((e,a)=>{const s=Math.floor(a/C),t=s===c;return j.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(p[e.id]?"bg-green-100 text-green-800 border border-green-300":t?"bg-blue-100 text-blue-800 border border-blue-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>{const e=Math.floor(a/C);d(e)},title:`Pregunta ${a+1} - Página ${s+1}`,children:a+1},e.id)})}),j.jsxs("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[j.jsxs("div",{className:"text-sm text-gray-600 mb-2",children:["Progreso: ",Object.keys(p).length,"/",n.length]}),j.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-3",children:j.jsx("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-300",style:{width:Object.keys(p).length/n.length*100+"%"}})}),j.jsx(Ca,{variant:"primary",className:"w-full",onClick:E,children:"Finalizar Test"})]})]})]})})]})}):j.jsx(ya,{children:j.jsx(Na,{children:j.jsxs("div",{className:"py-8 text-center",children:[j.jsx("p",{className:"text-gray-500",children:"No se encontraron preguntas para este test."}),j.jsx(Ca,{variant:"outline",className:"mt-4",onClick:()=>a("/student/tests"),children:"Volver a Tests"})]})})}):j.jsxs(ya,{children:[j.jsx(va,{children:j.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Test de Atención: Instrucciones"})}),j.jsx(Na,{children:j.jsxs("div",{className:"space-y-6",children:[j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Test de Atención?"}),j.jsx("p",{className:"text-gray-600 mb-2",children:"El test de atención evalúa tu capacidad para mantener la concentración y detectar estímulos específicos entre un conjunto de elementos similares. Esta habilidad es fundamental para el aprendizaje, el trabajo y muchas actividades cotidianas."}),j.jsx("p",{className:"text-gray-600",children:"Esta prueba mide específicamente tu atención selectiva, velocidad perceptiva, discriminación visual y capacidad de concentración sostenida."})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"}),j.jsx("p",{className:"text-gray-600 mb-3",children:"Esta prueba trata de evaluar tu rapidez y tu precisión trabajando con símbolos. En cada ejercicio aparece una fila con diferentes símbolos y tu tarea consistirá en localizar cuántas veces aparece uno determinado."}),j.jsx("p",{className:"text-gray-600 mb-3",children:"El símbolo que tienes que localizar es siempre el mismo y se presenta en la parte superior de la página; en cada ejercicio puede aparecer 0, 1, 2, 3 o 4 veces."}),j.jsx("p",{className:"text-gray-600",children:"Deberás seleccionar cuántas veces aparece el símbolo en cada fila (0, 1, 2, 3 o 4) asegurándote de que tu respuesta se corresponda con el número del ejercicio que estás contestando."})]}),j.jsxs("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[j.jsx("h3",{className:"text-lg font-medium text-indigo-700 mb-2",children:"Ejemplos"}),j.jsxs("div",{className:"mb-6",children:[j.jsx("div",{className:"flex justify-center mb-3",children:j.jsx("img",{src:Ua("atencion","Atencion.png"),alt:"Ejemplos de atención",className:"max-w-md h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/300x150?text=Imagen+no+disponible"}})}),j.jsxs("p",{className:"text-gray-600 mt-3",children:[j.jsx("strong",{className:"text-indigo-600",children:"Ejemplo A1:"})," El símbolo del óvalo aparece una única vez, y es el tercer símbolo de la fila. Por eso la respuesta correcta es ",j.jsx("strong",{children:"1"}),"."]}),j.jsxs("p",{className:"text-gray-600 mt-2",children:[j.jsx("strong",{className:"text-indigo-600",children:"Ejemplo A2:"})," En esta ocasión no hay ningún símbolo que coincida exactamente con el modelo; por tanto la respuesta correcta es ",j.jsx("strong",{children:"0"}),"."]}),j.jsxs("p",{className:"text-gray-600 mt-2",children:[j.jsx("strong",{className:"text-indigo-600",children:"Ejemplo A3:"})," El símbolo del óvalo aparece en dos ocasiones, en primera y quinta posición. Por eso, la respuesta correcta es ",j.jsx("strong",{children:"2"}),"."]})]})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"}),j.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[j.jsx("li",{children:"El test consta de 80 preguntas de atención."}),j.jsxs("li",{children:["Dispondrás de ",j.jsx("span",{className:"font-medium",children:"8 minutos"})," para completar todas las preguntas."]}),j.jsx("li",{children:"Las preguntas se presentan en páginas de 10 preguntas cada una."}),j.jsx("li",{children:"Puedes navegar libremente entre las páginas y modificar tus respuestas durante el tiempo disponible."}),j.jsx("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'}),j.jsx("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."}),j.jsx("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."})]})]}),j.jsxs("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[j.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"}),j.jsx("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."})]})]})}),j.jsx(wa,{className:"flex justify-end",children:j.jsx(Ca,{variant:"primary",onClick:()=>{v(!0),u.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2",children:"Comenzar Test"})})]})]})},Wa=Object.freeze(Object.defineProperty({__proto__:null,default:Ja},Symbol.toStringTag,{value:"Module"})),Ya=()=>{var e;const a=f(),s=w(),[t,r]=b.useState(!0),[n,i]=b.useState([]),[c,d]=b.useState(0),[p,x]=b.useState({}),[h,g]=b.useState(900),[y,v]=b.useState(!1),N=null==(e=s.state)?void 0:e.patientId,C={1:"C",2:"D",3:"B",4:"A",5:"A",6:"A",7:"D",8:"B",9:"D",10:"D",11:"C",12:"A",13:"D",14:"A",15:"A",16:"B",17:"C",18:"A",19:"C",20:"D",21:"D",22:"C",23:"D",24:"B",25:"C",26:"C",27:"D",28:"C"};b.useEffect(()=>{m(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800));const e=Array.from({length:28},(e,a)=>({id:a+1,type:"spatial",imageUrl:Ua("espacial",`Espacial${a+1}.png`),options:[{id:"A",text:"Opción A"},{id:"B",text:"Opción B"},{id:"C",text:"Opción C"},{id:"D",text:"Opción D"}],correctAnswer:C[a+1]}));i(e),r(!1)}catch(e){u.error("Error al cargar las preguntas del test"),r(!1)}})},[]),b.useEffect(()=>{if(!y||h<=0)return;const e=setInterval(()=>{g(a=>a<=1?(clearInterval(e),A(),0):a-1)},1e3);return()=>clearInterval(e)},[y,h]);const A=()=>m(null,null,function*(){try{const s=Object.keys(p).length,t=n.length,r=t-s;let i=0;Object.entries(p).forEach(([e,a])=>{const s=n.find(a=>a.id.toString()===e);s&&a===s.correctAnswer&&i++});const o=s-i,l=900-h,c={correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"espacial"};if(N)try{yield Ba.saveTestResult({patientId:N,testType:"espacial",correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:p,errores:o})}catch(e){}u.success(`Test completado. Has respondido ${s} de ${t} preguntas. Respuestas correctas: ${i}`),a("/test/results/espacial",{state:c})}catch(e){u.error("Error al procesar los resultados del test")}}),E=e=>{const a=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},S=n[c],T=!!S&&p[S.id];return j.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[j.jsxs("div",{className:"mb-6",children:[j.jsxs("div",{className:"text-center mb-4",children:[j.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[j.jsx("i",{className:"fas fa-cube mr-2 text-indigo-600"}),"Test de Aptitud Espacial"]}),j.jsx("p",{className:"text-gray-600",children:"Razonamiento espacial con cubos y redes"})]}),y&&j.jsx("div",{className:"text-center",children:j.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:E(h)})})]}),t?j.jsx("div",{className:"py-16 text-center",children:j.jsxs("div",{className:"flex flex-col items-center justify-center",children:[j.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),j.jsx("p",{className:"text-gray-500",children:"Cargando test de razonamiento espacial..."})]})}):y?n.length>0?j.jsx(j.Fragment,{children:j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[j.jsx("div",{className:"md:col-span-3",children:j.jsxs(ya,{className:"mb-6",children:[j.jsxs(va,{className:"flex justify-between items-center",children:[j.jsxs("div",{children:[j.jsxs("h2",{className:"text-lg font-medium",children:["Pregunta ",c+1," de ",n.length]}),j.jsx("p",{className:"text-sm text-gray-500",children:S?(q=S.type,"spatial"===q?"Razonamiento Espacial":q):""})]}),j.jsx("div",{className:"text-sm text-gray-500",children:T?"Respondida":"Sin responder"})]}),j.jsx(Na,{children:S&&j.jsxs(j.Fragment,{children:[j.jsx("div",{className:"flex justify-center mb-6",children:j.jsx("img",{src:S.imageUrl,alt:`Pregunta ${c+1}`,className:"max-w-full h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/600x300?text=Imagen+no+disponible"}})}),j.jsx("div",{className:"space-y-3",children:S.options.map(e=>j.jsx("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(p[S.id]===e.id?"bg-indigo-50 border-indigo-500":"hover:bg-gray-50"),onClick:()=>{return a=S.id,s=e.id,void x(l(o({},p),{[a]:s}));var a,s},children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(p[S.id]===e.id?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"),children:e.id.toUpperCase()}),j.jsx("div",{className:"text-gray-700",children:e.text})]})},e.id))})]})}),j.jsxs(wa,{className:"flex justify-between",children:[j.jsx(Ca,{variant:"outline",onClick:()=>{c>0&&d(c-1)},disabled:0===c,children:"Anterior"}),c<n.length-1?j.jsx(Ca,{variant:"primary",onClick:()=>{c<n.length-1&&d(c+1)},children:"Siguiente"}):j.jsx(Ca,{variant:"primary",onClick:A,children:"Finalizar Test"})]})]})}),j.jsx("div",{children:j.jsxs(ya,{className:"sticky top-6",children:[j.jsx(va,{children:j.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),j.jsxs(Na,{children:[j.jsx("div",{className:"grid grid-cols-4 gap-2 max-h-64 overflow-y-auto p-1",children:n.map((e,a)=>j.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(c===a?"bg-indigo-500 text-white":p[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>d(a),title:`Pregunta ${a+1}`,children:a+1},e.id))}),j.jsxs("div",{className:"mt-6",children:[j.jsxs("div",{className:"flex items-center justify-between mb-2 text-sm",children:[j.jsx("span",{children:"Progreso"}),j.jsxs("span",{children:[Object.keys(p).length," de ",n.length]})]}),j.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:j.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(p).length/n.length*100+"%"}})})]}),j.jsx("div",{className:"mt-6",children:j.jsxs("div",{className:"bg-indigo-50 p-3 rounded-lg border border-indigo-100 mb-4",children:[j.jsx("h3",{className:"text-sm font-medium text-indigo-700 mb-1",children:"Información"}),j.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",j.jsx("span",{className:"font-medium",children:E(h)})]}),j.jsx("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."})]})}),j.jsx(Ca,{variant:"primary",className:"w-full mt-2",onClick:A,children:"Finalizar Test"})]})]})})]})}):j.jsx(ya,{children:j.jsx(Na,{children:j.jsxs("div",{className:"py-8 text-center",children:[j.jsx("p",{className:"text-gray-500",children:"No se encontraron preguntas para este test."}),j.jsx(Ca,{variant:"outline",className:"mt-4",onClick:()=>a("/student/tests"),children:"Volver a Tests"})]})})}):j.jsxs(ya,{children:[j.jsx(va,{children:j.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Razonamiento Espacial: Instrucciones"})}),j.jsx(Na,{children:j.jsxs("div",{className:"space-y-6",children:[j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Razonamiento Espacial?"}),j.jsx("p",{className:"text-gray-600 mb-2",children:"El razonamiento espacial es la capacidad para visualizar y manipular objetos mentalmente en el espacio tridimensional. Implica entender cómo se relacionan las formas y los objetos entre sí, y cómo se transforman cuando cambian de posición o perspectiva."}),j.jsx("p",{className:"text-gray-600",children:"Esta habilidad es fundamental en campos como la arquitectura, ingeniería, diseño, matemáticas y ciencias, siendo especialmente relevante para carreras que requieren visualización y manipulación de objetos en el espacio."})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"}),j.jsx("p",{className:"text-gray-600 mb-3",children:"En este test encontrarás un cubo junto con un modelo desplegado del mismo cubo. En el modelo desplegado falta una cara, marcada con un signo de interrogación (?)."}),j.jsx("p",{className:"text-gray-600 mb-3",children:"Tu tarea consistirá en averiguar qué opción (A, B, C o D) debería aparecer en lugar del interrogante para que el modelo desplegado corresponda al cubo cuando se pliega."}),j.jsx("p",{className:"text-gray-600",children:"Para facilitar tu tarea, en el cubo se han representado en color gris los números o letras que se encuentran en las caras de atrás (las que no se ven directamente)."})]}),j.jsxs("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[j.jsx("h3",{className:"text-lg font-medium text-indigo-700 mb-2",children:"Ejemplos"}),j.jsxs("div",{className:"mb-6",children:[j.jsx("p",{className:"text-gray-600 mb-3",children:j.jsx("strong",{className:"text-indigo-600",children:"Ejemplo 1:"})}),j.jsx("div",{className:"flex justify-center mb-3",children:j.jsx("img",{src:Ua("espacial","Modelo Espacial.png"),alt:"Ejemplo 1",className:"max-w-full h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/400x200?text=Ejemplo+1"}})}),j.jsxs("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",j.jsx("strong",{children:"B"}),". Si se sustituye el interrogante por la letra «h» y se pliegan las caras del modelo hasta formar el cubo, este se corresponde con el que aparece a la izquierda."]})]}),j.jsxs("div",{children:[j.jsx("p",{className:"text-gray-600 mb-3",children:j.jsx("strong",{className:"text-indigo-600",children:"Ejemplo 2:"})}),j.jsx("div",{className:"flex justify-center mb-3",children:j.jsx("img",{src:Ua("espacial","Espacial1.png"),alt:"Ejemplo 2",className:"max-w-full h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/400x200?text=Ejemplo+2"}})}),j.jsxs("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",j.jsx("strong",{children:"A"}),"."]})]})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"}),j.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[j.jsx("li",{children:"El test consta de 28 preguntas de razonamiento espacial."}),j.jsxs("li",{children:["Dispondrás de ",j.jsx("span",{className:"font-medium",children:"15 minutos"})," para completar todas las preguntas."]}),j.jsx("li",{children:"Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible."}),j.jsx("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'}),j.jsx("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."}),j.jsx("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."}),j.jsx("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas."})]})]}),j.jsxs("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[j.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"}),j.jsx("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."})]})]})}),j.jsx(wa,{className:"flex justify-end",children:j.jsx(Ca,{variant:"primary",onClick:()=>{v(!0),u.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2",children:"Comenzar Test"})})]})]});var q},Za=Object.freeze(Object.defineProperty({__proto__:null,default:Ya},Symbol.toStringTag,{value:"Module"})),Xa=()=>{var e;const a=f(),s=w(),[t,r]=b.useState(!1),n=null==(e=s.state)?void 0:e.patientId,[i,c]=b.useState(0),[d,p]=b.useState({}),[x,h]=b.useState(720),[g,y]=b.useState(!1),v=[{id:1,question:"¿Qué tipo de polea podrá subir MÁS peso sin vencerse?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico1.png",options:["A","B","C","D"],correctAnswer:0},{id:2,question:"¿Qué estante es MENOS resistente?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico2.png",options:["A","B","C","D"],correctAnswer:2},{id:3,question:"¿Qué tipo de listones permite mover la carga con MENOS esfuerzo?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico3.png",options:["A","B","C","D"],correctAnswer:0},{id:4,question:"Si el viento sopla en la dirección indicada, ¿hacia dónde tendríamos que golpear la bola para acercarla MÁS al hoyo?",subtitle:"",image:"/assets/images/mecanico/mecanico4.png",options:["A","B","C","D"],correctAnswer:1},{id:5,question:"¿En qué zona (A, B o C) es MÁS probable que se rompan las cuerdas al colocar la carga?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico5.png",options:["A","B","C","D"],correctAnswer:1},{id:6,question:"¿De qué recipiente saldrá el líquido con MÁS fuerza?",subtitle:"",image:"/assets/images/mecanico/mecanico6.png",options:["A","B","C","D"],correctAnswer:2},{id:7,question:"¿Cuál de estos tres recipientes llenos de agua pesa MENOS?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico7.png",options:["A","B","C","D"],correctAnswer:3},{id:8,question:"¿Qué torno deberá dar MÁS vueltas para enrollar los mismos metros de cuerda?",subtitle:"",image:"/assets/images/mecanico/mecanico8.png",options:["A","B","C","D"],correctAnswer:3},{id:9,question:"¿Hacia qué dirección (A, B, C o D) está soplando el viento?",subtitle:"",image:"/assets/images/mecanico/mecanico9.png",options:["A","B","C","D"],correctAnswer:1},{id:10,question:"¿Cuál de estos tres tejados es MÁS probable que se rompa en caso de nevada?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico10.png",options:["A","B","C","D"],correctAnswer:0},{id:11,question:"¿A cuál de estas personas le costará MÁS esfuerzo trasladar la carga?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico11.png",options:["A","B","C","D"],correctAnswer:2},{id:12,question:"¿Con qué bomba se inflará MÁS lentamente un colchón flotador?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico12.png",options:["A","B","C","D"],correctAnswer:2},{id:13,question:"¿En qué caso se debe ejercer MENOS fuerza en el punto indicado por la flecha para sujetar el mismo peso?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico13.png",options:["A","B","C","D"],correctAnswer:0},{id:14,question:"Si al frenar la bicicleta solo se usan los frenos delanteros, ¿hacia qué dirección será impulsado el ciclista?",subtitle:"",image:"/assets/images/mecanico/mecanico14.png",options:["A","B","C","D"],correctAnswer:3},{id:15,question:"¿Cuál de estos tres pesos (A, B o C) pesa MENOS?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico15.png",options:["A","B","C","D"],correctAnswer:3},{id:16,question:"¿Qué columna será MÁS resistente en caso de terremoto?",subtitle:"",image:"/assets/images/mecanico/mecanico16.png",options:["A","B","C","D"],correctAnswer:1},{id:17,question:"¿Qué micrófono tiene MENOS probabilidad de caerse ante un golpe?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico17.png",options:["A","B","C","D"],correctAnswer:0},{id:18,question:"¿Qué trayectoria (A, B o C) debe seguir el nadador para cruzar el río con MENOS esfuerzo?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico18.png",options:["A","B","C","D"],correctAnswer:1},{id:19,question:"¿En qué punto es necesario ejercer MÁS fuerza para cerrar la puerta?",subtitle:"",image:"/assets/images/mecanico/mecanico19.png",options:["A","B","C","D"],correctAnswer:0},{id:20,question:"¿En qué caso habrá que ejercer MENOS fuerza para levantar las ruedas delanteras del carro?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico20.png",options:["A","B","C","D"],correctAnswer:2},{id:21,question:"¿Qué coche ofrece MENOS resistencia al aire?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico21.png",options:["A","B","C","D"],correctAnswer:2},{id:22,question:"¿Cómo debe agarrarse la persona a la roca para que no la arrastre la corriente?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico22.png",options:["A","B","C","D"],correctAnswer:2},{id:23,question:"Si tenemos estas tres linternas, ¿cuál iluminará un área MAYOR?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico23.png",options:["A","B","C","D"],correctAnswer:2},{id:24,question:"¿Qué coche es MENOS probable que vuelque?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico24.png",options:["A","B","C","D"],correctAnswer:2},{id:25,question:"¿En qué punto alcanzará MÁS velocidad el paracaidista?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico25.png",options:["A","B","C","D"],correctAnswer:2},{id:26,question:"Si dejáramos tan solo UNO de los bloques (A, B, C o D), ¿con cuál se mantendría la estructura en equilibrio?",subtitle:"",image:"/assets/images/mecanico/mecanico26.png",options:["A","B","C","D"],correctAnswer:2},{id:27,question:"¿Hacia qué zona de la cápsula será impulsado el astronauta cuando la máquina gire en el sentido indicado por la flecha?",subtitle:"",image:"/assets/images/mecanico/mecanico27.png",options:["A","B","C","D"],correctAnswer:1},{id:28,question:"Si colgamos el peso de esta forma, ¿por cuál de los puntos (A, B o C) es MENOS probable que se rompa la madera?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico28.png",options:["A","B","C","D"],correctAnswer:2}];b.useEffect(()=>{let e;return t&&!g&&x>0&&(e=setInterval(()=>{h(e=>e<=1?(A(),0):e-1)},1e3)),()=>clearInterval(e)},[t,g,x]);const N=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`,C=e=>{c(e)},A=()=>m(null,null,function*(){try{y(!0);const s=Object.keys(d).length,t=v.length,r=t-s;let i=0;v.forEach(e=>{d[e.id]===e.correctAnswer&&i++});const o=s-i,l=720-x,c={correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"mecanico"};if(n)try{yield Ba.saveTestResult({patientId:n,testType:"mecanico",correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:d,errores:o})}catch(e){}u.success(`Test completado. Has respondido ${s} de ${t} preguntas. Respuestas correctas: ${i}`),a("/test/results/mecanico",{state:c})}catch(e){u.error("Error al procesar los resultados del test")}}),E=v[i];return j.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[j.jsxs("div",{className:"mb-6",children:[j.jsxs("div",{className:"text-center mb-4",children:[j.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[j.jsx("i",{className:"fas fa-cogs mr-2 text-orange-600"}),"Test de Aptitud Mecánica"]}),j.jsx("p",{className:"text-gray-600",children:"Evalúa tu comprensión de principios mecánicos y físicos básicos"})]}),t&&j.jsx("div",{className:"text-center",children:j.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:N(x)})})]}),t?j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[j.jsx("div",{className:"md:col-span-3",children:j.jsxs(ya,{className:"mb-6",children:[j.jsxs(va,{className:"flex justify-between items-center",children:[j.jsxs("div",{children:[j.jsxs("h2",{className:"text-lg font-medium",children:["Pregunta ",i+1," de ",v.length]}),j.jsx("p",{className:"text-sm text-gray-500",children:"Aptitud Mecánica"})]}),j.jsx("div",{className:"text-sm text-gray-500",children:void 0!==d[E.id]?"Respondida":"Sin responder"})]}),j.jsxs(Na,{children:[j.jsxs("div",{className:"mb-6",children:[j.jsx("h4",{className:"text-lg font-medium text-gray-800 mb-2",children:E.question}),E.subtitle&&j.jsx("p",{className:"text-sm text-gray-600 mb-4",children:E.subtitle}),j.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6 text-center",children:[j.jsx("img",{src:E.image,alt:`Pregunta ${E.id}`,className:"max-w-full h-auto mx-auto",style:{maxHeight:"400px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}}),j.jsxs("div",{className:"text-gray-500 text-sm",style:{display:"none"},children:["[Imagen no disponible: ",E.image,"]"]})]})]}),j.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:E.options.map((e,a)=>j.jsxs("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors text-center "+(d[E.id]===a?"bg-blue-50 border-blue-500":"hover:bg-gray-50"),onClick:()=>{return e=E.id,s=a,void p(a=>l(o({},a),{[e]:s}));var e,s},children:[j.jsx("div",{className:"w-8 h-8 flex items-center justify-center rounded-full mx-auto mb-2 "+(d[E.id]===a?"bg-blue-500 text-white":"bg-gray-200 text-gray-700"),children:e}),j.jsxs("div",{className:"text-sm text-gray-600",children:["Opción ",e]})]},a))})]}),j.jsxs(wa,{className:"flex justify-between",children:[j.jsx(Ca,{variant:"outline",onClick:()=>C(Math.max(0,i-1)),disabled:0===i,children:"Anterior"}),i<v.length-1?j.jsx(Ca,{variant:"primary",onClick:()=>C(Math.min(v.length-1,i+1)),children:"Siguiente"}):j.jsx(Ca,{variant:"primary",onClick:A,children:"Finalizar Test"})]})]})}),j.jsx("div",{children:j.jsxs(ya,{className:"sticky top-6",children:[j.jsx(va,{children:j.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),j.jsxs(Na,{children:[j.jsx("div",{className:"grid grid-cols-4 gap-2",children:v.map((e,a)=>j.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(i===a?"bg-blue-500 text-white":void 0!==d[v[a].id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>C(a),title:`Pregunta ${a+1}`,children:a+1},a))}),j.jsxs("div",{className:"mt-6",children:[j.jsxs("div",{className:"flex items-center justify-between mb-2 text-sm",children:[j.jsx("span",{children:"Progreso"}),j.jsxs("span",{children:[Object.keys(d).length," de ",v.length]})]}),j.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:j.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(d).length/v.length*100+"%"}})})]}),j.jsx("div",{className:"mt-6",children:j.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[j.jsx("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"}),j.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",j.jsx("span",{className:"font-medium",children:N(x)})]}),j.jsx("p",{className:"text-xs text-gray-600",children:"Observa cuidadosamente cada imagen antes de responder. Puedes cambiar tu respuesta antes de finalizar el test."})]})}),j.jsx(Ca,{variant:"primary",className:"w-full mt-2",onClick:A,children:"Finalizar Test"})]})]})})]}):j.jsxs(ya,{children:[j.jsx(va,{children:j.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Aptitud Mecánica: Instrucciones"})}),j.jsx(Na,{children:j.jsxs("div",{className:"space-y-6",children:[j.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[j.jsx("h3",{className:"text-lg font-medium text-blue-800 mb-3",children:"Confirmación"}),j.jsx("p",{className:"text-gray-700 mb-4",children:"He leído y acepto las instrucciones. Entiendo que una vez iniciado el test no podré pausarlo y deberé completarlo en su totalidad."})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-3",children:"Instrucciones"}),j.jsx("p",{className:"text-gray-600 mb-4",children:"En esta prueba aparecen varios tipos de situaciones sobre las cuales se te harán algunas preguntas."}),j.jsx("p",{className:"text-gray-600 mb-4",children:"Lee atentamente cada pregunta, observa el dibujo y elige cuál de las opciones es la más adecuada."}),j.jsxs("p",{className:"text-gray-600 mb-4",children:["Recuerda que solo existe ",j.jsx("strong",{children:"UNA opción correcta"}),". Cuando hayas decidido qué opción es, marca la letra correspondiente (A, B, C o D), asegurándote de que coincida con el número del ejercicio que estás contestando."]})]}),j.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[j.jsx("h4",{className:"text-md font-medium text-blue-800 mb-3",children:"Ejemplo M1"}),j.jsxs("p",{className:"text-gray-700 mb-4 font-medium",children:["¿Cuál de las tres botellas podría quitarse sin que se cayera la bandeja?",j.jsx("br",{}),j.jsx("span",{className:"text-sm text-gray-600",children:"(Si no hay diferencia, marca D)."})]}),j.jsxs("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[j.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-3 text-center",children:[j.jsx("img",{src:"/assets/images/mecanico/m1.png",alt:"Ejemplo M1 - Bandeja en equilibrio",className:"max-w-full h-auto mx-auto",style:{maxHeight:"300px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}}),j.jsx("div",{className:"text-gray-500 text-sm",style:{display:"none"},children:"[Imagen no disponible: Bandeja en equilibrio con 3 botellas]"})]}),j.jsxs("div",{className:"grid grid-cols-4 gap-2 mb-3",children:[j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"B"}),j.jsx("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"C"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D"})]}),j.jsxs("p",{className:"text-sm text-gray-600",children:[j.jsx("strong",{children:"Solución:"})," En este ejemplo se presenta una bandeja en equilibrio sobre una mesa y encima de ella 3 botellas. Si se quitase la botella A o la botella B, la bandeja perdería el equilibrio y caería al suelo; si quitáramos la botella C la bandeja se mantendría en equilibrio. Por lo tanto, la solución al ejemplo M1 es ",j.jsx("strong",{children:"C"}),"."]})]})]}),j.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[j.jsx("h4",{className:"text-md font-medium text-green-800 mb-3",children:"Ejemplo M2"}),j.jsxs("p",{className:"text-gray-700 mb-4 font-medium",children:["Si los tres vehículos se están desplazando a 70 km/h, ¿cuál va MÁS rápido?",j.jsx("br",{}),j.jsx("span",{className:"text-sm text-gray-600",children:"(Si no hay diferencia, marca D)."})]}),j.jsxs("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[j.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-3 text-center",children:[j.jsx("img",{src:"/assets/images/mecanico/m2.png",alt:"Ejemplo M2 - Tres vehículos a 70 km/h",className:"max-w-full h-auto mx-auto",style:{maxHeight:"300px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}}),j.jsx("div",{className:"text-gray-500 text-sm",style:{display:"none"},children:"[Imagen no disponible: Tres vehículos a 70 km/h]"})]}),j.jsxs("div",{className:"grid grid-cols-4 gap-2 mb-3",children:[j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"B"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C"}),j.jsx("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"D"})]}),j.jsxs("p",{className:"text-sm text-gray-600",children:[j.jsx("strong",{children:"Solución:"})," Al desplazarse los tres vehículos a la misma velocidad (70 km/h), los tres van igual de rápido. Por lo tanto, la solución a este ejemplo es la opción ",j.jsx("strong",{children:"D"})," (no hay diferencia)."]})]})]}),j.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[j.jsx("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Instrucciones para el Test"}),j.jsxs("p",{className:"text-gray-700 mb-4",children:["El tiempo máximo para la realización de esta prueba es de ",j.jsx("strong",{children:"12 minutos"}),", por lo que deberás trabajar rápidamente, esforzándote al máximo en encontrar la respuesta correcta."]}),j.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-700 mb-4",children:[j.jsx("li",{children:"Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta de las que aparecen."}),j.jsxs("li",{children:[j.jsx("strong",{children:"No se penalizará el error"}),", así que intenta responder todas las preguntas."]}),j.jsx("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas, pero NO continúes con las demás pruebas."}),j.jsx("li",{children:"Puedes navegar libremente entre las preguntas durante el tiempo disponible."})]})]}),j.jsxs("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[j.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"}),j.jsx("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."})]})]})}),j.jsx(wa,{className:"flex justify-end",children:j.jsx(Ca,{variant:"primary",onClick:()=>{r(!0)},className:"px-6 py-2",children:"Comenzar Test"})})]})]})},Ka=Object.freeze(Object.defineProperty({__proto__:null,default:Xa},Symbol.toStringTag,{value:"Module"})),es=[{id:1,type:"equality",question:"6 + 22 = 30 - ?",options:["2","8","10","12","28"],correct:0},{id:2,type:"equality",question:"18 - 6 = 16 - ?",options:["2","3","4","6","10"],correct:2},{id:3,type:"equality",question:"7² - 9 = ? x 2",options:["2","7","10","20","40"],correct:3},{id:4,type:"equality",question:"(6 + 8) x ? = 4 x 7",options:["2","3","4","7","10"],correct:0},{id:5,type:"equality",question:"(3 + 9) x 3 = (? x 2) x 6",options:["1","2","3","4","6"],correct:2},{id:6,type:"series",question:"23 • 18 • 14 • 11 • ?",options:["5","6","7","8","9"],correct:4},{id:7,type:"series",question:"9 • 11 • 10 • 12 • 11 • 13 • ?",options:["11","12","13","14","15"],correct:1},{id:8,type:"series",question:"2 • 6 • 11 • 17 • 24 • 32 • ?",options:["36","37","40","41","42"],correct:3},{id:9,type:"series",question:"21 • 23 • 20 • 24 • 19 • 25 • 18 • ?",options:["16","20","21","22","26"],correct:4},{id:10,type:"series",question:"16 • 8 • 16 • 20 • 10 • 20 • 24 • 12 • ?",options:["4","6","14","24","25"],correct:3},{id:11,type:"table",question:'Tabla: "Puntos obtenidos en la compra"',tableData:{headers:["Artículo","Unidades","Puntos/Unidad","Total puntos"],rows:[["Café","55","15","825"],["Galletas","?","6","240"],["Sal","20","5","100"],["","","","1.165"]]},questionText:"El interrogante (?) está en las Unidades de Galletas.",options:["4","40","60","75","234"],correct:1},{id:12,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Televisión","Altavoces","Auriculares","Total"],rows:[["Enero","?","(dato borrado)","35","85"],["Febrero","45","(dato borrado)","80","175"],["Marzo","60","45","(dato borrado)","(dato borrado)"],["Total","125","(dato borrado)","155","(dato borrado)"]]},questionText:"El interrogante (?) está en Televisión de Enero.",options:["10","20","25","30","50"],correct:1},{id:13,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Secadoras","Lavadoras","Frigoríficos","Total"],rows:[["Enero","(dato borrado)","(dato borrado)","30","90"],["Febrero","5","40","25","70"],["Marzo","(dato borrado)","30","35","105"],["Abril","50","45","?","(dato borrado)"],["Total","(dato borrado)","155","145","(dato borrado)"]]},questionText:"El interrogante (?) está en Frigoríficos de Abril.",options:["30","45","55","65","90"],correct:2},{id:14,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Televisión","Altavoces","Auriculares","Total"],rows:[["Abril","5","8","(dato borrado)","33"],["Mayo","(dato borrado)","15","5","30"],["Junio","10","(dato borrado)","(dato borrado)","(dato borrado)"],["Total","?","38","32","(dato borrado)"]]},questionText:"El interrogante (?) está en el Total de Televisión.",options:["10","15","20","25","30"],correct:3},{id:15,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Televisión","Altavoces","Auriculares","Total"],rows:[["Enero","20","(dato borrado)","15","(dato borrado)"],["Febrero","?","(dato borrado)","30","70"],["Marzo","20","(dato borrado)","30","(dato borrado)"],["Abril","(dato borrado)","15","10","55"],["Total","85","(dato borrado)","80","(dato borrado)"]]},questionText:"El interrogante (?) está en Televisión de Febrero.",options:["10","15","25","40","45"],correct:1},{id:16,type:"equality",question:"(30 : 5) : (14 : 7) = [(? x 5) + 3] : 11",options:["1","2","3","4","6"],correct:4},{id:17,type:"equality",question:"[(23 - 9) - 4] x 2 = [(? : 6) - 3] x 5",options:["7","20","24","30","42"],correct:4},{id:18,type:"equality",question:"20 + 35 - 14 = (? x 2) - 19",options:["11","25","30","35","60"],correct:2},{id:19,type:"equality",question:"(9 x 7) : (? - 2) = 9 + 7 + 5",options:["3","4","5","6","12"],correct:2},{id:20,type:"equality",question:"[(? : 7) - 3] : 2 = 21 : 7",options:["2","9","42","49","63"],correct:4},{id:21,type:"series",question:"14 • 11 • 15 • 12 • 17 • 14 • 20 • 17 • 24 • 21 • ?",options:["18","25","26","27","29"],correct:4},{id:22,type:"series",question:"2 • 8 • 4 • 16 • 8 • ?",options:["4","14","24","26","32"],correct:4},{id:23,type:"series",question:"5 • 6 • 8 • 7 • 10 • 14 • 13 • 18 • 24 • 23 • ?",options:["22","24","26","28","30"],correct:4},{id:24,type:"series",question:"11 • 13 • 16 • 15 • 19 • 24 • 22 • ?",options:["23","24","25","26","28"],correct:4},{id:25,type:"series",question:"3 • 6 • 4 • 8 • 6 • ?",options:["4","9","10","11","12"],correct:4},{id:26,type:"series",question:"3 • 2 • 6 • 4 • 12 • 8 • 24 • 16 • 48 • 32 • 96 • ?",options:["64","80","89","92","95"],correct:0},{id:27,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Plancha","Depiladora","Afeitadora","Total"],rows:[["Mayo","20","5","(dato borrado)","40"],["Junio","(dato borrado)","(dato borrado)","10","(dato borrado)"],["Abril","(dato borrado)","5","(dato borrado)","25"],["Total","40","20","?","(dato borrado)"]]},questionText:"El interrogante (?) está en el Total de Afeitadora.",options:["60","65","75","90","95"],correct:3},{id:28,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Hornos","Microondas","Vitrocerámica","Total"],rows:[["Septiembre","25","40","5","70"],["Octubre","(dato borrado)","45","50","(dato borrado)"],["Noviembre","30","(dato borrado)","?","90"],["Diciembre","35","30","(dato borrado)","105"],["Total","145","155","(dato borrado)","(dato borrado)"]]},questionText:"El interrogante (?) está en Vitrocerámica de Noviembre.",options:["10","15","20","30","60"],correct:2},{id:29,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Cafetera","Tostadora","Freidora","Total"],rows:[["Enero","(dato borrado)","5","20","35"],["Febrero","(dato borrado)","(dato borrado)","5","30"],["Marzo","15","30","?","(dato borrado)"],["Total","(dato borrado)","55","40","(dato borrado)"]]},questionText:"El interrogante (?) está en Freidora de Marzo.",options:["5","10","15","20","25"],correct:2},{id:30,type:"table",question:'Tabla: "Puntos obtenidos en la compra"',tableData:{headers:["Artículo","Unidades","Puntos/Unidad","Total puntos"],rows:[["Chocolate","5","225","1.125"],["Harina","6","?","(dato borrado)"],["Nueces","8","140","(dato borrado)"],["","","","3.925"]]},questionText:"El interrogante (?) está en Puntos/Unidad de Harina.",options:["26","265","270","280","1.680"],correct:3},{id:31,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Hornos","Microondas","Vitrocerámica","Total"],rows:[["Mayo","(dato borrado)","15","20","45"],["Junio","15","10","(dato borrado)","55"],["Julio","(dato borrado)","5","20","(dato borrado)"],["Agosto","10","(dato borrado)","10","25"],["Total","?","(dato borrado)","80","155"]]},questionText:"El interrogante (?) está en el Total de Hornos.",options:["25","35","40","45","50"],correct:2},{id:32,type:"table",question:'Tabla: "Puntos obtenidos en la compra"',tableData:{headers:["Artículo","Unidades","Puntos/Unidad","Total puntos"],rows:[["Grapa","2.500","0,05","125"],["Chincheta","3.000","?","(dato borrado)"],["Tornillo","1.200","0,1","(dato borrado)"],["","","","845"]]},questionText:"El interrogante (?) está en Puntos/Unidad de Chincheta.",options:["0,03","0,1","0,2","0,5","5"],correct:2}];function as({children:e="dato borrado"}){return j.jsx("span",{className:"line-through text-red-600 font-medium",children:e})}const ss=()=>{var e;const a=f(),s=w(),[t,r]=b.useState(0),[n,i]=b.useState({}),[c,d]=b.useState(1200),[p,x]=b.useState(!1),[h,g]=b.useState(!1),y=null==(e=s.state)?void 0:e.patientId,v=es,N=b.useCallback(()=>m(null,null,function*(){try{const s=Object.keys(n).length,t=v.length,r=t-s;let i=0;Object.entries(n).forEach(([e,a])=>{const s=v.find(a=>a.id.toString()===e);s&&parseInt(a)===s.correct&&i++});const o=s-i,l=1200-c,d={correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"numerico"};if(y)try{yield Ba.saveTestResult({patientId:y,testType:"numerico",correctCount:i,incorrectCount:o,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:n,errores:o})}catch(e){}u.success(`Test completado. Has respondido ${s} de ${t} preguntas. Respuestas correctas: ${i}`),a("/test/results/numerico",{state:d})}catch(e){u.error("Error al procesar los resultados del test")}}),[n,v,c,a,y]);b.useEffect(()=>{if(h&&c>0&&!p){const e=setTimeout(()=>d(c-1),1e3);return()=>clearTimeout(e)}0===c&&setTimeout(()=>N(),0)},[c,p,h,N]);const C=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`,A=e=>{r(e)},E=v[t];return j.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[j.jsxs("div",{className:"mb-6",children:[j.jsxs("div",{className:"text-center mb-4",children:[j.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[j.jsx("i",{className:"fas fa-calculator mr-2 text-teal-600"}),"Test de Aptitud Numérica"]}),j.jsx("p",{className:"text-gray-600",children:"Resolución de igualdades, series numéricas y análisis de tablas de datos"})]}),h&&j.jsx("div",{className:"text-center",children:j.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:C(c)})})]}),h?j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[j.jsx("div",{className:"md:col-span-3",children:j.jsxs(ya,{className:"mb-6",children:[j.jsxs(va,{className:"flex justify-between items-center",children:[j.jsxs("div",{children:[j.jsxs("h2",{className:"text-lg font-medium",children:["Pregunta ",t+1," de ",v.length]}),j.jsx("p",{className:"text-sm text-gray-500",children:"equality"===E.type?"Igualdades Numéricas":"series"===E.type?"Series Numéricas":"Tablas de Datos"})]}),j.jsx("div",{className:"text-sm text-gray-500",children:void 0!==n[E.id]?"Respondida":"Sin responder"})]}),j.jsxs(Na,{children:[j.jsxs("div",{className:"mb-6",children:[j.jsxs("h4",{className:"text-lg font-medium text-gray-800 mb-4",children:["equality"===E.type&&"¿Qué número debe aparecer en lugar del interrogante (?) para que se cumpla la igualdad?","series"===E.type&&"¿Qué número debe aparecer en lugar del interrogante (?) de modo que continúe la serie?","table"===E.type&&"¿Qué número debe aparecer en lugar del interrogante (?) a partir de los datos de la tabla?"]}),"table"===E.type?j.jsxs("div",{children:[j.jsx("h5",{className:"font-medium mb-3",children:E.question}),j.jsx("div",{className:"overflow-x-auto mb-4",children:j.jsxs("table",{className:"min-w-full border border-gray-300",children:[j.jsx("thead",{children:j.jsx("tr",{className:"bg-gray-50",children:E.tableData.headers.map((e,a)=>j.jsx("th",{className:"border border-gray-300 px-3 py-2 text-center font-medium",children:e},a))})}),j.jsx("tbody",{children:E.tableData.rows.map((e,a)=>j.jsx("tr",{className:a%2==0?"bg-white":"bg-gray-50",children:e.map((e,a)=>j.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"(dato borrado)"===e?j.jsx(as,{}):e},a))},a))})]})}),j.jsx("p",{className:"text-gray-700 mb-4",children:E.questionText})]}):j.jsx("div",{className:"bg-gray-50 p-4 rounded-lg text-center",children:j.jsx("span",{className:"text-xl font-mono",children:E.question})})]}),j.jsx("div",{className:"space-y-3",children:E.options.map((e,a)=>j.jsx("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(n[E.id]===a?"bg-blue-50 border-blue-500":"hover:bg-gray-50"),onClick:()=>{return e=E.id,s=a,void i(a=>l(o({},a),{[e]:s}));var e,s},children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(n[E.id]===a?"bg-blue-500 text-white":"bg-gray-200 text-gray-700"),children:String.fromCharCode(65+a)}),j.jsx("div",{className:"text-gray-700",children:e})]})},a))})]}),j.jsxs(wa,{className:"flex justify-between",children:[j.jsx(Ca,{variant:"outline",onClick:()=>A(Math.max(0,t-1)),disabled:0===t,children:"Anterior"}),t<v.length-1?j.jsx(Ca,{variant:"primary",onClick:()=>A(Math.min(v.length-1,t+1)),children:"Siguiente"}):j.jsx(Ca,{variant:"primary",onClick:N,children:"Finalizar Test"})]})]})}),j.jsx("div",{children:j.jsxs(ya,{className:"sticky top-6",children:[j.jsx(va,{children:j.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),j.jsxs(Na,{children:[j.jsx("div",{className:"grid grid-cols-4 gap-2",children:v.map((e,a)=>j.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(t===a?"bg-blue-500 text-white":void 0!==n[v[a].id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>A(a),title:`Pregunta ${a+1}`,children:a+1},a))}),j.jsxs("div",{className:"mt-6",children:[j.jsxs("div",{className:"flex items-center justify-between mb-2 text-sm",children:[j.jsx("span",{children:"Progreso"}),j.jsxs("span",{children:[Object.keys(n).length," de ",v.length]})]}),j.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:j.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(n).length/v.length*100+"%"}})})]}),j.jsx("div",{className:"mt-6",children:j.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[j.jsx("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"}),j.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",j.jsx("span",{className:"font-medium",children:C(c)})]}),j.jsx("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."})]})}),j.jsx(Ca,{variant:"primary",className:"w-full mt-2",onClick:N,children:"Finalizar Test"})]})]})})]}):j.jsxs(ya,{children:[j.jsx(va,{children:j.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Aptitud Numérica: Instrucciones"})}),j.jsx(Na,{children:j.jsxs("div",{className:"space-y-6",children:[j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-3",children:"Instrucciones"}),j.jsx("p",{className:"text-gray-600 mb-4",children:"En esta prueba encontrarás distintos ejercicios numéricos que tendrás que resolver. Para ello tendrás que analizar la información que se presenta y determinar qué debe aparecer en lugar del interrogante. Cuando lo hayas decidido, deberás marcar la letra de la opción correspondiente (A, B, C, D o E), asegurándote de que coincida con el ejercicio que estás contestando. Ten en cuenta que en este ejercicio hay 5 posibles opciones de respuesta."})]}),j.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[j.jsx("h4",{className:"text-md font-medium text-blue-800 mb-3",children:"Tipo 1: Igualdades Numéricas"}),j.jsx("p",{className:"text-gray-700 mb-4",children:"En un primer tipo de ejercicios aparecerá una igualdad numérica en la que se ha sustituido uno de los elementos por un interrogante (?). Tu tarea consistirá en averiguar qué valor numérico debe aparecer en lugar del interrogante para que se cumpla la igualdad."}),j.jsxs("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[j.jsx("h5",{className:"font-medium text-gray-800 mb-2",children:"Ejemplo N1: ¿Qué número debe aparecer en lugar del interrogante (?) para que se cumpla la igualdad?"}),j.jsx("div",{className:"bg-gray-100 p-3 rounded text-center mb-3",children:j.jsx("span",{className:"text-xl font-mono",children:"16 - 4 = ? + 2"})}),j.jsxs("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A. 8"}),j.jsx("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"B. 10"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C. 12"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D. 14"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"E. 16"})]}),j.jsxs("p",{className:"text-sm text-gray-600",children:[j.jsx("strong",{children:"Solución:"})," La primera parte de la igualdad, 16 – 4, da lugar a 12. Para que en la segunda parte se obtenga el mismo resultado sería necesario sustituir el interrogante por 10, quedando la igualdad como 16 – 4 = 10 + 2. Por tanto, la respuesta correcta es ",j.jsx("strong",{children:"B"}),"."]})]})]}),j.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[j.jsx("h4",{className:"text-md font-medium text-green-800 mb-3",children:"Tipo 2: Series Numéricas"}),j.jsx("p",{className:"text-gray-700 mb-4",children:"En otros ejercicios tendrás que observar una serie de números ordenados de acuerdo con una ley y determinar cuál debe continuar la serie ocupando el lugar del interrogante."}),j.jsxs("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[j.jsx("h5",{className:"font-medium text-gray-800 mb-2",children:"Ejemplo N2: ¿Qué número debe aparecer en lugar del interrogante (?) de modo que continúe la serie?"}),j.jsx("div",{className:"bg-gray-100 p-3 rounded text-center mb-3",children:j.jsx("span",{className:"text-xl font-mono",children:"3 • 5 • 6 • 8 • 9 • 11 • 12 • 14 • ?"})}),j.jsxs("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A. 13"}),j.jsx("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"B. 15"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C. 16"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D. 18"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"E. 20"})]}),j.jsx("div",{className:"bg-gray-50 p-3 rounded mb-3",children:j.jsxs("div",{className:"text-center text-sm font-mono",children:["3 → 5 → 6 → 8 → 9 → 11 → 12 → 14 → ?",j.jsx("br",{}),j.jsx("span",{className:"text-blue-600",children:"+2 +1 +2 +1 +2 +1 +2 +1"})]})}),j.jsxs("p",{className:"text-sm text-gray-600",children:[j.jsx("strong",{children:"Solución:"})," En este ejemplo la serie combina aumentos de 2 unidades y de 1 unidad (+2, +1, +2, +1...). Como puede observarse, en el lugar del interrogante debe aumentarse 1 unidad con respecto al número anterior, por lo que el número que continuaría la serie sería el 15. Por tanto, la respuesta correcta es ",j.jsx("strong",{children:"B"}),"."]})]})]}),j.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[j.jsx("h4",{className:"text-md font-medium text-purple-800 mb-3",children:"Tipo 3: Tablas de Datos"}),j.jsx("p",{className:"text-gray-700 mb-4",children:"Finalmente, en un tercer tipo de ejercicios, aparecen tablas en las que un valor se ha sustituido intencionadamente por un interrogante (?) y otros valores han sido borrados (<<>>). Tu tarea consistirá en averiguar el número que debería aparecer en lugar del interrogante."}),j.jsxs("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[j.jsx("h5",{className:"font-medium text-gray-800 mb-2",children:"Ejemplo N3: De acuerdo con los datos de la tabla, ¿qué número debe aparecer en lugar del interrogante (?)?"}),j.jsxs("div",{className:"mb-4",children:[j.jsx("h6",{className:"text-center font-medium mb-3",children:"Puntos obtenidos en la compra"}),j.jsx("div",{className:"overflow-x-auto",children:j.jsxs("table",{className:"w-full border border-gray-300",children:[j.jsx("thead",{children:j.jsxs("tr",{className:"bg-gray-100",children:[j.jsx("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Artículo"}),j.jsx("th",{className:"border border-gray-300 px-3 py-2 text-center",children:"Unidades"}),j.jsx("th",{className:"border border-gray-300 px-3 py-2 text-center",children:"Puntos/Unidad"}),j.jsx("th",{className:"border border-gray-300 px-3 py-2 text-center",children:"Total puntos"})]})}),j.jsxs("tbody",{children:[j.jsxs("tr",{children:[j.jsx("td",{className:"border border-gray-300 px-3 py-2",children:"Jabón"}),j.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"10"}),j.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center font-bold text-red-600",children:"?"}),j.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"30"})]}),j.jsxs("tr",{className:"bg-gray-50",children:[j.jsx("td",{className:"border border-gray-300 px-3 py-2",children:"Aceite"}),j.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"20"}),j.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"2"}),j.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center",children:j.jsx("span",{className:"line-through",children:"40"})})]}),j.jsxs("tr",{children:[j.jsx("td",{className:"border border-gray-300 px-3 py-2 font-bold",colSpan:"3",children:"Total"}),j.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center font-bold",children:"70"})]})]})]})})]}),j.jsxs("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[j.jsx("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"A. 3"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"B. 5"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C. 10"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D. 40"}),j.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"E. 60"})]}),j.jsxs("p",{className:"text-sm text-gray-600",children:[j.jsx("strong",{children:"Solución:"})," A partir de los datos de la tabla sabemos que se han comprado 10 unidades de jabón y que se han obtenido 30 puntos, por lo que se puede deducir que el valor del interrogante es igual a 3 (10 unidades × 3 puntos/unidad = 30 puntos). Por tanto, la respuesta correcta es ",j.jsx("strong",{children:"A"}),". Fíjate que en este ejemplo no es necesario calcular el valor que ha sido borrado para obtener el valor del interrogante, pero en otros ejercicios sí será necesario calcular todos o algunos de estos valores para alcanzar la solución."]})]})]}),j.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[j.jsx("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Instrucciones para el Test"}),j.jsxs("p",{className:"text-gray-700 mb-4",children:["Cuando comience la prueba encontrarás más ejercicios como estos. El tiempo máximo para su realización es de ",j.jsx("strong",{children:"20 minutos"}),", por lo que deberás trabajar rápidamente, esforzándote al máximo en encontrar la respuesta correcta."]}),j.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-700 mb-4",children:[j.jsx("li",{children:"Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta de las cinco que aparecen."}),j.jsxs("li",{children:[j.jsx("strong",{children:"No se penalizará el error"}),", así que intenta responder todas las preguntas."]}),j.jsx("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas, pero NO continúes con las demás pruebas."}),j.jsx("li",{children:"Puedes navegar libremente entre las preguntas durante el tiempo disponible."})]})]}),j.jsxs("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[j.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"}),j.jsx("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."})]})]})}),j.jsx(wa,{className:"flex justify-end",children:j.jsx(Ca,{variant:"primary",onClick:()=>{g(!0)},className:"px-6 py-2",children:"Comenzar Test"})})]})]})},ts=Object.freeze(Object.defineProperty({__proto__:null,default:ss},Symbol.toStringTag,{value:"Module"})),rs=({data:e})=>{var a;const s=e.reduce((e,a)=>e+a.value,0);if(!e.length||0===s)return j.jsx("div",{className:"flex items-center justify-center h-full",children:j.jsx("p",{className:"text-gray-500",children:"No hay datos disponibles"})});let t=0;const r=e.map(e=>{const a=e.value/s*100,r=a/100*360,n=l(o({},e),{percentage:a,startAngle:t,endAngle:t+r});return t+=r,n}),n=(e,a=50)=>{const s=(e-90)*Math.PI/180;return{x:50+a*Math.cos(s),y:50+a*Math.sin(s)}},i=e=>{const a=n(e.startAngle),s=n(e.endAngle),t=e.endAngle-e.startAngle<=180?"0":"1";return`M 50 50 L ${a.x} ${a.y} A 50 50 0 ${t} 1 ${s.x} ${s.y} Z`},c=1===r.length||r.some(e=>100===e.percentage);return j.jsxs("div",{className:"flex flex-col items-center h-full",children:[j.jsx("div",{className:"w-full max-w-xs",children:j.jsx("svg",{viewBox:"0 0 100 100",className:"w-48 h-48 mx-auto mb-4",children:c?j.jsx("circle",{cx:"50",cy:"50",r:"50",fill:(null==(a=r.find(e=>100===e.percentage))?void 0:a.color)||r[0].color}):r.map((e,a)=>j.jsx("path",{d:i(e),fill:e.color,stroke:"#fff",strokeWidth:"0.5"},a))})}),j.jsx("div",{className:"flex flex-col items-center w-full",children:r.map((e,a)=>j.jsxs("div",{className:"flex items-center mb-2 w-full justify-between max-w-xs",children:[j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"w-4 h-4 mr-2",style:{backgroundColor:e.color}}),j.jsx("span",{className:"text-sm text-gray-700",children:e.name})]}),j.jsxs("div",{className:"flex items-center",children:[j.jsx("span",{className:"font-medium text-sm mr-2",children:e.value}),j.jsxs("span",{className:"text-xs text-gray-500",children:["(",e.percentage.toFixed(1),"%)"]})]})]},a))})]})},ns=()=>{const e=w().state||{correctCount:0,incorrectCount:0,unansweredCount:0,timeUsed:0,totalQuestions:32,testType:"unknown"},{correctCount:a,incorrectCount:s,unansweredCount:t,timeUsed:r,totalQuestions:n,testType:i}=e,o=[{name:"Correctas",value:a,color:"#10B981"},{name:"Incorrectas",value:s,color:"#EF4444"},{name:"Sin responder",value:t,color:"#9CA3AF"}].filter(e=>e.value>0);0===o.length&&o.push({name:"Correctas",value:a,color:"#10B981"});const l=Math.round(a/n*100),c=((e,a)=>{let s=[];return s="ortografia"===e?a<60?["Repasa las reglas básicas de ortografía, especialmente las reglas de acentuación","Practica la identificación de palabras correctas e incorrectas con ejercicios diarios","Presta especial atención a las letras que suelen causar confusión (b/v, g/j, h)"]:a<80?["Refuerza tu conocimiento en acentuación, especialmente en palabras agudas, llanas y esdrújulas","Practica con palabras que contengan h, b/v, g/j para mejorar tu precisión","Dedica tiempo a la lectura para familiarizarte con la escritura correcta de las palabras"]:["Continúa practicando con palabras poco comunes para expandir tu dominio ortográfico","Profundiza en las excepciones a las reglas de acentuación","Mantén el hábito de lectura para reforzar tu ortografía"]:"espacial"===e?100===a?["¡Felicidades! Has demostrado una capacidad excepcional de razonamiento espacial","Considera explorar campos profesionales como arquitectura, ingeniería, diseño 3D o ciencias que requieran esta habilidad","Tu capacidad para visualizar y manipular objetos mentalmente es extraordinaria","Podrías compartir tus técnicas y estrategias con otros para ayudarles a mejorar sus habilidades espaciales"]:a<60?["Practica con rompecabezas tridimensionales y juegos de construcción para mejorar tu visualización espacial","Realiza ejercicios de rotación mental, como imaginar objetos desde diferentes ángulos","Intenta dibujar objetos tridimensionales desde diferentes perspectivas","Utiliza aplicaciones o juegos que ejerciten el razonamiento espacial"]:a<80?["Continúa practicando con ejercicios de visualización espacial más complejos","Intenta resolver problemas de plegado de papel (origami) para mejorar tu comprensión de transformaciones espaciales","Practica con juegos de construcción y ensamblaje que requieran visualización tridimensional","Analiza las preguntas que te resultaron más difíciles para identificar patrones específicos"]:["Desafíate con problemas de visualización espacial más avanzados","Explora campos como la geometría tridimensional, el diseño 3D o la arquitectura","Comparte tus conocimientos y estrategias con otros para reforzar tu comprensión","Considera carreras o actividades que aprovechen tu excelente capacidad de razonamiento espacial"]:"mecanico"===e?100===a?["¡Excelente! Has demostrado una comprensión excepcional de principios mecánicos y físicos","Considera carreras en ingeniería mecánica, física aplicada, o diseño industrial","Tu capacidad para analizar sistemas mecánicos y predecir comportamientos es sobresaliente","Podrías explorar campos como robótica, automatización o diseño de maquinaria"]:a<60?["Repasa los principios básicos de física: fuerzas, palancas, poleas y equilibrio","Practica con ejercicios de mecánica básica y análisis de sistemas simples","Observa cómo funcionan las máquinas simples en la vida cotidiana","Dedica tiempo a entender conceptos como centro de gravedad, resistencia y fricción"]:a<80?["Profundiza en el estudio de máquinas simples y compuestas","Practica con problemas de equilibrio de fuerzas y análisis de estructuras","Estudia casos prácticos de aplicaciones mecánicas en la industria","Refuerza tu comprensión de principios como ventaja mecánica y eficiencia"]:["Explora conceptos avanzados de mecánica y termodinámica","Considera estudiar ingeniería mecánica o campos relacionados","Practica con simulaciones y modelado de sistemas mecánicos complejos","Mantén tu conocimiento actualizado con las últimas tecnologías mecánicas"]:"numerico"===e?100===a?["¡Excelente! Has demostrado una capacidad excepcional en razonamiento numérico","Considera carreras en matemáticas, estadística, ingeniería, economía o ciencias actuariales","Tu habilidad para resolver problemas numéricos complejos es sobresaliente","Podrías explorar campos como análisis de datos, investigación operativa o finanzas cuantitativas"]:a<60?["Repasa las operaciones básicas: suma, resta, multiplicación y división","Practica con ejercicios de igualdades numéricas y resolución de ecuaciones simples","Dedica tiempo a entender patrones en series numéricas","Refuerza tu comprensión de fracciones, decimales y porcentajes"]:a<80?["Practica con series numéricas más complejas para identificar patrones","Mejora tu velocidad en cálculo mental y operaciones aritméticas","Estudia problemas de proporcionalidad y regla de tres","Analiza tablas de datos y practica la interpretación de información numérica"]:["Desafíate con problemas de matemáticas más avanzados","Explora áreas como álgebra, estadística y análisis de datos","Considera estudiar carreras que requieran fuerte razonamiento cuantitativo","Mantén tu agilidad mental practicando regularmente con ejercicios numéricos"]:["Continúa practicando ejercicios similares para mejorar tu desempeño","Revisa los conceptos básicos relacionados con este tipo de prueba","Analiza las preguntas que te resultaron más difíciles para identificar áreas de mejora"],s})(i,l);return j.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[j.jsxs("div",{className:"mb-6",children:[j.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:(e=>{switch(e){case"verbal":return"Test de Aptitud Verbal";case"ortografia":return"Test de Ortografía";case"razonamiento":return"Test de Razonamiento";case"atencion":return"Test de Atención";case"espacial":return"Test de Visualización Espacial";case"mecanico":return"Test de Razonamiento Mecánico";case"numerico":return"Test de Razonamiento Numérico";default:return"Resultados del Test"}})(i)}),j.jsx("p",{className:"text-gray-600",children:"Resumen de tu desempeño en el test."})]}),j.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[j.jsxs("div",{className:"bg-white shadow-md rounded-lg p-6",children:[j.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Resultados"}),j.jsx("div",{className:"h-64",children:j.jsx(rs,{data:o})})]}),j.jsxs("div",{className:"bg-white shadow-md rounded-lg p-6",children:[j.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Estadísticas"}),j.jsxs("div",{className:"mb-6",children:[j.jsxs("div",{className:"text-3xl font-bold mb-1 "+(d=l,100===d?"text-green-600 animate-pulse":d>=90?"text-green-600":d>=75?"text-green-500":d>=60?"text-blue-500":d>=50?"text-yellow-500":"text-red-500"),children:[l,"%"]}),j.jsx("p",{className:"text-gray-700 font-medium",children:(e=>100===e||e>=90?"Excelente desempeño":e>=75?"Muy buen desempeño":e>=60?"Buen desempeño":e>=50?"Desempeño aceptable":"Necesita mejorar")(l)})]}),j.jsxs("div",{className:"space-y-3",children:[j.jsxs("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[j.jsx("span",{className:"text-gray-600",children:"Respuestas correctas:"}),j.jsxs("span",{className:"font-medium text-gray-800",children:[a," de ",n]})]}),j.jsxs("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[j.jsx("span",{className:"text-gray-600",children:"Respuestas incorrectas:"}),j.jsx("span",{className:"font-medium text-gray-800",children:s})]}),j.jsxs("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[j.jsx("span",{className:"text-gray-600",children:"Sin responder:"}),j.jsx("span",{className:"font-medium text-gray-800",children:t})]}),j.jsxs("div",{className:"flex justify-between py-2",children:[j.jsx("span",{className:"text-gray-600",children:"Tiempo utilizado:"}),j.jsx("span",{className:"font-medium text-gray-800",children:(e=>{const a=e%60;return`${Math.floor(e/60)}:${a<10?"0":""}${a}`})(r)})]})]})]})]}),j.jsxs("div",{className:"bg-white shadow-md rounded-lg p-6 mb-6",children:[j.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Recomendaciones"}),j.jsx("ul",{className:"space-y-2",children:c.map((e,a)=>j.jsxs("li",{className:"flex items-start",children:[j.jsx("div",{className:"flex-shrink-0 h-5 w-5 mt-0.5",children:j.jsx("svg",{className:"h-5 w-5 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:j.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),j.jsx("span",{className:"ml-2 text-gray-700",children:e})]},a))})]}),j.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[j.jsx(y,{to:`/test/instructions/${i}`,className:"flex-1 bg-blue-600 text-white text-center py-3 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Realizar el Test Nuevamente"}),j.jsx(y,{to:"/student/questionnaire",className:"flex-1 bg-gray-100 text-gray-800 text-center py-3 px-4 rounded-md hover:bg-gray-200 transition-colors",children:"Volver a la Lista de Tests"})]})]});var d},is=Object.freeze(Object.defineProperty({__proto__:null,default:ns},Symbol.toStringTag,{value:"Module"})),os=b.createContext(),ls=e=>{if(!e)return null;const a=new Date,s=new Date(e);let t=a.getFullYear()-s.getFullYear();const r=a.getMonth()-s.getMonth();return(r<0||0===r&&a.getDate()<s.getDate())&&t--,t},cs=e=>{if(!e)return"";return new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"2-digit",day:"2-digit"})},ds=e=>{if(!e)return"";return new Date(e).toLocaleDateString("es-ES",{weekday:"long",year:"numeric",month:"long",day:"numeric"})},ms=e=>{if("number"==typeof e){if(e<0)return"N/A";const a=Math.floor(e/3600),s=Math.floor(e%3600/60),t=Math.floor(e%60);return a>0?`${a}:${s.toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`:`${s}:${t.toString().padStart(2,"0")}`}if(!e)return"";return new Date(e).toLocaleTimeString("es-ES",{hour:"2-digit",minute:"2-digit"})},us=b.forwardRef((e,a)=>{var s=e,{checked:t=!1,indeterminate:r=!1,onChange:n,disabled:i=!1,className:l="",children:d,id:m,"aria-label":u,"aria-describedby":p}=s,x=c(s,["checked","indeterminate","onChange","disabled","className","children","id","aria-label","aria-describedby"]);const h=b.useRef(null),g=a||h;b.useEffect(()=>{g.current&&(g.current.indeterminate=r)},[r,g]);const f=`\n    relative inline-flex items-center justify-center w-5 h-5 border-2 rounded transition-all duration-200\n    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\n    ${i?"cursor-not-allowed opacity-50":"cursor-pointer"}\n  `,y="border-gray-300 bg-white hover:border-gray-400",v="border-blue-600 bg-blue-600 hover:border-blue-700 hover:bg-blue-700",N="border-blue-600 bg-blue-600 hover:border-blue-700 hover:bg-blue-700";return j.jsxs("label",{className:`inline-flex items-center ${i?"cursor-not-allowed":"cursor-pointer"} ${l}`,htmlFor:m,children:[j.jsxs("div",{className:"relative",children:[j.jsx("input",o({ref:g,type:"checkbox",id:m,checked:t,onChange:e=>{n&&!i&&n(e)},disabled:i,className:"sr-only","aria-label":u,"aria-describedby":p},x)),j.jsx("div",{className:`${f} ${r?N:t?v:y}`,children:r?j.jsx(P,{className:"w-3 h-3 text-white","aria-hidden":"true"}):t?j.jsx(D,{className:"w-3 h-3 text-white","aria-hidden":"true"}):null})]}),d&&j.jsx("span",{className:"ml-2 text-sm "+(i?"text-gray-400":"text-gray-700"),children:d})]})});us.displayName="Checkbox";const ps=e=>{var a=e,{children:s,content:t,position:r="top",delay:n=300,disabled:i=!1,className:l="",contentClassName:d="",arrow:m=!0}=a,u=c(a,["children","content","position","delay","disabled","className","contentClassName","arrow"]);const[p,x]=b.useState(!1),[h,g]=b.useState({top:0,left:0}),f=b.useRef(null),y=b.useRef(null),v=b.useRef(null),N=()=>{if(!f.current||!y.current)return;const e=f.current.getBoundingClientRect(),a=y.current.getBoundingClientRect(),s=window.pageYOffset||document.documentElement.scrollTop,t=window.pageXOffset||document.documentElement.scrollLeft;let n,i;switch(r){case"top":default:n=e.top+s-a.height-8,i=e.left+t+(e.width-a.width)/2;break;case"bottom":n=e.bottom+s+8,i=e.left+t+(e.width-a.width)/2;break;case"left":n=e.top+s+(e.height-a.height)/2,i=e.left+t-a.width-8;break;case"right":n=e.top+s+(e.height-a.height)/2,i=e.right+t+8}const o=window.innerWidth-a.width-8,l=window.innerHeight-a.height-8;i=Math.max(8,Math.min(i,o)),n=Math.max(8,Math.min(n,l)),g({top:n,left:i})},w=()=>{!i&&t&&(v.current&&clearTimeout(v.current),v.current=setTimeout(()=>{x(!0)},n))},C=()=>{v.current&&clearTimeout(v.current),x(!1)};b.useEffect(()=>{if(p){N();const e=()=>N();return window.addEventListener("resize",e),window.addEventListener("scroll",e,!0),()=>{window.removeEventListener("resize",e),window.removeEventListener("scroll",e,!0)}}},[p,r]),b.useEffect(()=>()=>{v.current&&clearTimeout(v.current)},[]);const A=b.cloneElement(s,o({ref:f,onMouseEnter:e=>{w(),s.props.onMouseEnter&&s.props.onMouseEnter(e)},onMouseLeave:e=>{C(),s.props.onMouseLeave&&s.props.onMouseLeave(e)},onFocus:e=>{w(),s.props.onFocus&&s.props.onFocus(e)},onBlur:e=>{C(),s.props.onBlur&&s.props.onBlur(e)},className:`${s.props.className||""} ${l}`.trim()},u)),E=p&&t&&j.jsxs("div",{ref:y,className:`\n        fixed z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg\n        pointer-events-none transition-opacity duration-200\n        ${p?"opacity-100":"opacity-0"}\n        ${d}\n      `,style:{top:h.top,left:h.left},role:"tooltip","aria-hidden":!p,children:[t,m&&j.jsx("div",{className:(()=>{const e="absolute w-2 h-2 bg-gray-900 transform rotate-45";switch(r){case"top":default:return`${e} -bottom-1 left-1/2 -translate-x-1/2`;case"bottom":return`${e} -top-1 left-1/2 -translate-x-1/2`;case"left":return`${e} -right-1 top-1/2 -translate-y-1/2`;case"right":return`${e} -left-1 top-1/2 -translate-y-1/2`}})()})]});return j.jsxs(j.Fragment,{children:[A,"undefined"!=typeof document&&I.createPortal(E,document.body)]})},xs=e=>{var a=e,{className:s="",width:t,height:r,variant:n="rectangular",animation:i="pulse",lines:d=1}=a,m=c(a,["className","width","height","variant","animation","lines"]);const u={pulse:"animate-pulse",wave:"animate-wave",none:""},p={rectangular:"rounded",circular:"rounded-full",text:"rounded h-4",avatar:"rounded-full w-10 h-10"},x=()=>`\n      bg-gray-200\n      ${u[i]||u.pulse}\n      ${p[n]||p.rectangular}\n      ${s}\n    `.trim(),h=()=>{const e={};return t&&(e.width="number"==typeof t?`${t}px`:t),r&&(e.height="number"==typeof r?`${r}px`:r),e};return"text"===n&&d>1?j.jsx("div",l(o({className:`space-y-2 ${s}`},m),{children:Array.from({length:d},(e,a)=>j.jsx("div",{className:x(),style:l(o({},h()),{width:a===d-1?"75%":"100%"})},a))})):j.jsx("div",o({className:x(),style:h()},m))},hs=b.memo(({index:e,style:a,data:s})=>{var t,r,n,i,o;const{informes:l,selectedInformes:c,onToggleSelection:d,onViewInforme:m,onDeleteInforme:u,onViewChart:p,isLoading:x}=s,h=l[e],g=c.has(null==h?void 0:h.id),f=e%2==0,y=b.useCallback(()=>{d(h.id)},[null==h?void 0:h.id,d]),v=b.useCallback(()=>{m(h)},[h,m]),N=b.useCallback(()=>{u(h.id)},[null==h?void 0:h.id,u]),w=b.useCallback(()=>{p(h)},[h,p]);return x||!h?j.jsx("div",{style:a,className:"flex items-center px-4 py-3",children:j.jsx(xs,{className:"w-full h-16"})}):j.jsxs("div",{style:a,className:`\n        flex items-center px-4 py-3 border-b border-gray-200 hover:bg-gray-50 transition-colors\n        ${f?"bg-white":"bg-gray-25"}\n        ${g?"bg-blue-50 border-blue-200":""}\n      `,role:"row","aria-selected":g,children:[j.jsx("div",{className:"flex-shrink-0 w-12",children:j.jsx(us,{checked:g,onChange:y,"aria-label":`Seleccionar informe ${h.titulo}`})}),j.jsxs("div",{className:"flex-1 min-w-0 px-3",children:[j.jsxs("div",{className:"flex items-center space-x-2",children:[j.jsx(k,{className:"w-4 h-4 text-gray-400 flex-shrink-0","aria-hidden":"true"}),j.jsx("span",{className:"font-medium text-gray-900 truncate",children:(null==(t=h.resultados)?void 0:t.nombre_paciente)||"Paciente no especificado"})]}),j.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[j.jsx(R,{className:"w-3 h-3 text-gray-400 flex-shrink-0","aria-hidden":"true"}),j.jsx("span",{className:"text-sm text-gray-600 truncate",children:h.titulo})]})]}),j.jsx("div",{className:"flex-shrink-0 w-40 px-3",children:j.jsxs("div",{className:"flex items-center space-x-2",children:[j.jsx(M,{className:"w-4 h-4 text-gray-400","aria-hidden":"true"}),j.jsxs("div",{className:"text-sm",children:[j.jsx("div",{className:"text-gray-900",children:cs(h.fecha_generacion)}),j.jsx("div",{className:"text-gray-500",children:ms(h.fecha_generacion)})]})]})}),j.jsx("div",{className:"flex-shrink-0 w-40 px-3",children:j.jsxs("div",{className:"flex items-center space-x-2",children:[j.jsx(M,{className:"w-4 h-4 text-gray-400","aria-hidden":"true"}),j.jsx("div",{className:"text-sm",children:j.jsx("div",{className:"text-gray-900",children:(null==(r=h.resultados)?void 0:r.fecha_evaluacion)?cs(h.resultados.fecha_evaluacion):"No especificada"})})]})}),j.jsx("div",{className:"flex-shrink-0 w-32 px-3",children:j.jsxs("div",{className:"flex items-center space-x-1",children:[j.jsx(ps,{content:"Ver informe",children:j.jsx(Ca,{variant:"ghost",size:"sm",onClick:v,className:"p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100","aria-label":`Ver informe de ${null==(n=h.resultados)?void 0:n.nombre_paciente}`,children:j.jsx(O,{className:"w-4 h-4"})})}),j.jsx(ps,{content:"Ver gráfico",children:j.jsx(Ca,{variant:"ghost",size:"sm",onClick:w,className:"p-2 text-green-600 hover:text-green-800 hover:bg-green-100","aria-label":`Ver gráfico de ${null==(i=h.resultados)?void 0:i.nombre_paciente}`,children:j.jsx(B,{className:"w-4 h-4"})})}),j.jsx(ps,{content:"Eliminar informe",children:j.jsx(Ca,{variant:"ghost",size:"sm",onClick:N,className:"p-2 text-red-600 hover:text-red-800 hover:bg-red-100","aria-label":`Eliminar informe de ${null==(o=h.resultados)?void 0:o.nombre_paciente}`,children:j.jsx($,{className:"w-4 h-4"})})})]})})]})});hs.displayName="InformeRow";const gs=b.memo(({selectedCount:e,totalCount:a,onSelectAll:s,onDeselectAll:t,allSelected:r,someSelected:n})=>{const i=b.useCallback(()=>{r||n?t():s()},[r,n,s,t]);return j.jsxs("div",{className:"flex items-center px-4 py-3 bg-gray-50 border-b border-gray-200 font-medium text-gray-700",role:"row",children:[j.jsx("div",{className:"flex-shrink-0 w-12",children:j.jsx(us,{checked:r,indeterminate:n&&!r,onChange:i,"aria-label":"Seleccionar todos los informes"})}),j.jsxs("div",{className:"flex-1 px-3",children:[j.jsx("span",{children:"Paciente / Título"}),e>0&&j.jsxs("span",{className:"ml-2 text-sm text-blue-600",children:["(",e," seleccionados)"]})]}),j.jsx("div",{className:"flex-shrink-0 w-40 px-3",children:j.jsx("span",{children:"Fecha Generación"})}),j.jsx("div",{className:"flex-shrink-0 w-40 px-3",children:j.jsx("span",{children:"Fecha Evaluación"})}),j.jsx("div",{className:"flex-shrink-0 w-32 px-3",children:j.jsx("span",{children:"Acciones"})})]})});gs.displayName="TableHeader";const bs=b.memo(({searchTerm:e,onClearSearch:a})=>j.jsxs("div",{className:"flex flex-col items-center justify-center py-12 px-4",children:[j.jsx(R,{className:"w-16 h-16 text-gray-300 mb-4"}),j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:e?"No se encontraron informes":"No hay informes generados"}),j.jsx("p",{className:"text-gray-600 text-center max-w-md",children:e?`No se encontraron informes que coincidan con "${e}"`:"Aún no se han generado informes. Los informes aparecerán aquí una vez que se generen."}),e&&a&&j.jsx(Ca,{onClick:a,variant:"outline",className:"mt-4",children:"Limpiar búsqueda"})]}));bs.displayName="EmptyState";const js=b.memo(({informes:e=[],selectedInformes:a=new Set,onToggleSelection:s,onSelectAll:t,onDeselectAll:r,onViewInforme:n,onDeleteInforme:i,onViewChart:o,isLoading:l=!1,searchTerm:c="",onClearSearch:d,height:m=600})=>{const{allSelected:u,someSelected:p,selectedCount:x}=b.useMemo(()=>{const s=a.size,t=e.length;return{allSelected:t>0&&s===t,someSelected:s>0&&s<t,selectedCount:s}},[a.size,e.length]),h=b.useMemo(()=>({informes:e,selectedInformes:a,onToggleSelection:s,onViewInforme:n,onDeleteInforme:i,onViewChart:o,isLoading:l}),[e,a,s,n,i,o,l]);return l||0!==e.length?j.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 overflow-hidden",role:"table","aria-label":"Tabla de informes generados",children:[j.jsx(gs,{selectedCount:x,totalCount:e.length,onSelectAll:t,onDeselectAll:r,allSelected:u,someSelected:p}),j.jsxs("div",{className:"relative",children:[j.jsx(L,{height:m-60,itemCount:l?10:e.length,itemSize:80,itemData:h,overscanCount:5,children:hs}),l&&j.jsx("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center",children:j.jsxs("div",{className:"flex items-center space-x-2",children:[j.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),j.jsx("span",{className:"text-gray-600",children:"Cargando informes..."})]})})]}),j.jsx("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-200 text-sm text-gray-600",children:l?j.jsx("span",{children:"Cargando..."}):j.jsxs("span",{children:["Mostrando ",e.length," informe",1!==e.length?"s":"",x>0&&j.jsxs("span",{className:"ml-2 text-blue-600",children:["• ",x," seleccionado",1!==x?"s":""]})]})})]}):j.jsxs("div",{className:"bg-white rounded-lg border border-gray-200",children:[j.jsx(gs,{selectedCount:x,totalCount:e.length,onSelectAll:t,onDeselectAll:r,allSelected:u,someSelected:p}),j.jsx(bs,{searchTerm:c,onClearSearch:d})]})});js.displayName="InformesTable";const fs=(e,a)=>{const[s,t]=b.useState(e);return b.useEffect(()=>{const s=setTimeout(()=>{t(e)},a);return()=>{clearTimeout(s)}},[e,a]),s},ys=({searchTerm:e,onSearchChange:a,onClearSearch:s,totalCount:t,selectedCount:r})=>j.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200 mb-6",children:j.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[j.jsx("div",{className:"flex-1 max-w-md",children:j.jsxs("div",{className:"relative",children:[j.jsx(Q,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),j.jsx("input",{type:"text",placeholder:"Buscar por paciente o título...",value:e,onChange:e=>a(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500","aria-label":"Buscar informes"}),e&&j.jsx("button",{onClick:s,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600","aria-label":"Limpiar búsqueda",children:"×"})]})}),j.jsxs("div",{className:"flex items-center gap-4",children:[j.jsxs("div",{className:"text-sm text-gray-600",children:[t," informe",1!==t?"s":"",r>0&&j.jsxs("span",{className:"ml-2 text-blue-600 font-medium",children:["• ",r," seleccionado",1!==r?"s":""]})]}),j.jsxs(Ca,{variant:"outline",size:"sm",className:"flex items-center gap-2",children:[j.jsx(U,{className:"w-4 h-4"}),"Filtros"]})]})]})}),vs=({selectedCount:e,onBulkDelete:a,onBulkExport:s,disabled:t})=>0===e?null:j.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:j.jsxs("div",{className:"flex items-center justify-between",children:[j.jsx("div",{className:"flex items-center gap-2",children:j.jsxs("span",{className:"text-sm font-medium text-blue-900",children:[e," informe",1!==e?"s":""," seleccionado",1!==e?"s":""]})}),j.jsxs("div",{className:"flex items-center gap-2",children:[j.jsxs(Ca,{variant:"outline",size:"sm",onClick:s,disabled:t,className:"flex items-center gap-2",children:[j.jsx(H,{className:"w-4 h-4"}),"Exportar"]}),j.jsxs(Ca,{variant:"destructive",size:"sm",onClick:a,disabled:t,className:"flex items-center gap-2",children:[j.jsx($,{className:"w-4 h-4"}),"Eliminar"]})]})]})}),Ns=()=>{const[e,a]=b.useState(""),[s,t]=b.useState(new Set),[r,n]=b.useState(1),i=fs(e,300),{informes:c,loading:d,error:u,totalCount:p,totalPages:x,refetch:h,deleteInforme:g,deleteBulkInformes:f}=(({itemsPerPage:e=10,autoRefresh:a=!1,refreshInterval:s=3e4}={})=>{const[t,r]=b.useState({informes:[],loading:!1,error:null,totalCount:0,currentPage:1,hasNextPage:!1,hasPrevPage:!1}),n=b.useRef(null),i=b.useRef(null),c=b.useRef(new Map),d=b.useCallback(e=>{r(a=>o(o({},a),e))},[]),p=b.useCallback((...a)=>m(null,[...a],function*(a=1,s={}){n.current&&n.current.abort(),n.current=new AbortController;const{signal:t}=n.current,r=`page-${a}-${e}`;if(c.current.has(r)&&!s.forceRefresh){const e=c.current.get(r);return void d(l(o({},e),{currentPage:a,loading:!1}))}try{d({loading:!0,error:null});const s=(a-1)*e,{data:n,error:i,count:p}=yield Ia.from("informes_generados").select("\n          id,\n          titulo,\n          descripcion,\n          fecha_generacion,\n          metadatos,\n          contenido,\n          pacientes:paciente_id (\n            id,\n            nombre,\n            apellido,\n            documento,\n            genero\n          )\n        ",{count:"exact"}).eq("tipo_informe","completo").eq("estado","generado").range(s,s+e-1).order("fecha_generacion",{ascending:!1}).abortSignal(t);if(i)throw i;const x={informes:yield Promise.all((n||[]).map(e=>m(null,null,function*(){var a;try{const{data:s,error:r}=yield Ia.from("resultados").select("\n                id,\n                puntaje_directo,\n                percentil,\n                errores,\n                tiempo_segundos,\n                concentracion,\n                created_at,\n                aptitudes:aptitud_id (\n                  codigo,\n                  nombre,\n                  descripcion\n                )\n              ").eq("paciente_id",null==(a=e.pacientes)?void 0:a.id).not("puntaje_directo","is",null).not("percentil","is",null).order("created_at",{ascending:!1}).limit(20).abortSignal(t);return l(o({},e),r?{resultados:[]}:{resultados:s||[]})}catch(u){if("AbortError"===u.name)throw u;return l(o({},e),{resultados:[]})}}))),totalCount:p||0,hasNextPage:s+e<(p||0),hasPrevPage:a>1,loading:!1,error:null};if(c.current.set(r,x),c.current.size>10){const e=c.current.keys().next().value;c.current.delete(e)}d(l(o({},x),{currentPage:a}))}catch(u){if("AbortError"===u.name)return;d({loading:!1,error:u.message||"Error al cargar los informes"})}}),[e,d]),x=b.useCallback(a=>{a>=1&&a<=Math.ceil(t.totalCount/e)&&p(a)},[p,t.totalCount,e]),h=b.useCallback(()=>{t.hasNextPage&&x(t.currentPage+1)},[x,t.hasNextPage,t.currentPage]),g=b.useCallback(()=>{t.hasPrevPage&&x(t.currentPage-1)},[x,t.hasPrevPage,t.currentPage]),j=b.useCallback(()=>{const a=`page-${t.currentPage}-${e}`;c.current.delete(a),p(t.currentPage,{forceRefresh:!0})},[p,t.currentPage,e]),f=b.useCallback(()=>{c.current.clear()},[]),y=b.useCallback(e=>m(null,null,function*(){try{d({loading:!0});const{error:a}=yield Ia.from("informes_generados").delete().eq("id",e);if(a)throw a;return f(),yield p(t.currentPage,{forceRefresh:!0}),{success:!0}}catch(u){return d({loading:!1,error:u.message||"Error al eliminar el informe"}),{success:!1,error:u.message}}}),[p,t.currentPage,f,d]),v=b.useCallback(e=>m(null,null,function*(){try{d({loading:!0});const{error:a}=yield Ia.from("informes_generados").delete().in("id",e);if(a)throw a;return f(),yield p(t.currentPage,{forceRefresh:!0}),{success:!0,deletedCount:e.length}}catch(u){return d({loading:!1,error:u.message||"Error al eliminar los informes"}),{success:!1,error:u.message}}}),[p,t.currentPage,f,d]);return b.useEffect(()=>{if(a&&s>0)return i.current=setInterval(()=>{j()},s),()=>{i.current&&clearInterval(i.current)}},[a,s,j]),b.useEffect(()=>()=>{n.current&&n.current.abort(),i.current&&clearInterval(i.current)},[]),b.useEffect(()=>{p(1)},[p]),{informes:t.informes,loading:t.loading,error:t.error,currentPage:t.currentPage,totalCount:t.totalCount,totalPages:Math.ceil(t.totalCount/e),hasNextPage:t.hasNextPage,hasPrevPage:t.hasPrevPage,fetchInformes:p,goToPage:x,nextPage:h,prevPage:g,refresh:j,clearCache:f,deleteInforme:y,bulkDeleteInformes:v}})({}),y=s.size;c.length>0&&c.length,y>0&&c.length;const v=b.useCallback(e=>{a(e),n(1)},[]),N=b.useCallback(()=>{a(""),n(1)},[]),w=b.useCallback(e=>{t(a=>{const s=new Set(a);return s.has(e)?s.delete(e):s.add(e),s})},[]),C=b.useCallback(()=>{t(new Set(c.map(e=>e.id)))},[c]),A=b.useCallback(()=>{t(new Set)},[]),E=b.useCallback(e=>{V.success(`Abriendo informe: ${e.titulo}`)},[]),S=b.useCallback(e=>m(null,null,function*(){if(window.confirm("¿Está seguro de que desea eliminar este informe?"))try{yield g(e),t(a=>{const s=new Set(a);return s.delete(e),s}),V.success("Informe eliminado correctamente")}catch(a){V.error("Error al eliminar el informe")}}),[g]),T=b.useCallback(e=>{V.success(`Abriendo gráfico: ${e.titulo}`)},[]),q=b.useCallback(()=>m(null,null,function*(){const e=y;if(window.confirm(`¿Está seguro de que desea eliminar ${e} informe${1!==e?"s":""}?`))try{yield f(Array.from(s)),t(new Set),V.success(`${e} informe${1!==e?"s":""} eliminado${1!==e?"s":""} correctamente`)}catch(a){V.error("Error al eliminar los informes")}}),[y,s,f]),z=b.useCallback(()=>{V.success(`Exportando ${y} informe${1!==y?"s":""}...`)},[s,y]);return u?j.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 text-center",children:[j.jsx("h3",{className:"text-lg font-medium text-red-900 mb-2",children:"Error al cargar informes"}),j.jsx("p",{className:"text-red-700 mb-4",children:u.message}),j.jsx(Ca,{onClick:h,variant:"outline",children:"Reintentar"})]}):j.jsx(xa,{children:j.jsxs("div",{className:"space-y-6",children:[j.jsxs("div",{className:"flex items-center justify-between",children:[j.jsxs("div",{children:[j.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Informes Generados"}),j.jsx("p",{className:"text-gray-600 mt-1",children:"Gestiona y visualiza los informes de evaluación generados"})]}),j.jsxs(Ca,{className:"flex items-center gap-2",children:[j.jsx(F,{className:"w-4 h-4"}),"Nuevo Informe"]})]}),j.jsx(ys,{searchTerm:e,onSearchChange:v,onClearSearch:N,totalCount:p,selectedCount:y}),j.jsx(vs,{selectedCount:y,onBulkDelete:q,onBulkExport:z,disabled:d}),j.jsx(js,{informes:c,selectedInformes:s,onToggleSelection:w,onSelectAll:C,onDeselectAll:A,onViewInforme:E,onDeleteInforme:S,onViewChart:T,isLoading:d,searchTerm:i,onClearSearch:N,height:600}),x>1&&j.jsxs("div",{className:"flex items-center justify-between bg-white p-4 rounded-lg border border-gray-200",children:[j.jsxs("div",{className:"text-sm text-gray-600",children:["Página ",r," de ",x]}),j.jsxs("div",{className:"flex items-center gap-2",children:[j.jsx(Ca,{variant:"outline",size:"sm",onClick:()=>n(e=>Math.max(1,e-1)),disabled:1===r||d,children:"Anterior"}),j.jsx(Ca,{variant:"outline",size:"sm",onClick:()=>n(e=>Math.min(x,e+1)),disabled:r===x||d,children:"Siguiente"})]})]})]})})},ws=()=>{const e=f(),{resultados:a,aplicacionId:s,testCompletado:t,cargarResultados:r,cargando:n}=b.useContext(os),[i,o]=b.useState(!0),[l,c]=b.useState(null),[d,u]=b.useState({fortalezas:[],areas_mejora:[],recomendaciones:[]});b.useEffect(()=>{m(null,null,function*(){try{if(o(!0),!a&&s&&(yield r(s)),s){const e={fecha_evaluacion:"2025-06-12T10:00:00Z",candidatos:{id_candidato:"367894512",nombre:"Camila",apellido:"Vargas Vargas",sexo:"Femenino"}};e&&e.candidatos&&c({nombreCompleto:`${e.candidatos.nombre} ${e.candidatos.apellido}`,id_paciente:e.candidatos.id_candidato,sexo:e.candidatos.sexo,fecha_evaluacion:new Date(e.fecha_evaluacion).toLocaleDateString("es-ES",{day:"numeric",month:"long",year:"numeric"})})}a&&p(a)}catch(e){}finally{o(!1)}})},[s,a,r]);const p=e=>{const a=[],s=[],t=[];Object.entries(e).forEach(([e,t])=>{const{codigo:r,nombre:n,puntuacionCentil:i,interpretacion:o}=t;i>=70?a.push({codigo:r,nombre:n,interpretacion:`${o} (PC: ${i})`,descripcion:x(r,!0)}):i<=30&&s.push({codigo:r,nombre:n,interpretacion:`${o} (PC: ${i})`,descripcion:x(r,!1)})}),s.forEach(e=>{t.push({codigo:e.codigo,recomendacion:h(e.codigo)})}),u({fortalezas:a,areas_mejora:s,recomendaciones:t})},x=(e,a)=>{const s={V:{fortaleza:"Alta capacidad para comprender, utilizar y analizar el lenguaje escrito y hablado.",debilidad:"Dificultades para comprender conceptos expresados a través de palabras."},E:{fortaleza:"Excelente capacidad para visualizar y manipular mentalmente formas y patrones espaciales.",debilidad:"Dificultades para comprender relaciones espaciales y visualizar objetos en diferentes dimensiones."},A:{fortaleza:"Gran capacidad para mantener el foco en tareas específicas, detectando detalles con precisión.",debilidad:"Dificultad para mantener la concentración y detectar detalles específicos en tareas que requieren atención sostenida."},R:{fortaleza:"Destacada habilidad para identificar patrones lógicos y resolver problemas mediante el razonamiento.",debilidad:"Dificultades para identificar reglas lógicas y establecer inferencias en situaciones nuevas."},N:{fortaleza:"Excelente capacidad para comprender y manipular conceptos numéricos y resolver problemas matemáticos.",debilidad:"Dificultades en el manejo de conceptos numéricos y operaciones matemáticas básicas."},M:{fortaleza:"Buena comprensión de principios físicos y mecánicos básicos aplicados a situaciones cotidianas.",debilidad:"Dificultades para comprender el funcionamiento de dispositivos mecánicos y principios físicos básicos."},O:{fortaleza:"Excelente dominio de las reglas ortográficas y alta precisión en la escritura.",debilidad:"Dificultades con las reglas ortográficas y tendencia a cometer errores en la escritura."}};return s[e]?a?s[e].fortaleza:s[e].debilidad:"No hay descripción disponible."},h=e=>({V:"Fomentar la lectura diaria y realizar actividades que enriquezcan el vocabulario como juegos de palabras, debates y redacción.",E:"Practicar con rompecabezas, ejercicios de rotación mental, dibujo técnico y actividades que involucren navegación espacial.",A:"Realizar ejercicios de mindfulness, practicar tareas que requieran concentración por períodos cortos e ir aumentando gradualmente el tiempo.",R:"Resolver acertijos lógicos, participar en juegos de estrategia y analizar problemas complejos dividiéndolos en partes más sencillas.",N:"Practicar operaciones matemáticas diariamente, resolver problemas aplicados a la vida real y utilizar juegos que involucren cálculos.",M:"Construir modelos, experimentar con el funcionamiento de objetos cotidianos y estudiar los principios básicos de la física.",O:"Realizar ejercicios de dictado, revisión de textos y practicar la escritura consciente prestando atención a las reglas ortográficas."}[e]||"No hay recomendaciones específicas disponibles.");return i||n?j.jsx("div",{className:"flex items-center justify-center h-screen",children:j.jsx(pa,{fullScreen:!0,message:"Cargando resultados..."})}):a&&0!==Object.keys(a).length?j.jsx("div",{className:"min-h-screen bg-gray-100 py-8 px-4 sm:px-6 lg:px-8",children:j.jsxs("div",{className:"max-w-5xl mx-auto",children:[j.jsx("h1",{className:"text-3xl font-bold text-gray-800 mb-6",children:"Resultados Detallados"}),l&&j.jsxs("div",{className:"bg-white shadow-lg rounded-lg p-6 mb-8 flex items-center justify-between",children:[j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"mr-4",children:"Femenino"===l.sexo?j.jsx(G,{className:"text-pink-500 text-5xl"}):"Masculino"===l.sexo?j.jsx(J,{className:"text-blue-500 text-5xl"}):j.jsx(k,{className:"text-gray-500 text-5xl"})}),j.jsxs("div",{children:[j.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:l.nombreCompleto}),j.jsxs("p",{className:"text-sm text-gray-600",children:["ID: ",l.id_paciente]})]})]}),j.jsxs("div",{className:"text-right",children:[j.jsx("p",{className:"text-sm text-gray-700",children:"Fecha de Evaluación:"}),j.jsx("p",{className:"text-md font-semibold text-gray-800",children:l.fecha_evaluacion})]})]}),a&&Object.keys(a).length>0?j.jsxs("div",{className:"bg-white shadow-lg rounded-lg overflow-hidden mb-8",children:[j.jsx("div",{className:"overflow-x-auto",children:j.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[j.jsx("thead",{className:"bg-gray-50",children:j.jsxs("tr",{children:[j.jsx("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Test"}),j.jsx("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Puntaje PD"}),j.jsx("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Puntuación T"}),j.jsx("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Errores"}),j.jsx("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tiempo"})]})}),j.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:Object.entries(a).map(([e,a])=>j.jsxs("tr",{children:[j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:j.jsx("div",{className:"text-sm font-medium text-gray-900",children:a.nombre||"N/A"})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:j.jsx("div",{className:"text-sm text-gray-900",children:void 0!==a.puntuacionDirecta?a.puntuacionDirecta:"N/A"})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:j.jsx("div",{className:"text-sm text-gray-900",children:void 0!==a.puntuacionCentil?a.puntuacionCentil:"N/A"})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:j.jsx("div",{className:"text-sm text-gray-900",children:void 0!==a.errores?a.errores:"N/A"})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:j.jsx("div",{className:"text-sm text-gray-900",children:a.tiempo||"N/A"})})]},e))})]})}),j.jsxs("div",{className:"p-4 text-xs text-gray-500 bg-gray-50 border-t",children:[j.jsxs("p",{children:[j.jsx("span",{className:"font-medium",children:"Puntaje PD:"})," Puntuación Directa - Número de respuestas correctas."]}),j.jsxs("p",{children:[j.jsx("span",{className:"font-medium",children:"Puntuación T:"})," Puntuación Transformada (ej. Percentil) - Posición relativa respecto a la población de referencia."]})]})]}):j.jsx("div",{className:"bg-white shadow-lg rounded-lg p-6 mb-8 text-center",children:j.jsx("p",{className:"text-gray-600",children:"No hay resultados de pruebas para mostrar para este paciente."})}),(d.fortalezas.length>0||d.areas_mejora.length>0||d.recomendaciones.length>0)&&j.jsxs("div",{className:"bg-white shadow-lg rounded-lg mb-8",children:[j.jsx("h2",{className:"text-xl font-semibold p-4 bg-gray-50 border-b text-gray-800",children:"Informe Cualitativo"}),j.jsxs("div",{className:"p-6",children:[d.fortalezas.length>0&&j.jsxs("div",{className:"mb-6",children:[j.jsx("h3",{className:"text-lg font-medium text-green-700 mb-2",children:"Fortalezas"}),j.jsx("div",{className:"space-y-3",children:d.fortalezas.map((e,a)=>j.jsxs("div",{className:"bg-green-50 p-3 rounded-md",children:[j.jsxs("div",{className:"font-semibold text-green-800",children:[e.nombre,": ",e.interpretacion]}),j.jsx("p",{className:"text-sm text-green-700 mt-1",children:e.descripcion})]},a))})]}),d.areas_mejora.length>0&&j.jsxs("div",{className:"mb-6",children:[j.jsx("h3",{className:"text-lg font-medium text-red-700 mb-2",children:"Áreas de Mejora"}),j.jsx("div",{className:"space-y-3",children:d.areas_mejora.map((e,a)=>j.jsxs("div",{className:"bg-red-50 p-3 rounded-md",children:[j.jsxs("div",{className:"font-semibold text-red-800",children:[e.nombre,": ",e.interpretacion]}),j.jsx("p",{className:"text-sm text-red-700 mt-1",children:e.descripcion})]},a))})]}),d.recomendaciones.length>0&&j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-blue-700 mb-2",children:"Recomendaciones"}),j.jsx("div",{className:"space-y-3",children:d.recomendaciones.map((e,s)=>{var t;return j.jsxs("div",{className:"bg-blue-50 p-3 rounded-md",children:[j.jsxs("div",{className:"font-semibold text-blue-800",children:[e.codigo," - ",null==(t=a[Object.keys(a).find(s=>a[s].codigo===e.codigo)])?void 0:t.nombre]}),j.jsx("p",{className:"text-sm text-blue-700 mt-1",children:e.recomendacion})]},s)})})]})]})]}),j.jsxs("div",{className:"bg-white shadow-lg rounded-lg mb-8",children:[j.jsx("h2",{className:"text-xl font-semibold p-4 bg-gray-50 border-b text-gray-800",children:"Test de Conexión"}),j.jsx("div",{className:"p-6"})]}),j.jsxs("div",{className:"bg-white shadow-lg rounded-lg mb-8",children:[j.jsx("h2",{className:"text-xl font-semibold p-4 bg-gray-50 border-b text-gray-800",children:"Informes Generados"}),j.jsx("div",{className:"p-6",children:j.jsx(Ns,{})})]}),j.jsxs("div",{className:"flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 mt-8",children:[j.jsx("button",{className:"px-6 py-3 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition duration-150",onClick:()=>e("/test"),children:"Volver"}),j.jsxs("div",{className:"flex space-x-3",children:[j.jsx("button",{className:"px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition duration-150",onClick:()=>window.print(),children:"Imprimir Resultados"}),j.jsx("button",{className:"px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition duration-150",onClick:()=>alert("Función de exportar a PDF en desarrollo."),children:"Exportar a PDF"})]})]})]})}):j.jsx("div",{className:"flex items-center justify-center h-screen bg-blue-50",children:j.jsx("div",{className:"bg-white p-8 rounded-lg shadow-md max-w-md",children:j.jsxs("div",{className:"text-center",children:[j.jsx("svg",{className:"mx-auto h-16 w-16 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:j.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),j.jsx("h2",{className:"mt-4 text-xl font-bold text-gray-800",children:"No hay resultados disponibles"}),j.jsx("p",{className:"mt-2 text-gray-600",children:"No se han encontrado resultados para mostrar. Es posible que aún no hayas completado el test o que haya ocurrido un error."}),j.jsx("div",{className:"mt-6",children:j.jsx("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",onClick:()=>e("/test"),children:"Volver a Tests"})})]})})})},Cs=()=>j.jsxs(W,{children:[j.jsx(Y,{path:"/instructions/:testId",element:j.jsx(Sa,{})}),j.jsx(Y,{path:"/verbal",element:j.jsx($a,{})}),j.jsx(Y,{path:"/ortografia",element:j.jsx(Va,{})}),j.jsx(Y,{path:"/razonamiento",element:j.jsx(Ha,{})}),j.jsx(Y,{path:"/atencion",element:j.jsx(Ja,{})}),j.jsx(Y,{path:"/espacial",element:j.jsx(Ya,{})}),j.jsx(Y,{path:"/mecanico",element:j.jsx(Xa,{})}),j.jsx(Y,{path:"/numerico",element:j.jsx(ss,{})}),j.jsx(Y,{path:"/results/:resultId",element:j.jsx(ns,{})}),j.jsx(Y,{path:"/resultados/:resultId",element:j.jsx(ws,{})}),j.jsx(Y,{path:"/",element:j.jsx(S,{to:"/student/tests",replace:!0})})]}),As=()=>j.jsx("div",{className:"flex justify-center items-center min-h-screen",children:j.jsxs("div",{className:"text-center",children:[j.jsx(Z,{className:"animate-spin text-blue-600 mx-auto mb-4 text-4xl"}),j.jsx("p",{className:"text-gray-600 font-medium",children:"Cargando..."})]})}),Es=e=>a=>j.jsx(xa,{children:j.jsx(e,o({},a))}),Ss=e=>a=>j.jsx(xa,{children:j.jsx(b.Suspense,{fallback:j.jsx(As,{}),children:j.jsx(e,o({},a))})}),Ts=Ss(b.lazy(()=>z(()=>import("./Dashboard-CORJVBNr.js"),__vite__mapDeps([6,1,2,7,3,4,5])))),qs=Ss(b.lazy(()=>z(()=>import("./Profile-DQTcor2G.js"),__vite__mapDeps([8,1,2])))),zs=Ss(b.lazy(()=>z(()=>import("./Settings-BwazZz_F.js"),__vite__mapDeps([9,1,2])))),_s=Ss(b.lazy(()=>z(()=>import("./Home-DdavNFCp.js"),__vite__mapDeps([10,1,2])))),Ps=Ss(b.lazy(()=>z(()=>import("./Help-Brqj9_nb.js"),__vite__mapDeps([11,1,2,12])))),Ds=Ss(b.lazy(()=>z(()=>import("./ConfiguracionTemp-BKQHGnpu.js"),__vite__mapDeps([13,1,2,12,14,15])))),Is=Es(b.lazy(()=>z(()=>import("./Candidates-1amhvm6H.js"),__vite__mapDeps([16,1,2,17])))),ks=Es(b.lazy(()=>z(()=>import("./VerbalInfo-TG6K1YOs.js"),__vite__mapDeps([18,1,2])))),Rs=Ss(b.lazy(()=>z(()=>import("./Users-BPA-1jvj.js"),__vite__mapDeps([19,1,2])))),Ms=Ss(b.lazy(()=>z(()=>import("./Institutions-DQe7h0iW.js"),__vite__mapDeps([20,1,2])))),Os=Ss(b.lazy(()=>z(()=>import("./Reports-BXeRIjfA.js"),__vite__mapDeps([21,1,2,0,3,4,5,12,22])))),Bs=Ss(b.lazy(()=>z(()=>import("./Patients-CcGkFcaD.js"),__vite__mapDeps([23,1,2,12])))),$s=Ss(b.lazy(()=>z(()=>import("./Administration-Bg2xJkhl.js"),__vite__mapDeps([24,1,2,12])))),Ls=Ss(b.lazy(()=>z(()=>import("./TestPage-DLZQfqPn.js"),__vite__mapDeps([25,1,2,7])))),Vs=Ss(b.lazy(()=>z(()=>import("./CompleteReport-CuUa_A2s.js"),__vite__mapDeps([26,1,2,27])))),Fs=Ss(b.lazy(()=>z(()=>import("./SavedReports-D6OwDcM_.js"),__vite__mapDeps([28,1,2])))),Qs=Ss(b.lazy(()=>z(()=>import("./ViewSavedReport-BhOcD-SZ.js"),__vite__mapDeps([29,1,2,27])))),Us=Ss(b.lazy(()=>z(()=>import("./PinAssignmentPanel-uABOWMMP.js"),__vite__mapDeps([30,1,2,31])))),Hs=Ss(b.lazy(()=>z(()=>import("./PinUsageReports-BsptD2em.js"),__vite__mapDeps([32,1,2,33,4,34,35,36,15,14])))),Gs=Ss(b.lazy(()=>z(()=>import("./PinRechargeManagement-BjcgmkTM.js"),__vite__mapDeps([37,1,2])))),Js=Ss(b.lazy(()=>z(()=>import("./ReportGenerationDemo-BtVTmKZt.js"),__vite__mapDeps([38,1,2,34,35,4])))),Ws=Ss(b.lazy(()=>z(()=>import("./Students-ZoQ4wGME.js"),__vite__mapDeps([39,1,2])))),Ys=Ss(b.lazy(()=>z(()=>import("./Tests-dDef3CXv.js"),__vite__mapDeps([40,1,2])))),Zs=Ss(b.lazy(()=>z(()=>import("./Reports-DFrD-ZUT.js"),__vite__mapDeps([41,1,2])))),Xs=Ss(b.lazy(()=>z(()=>Promise.resolve().then(()=>jt),void 0))),Ks=Ss(b.lazy(()=>z(()=>import("./Tests-dTtTTLPI.js"),__vite__mapDeps([42,1,2,43,44])))),et=Ss(b.lazy(()=>z(()=>import("./Results-B131fvQB.js"),__vite__mapDeps([45,1,2,17,12])))),at=Ss(b.lazy(()=>z(()=>import("./Patients-27jxQMcc.js"),__vite__mapDeps([46,1,2,47])))),st=Ss(b.lazy(()=>z(()=>import("./Questionnaire-BLmNuSfo.js"),__vite__mapDeps([48,1,2,43,44,12])))),tt=Ss(b.lazy(()=>z(()=>import("./ChangePassword-CHe3wYuj.js"),__vite__mapDeps([49,1,2,50])))),rt=Ss(b.lazy(()=>z(()=>import("./InformePaciente-Cj00uQnj.js"),__vite__mapDeps([51,1,2])))),nt=Ss(b.lazy(()=>z(()=>Promise.resolve().then(()=>Ta),void 0))),it=Ss(b.lazy(()=>z(()=>Promise.resolve().then(()=>La),void 0))),ot=Ss(b.lazy(()=>z(()=>Promise.resolve().then(()=>Za),void 0))),lt=Ss(b.lazy(()=>z(()=>Promise.resolve().then(()=>Wa),void 0))),ct=Ss(b.lazy(()=>z(()=>Promise.resolve().then(()=>Ga),void 0))),dt=Ss(b.lazy(()=>z(()=>Promise.resolve().then(()=>ts),void 0))),mt=Ss(b.lazy(()=>z(()=>Promise.resolve().then(()=>Ka),void 0))),ut=Ss(b.lazy(()=>z(()=>Promise.resolve().then(()=>Fa),void 0))),pt=Ss(b.lazy(()=>z(()=>Promise.resolve().then(()=>is),void 0))),xt=Ss(b.lazy(()=>z(()=>import("./BasicLogin-CdU8_yxW.js"),__vite__mapDeps([52,1,2,50])))),ht=()=>{const e=w();return b.useEffect(()=>{},[e.pathname]),j.jsx(xa,{children:j.jsx(b.Suspense,{fallback:j.jsx(As,{}),children:j.jsxs(W,{children:[j.jsx(Y,{path:"/",element:j.jsx(S,{to:"/login",replace:!0})}),j.jsx(Y,{path:"/login",element:j.jsx(xt,{})}),j.jsx(Y,{path:"/register",element:j.jsx(S,{to:"/login",replace:!0})}),j.jsx(Y,{path:"/auth/troubleshooting",element:j.jsx(S,{to:"/login",replace:!0})}),j.jsx(Y,{path:"/auth",element:j.jsx(S,{to:"/login",replace:!0})}),j.jsx(Y,{path:"/force-admin",element:j.jsx(S,{to:"/admin/administration",replace:!0})}),j.jsx(Y,{path:"/info/verbal",element:j.jsx(ks,{})}),j.jsx(Y,{path:"/test/instructions/:testId",element:j.jsx(nt,{})}),j.jsx(Y,{path:"/test/verbal",element:j.jsx(it,{})}),j.jsx(Y,{path:"/test/espacial",element:j.jsx(ot,{})}),j.jsx(Y,{path:"/test/atencion",element:j.jsx(lt,{})}),j.jsx(Y,{path:"/test/razonamiento",element:j.jsx(ct,{})}),j.jsx(Y,{path:"/test/numerico",element:j.jsx(dt,{})}),j.jsx(Y,{path:"/test/mecanico",element:j.jsx(mt,{})}),j.jsx(Y,{path:"/test/ortografia",element:j.jsx(ut,{})}),j.jsx(Y,{path:"/test/results/:applicationId",element:j.jsx(pt,{})}),j.jsx(Y,{path:"/test/*",element:j.jsx(Cs,{})}),j.jsxs(Y,{element:j.jsx(ua,{}),children:[j.jsx(Y,{path:"/dashboard",element:j.jsx(S,{to:"/home",replace:!0})}),j.jsx(Y,{path:"/profile",element:j.jsx(qs,{})}),j.jsx(Y,{path:"/settings",element:j.jsx(zs,{})}),j.jsx(Y,{path:"/home",element:j.jsx(_s,{})}),j.jsx(Y,{path:"/help",element:j.jsx(Ps,{})}),j.jsx(Y,{path:"/configuracion",element:j.jsx(ha,{children:j.jsx(Ds,{})})}),j.jsxs(Y,{path:"/admin",children:[j.jsx(Y,{index:!0,element:j.jsx(ha,{children:j.jsx($s,{})})}),j.jsx(Y,{path:"dashboard",element:j.jsx(ha,{children:j.jsx(Ts,{})})}),j.jsx(Y,{path:"users",element:j.jsx(ha,{children:j.jsx(Rs,{})})}),j.jsx(Y,{path:"institutions",element:j.jsx(ha,{children:j.jsx(Ms,{})})}),j.jsx(Y,{path:"reports",element:j.jsx(ha,{children:j.jsx(Os,{})})}),j.jsx(Y,{path:"patients",element:j.jsx(ha,{children:j.jsx(Bs,{})})}),j.jsx(Y,{path:"administration",element:j.jsx(ha,{children:j.jsx($s,{})})}),j.jsx(Y,{path:"configuracion",element:j.jsx(ha,{children:j.jsx(Ds,{})})}),j.jsx(Y,{path:"tests",element:j.jsx(ha,{children:j.jsx(Ls,{})})}),j.jsx(Y,{path:"pines",element:j.jsx(ha,{children:j.jsx(Us,{})})}),j.jsx(Y,{path:"pin-recharge",element:j.jsx(ha,{children:j.jsx(Gs,{})})}),j.jsx(Y,{path:"informe-completo/:patientId",element:j.jsx(ha,{children:j.jsx(Vs,{})})}),j.jsx(Y,{path:"informes-guardados",element:j.jsx(ha,{children:j.jsx(Fs,{})})}),j.jsx(Y,{path:"informe-guardado/:reportId",element:j.jsx(ha,{children:j.jsx(Qs,{})})})]}),j.jsxs(Y,{path:"/professional",children:[j.jsx(Y,{index:!0,element:j.jsx(Ts,{})}),j.jsx(Y,{path:"dashboard",element:j.jsx(Ts,{})}),j.jsx(Y,{path:"students",element:j.jsx(Ws,{})}),j.jsx(Y,{path:"tests",element:j.jsx(Ys,{})}),j.jsx(Y,{path:"reports",element:j.jsx(Zs,{})}),j.jsx(Y,{path:"candidates",element:j.jsx(Is,{})}),j.jsx(Y,{path:"patients",element:j.jsx(Xs,{})}),j.jsx(Y,{path:"pin-usage",element:j.jsx(Hs,{})}),j.jsx(Y,{path:"report-demo",element:j.jsx(Js,{})})]}),j.jsxs(Y,{path:"/student",children:[j.jsx(Y,{index:!0,element:j.jsx(Ts,{})}),j.jsx(Y,{path:"dashboard",element:j.jsx(Ts,{})}),j.jsx(Y,{path:"tests",element:j.jsx(Ks,{})}),j.jsx(Y,{path:"questionnaire",element:j.jsx(st,{})}),j.jsx(Y,{path:"change-password",element:j.jsx(tt,{})}),j.jsx(Y,{path:"results",element:j.jsx(et,{})}),j.jsx(Y,{path:"informe/:pacienteId",element:j.jsx(rt,{})}),j.jsx(Y,{path:"patients",element:j.jsx(at,{})})]})]}),j.jsx(Y,{path:"*",element:j.jsx(S,{to:"/home",replace:!0})})]})})})},gt=()=>j.jsx(X,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:j.jsx(fa,{children:j.jsx(ht,{})})});function bt(){return b.useEffect(()=>{const e=localStorage.getItem("userTheme");("dark"===e||"system"===e&&window.matchMedia("(prefers-color-scheme: dark)").matches)&&document.body.classList.add("dark-mode");const a=window.matchMedia("(prefers-color-scheme: dark)"),s=e=>{"system"===localStorage.getItem("userTheme")&&(e.matches?document.body.classList.add("dark-mode"):document.body.classList.remove("dark-mode"))};return a.addEventListener("change",s),()=>{a.removeEventListener("change",s)}},[]),j.jsxs("div",{className:"app",children:[j.jsx(N,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!0,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0}),j.jsx(gt,{})]})}K.createRoot(document.getElementById("root")).render(j.jsx(C.StrictMode,{children:j.jsx(ee,{store:na,children:j.jsx(oa,{children:j.jsx(bt,{})})})}));const jt=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));export{Ca as B,ya as C,rs as P,Oa as S,Na as a,va as b,Ma as c,ls as d,wa as e,Ra as f,Qa as g,ja as h,fs as i,ds as j,cs as k,Ia as s,la as u};
