-- Script para arreglar la tabla usuarios agregando la columna updated_at faltante
-- EJECUTAR ESTE SCRIPT EN EL EDITOR SQL DE SUPABASE

-- =====================================================
-- VERIFICAR ESTRUCTURA ACTUAL DE LA TABLA
-- =====================================================

-- Mostrar columnas actuales de la tabla usuarios
SELECT 
  'COLUMNAS ACTUALES EN LA TABLA USUARIOS:' as info,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'usuarios'
ORDER BY ordinal_position;

-- =====================================================
-- AGREGAR COLUMNA UPDATED_AT SI NO EXISTE
-- =====================================================

DO $$
DECLARE
  column_exists BOOLEAN;
BEGIN
  -- Verificar si la columna updated_at existe
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'usuarios' 
    AND column_name = 'updated_at'
  ) INTO column_exists;
  
  IF NOT column_exists THEN
    -- Agregar la columna updated_at
    ALTER TABLE public.usuarios ADD COLUMN updated_at TIMESTAMPTZ DEFAULT now();
    RAISE NOTICE 'Columna updated_at agregada a la tabla usuarios';
    
    -- Actualizar registros existentes con la fecha actual
    UPDATE public.usuarios SET updated_at = now() WHERE updated_at IS NULL;
    RAISE NOTICE 'Registros existentes actualizados con fecha actual';
  ELSE
    RAISE NOTICE 'La columna updated_at ya existe en la tabla usuarios';
  END IF;
END $$;

-- =====================================================
-- CREAR FUNCIÓN PARA ACTUALIZAR UPDATED_AT AUTOMÁTICAMENTE
-- =====================================================

-- Crear o reemplazar la función handle_updated_at
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CREAR TRIGGER PARA ACTUALIZAR UPDATED_AT
-- =====================================================

-- Verificar si el trigger existe y crearlo si no
DO $$
DECLARE
  trigger_exists BOOLEAN;
BEGIN
  -- Verificar si el trigger existe
  SELECT EXISTS (
    SELECT FROM pg_trigger
    WHERE tgname = 'on_usuarios_update'
    AND tgrelid = 'public.usuarios'::regclass
  ) INTO trigger_exists;
  
  -- Si el trigger no existe, crearlo
  IF NOT trigger_exists THEN
    -- Crear el trigger
    CREATE TRIGGER on_usuarios_update
    BEFORE UPDATE ON public.usuarios
    FOR EACH ROW
    EXECUTE PROCEDURE public.handle_updated_at();
    
    RAISE NOTICE 'Trigger on_usuarios_update creado correctamente';
  ELSE
    RAISE NOTICE 'El trigger on_usuarios_update ya existe';
  END IF;
END $$;

-- =====================================================
-- VERIFICAR ESTRUCTURA FINAL
-- =====================================================

-- Mostrar columnas después de los cambios
SELECT 
  'COLUMNAS DESPUÉS DE LOS CAMBIOS:' as info,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'usuarios'
ORDER BY ordinal_position;

-- =====================================================
-- VERIFICAR TRIGGERS
-- =====================================================

-- Mostrar triggers de la tabla usuarios
SELECT 
  'TRIGGERS EN LA TABLA USUARIOS:' as info,
  trigger_name,
  event_manipulation,
  action_timing,
  action_statement
FROM information_schema.triggers 
WHERE event_object_schema = 'public' 
  AND event_object_table = 'usuarios';

-- =====================================================
-- PROBAR FUNCIONAMIENTO
-- =====================================================

-- Función de prueba para verificar que el trigger funciona
CREATE OR REPLACE FUNCTION test_updated_at_trigger()
RETURNS TEXT AS $$
DECLARE
  test_user_id UUID;
  old_updated_at TIMESTAMPTZ;
  new_updated_at TIMESTAMPTZ;
BEGIN
  -- Obtener un usuario existente para probar
  SELECT id, updated_at INTO test_user_id, old_updated_at
  FROM public.usuarios 
  LIMIT 1;
  
  IF test_user_id IS NULL THEN
    RETURN 'No hay usuarios para probar el trigger';
  END IF;
  
  -- Esperar un momento para asegurar diferencia de tiempo
  PERFORM pg_sleep(1);
  
  -- Actualizar el usuario
  UPDATE public.usuarios 
  SET nombre = nombre 
  WHERE id = test_user_id;
  
  -- Obtener la nueva fecha
  SELECT updated_at INTO new_updated_at
  FROM public.usuarios 
  WHERE id = test_user_id;
  
  -- Verificar que la fecha cambió
  IF new_updated_at > old_updated_at THEN
    RETURN 'ÉXITO: El trigger updated_at funciona correctamente';
  ELSE
    RETURN 'ERROR: El trigger updated_at no está funcionando';
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Ejecutar la prueba
SELECT test_updated_at_trigger();

-- Limpiar función de prueba
DROP FUNCTION IF EXISTS test_updated_at_trigger();

-- =====================================================
-- RESUMEN FINAL
-- =====================================================

SELECT 
  'RESUMEN FINAL:' as info,
  'Tabla usuarios reparada exitosamente' as status,
  'Columna updated_at agregada' as columna,
  'Trigger automático configurado' as trigger_status,
  'Sistema listo para usar' as resultado;
