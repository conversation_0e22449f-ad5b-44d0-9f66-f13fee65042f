/**
 * @file interpretacionesOficialesConsolidadas.js
 * @description Interpretaciones oficiales consolidadas del BAT-7
 * Copiadas a pie de letra del documento oficial "Interpretacion de aptitudes y Generalidaes.txt"
 * 
 * IMPORTANTE: Estos textos son EXACTOS del documento oficial, sin modificaciones ni resúmenes
 */

import { INTERPRETACIONES_OFICIALES } from './interpretacionesOficiales.js';
import { INTERPRETACIONES_OFICIALES_PARTE2 } from './interpretacionesOficiales_parte2.js';
import { INTERPRETACIONES_OFICIALES_PARTE3 } from './interpretacionesOficiales_parte3.js';

/**
 * Configuración consolidada de todas las interpretaciones oficiales
 */
export const INTERPRETACIONES_OFICIALES_CONSOLIDADAS = {
  // Mapeo de percentiles a niveles según documento oficial
  niveles: INTERPRETACIONES_OFICIALES.niveles,

  // Función para obtener nivel por percentil según documento oficial
  obtenerNivelPorPercentil: INTERPRETACIONES_OFICIALES.obtenerNivelPorPercentil,

  // Interpretaciones oficiales completas por aptitud - COPIADAS A PIE DE LETRA
  interpretaciones: {
    // Aptitudes del primer archivo
    'V': INTERPRETACIONES_OFICIALES.interpretaciones.V,
    'E': INTERPRETACIONES_OFICIALES.interpretaciones.E,
    
    // Aptitudes del segundo archivo
    'A': INTERPRETACIONES_OFICIALES_PARTE2.A,
    'R': INTERPRETACIONES_OFICIALES_PARTE2.R,
    'N': INTERPRETACIONES_OFICIALES_PARTE2.N,
    
    // Aptitudes del tercer archivo
    'M': INTERPRETACIONES_OFICIALES_PARTE3.M,
    'O': INTERPRETACIONES_OFICIALES_PARTE3.O
  },

  /**
   * Función principal para obtener interpretación oficial de una aptitud
   * @param {string} aptitudCodigo - Código de la aptitud (V, E, A, R, N, M, O)
   * @param {number} percentil - Percentil obtenido (0-100)
   * @returns {Object} Objeto con interpretaciones oficiales de rendimiento, académico y vocacional
   */
  obtenerInterpretacionAptitud: (aptitudCodigo, percentil) => {
    const nivel = INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerNivelPorPercentil(percentil);
    const interpretacion = INTERPRETACIONES_OFICIALES_CONSOLIDADAS.interpretaciones[aptitudCodigo]?.[nivel.id];
    
    if (!interpretacion) {
      return {
        nivel_nombre: nivel.nombre,
        percentil_rango: nivel.rango,
        rendimiento: 'Interpretación oficial no disponible para este nivel de rendimiento.',
        academico: 'Se requiere evaluación complementaria para determinar implicaciones académicas específicas.',
        vocacional: 'Se recomienda consulta con profesional especializado para orientación vocacional personalizada.'
      };
    }
    
    return {
      nivel_nombre: nivel.nombre,
      percentil_rango: nivel.rango,
      percentil_valor: percentil,
      rendimiento: interpretacion.rendimiento,
      academico: interpretacion.academico,
      vocacional: interpretacion.vocacional
    };
  },

  /**
   * Función para obtener todas las interpretaciones oficiales de una aptitud
   * @param {string} aptitudCodigo - Código de la aptitud
   * @returns {Object} Todas las interpretaciones oficiales por nivel
   */
  obtenerTodasInterpretacionesAptitud: (aptitudCodigo) => {
    return INTERPRETACIONES_OFICIALES_CONSOLIDADAS.interpretaciones[aptitudCodigo] || {};
  },

  /**
   * Función para validar si existe interpretación oficial para una aptitud y nivel
   * @param {string} aptitudCodigo - Código de la aptitud
   * @param {number} nivel - Nivel (1-7)
   * @returns {boolean} True si existe la interpretación oficial
   */
  existeInterpretacion: (aptitudCodigo, nivel) => {
    return !!(INTERPRETACIONES_OFICIALES_CONSOLIDADAS.interpretaciones[aptitudCodigo]?.[nivel]);
  },

  /**
   * Función para obtener resumen de completitud de interpretaciones oficiales
   * @returns {Object} Resumen de qué interpretaciones oficiales están disponibles
   */
  obtenerResumenCompletitud: () => {
    const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
    const niveles = [1, 2, 3, 4, 5, 6, 7];
    const resumen = {};

    aptitudes.forEach(aptitud => {
      resumen[aptitud] = {
        total: niveles.length,
        disponibles: 0,
        faltantes: []
      };

      niveles.forEach(nivel => {
        if (INTERPRETACIONES_OFICIALES_CONSOLIDADAS.existeInterpretacion(aptitud, nivel)) {
          resumen[aptitud].disponibles++;
        } else {
          resumen[aptitud].faltantes.push(nivel);
        }
      });

      resumen[aptitud].porcentaje = Math.round((resumen[aptitud].disponibles / resumen[aptitud].total) * 100);
    });

    return resumen;
  },

  /**
   * Función para obtener interpretaciones oficiales múltiples (para informes completos)
   * @param {Array} resultados - Array de objetos con {aptitud, percentil}
   * @returns {Array} Array de interpretaciones oficiales
   */
  obtenerInterpretacionesMultiples: (resultados) => {
    return resultados.map(resultado => ({
      aptitud: resultado.aptitud,
      percentil: resultado.percentil,
      interpretacion: INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerInterpretacionAptitud(
        resultado.aptitud, 
        resultado.percentil
      )
    }));
  }
};

/**
 * Función de compatibilidad con el sistema existente
 * Mantiene la interfaz original pero usa las interpretaciones oficiales
 */
export const obtenerInterpretacionOficial = (aptitudCodigo, percentil) => {
  return INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerInterpretacionAptitud(aptitudCodigo, percentil);
};

/**
 * Función para verificar completitud de interpretaciones oficiales
 */
export const verificarCompletitudOficial = () => {
  const resumen = INTERPRETACIONES_OFICIALES_CONSOLIDADAS.obtenerResumenCompletitud();
  console.log('📊 Resumen de Interpretaciones Oficiales Disponibles:');
  
  Object.keys(resumen).forEach(aptitud => {
    const info = resumen[aptitud];
    console.log(`${aptitud}: ${info.disponibles}/${info.total} (${info.porcentaje}%) - Faltantes: ${info.faltantes.join(', ') || 'Ninguno'}`);
  });
  
  return resumen;
};

// Exportar como default para compatibilidad
export default INTERPRETACIONES_OFICIALES_CONSOLIDADAS;
