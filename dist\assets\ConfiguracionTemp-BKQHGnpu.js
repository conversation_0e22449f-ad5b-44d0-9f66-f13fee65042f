var e=Object.defineProperty,s=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,l=(s,a,t)=>a in s?e(s,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[a]=t,n=(e,s)=>{for(var a in s||(s={}))r.call(s,a)&&l(e,a,s[a]);if(t)for(var a of t(s))i.call(s,a)&&l(e,a,s[a]);return e},o=(e,t)=>s(e,a(t)),d=(e,s,a)=>new Promise((t,r)=>{var i=e=>{try{n(a.next(e))}catch(s){r(s)}},l=e=>{try{n(a.throw(e))}catch(s){r(s)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(i,l);n((a=a.apply(e,s)).next())});import{r as c,m,v as x,ao as u,k as p,V as g,J as h,j as b,Q as f,w as y,x as j,y as v,h as N,F as w,z as C,A as P,B as _,C as k,D as S,Y as E,ap as A,a0 as I,O as U,aq as L,ar as z,M,R,N as q,as as D,l as $,e as T,I as B,G as O,at as G,au as F,av as H,f as V,s as Y,a as J}from"./vendor-BqMjyOVw.js";import{s as W,i as Z,u as K}from"./index-Bdl1jgS_.js";import{P as Q}from"./PageHeader-DzW86ZOX.js";import{P as X}from"./pinRechargeRequests-BUqDaw89.js";import{P as ee}from"./pinNotifications-BMRChPcj.js";const se=({title:e,value:s,icon:a,color:t,percentage:r,trend:i,subtitle:l})=>b.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow",children:[b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{className:"flex-1",children:[b.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:e}),b.jsx("p",{className:"text-3xl font-bold text-gray-900",children:s}),l&&b.jsx("p",{className:"text-xs text-gray-500 mt-1",children:l})]}),b.jsx("div",{className:"flex-shrink-0",children:b.jsx("div",{className:"w-12 h-12 rounded-full flex items-center justify-center text-white",style:{backgroundColor:t},children:b.jsx(a,{className:"w-6 h-6"})})})]}),r&&b.jsxs("div",{className:"mt-4 flex items-center",children:[b.jsxs("span",{className:"text-sm font-medium px-2 py-1 rounded "+("up"===i?"text-green-700 bg-green-100":"text-red-700 bg-red-100"),children:["up"===i?"+":"",r,"%"]}),b.jsx("span",{className:"text-sm text-gray-500 ml-2",children:l||"vs sem. anterior"})]})]}),ae=()=>{const[e,s]=c.useState({totalUsers:0,totalPacientes:0,totalEvaluaciones:0,totalInformes:0,evaluacionesHoy:0,informesGenerados:0,promedioPercentil:0,aptitudesAltas:0}),[a,t]=c.useState(!0);c.useEffect(()=>{d(null,null,function*(){try{t(!0);const{data:e,error:a}=yield W.from("usuarios").select("id",{count:"exact"}),{data:r,error:i}=yield W.from("pacientes").select("id",{count:"exact"}),{data:l,error:n}=yield W.from("evaluaciones").select("id",{count:"exact"}),{data:o,error:d}=yield W.from("informes_generados").select("id",{count:"exact"}).neq("estado","eliminado"),c=(new Date).toISOString().split("T")[0],{data:m,error:x}=yield W.from("evaluaciones").select("id",{count:"exact"}).gte("fecha_inicio",`${c}T00:00:00`).lt("fecha_inicio",`${c}T23:59:59`),{data:u,error:p}=yield W.from("resultados").select("percentil");let g=0,h=0;if(u&&u.length>0){const e=u.map(e=>e.percentil||0);g=Math.round(e.reduce((e,s)=>e+s,0)/e.length),h=u.filter(e=>(e.percentil||0)>=75).length}s({totalUsers:(null==e?void 0:e.length)||0,totalPacientes:(null==r?void 0:r.length)||0,totalEvaluaciones:(null==l?void 0:l.length)||0,totalInformes:(null==o?void 0:o.length)||0,evaluacionesHoy:(null==m?void 0:m.length)||0,informesGenerados:(null==o?void 0:o.length)||0,promedioPercentil:g,aptitudesAltas:h})}catch(e){}finally{t(!1)}})},[]);const r=[{title:"Usuarios Registrados",value:e.totalUsers,icon:m,color:"#3B82F6",subtitle:"Total en el sistema"},{title:"Pacientes Evaluados",value:e.totalPacientes,icon:x,color:"#10B981",subtitle:"Registros de pacientes"},{title:"Evaluaciones Realizadas",value:e.totalEvaluaciones,icon:u,color:"#F59E0B",subtitle:"Total completadas"},{title:"Informes Generados",value:e.totalInformes,icon:p,color:"#8B5CF6",subtitle:"Documentos creados"}],i=[{title:"Evaluaciones Hoy",value:e.evaluacionesHoy,icon:g,color:"#06B6D4",subtitle:"Actividad del día"},{title:"Promedio Percentil",value:`${e.promedioPercentil}%`,icon:h,color:"#F97316",subtitle:"Rendimiento general"},{title:"Aptitudes Altas",value:e.aptitudesAltas,icon:g,color:"#22C55E",subtitle:"Percentil ≥ 75"}],l=[{id:1,type:"Sistema BAT-7 Activo",user:`${e.totalUsers} usuarios registrados`,time:"En tiempo real",icon:m,color:"#10B981"},{id:2,type:"Evaluaciones Completadas",user:`${e.totalEvaluaciones} evaluaciones realizadas`,time:"Total acumulado",icon:u,color:"#F59E0B"},{id:3,type:"Informes Generados",user:`${e.totalInformes} documentos creados`,time:"Disponibles",icon:p,color:"#8B5CF6"},{id:4,type:"Rendimiento Promedio",user:`${e.promedioPercentil}% percentil general`,time:"Estadística",icon:g,color:"#06B6D4"}];return a?b.jsxs("div",{className:"flex items-center justify-center h-64",children:[b.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"}),b.jsx("span",{className:"ml-3 text-gray-600",children:"Cargando..."})]}):b.jsxs("div",{className:"space-y-6",children:[b.jsxs("div",{className:"text-center",children:[b.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Dashboard de Administración"}),b.jsx("p",{className:"text-gray-600",children:"Resumen general del sistema BAT-7"})]}),b.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:r.map((e,s)=>b.jsx(se,{title:e.title,value:e.value,icon:e.icon,color:e.color,subtitle:e.subtitle},s))}),b.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:i.map((e,s)=>b.jsx(se,{title:e.title,value:e.value,icon:e.icon,color:e.color,subtitle:e.subtitle},`additional-${s}`))}),b.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[b.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Distribución de Datos del Sistema"}),b.jsx("div",{className:"h-48 flex items-end justify-between space-x-2",children:[{label:"Usuarios",value:e.totalUsers,color:"#3B82F6"},{label:"Pacientes",value:e.totalPacientes,color:"#10B981"},{label:"Evaluaciones",value:Math.min(e.totalEvaluaciones,50),color:"#F59E0B"},{label:"Informes",value:e.totalInformes,color:"#8B5CF6"},{label:"Hoy",value:e.evaluacionesHoy,color:"#06B6D4"},{label:"Aptitudes+",value:Math.min(e.aptitudesAltas,50),color:"#22C55E"}].map((s,a)=>{const t=Math.max(e.totalUsers,e.totalPacientes,50),r=t>0?s.value/t*100:0;return b.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[b.jsx("div",{className:"text-xs font-medium text-gray-700 mb-1",children:s.value}),b.jsx("div",{className:"w-full rounded-t-lg",style:{backgroundColor:s.color,height:`${Math.max(r,10)}%`,minHeight:"20px"}}),b.jsx("span",{className:"text-xs text-gray-500 mt-2 text-center",children:s.label})]},a)})})]}),b.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Actividad Reciente"}),b.jsx("div",{className:"space-y-4",children:l.map(e=>{const s=e.icon;return b.jsxs("div",{className:"flex items-center space-x-3",children:[b.jsx("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white flex-shrink-0",style:{backgroundColor:e.color},children:b.jsx(s,{className:"w-4 h-4"})}),b.jsxs("div",{className:"flex-1 min-w-0",children:[b.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.type}),b.jsx("p",{className:"text-sm text-gray-500 truncate",children:e.user})]}),b.jsx("div",{className:"text-sm text-gray-500",children:e.time})]},e.id)})})]})]})]})},te=()=>{const[e,s]=c.useState([]),[a,t]=c.useState(!0),[r,i]=c.useState(null),[l,u]=c.useState(""),[p,g]=c.useState("all"),[h,A]=c.useState("all"),[I,U]=c.useState(!1),[L,z]=c.useState(!1),[M,R]=c.useState(!1),[q,D]=c.useState(null),[$,T]=c.useState({email:"",password:"",nombre:"",apellido:"",documento:"",rol:"paciente",activo:!0}),[B,O]=c.useState({total:0,activos:0,inactivos:0,administradores:0,psicologos:0,pacientes:0}),G=Z(l,300);c.useEffect(()=>{F()},[]);const F=()=>d(null,null,function*(){try{t(!0),i(null);const{data:e,error:a}=yield W.from("usuarios").select("*").order("fecha_creacion",{ascending:!1});if(a)throw a;s(e||[]),H(e||[])}catch(e){i("Error al cargar usuarios: "+e.message),f.error("Error al cargar usuarios")}finally{t(!1)}}),H=e=>{const s=e.length,a=e.filter(e=>e.activo).length,t=s-a,r=e.filter(e=>"administrador"===e.rol).length,i=e.filter(e=>"psicologo"===e.rol).length,l=e.filter(e=>"paciente"===e.rol).length;O({total:s,activos:a,inactivos:t,administradores:r,psicologos:i,pacientes:l})},V=(()=>{let s=[...e];if(G.trim()){const e=G.toLowerCase().trim();s=s.filter(s=>{var a,t,r,i;return(null==(a=s.nombre)?void 0:a.toLowerCase().includes(e))||(null==(t=s.apellido)?void 0:t.toLowerCase().includes(e))||(null==(r=s.email)?void 0:r.toLowerCase().includes(e))||(null==(i=s.documento)?void 0:i.toLowerCase().includes(e))})}if("all"!==p&&(s=s.filter(e=>e.rol===p)),"all"!==h){const e="active"===h;s=s.filter(s=>s.activo===e)}return s})(),{paginatedData:Y,currentPage:J,totalPages:K,goToPage:Q,goToNextPage:X,goToPreviousPage:ee,pageSize:se,setPageSize:ae,startIndex:te,endIndex:re}=((e=[],s={})=>{const{initialPage:a=1,initialPageSize:t=10,pageSizeOptions:r=[5,10,25,50,100],maxVisiblePages:i=5}=s,[l,n]=c.useState(a),[o,d]=c.useState(t),m=c.useMemo(()=>e.length,[e.length]),x=c.useMemo(()=>Math.ceil(m/o)||1,[m,o]),u=c.useMemo(()=>(l-1)*o,[l,o]),p=c.useMemo(()=>Math.min(u+o,m),[u,o,m]),g=c.useMemo(()=>e.slice(u,p),[e,u,p]),h=c.useMemo(()=>{const e=[],s=Math.floor(i/2);let a=Math.max(1,l-s),t=Math.min(x,a+i-1);t-a+1<i&&(a=Math.max(1,t-i+1));for(let r=a;r<=t;r++)e.push(r);return e},[l,x,i]),b=c.useCallback(e=>{const s=Math.max(1,Math.min(e,x));n(s)},[x]),f=c.useCallback(()=>{n(1)},[]),y=c.useCallback(()=>{n(x)},[x]),j=c.useCallback(()=>{l<x&&n(e=>e+1)},[l,x]),v=c.useCallback(()=>{l>1&&n(e=>e-1)},[l]),N=c.useCallback(e=>{const s=Math.ceil(m/e)||1,a=Math.min(l,s);d(e),n(a)},[l,m]),w=c.useCallback(()=>{n(1),d(t)},[t]),C=c.useMemo(()=>{if(0===m)return{start:0,end:0,total:0,text:"No items to display"};const e=u+1;return{start:e,end:p,total:m,text:`Showing ${e} to ${p} of ${m} items`}},[u,p,m]),P=c.useMemo(()=>m>o,[m,o]),_=c.useMemo(()=>l>1,[l]),k=c.useMemo(()=>l<x,[l,x]),S=c.useMemo(()=>l>1,[l]),E=c.useMemo(()=>l<x,[l,x]),A=c.useMemo(()=>({start:u,end:p,size:p-u}),[u,p]);return{paginatedData:g,currentPage:l,pageSize:o,totalPages:x,totalItems:m,goToPage:b,goToFirstPage:f,goToLastPage:y,goToNextPage:j,goToPreviousPage:v,changePageSize:N,pageSizeOptions:r,resetPagination:w,visiblePages:h,paginationInfo:C,pageRange:A,isPaginationNeeded:P,canGoToPrevious:_,canGoToNext:k,canGoToFirst:S,canGoToLast:E,startIndex:u,endIndex:p}})(V,10),ie=()=>{u(""),g("all"),A("all")};return b.jsxs("div",{className:"space-y-6",children:[b.jsxs("div",{className:"text-center mb-6",children:[b.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Gestión de Usuarios"}),b.jsx("p",{className:"text-gray-600 mt-2",children:"Administra usuarios del sistema"})]}),b.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[b.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:b.jsxs("div",{className:"flex items-center",children:[b.jsx(m,{className:"w-8 h-8 text-blue-500 mr-3"}),b.jsxs("div",{children:[b.jsx("p",{className:"text-sm text-gray-600",children:"Total"}),b.jsx("p",{className:"text-2xl font-bold text-gray-900",children:B.total})]})]})}),b.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:b.jsxs("div",{className:"flex items-center",children:[b.jsx(y,{className:"w-8 h-8 text-green-500 mr-3"}),b.jsxs("div",{children:[b.jsx("p",{className:"text-sm text-gray-600",children:"Activos"}),b.jsx("p",{className:"text-2xl font-bold text-gray-900",children:B.activos})]})]})}),b.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:b.jsxs("div",{className:"flex items-center",children:[b.jsx(j,{className:"w-8 h-8 text-red-500 mr-3"}),b.jsxs("div",{children:[b.jsx("p",{className:"text-sm text-gray-600",children:"Inactivos"}),b.jsx("p",{className:"text-2xl font-bold text-gray-900",children:B.inactivos})]})]})}),b.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:b.jsxs("div",{className:"flex items-center",children:[b.jsx(v,{className:"w-8 h-8 text-purple-500 mr-3"}),b.jsxs("div",{children:[b.jsx("p",{className:"text-sm text-gray-600",children:"Admins"}),b.jsx("p",{className:"text-2xl font-bold text-gray-900",children:B.administradores})]})]})}),b.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:b.jsxs("div",{className:"flex items-center",children:[b.jsx(m,{className:"w-8 h-8 text-indigo-500 mr-3"}),b.jsxs("div",{children:[b.jsx("p",{className:"text-sm text-gray-600",children:"Psicólogos"}),b.jsx("p",{className:"text-2xl font-bold text-gray-900",children:B.psicologos})]})]})}),b.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:b.jsxs("div",{className:"flex items-center",children:[b.jsx(m,{className:"w-8 h-8 text-teal-500 mr-3"}),b.jsxs("div",{children:[b.jsx("p",{className:"text-sm text-gray-600",children:"Pacientes"}),b.jsx("p",{className:"text-2xl font-bold text-gray-900",children:B.pacientes})]})]})})]}),b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[b.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4",children:[b.jsx("div",{className:"flex-1 max-w-md",children:b.jsxs("div",{className:"relative",children:[b.jsx(N,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),b.jsx("input",{type:"text",placeholder:"Buscar por nombre, email o documento...",value:l,onChange:e=>u(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),l!==G&&b.jsx(w,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 animate-spin"})]})}),b.jsxs("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3",children:[b.jsxs("div",{className:"flex items-center space-x-2",children:[b.jsx(C,{className:"text-gray-400 w-4 h-4"}),b.jsxs("select",{value:p,onChange:e=>g(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[b.jsx("option",{value:"all",children:"Todos los roles"}),b.jsx("option",{value:"administrador",children:"Administradores"}),b.jsx("option",{value:"psicologo",children:"Psicólogos"}),b.jsx("option",{value:"paciente",children:"Pacientes"})]})]}),b.jsxs("div",{className:"flex items-center space-x-2",children:[b.jsx(y,{className:"text-gray-400 w-4 h-4"}),b.jsxs("select",{value:h,onChange:e=>A(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[b.jsx("option",{value:"all",children:"Todos los estados"}),b.jsx("option",{value:"active",children:"Activos"}),b.jsx("option",{value:"inactive",children:"Inactivos"})]})]}),(l||"all"!==p||"all"!==h)&&b.jsxs("button",{onClick:ie,className:"px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors text-sm flex items-center space-x-1",title:"Limpiar filtros",children:[b.jsx(P,{className:"w-4 h-4"}),b.jsx("span",{children:"Limpiar"})]}),b.jsxs("button",{onClick:()=>U(!0),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[b.jsx(x,{className:"mr-2"}),"Crear Usuario"]})]})]}),b.jsxs("div",{className:"mt-4 flex items-center justify-between text-sm text-gray-600",children:[b.jsxs("span",{children:["Mostrando ",te+1," a ",Math.min(re,V.length)," de ",V.length," usuarios"]}),(l||"all"!==p||"all"!==h)&&b.jsx("span",{className:"text-blue-600",children:"Filtros activos"})]})]}),b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[b.jsx("div",{className:"overflow-x-auto",children:b.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[b.jsx("thead",{className:"bg-gray-50",children:b.jsxs("tr",{children:[b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Usuario"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Documento"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rol"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Estado"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acciones"})]})}),b.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:a?b.jsx("tr",{children:b.jsx("td",{colSpan:"6",className:"px-6 py-12 text-center",children:b.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[b.jsx(w,{className:"animate-spin text-blue-500 w-5 h-5"}),b.jsx("span",{className:"text-gray-600",children:"Cargando usuarios..."})]})})}):0===Y.length?b.jsx("tr",{children:b.jsx("td",{colSpan:"6",className:"px-6 py-12 text-center",children:b.jsxs("div",{className:"flex flex-col items-center space-y-3",children:[b.jsx(N,{className:"w-12 h-12 text-gray-300"}),b.jsxs("div",{className:"text-gray-500",children:[b.jsx("p",{className:"text-lg font-medium",children:"No se encontraron usuarios"}),b.jsx("p",{className:"text-sm",children:l||"all"!==p||"all"!==h?"Intenta ajustar los filtros de búsqueda":"No hay usuarios registrados en el sistema"})]}),(l||"all"!==p||"all"!==h)&&b.jsx("button",{onClick:ie,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm",children:"Limpiar filtros"})]})})}):Y.map(e=>{var s;return b.jsxs("tr",{className:"hover:bg-gray-50",children:[b.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:b.jsxs("div",{className:"flex items-center",children:[b.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:b.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:b.jsx(m,{className:"h-5 w-5 text-gray-600"})})}),b.jsxs("div",{className:"ml-4",children:[b.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]}),b.jsxs("div",{className:"text-sm text-gray-500",children:["ID: ",null==(s=e.id)?void 0:s.substring(0,8),"..."]})]})]})}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.email||"Sin email"}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.documento||"Sin documento"}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:b.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full "+("administrador"===e.rol?"bg-purple-100 text-purple-800":"psicologo"===e.rol?"bg-indigo-100 text-indigo-800":"bg-teal-100 text-teal-800"),children:"administrador"===e.rol?"Administrador":"psicologo"===e.rol?"Psicólogo":"Paciente"})}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:b.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full "+(e.activo?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.activo?"Activo":"Inactivo"})}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:b.jsxs("div",{className:"flex space-x-2",children:[b.jsx("button",{onClick:()=>{D(e),T({email:e.email||"",password:"",nombre:e.nombre||"",apellido:e.apellido||"",documento:e.documento||"",rol:e.rol||"paciente",activo:e.activo}),z(!0)},className:"text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50",title:"Editar usuario",children:b.jsx(_,{className:"w-4 h-4"})}),b.jsx("button",{onClick:()=>(e=>d(null,null,function*(){try{const s=!e.activo,a=s?"activar":"desactivar";if(!confirm(`¿Estás seguro de que quieres ${a} a ${e.nombre} ${e.apellido}?`))return;const{error:t}=yield W.from("usuarios").update({activo:s,fecha_actualizacion:(new Date).toISOString()}).eq("id",e.id);if(t)throw t;f.success(`Usuario ${a}do exitosamente`),yield F()}catch(s){f.error("Error al cambiar estado del usuario")}}))(e),className:"p-1 rounded "+(e.activo?"text-red-600 hover:text-red-900 hover:bg-red-50":"text-green-600 hover:text-green-900 hover:bg-green-50"),title:e.activo?"Desactivar usuario":"Activar usuario",children:e.activo?b.jsx(k,{className:"w-4 h-4"}):b.jsx(S,{className:"w-4 h-4"})}),b.jsx("button",{onClick:()=>{D(e),R(!0)},className:"text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50",title:"Eliminar usuario",children:b.jsx(E,{className:"w-4 h-4"})})]})})]},e.id)})})]})}),K>1&&b.jsxs("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[b.jsxs("div",{className:"flex-1 flex justify-between sm:hidden",children:[b.jsx("button",{onClick:ee,disabled:1===J,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Anterior"}),b.jsx("button",{onClick:X,disabled:J===K,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Siguiente"})]}),b.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[b.jsxs("div",{className:"flex items-center space-x-4",children:[b.jsxs("p",{className:"text-sm text-gray-700",children:["Mostrando"," ",b.jsx("span",{className:"font-medium",children:te+1})," ","a"," ",b.jsx("span",{className:"font-medium",children:Math.min(re,V.length)})," ","de"," ",b.jsx("span",{className:"font-medium",children:V.length})," ","usuarios"]}),b.jsxs("select",{value:se,onChange:e=>ae(Number(e.target.value)),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[b.jsx("option",{value:5,children:"5 por página"}),b.jsx("option",{value:10,children:"10 por página"}),b.jsx("option",{value:25,children:"25 por página"}),b.jsx("option",{value:50,children:"50 por página"})]})]}),b.jsx("div",{children:b.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[b.jsxs("button",{onClick:ee,disabled:1===J,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[b.jsx("span",{className:"sr-only",children:"Anterior"}),b.jsx("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:b.jsx("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:Math.min(5,K)},(e,s)=>{const a=s+1;return b.jsx("button",{onClick:()=>Q(a),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium "+(a===J?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"),children:a},a)}),b.jsxs("button",{onClick:X,disabled:J===K,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[b.jsx("span",{className:"sr-only",children:"Siguiente"}),b.jsx("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:b.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})]}),r&&b.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:b.jsxs("div",{className:"flex",children:[b.jsx("div",{className:"flex-shrink-0",children:b.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:b.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),b.jsxs("div",{className:"ml-3",children:[b.jsx("h3",{className:"text-sm font-medium text-red-800",children:"Error al cargar usuarios"}),b.jsx("div",{className:"mt-2 text-sm text-red-700",children:b.jsx("p",{children:r})}),b.jsx("div",{className:"mt-4",children:b.jsx("button",{onClick:F,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"Reintentar"})})]})]})}),I&&b.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:b.jsx("div",{className:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white",children:b.jsxs("div",{className:"mt-3",children:[b.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Crear Nuevo Usuario"}),b.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-4",children:b.jsxs("div",{className:"flex",children:[b.jsx("div",{className:"flex-shrink-0",children:b.jsx("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:b.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),b.jsxs("div",{className:"ml-3",children:[b.jsx("h3",{className:"text-sm font-medium text-green-800",children:"✅ Creación Completa de Usuario"}),b.jsx("div",{className:"mt-2 text-sm text-green-700",children:b.jsx("p",{children:"Se creará una cuenta completa con acceso inmediato. El usuario podrá hacer login inmediatamente con las credenciales proporcionadas."})})]})]})}),b.jsxs("form",{onSubmit:e=>d(null,null,function*(){var s,a;e.preventDefault();try{if(t(!0),!($.email&&$.password&&$.nombre&&$.apellido))return void f.error("Por favor completa todos los campos requeridos");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test($.email))return void f.error("Por favor ingresa un email válido");if($.password.length<6)return void f.error("La contraseña debe tener al menos 6 caracteres");const{data:e,error:i}=yield W.from("usuarios").select("id, email").eq("email",$.email.toLowerCase().trim()).maybeSingle();if(i&&"PGRST116"!==i.code)throw new Error("Error verificando email: "+i.message);if(e)return void f.error("Ya existe un usuario con ese email");const{data:l,error:n}=yield W.auth.signUp({email:$.email.toLowerCase().trim(),password:$.password,options:{data:{nombre:$.nombre.trim(),apellido:$.apellido.trim(),documento:(null==(s=$.documento)?void 0:s.trim())||null,rol:$.rol}}});if(n)throw new Error("Error creando cuenta: "+n.message);if(!l.user)throw new Error("No se pudo crear la cuenta de usuario");const{error:o}=yield W.from("usuarios").upsert([{id:l.user.id,email:$.email.toLowerCase().trim(),nombre:$.nombre.trim(),apellido:$.apellido.trim(),documento:(null==(a=$.documento)?void 0:a.trim())||null,rol:$.rol,activo:$.activo,fecha_creacion:(new Date).toISOString(),require_password_change:!1}],{onConflict:"id"});if(o){try{yield W.auth.admin.deleteUser(l.user.id)}catch(r){}throw new Error("Error creando perfil: "+o.message)}f.success(`Usuario ${$.nombre} ${$.apellido} creado exitosamente. El usuario puede hacer login inmediatamente con sus credenciales.`,{duration:5e3}),U(!1),T({email:"",password:"",nombre:"",apellido:"",documento:"",rol:"paciente",activo:!0}),yield F()}catch(i){let e="Error al crear usuario";i.message.includes("User already registered")?e="Ya existe un usuario registrado con ese email":i.message.includes("duplicate key")?e="Ya existe un usuario con ese email":i.message.includes("invalid input")?e="Datos inválidos. Verifica la información ingresada":i.message.includes("Password should be at least")?e="La contraseña debe tener al menos 6 caracteres":i.message.includes("Invalid email")?e="El formato del email no es válido":i.message&&(e="Error: "+i.message),f.error(e)}finally{t(!1)}}),className:"space-y-4",children:[b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email *"}),b.jsx("input",{type:"email",required:!0,value:$.email,onChange:e=>T(o(n({},$),{email:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"<EMAIL>"})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Contraseña *"}),b.jsx("input",{type:"password",required:!0,value:$.password,onChange:e=>T(o(n({},$),{password:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Mínimo 6 caracteres",minLength:"6"}),b.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"El usuario podrá hacer login inmediatamente con esta contraseña."})]}),b.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre *"}),b.jsx("input",{type:"text",required:!0,value:$.nombre,onChange:e=>T(o(n({},$),{nombre:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido *"}),b.jsx("input",{type:"text",required:!0,value:$.apellido,onChange:e=>T(o(n({},$),{apellido:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"}),b.jsx("input",{type:"text",value:$.documento,onChange:e=>T(o(n({},$),{documento:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Número de documento"})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rol *"}),b.jsxs("select",{required:!0,value:$.rol,onChange:e=>T(o(n({},$),{rol:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[b.jsx("option",{value:"paciente",children:"Paciente"}),b.jsx("option",{value:"psicologo",children:"Psicólogo"}),b.jsx("option",{value:"administrador",children:"Administrador"})]})]}),b.jsxs("div",{className:"flex items-center",children:[b.jsx("input",{type:"checkbox",id:"activo",checked:$.activo,onChange:e=>T(o(n({},$),{activo:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),b.jsx("label",{htmlFor:"activo",className:"ml-2 text-sm text-gray-700",children:"Usuario activo"})]}),b.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[b.jsx("button",{type:"button",onClick:()=>{U(!1),T({email:"",password:"",nombre:"",apellido:"",documento:"",rol:"paciente",activo:!0})},className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors",children:"Cancelar"}),b.jsx("button",{type:"submit",disabled:a,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors",children:a?"Creando...":"Crear Usuario"})]})]})]})})}),L&&q&&b.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:b.jsx("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:b.jsxs("div",{className:"mt-3",children:[b.jsxs("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:["Editar Usuario: ",q.nombre," ",q.apellido]}),b.jsxs("form",{onSubmit:e=>d(null,null,function*(){if(e.preventDefault(),q)try{t(!0);const{error:e}=yield W.from("usuarios").update({nombre:$.nombre,apellido:$.apellido,documento:$.documento,rol:$.rol,activo:$.activo}).eq("id",q.id);if(e)throw e;if($.password&&$.password.trim()){const{error:e}=yield W.auth.admin.updateUserById(q.id,{password:$.password});e&&f.warn("Usuario actualizado, pero hubo un problema al cambiar la contraseña")}if(q.rol!==$.rol){const e={paciente:"Paciente",psicologo:"Psicólogo",administrador:"Administrador"};f.success(`Usuario actualizado. Rol cambiado a: ${e[$.rol]}`)}else f.success("Usuario actualizado exitosamente");z(!1),D(null),T({email:"",password:"",nombre:"",apellido:"",documento:"",rol:"paciente",activo:!0}),yield F()}catch(s){f.error("Error al actualizar usuario: "+s.message)}finally{t(!1)}}),className:"space-y-4",children:[b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),b.jsx("input",{type:"email",value:$.email,disabled:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-100 text-gray-500"}),b.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"El email no se puede modificar"})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nueva Contraseña (opcional)"}),b.jsx("input",{type:"password",value:$.password,onChange:e=>T(o(n({},$),{password:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Dejar vacío para no cambiar",minLength:"6"})]}),b.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre *"}),b.jsx("input",{type:"text",required:!0,value:$.nombre,onChange:e=>T(o(n({},$),{nombre:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido *"}),b.jsx("input",{type:"text",required:!0,value:$.apellido,onChange:e=>T(o(n({},$),{apellido:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"}),b.jsx("input",{type:"text",value:$.documento,onChange:e=>T(o(n({},$),{documento:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rol *"}),b.jsxs("select",{required:!0,value:$.rol,onChange:e=>T(o(n({},$),{rol:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[b.jsx("option",{value:"paciente",children:"Paciente"}),b.jsx("option",{value:"psicologo",children:"Psicólogo"}),b.jsx("option",{value:"administrador",children:"Administrador"})]})]}),b.jsxs("div",{className:"flex items-center",children:[b.jsx("input",{type:"checkbox",id:"activo-edit",checked:$.activo,onChange:e=>T(o(n({},$),{activo:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),b.jsx("label",{htmlFor:"activo-edit",className:"ml-2 text-sm text-gray-700",children:"Usuario activo"})]}),b.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[b.jsx("button",{type:"button",onClick:()=>{z(!1),D(null),T({email:"",password:"",nombre:"",apellido:"",documento:"",rol:"paciente",activo:!0})},className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors",children:"Cancelar"}),b.jsx("button",{type:"submit",disabled:a,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors",children:a?"Guardando...":"Guardar Cambios"})]})]})]})})}),M&&q&&b.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:b.jsx("div",{className:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white",children:b.jsxs("div",{className:"mt-3",children:[b.jsx("div",{className:"flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mx-auto",children:b.jsx(E,{className:"h-6 w-6 text-red-600"})}),b.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900 mt-4 text-center",children:"Eliminar Usuario"}),b.jsxs("div",{className:"mt-4 px-4",children:[b.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4",children:b.jsxs("div",{className:"flex",children:[b.jsx("div",{className:"flex-shrink-0",children:b.jsx("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:b.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),b.jsxs("div",{className:"ml-3",children:[b.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"⚠️ Acción Irreversible"}),b.jsx("div",{className:"mt-2 text-sm text-yellow-700",children:b.jsx("p",{children:"Esta acción eliminará permanentemente:"})})]})]})}),b.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:[b.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Usuario a eliminar:"}),b.jsxs("div",{className:"space-y-1 text-sm text-gray-600",children:[b.jsxs("p",{children:[b.jsx("strong",{children:"Nombre:"})," ",q.nombre," ",q.apellido]}),b.jsxs("p",{children:[b.jsx("strong",{children:"Email:"})," ",q.email]}),b.jsxs("p",{children:[b.jsx("strong",{children:"Rol:"})," ",q.rol]}),b.jsxs("p",{children:[b.jsx("strong",{children:"Estado:"})," ",q.activo?"Activo":"Inactivo"]})]})]}),b.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4",children:[b.jsx("h4",{className:"font-medium text-red-800 mb-2",children:"Se eliminarán:"}),b.jsxs("ul",{className:"text-sm text-red-700 space-y-1",children:[b.jsx("li",{children:"• Perfil del usuario en la base de datos"}),b.jsx("li",{children:"• Cuenta de autenticación"}),b.jsx("li",{children:"• Todos los datos relacionados (resultados, sesiones, etc.)"}),b.jsx("li",{children:"• Historial de actividad del usuario"})]})]}),b.jsxs("div",{className:"text-center text-sm text-gray-600 mb-6",children:[b.jsxs("p",{children:["¿Estás seguro de que quieres eliminar a"," ",b.jsxs("strong",{className:"text-gray-900",children:[q.nombre," ",q.apellido]}),"?"]}),b.jsx("p",{className:"mt-1 text-red-600 font-medium",children:"Esta acción no se puede deshacer."})]})]}),b.jsxs("div",{className:"flex justify-center items-center space-x-4 mt-6 px-4",children:[b.jsx("button",{type:"button",onClick:()=>{R(!1),D(null)},className:"px-6 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors",disabled:a,children:"Cancelar"}),b.jsxs("button",{type:"button",onClick:()=>d(null,null,function*(){if(q)try{t(!0);const s=["resultados","sesiones","evaluaciones","asignaciones"];for(const t of s)try{const{error:e}=yield W.from(t).delete().eq("usuario_id",q.id);e&&!e.message.includes("relation")&&e.message.includes("does not exist")}catch(e){}const{error:a}=yield W.from("usuarios").delete().eq("id",q.id);if(a)throw new Error(`Error al eliminar perfil: ${a.message}`);const{error:r}=yield W.auth.admin.deleteUser(q.id);r&&("User not found"===r.message||f.warn("El perfil fue eliminado, pero hubo un problema con la cuenta de autenticación. Contacte a soporte.")),f.success(`Usuario ${q.nombre} ${q.apellido} eliminado exitosamente`),R(!1),D(null),yield F()}catch(s){f.error(`Error al eliminar usuario: ${s.message}`)}finally{t(!1)}}),disabled:a,className:"px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 transition-colors flex items-center",children:[a&&b.jsx(w,{className:"animate-spin -ml-1 mr-2 h-4 w-4"}),a?"Eliminando...":"Eliminar Definitivamente"]})]})]})})})]})},re=()=>{const[e,s]=c.useState(""),[a,t]=c.useState(!1),[r,i]=c.useState([{id:1,name:"Inicio",path:"/home",description:"Página principal del sistema",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!0},specificUsers:["U1","U2"]},{id:2,name:"Cuestionario",path:"/cuestionario",description:"Evaluaciones psicológicas BAT-7",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!0},specificUsers:[]},{id:3,name:"Resultados",path:"/resultados",description:"Visualización de resultados de evaluaciones",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!1},specificUsers:[]},{id:4,name:"Informes",path:"/informes",description:"Generación y gestión de informes",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!1},specificUsers:[]},{id:5,name:"Administración",path:"/admin/administration",description:"Panel de administración del sistema",status:"Activa",permissions:{administrador:!0,psicologo:!1,candidato:!1},specificUsers:[]},{id:6,name:"Configuración",path:"/configuracion",description:"Configuración del sistema",status:"Activa",permissions:{administrador:!0,psicologo:!1,candidato:!1},specificUsers:[]},{id:7,name:"Pacientes",path:"/pacientes",description:"Gestión de pacientes",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!1},specificUsers:[]},{id:8,name:"Soporte",path:"/soporte",description:"Centro de ayuda y soporte técnico",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!0},specificUsers:[]},{id:9,name:"Ayuda",path:"/ayuda",description:"Documentación y guías de usuario",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!0},specificUsers:[]}]),l=r.filter(s=>s.name.toLowerCase().includes(e.toLowerCase())||s.path.toLowerCase().includes(e.toLowerCase())),d=(e,s)=>{switch(e){case"administrador":return"bg-blue-500";case"psicologo":return"bg-green-500";case"candidato":return"bg-orange-500";default:return"bg-gray-500"}};return b.jsxs("div",{className:"space-y-6",children:[b.jsxs("div",{className:"flex items-center space-x-3",children:[b.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:b.jsx(m,{className:"w-5 h-5 text-blue-600"})}),b.jsxs("div",{children:[b.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Control de Acceso a Páginas"}),b.jsx("p",{className:"text-gray-600",children:"Gestiona qué roles pueden acceder a cada página del sistema. Los cambios se aplican inmediatamente."})]})]}),b.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:b.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[b.jsxs("div",{className:"flex-1 relative",children:[b.jsx(N,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),b.jsx("input",{type:"text",placeholder:"Buscar rutas por nombre o path...",value:e,onChange:e=>s(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),b.jsxs("button",{onClick:()=>t(!a),className:"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2",children:[b.jsx(C,{className:"w-4 h-4"}),b.jsx("span",{children:"Filtros Avanzados"})]})]})}),b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[b.jsx("div",{className:"bg-blue-600 text-white",children:b.jsxs("div",{className:"grid grid-cols-12 gap-4 px-6 py-4 font-medium",children:[b.jsx("div",{className:"col-span-2",children:"Página"}),b.jsx("div",{className:"col-span-2",children:"Estado"}),b.jsx("div",{className:"col-span-2 text-center",children:"👑 Administrador"}),b.jsx("div",{className:"col-span-2 text-center",children:"👨‍⚕️ Psicólogo"}),b.jsx("div",{className:"col-span-2 text-center",children:"🎓 Candidato"}),b.jsx("div",{className:"col-span-1 text-center",children:"👥 Usuarios Específicos"}),b.jsx("div",{className:"col-span-1 text-center",children:"Info"})]})}),b.jsx("div",{className:"divide-y divide-gray-200",children:l.map(e=>b.jsxs("div",{className:"grid grid-cols-12 gap-4 px-6 py-4 hover:bg-gray-50",children:[b.jsxs("div",{className:"col-span-2",children:[b.jsx("div",{className:"font-medium text-gray-900",children:e.name}),b.jsx("div",{className:"text-sm text-gray-500",children:e.path}),b.jsx("div",{className:"text-xs text-gray-400",children:e.description})]}),b.jsx("div",{className:"col-span-2 flex items-center",children:b.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("Activa"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.status})}),["administrador","psicologo","candidato"].map(s=>b.jsx("div",{className:"col-span-2 flex justify-center",children:b.jsx("button",{onClick:()=>((e,s)=>{i(r.map(a=>a.id===e?o(n({},a),{permissions:o(n({},a.permissions),{[s]:!a.permissions[s]})}):a))})(e.id,s),className:`w-12 h-6 rounded-full transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${e.permissions[s]?d(s):"bg-gray-300"}`,children:b.jsx("div",{className:"w-5 h-5 bg-white rounded-full shadow transform transition-transform duration-200 ease-in-out "+(e.permissions[s]?"translate-x-6":"translate-x-0.5")})})},s)),b.jsx("div",{className:"col-span-1 flex justify-center",children:b.jsxs("button",{className:"px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200",children:["👥 ",e.specificUsers.length," usuario(s)"]})}),b.jsx("div",{className:"col-span-1 flex justify-center",children:b.jsx("button",{className:"p-1 text-gray-400 hover:text-gray-600",children:b.jsx(A,{className:"w-4 h-4"})})})]},e.id))})]}),b.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[b.jsxs("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[b.jsx("div",{className:"text-2xl font-bold text-blue-600",children:r.length}),b.jsx("div",{className:"text-sm text-gray-600",children:"Total de Páginas"})]}),b.jsxs("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[b.jsx("div",{className:"text-2xl font-bold text-green-600",children:r.filter(e=>"Activa"===e.status).length}),b.jsx("div",{className:"text-sm text-gray-600",children:"Páginas Activas"})]}),b.jsxs("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[b.jsx("div",{className:"text-2xl font-bold text-orange-600",children:r.filter(e=>e.permissions.candidato).length}),b.jsx("div",{className:"text-sm text-gray-600",children:"Accesibles a Candidatos"})]}),b.jsxs("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[b.jsx("div",{className:"text-2xl font-bold text-purple-600",children:r.filter(e=>e.specificUsers.length>0).length}),b.jsx("div",{className:"text-sm text-gray-600",children:"Con Usuarios Específicos"})]})]})]})},ie=()=>{const[e,s]=c.useState([]),[a,t]=c.useState([]),[r,i]=c.useState([]),[l,m]=c.useState(!0),[x,u]=c.useState(""),[p,g]=c.useState(""),[h,y]=c.useState([]),[j,v]=c.useState(!1),[w,C]=c.useState([]),[P,_]=c.useState("");c.useEffect(()=>{k()},[]);const k=()=>d(null,null,function*(){try{m(!0),S(),E(),A()}catch(e){f.error("Error al cargar datos")}finally{m(!1)}}),S=()=>{s([{id:"1",nombre:"Dr. Rodriguez",apellido:"Martínez",email:"<EMAIL>",tipo_usuario:"psicologo",especialidad:"Psicología Clínica"},{id:"2",nombre:"Dra. Martínez",apellido:"López",email:"<EMAIL>",tipo_usuario:"psicologo",especialidad:"Psicología Educativa"},{id:"3",nombre:"Dr. García",apellido:"Fernández",email:"<EMAIL>",tipo_usuario:"psicologo",especialidad:"Psicología Organizacional"}])},E=()=>{t([{id:"3",nombre:"Juan",apellido:"Pérez",email:"<EMAIL>",tipo_usuario:"candidato",documento:"12345678"},{id:"4",nombre:"María",apellido:"García",email:"<EMAIL>",tipo_usuario:"candidato",documento:"87654321"},{id:"5",nombre:"Carlos",apellido:"López",email:"<EMAIL>",tipo_usuario:"candidato",documento:"11223344"},{id:"6",nombre:"Ana",apellido:"Martínez",email:"<EMAIL>",tipo_usuario:"candidato",documento:"44332211"}])},A=()=>{i([{id:1,psicologo_id:"1",paciente_id:"3",assigned_at:"2025-07-15T10:30:00Z",is_active:!0,psychologist:{id:"1",nombre:"Dr. Rodriguez",apellido:"Martínez",email:"<EMAIL>"},patient:{id:"3",nombre:"Juan",apellido:"Pérez",email:"<EMAIL>"}},{id:2,psicologo_id:"2",paciente_id:"4",assigned_at:"2025-07-14T14:20:00Z",is_active:!0,psychologist:{id:"2",nombre:"Dra. Martínez",apellido:"López",email:"<EMAIL>"},patient:{id:"4",nombre:"María",apellido:"García",email:"<EMAIL>"}}])},D=()=>{const e=r.filter(e=>!0===e.is_active).map(e=>e.paciente_id);return a.filter(s=>!e.includes(s.id))},$=e.filter(e=>{var s,a,t;return(null==(s=e.nombre)?void 0:s.toLowerCase().includes(x.toLowerCase()))||(null==(a=e.apellido)?void 0:a.toLowerCase().includes(x.toLowerCase()))||(null==(t=e.email)?void 0:t.toLowerCase().includes(x.toLowerCase()))});return l?b.jsx("div",{className:"flex justify-center items-center h-64",children:b.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):b.jsxs("div",{className:"space-y-6",children:[b.jsxs("div",{className:"text-center mb-6",children:[b.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Asignación de Pacientes"}),b.jsx("p",{className:"text-gray-600 mt-2",children:"Gestiona la asignación de pacientes a psicólogos"})]}),b.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[b.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Psicólogos"}),b.jsx("p",{className:"text-3xl font-bold text-gray-900",children:e.length})]}),b.jsx(I,{className:"w-8 h-8 text-blue-500"})]})}),b.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Pacientes"}),b.jsx("p",{className:"text-3xl font-bold text-gray-900",children:a.length})]}),b.jsx(U,{className:"w-8 h-8 text-green-500"})]})}),b.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Asignaciones"}),b.jsx("p",{className:"text-3xl font-bold text-gray-900",children:r.filter(e=>"active"===e.status).length})]}),b.jsx(L,{className:"w-8 h-8 text-orange-500"})]})}),b.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Sin Asignar"}),b.jsx("p",{className:"text-3xl font-bold text-gray-900",children:D().length})]}),b.jsx(z,{className:"w-8 h-8 text-red-500"})]})})]}),b.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[b.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 mb-6",children:[b.jsxs("div",{className:"relative",children:[b.jsx(N,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),b.jsx("input",{type:"text",placeholder:"Buscar psicólogos...",value:x,onChange:e=>u(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),b.jsxs("button",{onClick:()=>v(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[b.jsx(M,{}),b.jsx("span",{children:"Nueva Asignación"})]})]}),b.jsx("div",{className:"space-y-6",children:$.map(e=>{const s=(t=e.id,r.filter(e=>e.psicologo_id===t&&!0===e.is_active).map(e=>{const s=a.find(s=>s.id===e.paciente_id);return o(n({},e),{patient:s})}).filter(e=>e.patient));var t;return b.jsxs("div",{className:"border border-gray-200 rounded-lg p-6",children:[b.jsxs("div",{className:"flex items-center justify-between mb-4",children:[b.jsxs("div",{className:"flex items-center space-x-3",children:[b.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:b.jsx(I,{className:"w-5 h-5 text-blue-600"})}),b.jsxs("div",{children:[b.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:[e.nombre," ",e.apellido]}),b.jsx("p",{className:"text-sm text-gray-600",children:e.email})]})]}),b.jsxs("div",{className:"text-right",children:[b.jsx("p",{className:"text-sm text-gray-600",children:"Pacientes asignados"}),b.jsx("p",{className:"text-2xl font-bold text-blue-600",children:s.length})]})]}),s.length>0?b.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:s.map(e=>b.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 flex items-center justify-between",children:[b.jsxs("div",{className:"flex items-center space-x-3",children:[b.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:b.jsx(U,{className:"w-4 h-4 text-green-600"})}),b.jsxs("div",{children:[b.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[e.patient.nombre," ",e.patient.apellido]}),b.jsx("p",{className:"text-xs text-gray-600",children:e.patient.email})]})]}),b.jsx("button",{onClick:()=>{return s=e.id,d(null,null,function*(){if(window.confirm("¿Estás seguro de que quieres desasignar este paciente?"))try{i(e=>e.filter(e=>e.id!==s));const{error:e}=yield supabase.from("patient_assignments").delete().eq("id",s);if(e&&"42P01"!==e.code)throw e;f.success("Paciente desasignado exitosamente")}catch(e){f.error("Error al desasignar paciente")}});var s},className:"text-red-600 hover:text-red-900 p-1",title:"Desasignar paciente",children:b.jsx(R,{})})]},e.id))}):b.jsxs("div",{className:"text-center py-8 text-gray-500",children:[b.jsx(U,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),b.jsx("p",{children:"No hay pacientes asignados"})]})]},e.id)})}),0===$.length&&b.jsx("div",{className:"text-center py-8",children:b.jsx("p",{className:"text-gray-500",children:"No se encontraron psicólogos"})})]}),j&&b.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:b.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-lg",children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Nueva Asignación"}),b.jsxs("div",{className:"space-y-4",children:[b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Psicólogo"}),b.jsxs("select",{value:p,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[b.jsx("option",{value:"",children:"Seleccionar psicólogo"}),e.map(e=>b.jsxs("option",{value:e.id,children:[e.nombre," ",e.apellido]},e.id))]})]}),b.jsxs("div",{children:[b.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Pacientes sin asignar (",D().filter(e=>e.nombre.toLowerCase().includes(P.toLowerCase())||e.apellido.toLowerCase().includes(P.toLowerCase())||e.email.toLowerCase().includes(P.toLowerCase())).length,")"]}),b.jsxs("div",{className:"relative mb-3",children:[b.jsx(N,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),b.jsx("input",{type:"text",placeholder:"Buscar pacientes...",value:P,onChange:e=>_(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),b.jsxs("div",{className:"max-h-48 overflow-y-auto border border-gray-300 rounded-lg",children:[D().filter(e=>e.nombre.toLowerCase().includes(P.toLowerCase())||e.apellido.toLowerCase().includes(P.toLowerCase())||e.email.toLowerCase().includes(P.toLowerCase())).map(e=>b.jsxs("div",{className:"p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-200 last:border-b-0 flex items-center justify-between "+(w.includes(e.id)?"bg-blue-50 border-blue-200":""),onClick:()=>{return s=e.id,void C(e=>e.includes(s)?e.filter(e=>e!==s):[...e,s]);var s},children:[b.jsxs("div",{className:"flex-1",children:[b.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]}),b.jsx("p",{className:"text-xs text-gray-600",children:e.email})]}),w.includes(e.id)&&b.jsx(q,{className:"w-4 h-4 text-blue-600"})]},e.id)),0===D().filter(e=>e.nombre.toLowerCase().includes(P.toLowerCase())||e.apellido.toLowerCase().includes(P.toLowerCase())||e.email.toLowerCase().includes(P.toLowerCase())).length&&b.jsx("div",{className:"p-4 text-center text-gray-500",children:P?"No se encontraron pacientes":"No hay pacientes sin asignar"})]}),w.length>0&&b.jsxs("div",{className:"mt-2 text-sm text-blue-600",children:[w.length," paciente(s) seleccionado(s)"]})]})]}),b.jsxs("div",{className:"flex space-x-2 mt-6",children:[b.jsx("button",{onClick:()=>{v(!1),C([]),g(""),_("")},className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors",children:"Cancelar"}),b.jsxs("button",{onClick:()=>d(null,null,function*(){if(p&&0!==w.length)try{const e=w.map(e=>({id:`${p}-${e}-${Date.now()}`,psicologo_id:p,paciente_id:e,assigned_at:(new Date).toISOString(),is_active:!0}));i(s=>[...s,...e]),C([]),g(""),_(""),v(!1),f.success(`${w.length} paciente(s) asignado(s) exitosamente`)}catch(e){f.error("Error al guardar asignaciones")}else f.error("Debe seleccionar un psicólogo y al menos un paciente")}),disabled:!p||0===w.length,className:"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:[b.jsx(q,{className:"w-4 h-4"}),b.jsxs("span",{children:["Guardar (",w.length,")"]})]})]})]})})]})};class le{static getPsychologistsWithPinInfo(){return d(this,null,function*(){try{const{data:e,error:s}=yield W.from("psicologos").select("\n          id,\n          nombre,\n          apellido,\n          email,\n          psychologist_usage_control!left (\n            total_uses,\n            used_uses,\n            is_unlimited,\n            is_active,\n            plan_type,\n            updated_at\n          )\n        ").order("nombre",{ascending:!0});if(s)throw s;return(e||[]).map(e=>{var s;const a=null==(s=e.psychologist_usage_control)?void 0:s[0],t=!!a;return{id:e.id,nombre:e.nombre,apellido:e.apellido,email:e.email||"Sin email",fullName:`${e.nombre} ${e.apellido}`.trim(),hasControl:t,totalPins:t&&a.total_uses||0,usedPins:t&&a.used_uses||0,remainingPins:t?Math.max(0,(a.total_uses||0)-(a.used_uses||0)):0,isUnlimited:t&&a.is_unlimited||!1,isActive:t&&a.is_active||!1,planType:t?a.plan_type||"assigned":null,lastUpdated:t?a.updated_at:null,status:t?a.is_active?"Activo":"Inactivo":"Sin pines",statusColor:t?a.is_active?"green":"yellow":"red"}})}catch(e){throw e}})}static assignPins(e,s,a=!1){return d(this,null,function*(){try{if(!e)throw new Error("ID de psicólogo requerido");if(!a&&(!s||s<=0))throw new Error("Cantidad de pines debe ser mayor a 0");const{data:t,error:r}=yield W.from("psicologos").select("id, nombre, apellido, email").eq("id",e).single();if(r||!t)throw new Error("Psicólogo no encontrado");const i={psychologist_id:e,total_uses:a?999999:s,used_uses:0,is_unlimited:a,is_active:!0,plan_type:a?"unlimited":"assigned",updated_at:(new Date).toISOString()},{data:l}=yield W.from("psychologist_usage_control").select("id").eq("psychologist_id",e).single();let n,o;if(l){const s=yield W.from("psychologist_usage_control").update(i).eq("psychologist_id",e).select().single();n=s.data,o=s.error}else{const e=yield W.from("psychologist_usage_control").insert([i]).select().single();n=e.data,o=e.error}if(o)throw o;const{error:d}=yield W.from("pin_usage_logs").insert([{psychologist_id:e,action_type:"pin_assigned",pins_consumed:0,metadata:{assigned_pins:a?"unlimited":s,assignment_type:a?"unlimited":"manual",psychologist_name:`${t.nombre} ${t.apellido}`,pins_remaining:a?999999:s}}]);return{success:!0,message:`Pines ${a?"ilimitados":s} asignados a ${t.nombre} ${t.apellido}`,data:n}}catch(t){throw t}})}static consumePins(e){return d(this,arguments,function*(e,s=1,a={}){try{const{data:t,error:r}=yield W.from("psychologist_usage_control").select("*").eq("psychologist_id",e).eq("is_active",!0).single();if(r||!t)throw new Error("No se encontró control de pines activo para este psicólogo");if(!t.is_unlimited){const e=(t.total_uses||0)-(t.used_uses||0);if(e<s)throw new Error(`Pines insuficientes. Disponibles: ${e}, Requeridos: ${s}`)}const i=(t.used_uses||0)+s,l=t.is_unlimited?999999:Math.max(0,(t.total_uses||0)-i),{error:d}=yield W.from("psychologist_usage_control").update({used_uses:i,updated_at:(new Date).toISOString()}).eq("psychologist_id",e);if(d)throw d;const{error:c}=yield W.from("pin_usage_logs").insert([{psychologist_id:e,action_type:"pin_consumed",pins_consumed:s,metadata:o(n({},a),{consumed_at:(new Date).toISOString(),pins_remaining:l})}]);return{success:!0,remainingPins:l,isUnlimited:t.is_unlimited}}catch(t){throw t}})}static subtractPins(e,s){return d(this,null,function*(){try{if(!e)throw new Error("ID de psicólogo requerido");if(!s||s<=0)throw new Error("Cantidad de pines a restar debe ser mayor a 0");const{data:a,error:t}=yield W.from("psychologist_usage_control").select("*").eq("psychologist_id",e).eq("is_active",!0).single();if(t||!a)throw new Error("No se encontró control de pines activo para este psicólogo");if(a.is_unlimited)throw new Error("No se pueden restar pines de un plan ilimitado");const r=Math.max(0,(a.total_uses||0)-s),i=a.used_uses||0,l=Math.min(i,r),{data:n,error:o}=yield W.from("psychologist_usage_control").update({total_uses:r,used_uses:l,updated_at:(new Date).toISOString()}).eq("psychologist_id",e).select().single();if(o)throw o;const{error:d}=yield W.from("pin_usage_logs").insert([{psychologist_id:e,action_type:"pin_subtracted",pins_consumed:s,metadata:{subtracted_pins:s,previous_total:a.total_uses,new_total:r,adjusted_used_pins:l!==i,subtracted_at:(new Date).toISOString()}}]);return{success:!0,message:`${s} pines restados exitosamente`,data:{previousTotal:a.total_uses,newTotal:r,usedPins:l,remainingPins:Math.max(0,r-l)}}}catch(a){throw a}})}static removePsychologistPins(e){return d(this,null,function*(){try{if(!e)throw new Error("ID de psicólogo requerido");const{data:s,error:a}=yield W.from("psicologos").select("id, nombre, apellido").eq("id",e).single();if(a||!s)throw new Error("Psicólogo no encontrado");const{data:t}=yield W.from("psychologist_usage_control").select("*").eq("psychologist_id",e).single(),{error:r}=yield W.from("psychologist_usage_control").delete().eq("psychologist_id",e);if(r)throw r;const{error:i}=yield W.from("pin_usage_logs").insert([{psychologist_id:e,action_type:"pin_control_removed",pins_consumed:0,metadata:{removed_control:t||{},psychologist_name:`${s.nombre} ${s.apellido}`,removed_at:(new Date).toISOString()}}]);return{success:!0,message:`Control de pines eliminado para ${s.nombre} ${s.apellido}`,data:{psychologist:s,removedControl:t}}}catch(s){throw s}})}static getSystemStats(){return d(this,null,function*(){var e,s;try{const{data:a,error:t}=yield W.from("psychologist_usage_control").select("total_uses, used_uses, is_unlimited, is_active"),{data:r,error:i}=yield W.from("pin_usage_logs").select("pins_consumed, created_at").gte("created_at",new Date(Date.now()-2592e6).toISOString()),l={totalPsychologists:(null==a?void 0:a.length)||0,activePsychologists:(null==(e=null==a?void 0:a.filter(e=>e.is_active))?void 0:e.length)||0,totalPinsAssigned:(null==a?void 0:a.reduce((e,s)=>e+(s.total_uses||0),0))||0,totalPinsUsed:(null==a?void 0:a.reduce((e,s)=>e+(s.used_uses||0),0))||0,unlimitedPlans:(null==(s=null==a?void 0:a.filter(e=>e.is_unlimited))?void 0:s.length)||0,recentConsumption:(null==r?void 0:r.reduce((e,s)=>e+(s.pins_consumed||0),0))||0};return l.remainingPins=l.totalPinsAssigned-l.totalPinsUsed,l.usagePercentage=l.totalPinsAssigned>0?Math.round(l.totalPinsUsed/l.totalPinsAssigned*100):0,l}catch(a){throw a}})}}const ne=()=>{const[e,s]=c.useState("overview"),[a,t]=c.useState(!1),[r,i]=c.useState([]),[l,u]=c.useState([]),[p,g]=c.useState([]),[f,y]=c.useState([]),[j,v]=c.useState(!1),[w,_]=c.useState(!1),[k,A]=c.useState({psychologistId:"",pins:"",isUnlimited:!1,reason:""}),[I,U]=c.useState(!1),[L,z]=c.useState({psychologistId:"",pins:""}),[M,H]=c.useState(""),[V,Y]=c.useState("all"),[J,W]=c.useState("all"),[Z,K]=c.useState(1),[Q,se]=c.useState(10),[ae,te]=c.useState(1),[re,ie]=c.useState(10),[ne,oe]=c.useState({totalRequests:0,pendingRequests:0,totalNotifications:0,unreadNotifications:0,totalPsychologists:0,totalPinsAssigned:0,totalPinsUsed:0}),de=[{id:"overview",name:"Resumen",icon:$,description:"Vista general del sistema de pines"},{id:"psychologists",name:"Gestión de Psicólogos",icon:m,description:"Administrar pines de psicólogos"},{id:"requests",name:"Solicitudes de Recarga",icon:h,description:"Gestionar solicitudes de recarga de pines"},{id:"notifications",name:"Centro de Notificaciones",icon:T,description:"Gestionar notificaciones del sistema"},{id:"history",name:"Historial",icon:B,description:"Ver historial de transacciones"}];c.useEffect(()=>{ce()},[]);const ce=()=>d(null,null,function*(){var e,s,a;t(!0);try{const t=yield le.getPsychologistsWithPinInfo();g(t);const r=yield X.getRequests({limit:10});r.success&&i(r.data||[]);const l=yield ee.getUserNotifications("74c8230e-6f01-4b5d-ae72-cf5ac61db33e",{limit:10});l.success&&u(l.data||[]),oe({totalRequests:(null==(e=r.data)?void 0:e.length)||0,pendingRequests:(null==(s=r.data)?void 0:s.filter(e=>"pending"===e.status).length)||0,totalNotifications:(null==(a=l.data)?void 0:a.length)||0,unreadNotifications:l.unread_count||0,totalPsychologists:(null==t?void 0:t.length)||0,totalPinsAssigned:0,totalPinsUsed:0})}catch(r){}finally{t(!1)}}),me=()=>d(null,null,function*(){if(0===f.length)return void alert("Selecciona al menos un psicólogo");const e=prompt("¿Cuántos pines asignar a cada psicólogo seleccionado?");if(!e||isNaN(e)||parseInt(e)<=0)alert("Cantidad de pines inválida");else try{t(!0);for(const s of f)yield ee.createPinAssignmentNotification(s,parseInt(e),!1);alert(`Pines asignados a ${f.length} psicólogos`),y([]),yield ce()}catch(s){alert("Error en asignación masiva")}finally{t(!1)}}),xe=()=>{let e=[...p];if(M.trim()){const s=M.toLowerCase().trim();e=e.filter(e=>e.fullName.toLowerCase().includes(s)||e.email.toLowerCase().includes(s)||e.nombre.toLowerCase().includes(s)||e.apellido.toLowerCase().includes(s))}return"all"!==V&&(e=e.filter(e=>{switch(V){case"active":return e.hasControl&&e.isActive;case"inactive":return e.hasControl&&!e.isActive;case"no-pins":return!e.hasControl;default:return!0}})),"all"!==J&&(e=e.filter(e=>{switch(J){case"limited":return e.hasControl&&!e.isUnlimited;case"unlimited":return e.hasControl&&e.isUnlimited;default:return!0}})),e},ue=()=>{H(""),Y("all"),W("all"),K(1)};D.useEffect(()=>{K(1)},[M,V,J]);const pe=e=>{const s=p.find(s=>s.id===e);return s?s.fullName:`ID: ${e.substring(0,8)}...`},ge=()=>{const e=xe(),s=(Z-1)*Q,a=s+Q;return e.slice(s,a)},he=e=>{K(e)},be=e=>{se(e),K(1)},fe=()=>{const e=(ae-1)*re,s=e+re;return r.slice(e,s)},ye=e=>{te(e)},je=e=>{ie(e),te(1)},ve=({currentPage:e,totalPages:s,onPageChange:a,itemsPerPage:t,onItemsPerPageChange:r,totalItems:i,itemName:l="elementos"})=>s<=1?null:b.jsxs("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[b.jsxs("div",{className:"flex-1 flex justify-between sm:hidden",children:[b.jsx("button",{onClick:()=>a(e-1),disabled:1===e,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Anterior"}),b.jsx("button",{onClick:()=>a(e+1),disabled:e===s,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Siguiente"})]}),b.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[b.jsxs("div",{className:"flex items-center space-x-4",children:[b.jsxs("p",{className:"text-sm text-gray-700",children:["Mostrando"," ",b.jsx("span",{className:"font-medium",children:Math.min((e-1)*t+1,i)})," ","a"," ",b.jsx("span",{className:"font-medium",children:Math.min(e*t,i)})," ","de"," ",b.jsx("span",{className:"font-medium",children:i})," ",l]}),b.jsxs("select",{value:t,onChange:e=>r(Number(e.target.value)),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[b.jsx("option",{value:5,children:"5 por página"}),b.jsx("option",{value:10,children:"10 por página"}),b.jsx("option",{value:25,children:"25 por página"}),b.jsx("option",{value:50,children:"50 por página"})]})]}),b.jsx("div",{children:b.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[b.jsxs("button",{onClick:()=>a(e-1),disabled:1===e,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[b.jsx("span",{className:"sr-only",children:"Anterior"}),b.jsx("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:b.jsx("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),(()=>{const a=[];if(s<=5)for(let e=1;e<=s;e++)a.push(e);else{const t=Math.max(1,e-2),r=Math.min(s,t+5-1);for(let e=t;e<=r;e++)a.push(e)}return a})().map(s=>b.jsx("button",{onClick:()=>a(s),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium "+(s===e?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"),children:s},s)),b.jsxs("button",{onClick:()=>a(e+1),disabled:e===s,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[b.jsx("span",{className:"sr-only",children:"Siguiente"}),b.jsx("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:b.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]}),Ne=()=>b.jsxs("div",{className:"space-y-6",children:[b.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:b.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[b.jsxs("div",{children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Gestión de Psicólogos"}),b.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Administra pines y permisos de psicólogos"})]}),b.jsxs("div",{className:"flex space-x-2",children:[b.jsxs("button",{onClick:()=>v(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[b.jsx(x,{className:"w-4 h-4"}),b.jsx("span",{children:"Asignar Pines"})]}),f.length>0&&b.jsxs("button",{onClick:me,className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[b.jsx(h,{className:"w-4 h-4"}),b.jsxs("span",{children:["Asignar a Seleccionados (",f.length,")"]})]})]})]})}),b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[b.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4",children:[b.jsx("div",{className:"flex-1 max-w-md",children:b.jsxs("div",{className:"relative",children:[b.jsx(N,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),b.jsx("input",{type:"text",placeholder:"Buscar por nombre, apellido o email...",value:M,onChange:e=>H(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),b.jsxs("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3",children:[b.jsxs("div",{className:"flex items-center space-x-2",children:[b.jsx(C,{className:"text-gray-400 w-4 h-4"}),b.jsxs("select",{value:V,onChange:e=>Y(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[b.jsx("option",{value:"all",children:"Todos los estados"}),b.jsx("option",{value:"active",children:"Activos"}),b.jsx("option",{value:"inactive",children:"Inactivos"}),b.jsx("option",{value:"no-pins",children:"Sin pines"})]})]}),b.jsxs("div",{className:"flex items-center space-x-2",children:[b.jsx(h,{className:"text-gray-400 w-4 h-4"}),b.jsxs("select",{value:J,onChange:e=>W(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[b.jsx("option",{value:"all",children:"Todos los tipos"}),b.jsx("option",{value:"limited",children:"Pines limitados"}),b.jsx("option",{value:"unlimited",children:"Pines ilimitados"})]})]}),b.jsxs("button",{onClick:ue,className:"px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors text-sm flex items-center space-x-1",title:"Limpiar filtros",children:[b.jsx(P,{className:"w-4 h-4"}),b.jsx("span",{children:"Limpiar"})]})]})]}),b.jsxs("div",{className:"mt-4 flex items-center justify-between text-sm text-gray-600",children:[b.jsxs("span",{children:["Mostrando ",xe().length," de ",p.length," psicólogos"]}),(M||"all"!==V||"all"!==J)&&b.jsx("span",{className:"text-blue-600",children:"Filtros activos"})]})]}),b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[b.jsx("div",{className:"overflow-x-auto",children:b.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[b.jsx("thead",{className:"bg-gray-50",children:b.jsxs("tr",{children:[b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:b.jsx("input",{type:"checkbox",checked:xe().length>0&&xe().every(e=>f.includes(e.id)),onChange:e=>{if(e.target.checked){const e=xe().map(e=>e.id);y([...new Set([...f,...e])])}else{const e=xe().map(e=>e.id);y(f.filter(s=>!e.includes(s)))}},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Psicólogo"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pines Cargados"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pines Gastados"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pines Restantes"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Estado"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acciones"})]})}),b.jsxs("tbody",{className:"bg-white divide-y divide-gray-200",children:[ge().map(e=>b.jsxs("tr",{className:"hover:bg-gray-50",children:[b.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:b.jsx("input",{type:"checkbox",checked:f.includes(e.id),onChange:s=>{s.target.checked?y([...f,e.id]):y(f.filter(s=>s!==e.id))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:b.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]})}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:b.jsxs("div",{className:"flex items-center",children:[b.jsx(h,{className:"w-4 h-4 text-yellow-500 mr-1"}),e.isUnlimited?"∞":e.totalPins]})}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:b.jsxs("div",{className:"flex items-center",children:[b.jsx(h,{className:"w-4 h-4 text-red-500 mr-1"}),e.usedPins]})}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:b.jsxs("div",{className:"flex items-center",children:[b.jsx(h,{className:"w-4 h-4 text-green-500 mr-1"}),e.isUnlimited?"∞":e.remainingPins]})}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:b.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full "+("green"===e.statusColor?"bg-green-100 text-green-800":"yellow"===e.statusColor?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.status})}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:b.jsxs("div",{className:"flex space-x-2",children:[b.jsx("button",{onClick:()=>{A(o(n({},k),{psychologistId:e.id})),v(!0)},className:"text-blue-600 hover:text-blue-900",title:"Asignar Pines",children:b.jsx(h,{className:"w-4 h-4"})}),e.hasControl&&b.jsxs(b.Fragment,{children:[b.jsx("button",{onClick:()=>{z({psychologistId:e.id,pins:""}),U(!0)},className:"text-orange-600 hover:text-orange-900",title:"Restar Pines",children:b.jsx(G,{className:"w-4 h-4"})}),b.jsx("button",{onClick:()=>{return s=e.id,a=e.fullName,d(null,null,function*(){if(confirm(`¿Estás seguro de que quieres eliminar completamente el control de pines de ${a}?`))try{t(!0);const e=yield le.removePsychologistPins(s);alert(e.message),yield ce()}catch(e){alert("Error al eliminar psicólogo: "+e.message)}finally{t(!1)}});var s,a},className:"text-red-600 hover:text-red-900",title:"Eliminar Control de Pines",children:b.jsx(E,{className:"w-4 h-4"})})]}),b.jsx("button",{className:"text-green-600 hover:text-green-900",title:"Ver Detalles",children:b.jsx(S,{className:"w-4 h-4"})})]})})]},e.id)),0===ge().length&&0===xe().length&&b.jsx("tr",{children:b.jsx("td",{colSpan:"7",className:"px-6 py-12 text-center",children:b.jsxs("div",{className:"flex flex-col items-center space-y-3",children:[b.jsx(N,{className:"w-12 h-12 text-gray-300"}),b.jsxs("div",{className:"text-gray-500",children:[b.jsx("p",{className:"text-lg font-medium",children:"No se encontraron psicólogos"}),b.jsx("p",{className:"text-sm",children:M||"all"!==V||"all"!==J?"Intenta ajustar los filtros de búsqueda":"No hay psicólogos registrados en el sistema"})]}),(M||"all"!==V||"all"!==J)&&b.jsx("button",{onClick:ue,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm",children:"Limpiar filtros"})]})})})]})]})}),b.jsx(ve,{currentPage:Z,totalPages:Math.ceil(xe().length/Q),onPageChange:he,itemsPerPage:Q,onItemsPerPageChange:be,totalItems:xe().length,itemName:"psicólogos"})]})]}),we=()=>b.jsx("div",{className:"space-y-6",children:b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[b.jsxs("div",{className:"px-6 py-4 border-b border-gray-200",children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Solicitudes de Recarga"}),b.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Gestiona las solicitudes de recarga de pines"})]}),b.jsx("div",{className:"overflow-x-auto",children:b.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[b.jsx("thead",{className:"bg-gray-50",children:b.jsxs("tr",{children:[b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Psicólogo"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pines Solicitados"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Urgencia"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Estado"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fecha"}),b.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acciones"})]})}),b.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:fe().map(e=>{var s,r;return b.jsxs("tr",{className:"hover:bg-gray-50",children:[b.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[b.jsx("div",{className:"text-sm font-medium text-gray-900",children:pe(e.psychologist_id)}),b.jsxs("div",{className:"text-sm text-gray-500",children:[null==(s=e.reason)?void 0:s.substring(0,50),(null==(r=e.reason)?void 0:r.length)>50?"...":""]})]}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.requested_pins}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:b.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full "+("urgent"===e.urgency?"bg-red-100 text-red-800":"high"===e.urgency?"bg-orange-100 text-orange-800":"bg-green-100 text-green-800"),children:e.urgency})}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:b.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full "+("pending"===e.status?"bg-yellow-100 text-yellow-800":"approved"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.status})}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),b.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:"pending"===e.status&&b.jsxs("div",{className:"flex space-x-2",children:[b.jsx("button",{onClick:()=>{return s=e.id,d(null,null,function*(){try{t(!0);const e=yield X.processRequest(s,{action:"approve",admin_id:"admin-test-id",admin_notes:"Aprobado desde panel de control"});e.success?(yield ce(),alert("Solicitud aprobada exitosamente")):alert("Error al aprobar solicitud: "+e.error)}catch(e){alert("Error al aprobar solicitud")}finally{t(!1)}});var s},disabled:a,className:"text-green-600 hover:text-green-900 disabled:opacity-50",title:"Aprobar",children:b.jsx(q,{className:"w-4 h-4"})}),b.jsx("button",{onClick:()=>{return s=e.id,d(null,null,function*(){try{t(!0);const e=yield X.processRequest(s,{action:"reject",admin_id:"admin-test-id",admin_notes:"Rechazado desde panel de control"});e.success?(yield ce(),alert("Solicitud rechazada")):alert("Error al rechazar solicitud: "+e.error)}catch(e){alert("Error al rechazar solicitud")}finally{t(!1)}});var s},disabled:a,className:"text-red-600 hover:text-red-900 disabled:opacity-50",title:"Rechazar",children:b.jsx(R,{className:"w-4 h-4"})})]})})]},e.id)})})]})}),b.jsx(ve,{currentPage:ae,totalPages:Math.ceil(r.length/re),onPageChange:ye,itemsPerPage:re,onItemsPerPageChange:je,totalItems:r.length,itemName:"solicitudes"})]})});return b.jsxs("div",{className:"space-y-6",children:[b.jsx("div",{className:"border-b border-gray-200",children:b.jsx("nav",{className:"-mb-px flex space-x-8","aria-label":"Tabs",children:de.map(a=>{const t=a.icon;return b.jsxs("button",{onClick:()=>s(a.id),className:(e===a.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")+" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors",children:[b.jsx(t,{className:"w-4 h-4"}),b.jsx("span",{children:a.name})]},a.id)})})}),a?b.jsx("div",{className:"flex justify-center items-center py-12",children:b.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(()=>{switch(e){case"overview":return b.jsxs("div",{className:"space-y-6",children:[b.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[b.jsx("div",{className:"bg-white border border-gray-200 rounded-lg shadow-sm p-6",children:b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("p",{className:"text-gray-600 text-sm font-medium",children:"Total Psicólogos"}),b.jsx("p",{className:"text-3xl font-bold text-gray-900",children:ne.totalPsychologists})]}),b.jsx(m,{className:"w-8 h-8 text-blue-500"})]})}),b.jsx("div",{className:"bg-white border border-gray-200 rounded-lg shadow-sm p-6",children:b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("p",{className:"text-gray-600 text-sm font-medium",children:"Solicitudes Pendientes"}),b.jsx("p",{className:"text-3xl font-bold text-orange-600",children:ne.pendingRequests})]}),b.jsx(O,{className:"w-8 h-8 text-orange-500"})]})}),b.jsx("div",{className:"bg-white border border-gray-200 rounded-lg shadow-sm p-6",children:b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("p",{className:"text-gray-600 text-sm font-medium",children:"Total Notificaciones"}),b.jsx("p",{className:"text-3xl font-bold text-gray-900",children:ne.totalNotifications})]}),b.jsx(T,{className:"w-8 h-8 text-green-500"})]})}),b.jsx("div",{className:"bg-white border border-gray-200 rounded-lg shadow-sm p-6",children:b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("p",{className:"text-gray-600 text-sm font-medium",children:"No Leídas"}),b.jsx("p",{className:"text-3xl font-bold text-red-600",children:ne.unreadNotifications})]}),b.jsx(T,{className:"w-8 h-8 text-red-500"})]})})]}),b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Acciones Rápidas"}),b.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[b.jsxs("button",{onClick:()=>v(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg flex items-center justify-center space-x-2 transition-colors",children:[b.jsx(x,{className:"w-5 h-5"}),b.jsx("span",{children:"Asignar Pines"})]}),b.jsxs("button",{onClick:()=>s("requests"),className:"bg-orange-600 hover:bg-orange-700 text-white px-4 py-3 rounded-lg flex items-center justify-center space-x-2 transition-colors",children:[b.jsx(h,{className:"w-5 h-5"}),b.jsx("span",{children:"Ver Solicitudes"})]}),b.jsxs("button",{onClick:ce,className:"bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg flex items-center justify-center space-x-2 transition-colors",children:[b.jsx(F,{className:"w-5 h-5"}),b.jsx("span",{children:"Actualizar Datos"})]})]})]}),b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Estado del Sistema de Pines"}),b.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[b.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[b.jsxs("div",{className:"flex items-center",children:[b.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full mr-3"}),b.jsx("span",{className:"text-green-800 font-medium",children:"Base de Datos"})]}),b.jsx("p",{className:"text-green-600 text-sm mt-1",children:"Conectado y funcionando"})]}),b.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[b.jsxs("div",{className:"flex items-center",children:[b.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full mr-3"}),b.jsx("span",{className:"text-blue-800 font-medium",children:"APIs"})]}),b.jsx("p",{className:"text-blue-600 text-sm mt-1",children:"Servicios activos"})]}),b.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[b.jsxs("div",{className:"flex items-center",children:[b.jsx("div",{className:"w-3 h-3 bg-purple-500 rounded-full mr-3"}),b.jsx("span",{className:"text-purple-800 font-medium",children:"Persistencia"})]}),b.jsx("p",{className:"text-purple-600 text-sm mt-1",children:"Datos reales activados"})]})]})]})]});case"psychologists":return Ne();case"requests":return we();case"notifications":return b.jsx("div",{className:"space-y-6",children:b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[b.jsxs("div",{className:"px-6 py-4 border-b border-gray-200",children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Centro de Notificaciones"}),b.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Gestiona las notificaciones del sistema"})]}),b.jsx("div",{className:"p-6",children:0===l.length?b.jsxs("div",{className:"text-center py-8",children:[b.jsx(T,{className:"mx-auto h-12 w-12 text-gray-400"}),b.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No hay notificaciones"}),b.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Las notificaciones aparecerán aquí cuando se generen."})]}):b.jsx("div",{className:"space-y-4",children:l.map(e=>b.jsx("div",{className:"p-4 rounded-lg border "+(e.read?"bg-gray-50 border-gray-200":"bg-blue-50 border-blue-200"),children:b.jsxs("div",{className:"flex items-start justify-between",children:[b.jsxs("div",{className:"flex-1",children:[b.jsx("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),b.jsx("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),b.jsx("p",{className:"text-xs text-gray-500 mt-2",children:new Date(e.created_at).toLocaleString()})]}),b.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full "+("success"===e.severity?"bg-green-100 text-green-800":"warning"===e.severity?"bg-orange-100 text-orange-800":"error"===e.severity?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:e.severity})]})},e.id))})})]})});case"history":return b.jsx("div",{className:"space-y-6",children:b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Historial de Transacciones"}),b.jsxs("div",{className:"text-center py-8",children:[b.jsx(B,{className:"mx-auto h-12 w-12 text-gray-400"}),b.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Historial en Desarrollo"}),b.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Esta funcionalidad se implementará próximamente."})]})]})});default:return b.jsx("div",{children:"Pestaña no encontrada"})}})(),j&&b.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:b.jsx("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:b.jsxs("div",{className:"mt-3",children:[b.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Asignar Pines"}),b.jsxs("div",{className:"space-y-4",children:[b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Psicólogo"}),b.jsxs("select",{value:k.psychologistId,onChange:e=>A(o(n({},k),{psychologistId:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[b.jsx("option",{value:"",children:"Seleccionar psicólogo"}),p.map(e=>b.jsxs("option",{value:e.id,children:[e.nombre," ",e.apellido]},e.id))]})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cantidad de Pines"}),b.jsx("input",{type:"number",value:k.pins,onChange:e=>A(o(n({},k),{pins:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ej: 50",min:"1",max:"1000"})]}),b.jsx("div",{children:b.jsxs("label",{className:"flex items-center",children:[b.jsx("input",{type:"checkbox",checked:k.isUnlimited,onChange:e=>A(o(n({},k),{isUnlimited:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),b.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"Pines ilimitados"})]})})]}),b.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[b.jsx("button",{onClick:()=>{v(!1),A({psychologistId:"",pins:"",isUnlimited:!1,reason:""})},className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors",children:"Cancelar"}),b.jsx("button",{onClick:()=>d(null,null,function*(){if(k.psychologistId&&(k.pins||k.isUnlimited))try{t(!0);const s=yield le.assignPins(k.psychologistId,parseInt(k.pins)||0,k.isUnlimited);try{yield ee.createPinAssignmentNotification(k.psychologistId,parseInt(k.pins)||0,k.isUnlimited)}catch(e){}alert(s.message),v(!1),A({psychologistId:"",pins:"",isUnlimited:!1}),yield ce()}catch(s){alert("Error al asignar pines: "+s.message)}finally{t(!1)}else alert("Por favor completa todos los campos requeridos")}),disabled:a,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors",children:a?"Asignando...":"Asignar Pines"})]})]})})}),I&&b.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:b.jsx("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:b.jsxs("div",{className:"mt-3",children:[b.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Restar Pines"}),b.jsxs("div",{className:"space-y-4",children:[b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Psicólogo"}),b.jsxs("select",{value:L.psychologistId,onChange:e=>z(o(n({},L),{psychologistId:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[b.jsx("option",{value:"",children:"Seleccionar psicólogo..."}),p.filter(e=>e.hasControl&&!e.isUnlimited).map(e=>b.jsxs("option",{value:e.id,children:[e.fullName," - ",e.remainingPins," pines disponibles"]},e.id))]})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cantidad de pines a restar"}),b.jsx("input",{type:"number",min:"1",value:L.pins,onChange:e=>z(o(n({},L),{pins:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ej: 10"})]}),b.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-3",children:b.jsxs("div",{className:"flex",children:[b.jsx(O,{className:"w-5 h-5 text-yellow-400 mr-2 mt-0.5"}),b.jsxs("div",{className:"text-sm text-yellow-700",children:[b.jsx("p",{className:"font-medium",children:"Advertencia:"}),b.jsx("p",{children:"Esta acción restará pines del total asignado. Si los pines restados hacen que el total sea menor que los ya consumidos, se ajustarán automáticamente."})]})]})})]}),b.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[b.jsx("button",{onClick:()=>{U(!1),z({psychologistId:"",pins:""})},className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors",children:"Cancelar"}),b.jsx("button",{onClick:()=>d(null,null,function*(){if(L.psychologistId&&L.pins)try{t(!0);const e=yield le.subtractPins(L.psychologistId,parseInt(L.pins));alert(e.message),U(!1),z({psychologistId:"",pins:""}),yield ce()}catch(e){alert("Error al restar pines: "+e.message)}finally{t(!1)}else alert("Por favor completa todos los campos requeridos")}),disabled:a,className:"px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 transition-colors",children:a?"Restando...":"Restar Pines"})]})]})})})]})},oe=()=>{const[e,s]=c.useState("profile"),[a,t]=c.useState({nombre:"Usuario Demo",apellido:"Sistema",email:"<EMAIL>",documento:"12345678"}),[r,i]=c.useState({emailNotifications:!0,pushNotifications:!1,weeklyReports:!0}),[l,d]=c.useState({twoFactorAuth:!1,sessionTimeout:"30",passwordExpiry:"90"}),m=[{id:"profile",name:"Perfil",icon:U,description:"Información personal"},{id:"notifications",name:"Notificaciones",icon:T,description:"Preferencias de notificaciones"},{id:"security",name:"Seguridad",icon:H,description:"Configuración de seguridad"}],x=(e,s)=>{t(a=>o(n({},a),{[e]:s}))},u=(e,s)=>{i(a=>o(n({},a),{[e]:s}))},p=(e,s)=>{d(a=>o(n({},a),{[e]:s}))},g=()=>b.jsxs("div",{className:"space-y-6",children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Información Personal"}),b.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nombre"}),b.jsxs("div",{className:"relative",children:[b.jsx(U,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),b.jsx("input",{type:"text",value:a.nombre,onChange:e=>x("nombre",e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Apellido"}),b.jsxs("div",{className:"relative",children:[b.jsx(U,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),b.jsx("input",{type:"text",value:a.apellido,onChange:e=>x("apellido",e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),b.jsxs("div",{className:"relative",children:[b.jsx(Y,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),b.jsx("input",{type:"email",value:a.email,onChange:e=>x("email",e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Documento"}),b.jsx("input",{type:"text",value:a.documento,onChange:e=>x("documento",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]});return b.jsxs("div",{className:"space-y-6",children:[b.jsxs("div",{className:"text-center mb-6",children:[b.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Configuración Personal"}),b.jsx("p",{className:"text-gray-600 mt-2",children:"Administra tu perfil y preferencias"})]}),b.jsx("div",{className:"flex flex-wrap gap-4 mb-8 justify-center",children:m.map(a=>b.jsxs("button",{onClick:()=>s(a.id),className:"flex items-center px-6 py-3 rounded-lg font-medium transition-colors "+(e===a.id?"bg-blue-600 text-white shadow-lg":"bg-white text-gray-700 hover:bg-gray-50 border border-gray-200"),children:[b.jsx(a.icon,{className:"w-5 h-5 mr-2"}),a.name]},a.id))}),b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(()=>{switch(e){case"profile":default:return g();case"notifications":return b.jsxs("div",{className:"space-y-6",children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Preferencias de Notificaciones"}),b.jsxs("div",{className:"space-y-4",children:[b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Notificaciones por Email"}),b.jsx("p",{className:"text-sm text-gray-500",children:"Recibir notificaciones importantes por correo"})]}),b.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[b.jsx("input",{type:"checkbox",checked:r.emailNotifications,onChange:e=>u("emailNotifications",e.target.checked),className:"sr-only peer"}),b.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Notificaciones Push"}),b.jsx("p",{className:"text-sm text-gray-500",children:"Notificaciones en tiempo real en el navegador"})]}),b.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[b.jsx("input",{type:"checkbox",checked:r.pushNotifications,onChange:e=>u("pushNotifications",e.target.checked),className:"sr-only peer"}),b.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Reportes Semanales"}),b.jsx("p",{className:"text-sm text-gray-500",children:"Resumen semanal de actividad"})]}),b.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[b.jsx("input",{type:"checkbox",checked:r.weeklyReports,onChange:e=>u("weeklyReports",e.target.checked),className:"sr-only peer"}),b.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]})]});case"security":return b.jsxs("div",{className:"space-y-6",children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Configuración de Seguridad"}),b.jsxs("div",{className:"space-y-4",children:[b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Autenticación de Dos Factores"}),b.jsx("p",{className:"text-sm text-gray-500",children:"Agregar una capa extra de seguridad"})]}),b.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[b.jsx("input",{type:"checkbox",checked:l.twoFactorAuth,onChange:e=>p("twoFactorAuth",e.target.checked),className:"sr-only peer"}),b.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tiempo de Sesión (minutos)"}),b.jsxs("select",{value:l.sessionTimeout,onChange:e=>p("sessionTimeout",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[b.jsx("option",{value:"15",children:"15 minutos"}),b.jsx("option",{value:"30",children:"30 minutos"}),b.jsx("option",{value:"60",children:"1 hora"}),b.jsx("option",{value:"120",children:"2 horas"})]})]}),b.jsxs("div",{children:[b.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Expiración de Contraseña (días)"}),b.jsxs("select",{value:l.passwordExpiry,onChange:e=>p("passwordExpiry",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[b.jsx("option",{value:"30",children:"30 días"}),b.jsx("option",{value:"60",children:"60 días"}),b.jsx("option",{value:"90",children:"90 días"}),b.jsx("option",{value:"180",children:"180 días"})]})]})]})]})}})(),b.jsx("div",{className:"mt-8 pt-6 border-t border-gray-200",children:b.jsxs("button",{onClick:()=>{alert("Configuración guardada correctamente")},className:"w-full md:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2 transition-colors",children:[b.jsx(V,{className:"w-5 h-5"}),b.jsx("span",{children:"Guardar Cambios"})]})})]})]})},de=()=>{const{user:e,userRole:s}=K(),[a,t]=c.useState("dashboard"),r="administrador"===s||"administrador"===(null==e?void 0:e.tipo_usuario),i=[{id:"dashboard",name:"Dashboard",icon:g,component:ae,adminOnly:!0,description:"Resumen general del sistema"},{id:"users",name:"Gestión de Usuarios",icon:m,component:te,adminOnly:!0,description:"Administra usuarios del sistema"},{id:"access",name:"Control de Acceso",icon:H,component:re,adminOnly:!0,description:"Gestiona permisos y accesos"},{id:"patients",name:"Asignación de Pacientes",icon:I,component:ie,adminOnly:!0,description:"Asigna pacientes a psicólogos"},{id:"pins",name:"Control de Pines",icon:$,component:ne,adminOnly:!0,description:"Gestiona el sistema de pines"},{id:"settings",name:"Configuración Personal",icon:J,component:oe,adminOnly:!1,description:"Configuración de usuario"}],l=i.filter(e=>!e.adminOnly||r);return b.jsxs("div",{className:"min-h-screen bg-gray-50",children:[b.jsx(Q,{title:"Configuración del Sistema",subtitle:"Administra usuarios, permisos y configuraciones generales",icon:J}),b.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 mb-8",children:[b.jsx("div",{className:"border-b border-gray-200",children:b.jsx("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:l.map(e=>{const s=e.icon;return b.jsxs("button",{onClick:()=>t(e.id),className:(a===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")+" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors",children:[b.jsx(s,{className:"w-4 h-4"}),b.jsx("span",{children:e.name})]},e.id)})})}),b.jsx("div",{className:"p-6",children:(()=>{const e=i.find(e=>e.id===a);if(!e)return b.jsx("div",{children:"Pestaña no encontrada"});if(e.adminOnly&&!r)return b.jsxs("div",{className:"text-center py-12",children:[b.jsx(H,{className:"mx-auto h-12 w-12 text-gray-400"}),b.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Acceso Restringido"}),b.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"No tienes permisos para acceder a esta sección."})]});const s=e.component;return b.jsx(s,{})})()})]}),r&&b.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[b.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Estado del Sistema"}),b.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[b.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[b.jsxs("div",{className:"flex items-center",children:[b.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full mr-3"}),b.jsx("span",{className:"text-green-800 font-medium",children:"Sistema Operativo"})]}),b.jsx("p",{className:"text-green-600 text-sm mt-1",children:"Todos los servicios funcionando correctamente"})]}),b.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[b.jsxs("div",{className:"flex items-center",children:[b.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full mr-3"}),b.jsx("span",{className:"text-blue-800 font-medium",children:"Base de Datos"})]}),b.jsx("p",{className:"text-blue-600 text-sm mt-1",children:"Conectado y sincronizado"})]}),b.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[b.jsxs("div",{className:"flex items-center",children:[b.jsx("div",{className:"w-3 h-3 bg-purple-500 rounded-full mr-3"}),b.jsx("span",{className:"text-purple-800 font-medium",children:"Sistema de Pines"})]}),b.jsx("p",{className:"text-purple-600 text-sm mt-1",children:"Persistencia real activada"})]}),b.jsxs("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:[b.jsxs("div",{className:"flex items-center",children:[b.jsx("div",{className:"w-3 h-3 bg-orange-500 rounded-full mr-3"}),b.jsx("span",{className:"text-orange-800 font-medium",children:"Autenticación"})]}),b.jsx("p",{className:"text-orange-600 text-sm mt-1",children:"Modo desarrollo (sin auth)"})]})]})]})]})]})};export{de as default};
