var e=(e,i,a)=>new Promise((r,l)=>{var o=e=>{try{t(a.next(e))}catch(i){l(i)}},s=e=>{try{t(a.throw(e))}catch(i){l(i)}},t=e=>e.done?r(e.value):Promise.resolve(e.value).then(o,s);t((a=a.apply(e,i)).next())});import{v as i,u as a,r,j as l,Q as o}from"./vendor-CIyllXGj.js";import{C as s,a as t,B as m,b as n,s as d,c}from"./index-CrXvaDRr.js";import{o as p}from"./interpretacionesAptitudes-Bt_sak-B.js";const u=()=>{const{patientId:u}=i(),b=a(),[N,g]=r.useState(null),[x,f]=r.useState([]),[v,h]=r.useState(!0),[j,C]=r.useState(!1);r.useEffect(()=>{u&&e(null,null,function*(){try{h(!0);const{data:e,error:i}=yield d.from("pacientes").select("*").eq("id",u).single();if(i)throw i;const{data:a,error:r}=yield d.from("resultados").select("\n            id,\n            puntaje_directo,\n            percentil,\n            errores,\n            tiempo_segundos,\n            concentracion,\n            created_at,\n            aptitudes:aptitud_id (\n              codigo,\n              nombre,\n              descripcion\n            )\n          ").eq("paciente_id",u).order("created_at",{ascending:!1});if(r)throw r;g(e),f(a||[])}catch(e){o.error("Error al cargar los datos del paciente"),b("/admin/reports")}finally{h(!1)}})},[u,b]);const V=e=>{if(!e)return"N/A";const i=new Date,a=new Date(e);let r=i.getFullYear()-a.getFullYear();const l=i.getMonth()-a.getMonth();return(l<0||0===l&&i.getDate()<a.getDate())&&r--,r};if(v)return l.jsxDEV("div",{className:"container mx-auto py-6",children:l.jsxDEV("div",{className:"py-16 text-center",children:[l.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:183,columnNumber:11},void 0),l.jsxDEV("p",{className:"text-gray-500",children:"Cargando informe completo..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:184,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:182,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:181,columnNumber:7},void 0);if(!N)return l.jsxDEV("div",{className:"container mx-auto py-6",children:l.jsxDEV(s,{children:l.jsxDEV(t,{children:l.jsxDEV("div",{className:"py-8 text-center",children:[l.jsxDEV("i",{className:"fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:196,columnNumber:15},void 0),l.jsxDEV("p",{className:"text-gray-500",children:"No se pudo cargar la información del paciente."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:197,columnNumber:15},void 0),l.jsxDEV(m,{onClick:()=>b("/admin/reports"),className:"mt-4",children:"Volver a Resultados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:198,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:195,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:194,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:193,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:192,columnNumber:7},void 0);const D=x.filter(e=>e.percentil).length>0?Math.round(x.filter(e=>e.percentil).reduce((e,i)=>e+i.percentil,0)/x.filter(e=>e.percentil).length):null,E=x.filter(e=>e.percentil),B=e=>E.find(i=>{var a;return(null==(a=i.aptitudes)?void 0:a.codigo)===e}),R=B("R"),y=B("N"),w=[null==R?void 0:R.percentil,null==y?void 0:y.percentil].filter(e=>void 0!==e),_=w.length>0?Math.round(w.reduce((e,i)=>e+i,0)/w.length):null,A=B("V"),k=B("O"),z=[null==A?void 0:A.percentil,null==k?void 0:k.percentil].filter(e=>void 0!==e),P=z.length>0?Math.round(z.reduce((e,i)=>e+i,0)/z.length):null,S=D,G=(e,i)=>{if(!i)return{nivel:"No evaluado",descripcion:"No hay datos suficientes para evaluar este índice."};let a,r,l;switch(a=i>=75?"Alto":i>=25?"Promedio":"Bajo",e){case"g":"Alto"===a?(r="Capacidad general elevada para comprender situaciones complejas, razonar y resolver problemas de manera efectiva.",l=["Habilidad para resolver eficientemente problemas complejos y novedosos","Buena capacidad para formular y contrastar hipótesis","Facilidad para abstraer información e integrarla con conocimiento previo","Elevado potencial para adquirir nuevos conocimientos"]):"Promedio"===a?(r="Capacidad general dentro del rango esperado para resolver problemas y comprender situaciones.",l=["Capacidad adecuada para resolver problemas de complejidad moderada","Habilidades de razonamiento en desarrollo","Potencial de aprendizaje dentro del rango promedio"]):(r="Dificultades en la capacidad general para resolver problemas complejos y comprender relaciones abstractas.",l=["Dificultades para aplicar el razonamiento a problemas complejos","Limitaciones para formar juicios que requieran abstracción","Posible necesidad de enseñanza más directiva y supervisada"]);break;case"Gf":"Alto"===a?(r="Excelente capacidad para el razonamiento inductivo y deductivo con problemas novedosos.",l=["Habilidad sobresaliente para aplicar razonamiento a problemas novedosos","Facilidad para identificar reglas y formular hipótesis","Nivel alto de razonamiento analítico","Buena integración de información visual y verbal"]):"Promedio"===a?(r="Capacidad adecuada para el razonamiento con contenidos abstractos y formales.",l=["Habilidades de razonamiento en desarrollo","Capacidad moderada para resolver problemas novedosos","Estrategias de resolución en proceso de consolidación"]):(r="Dificultades en el razonamiento inductivo y deductivo con problemas abstractos.",l=["Uso de estrategias poco eficaces para problemas novedosos","Falta de flexibilidad en soluciones alternativas","Dificultades para identificar reglas subyacentes","Integración defectuosa de información visual y verbal"]);break;case"Gc":"Alto"===a?(r="Excelente dominio de conocimientos adquiridos culturalmente y habilidades verbales.",l=["Habilidad para captar relaciones entre conceptos verbales","Buena capacidad de comprensión y expresión del lenguaje","Buen nivel de conocimiento léxico y ortográfico","Posiblemente buen nivel de cultura general"]):"Promedio"===a?(r="Conocimientos verbales y culturales dentro del rango esperado.",l=["Comprensión verbal adecuada para la edad","Conocimientos léxicos en desarrollo","Habilidades de expresión en proceso de consolidación"]):(r="Limitaciones en conocimientos verbales y habilidades de lenguaje adquiridas culturalmente.",l=["Procesamiento parcial de relaciones entre conceptos verbales","Dificultades en comprensión y expresión del lenguaje","Limitaciones en conocimiento léxico y ortográfico","Posible nivel bajo de cultura general"]);break;default:r="Interpretación no disponible para este índice.",l=[]}return{nivel:a,descripcion:r,caracteristicas:l}};return l.jsxDEV("div",{className:"container mx-auto py-6 max-w-6xl",children:[l.jsxDEV("div",{className:"mb-6 text-center",children:[l.jsxDEV("div",{className:"flex items-center justify-center mb-4",children:[l.jsxDEV("div",{className:`w-16 h-16 bg-${"masculino"===(null==N?void 0:N.genero)?"blue":"pink"}-100 rounded-full flex items-center justify-center mr-4`,children:l.jsxDEV("i",{className:`fas ${"masculino"===(null==N?void 0:N.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} text-2xl`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:401,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:400,columnNumber:11},void 0),l.jsxDEV("div",{children:[l.jsxDEV("h1",{className:"text-3xl font-bold text-blue-800",children:"Informe General BAT-7"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:404,columnNumber:13},void 0),l.jsxDEV("p",{className:"text-gray-600",children:[null==N?void 0:N.nombre," ",null==N?void 0:N.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:405,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:403,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:399,columnNumber:9},void 0),l.jsxDEV("div",{className:"flex justify-center space-x-4 print-hide",children:[l.jsxDEV(m,{onClick:()=>b("/admin/reports"),variant:"outline",children:[l.jsxDEV("i",{className:"fas fa-arrow-left mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:414,columnNumber:13},void 0),"Volver"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:410,columnNumber:11},void 0),l.jsxDEV(m,{onClick:()=>e(null,null,function*(){var e;try{C(!0);const i={resultado_id:null==(e=x[0])?void 0:e.id,paciente_id:N.id,titulo:`Informe Completo Admin - ${null==N?void 0:N.nombre} ${null==N?void 0:N.apellido}`,contenido:{paciente:N,resultados:x.map(e=>{var i,a,r;return{id:e.id,test:{codigo:null==(i=e.aptitudes)?void 0:i.codigo,nombre:null==(a=e.aptitudes)?void 0:a.nombre,descripcion:null==(r=e.aptitudes)?void 0:r.descripcion},puntajes:{puntaje_directo:e.puntaje_directo,percentil:e.percentil,errores:e.errores,tiempo_segundos:e.tiempo_segundos,concentracion:e.concentracion},interpretacion:e.percentil?c.obtenerInterpretacionPC(e.percentil):{nivel:"Pendiente",color:"text-gray-600",bg:"bg-gray-100"},fecha_evaluacion:e.created_at}}),resumen:{total_tests:x.length,promedio_percentil:x.filter(e=>e.percentil).length>0?Math.round(x.filter(e=>e.percentil).reduce((e,i)=>e+i.percentil,0)/x.filter(e=>e.percentil).length):null,fecha_primera_evaluacion:x.length>0?x[x.length-1].created_at:null,fecha_ultima_evaluacion:x.length>0?x[0].created_at:null},fecha_generacion:(new Date).toISOString()},generado_por:"Administrador",tipo_informe:"evaluacion_completa",estado:"generado"},{data:a,error:r}=yield d.from("informes").insert([i]).select().single();if(r)return void o.error("Error al guardar el informe completo");o.success("Informe completo guardado exitosamente")}catch(i){o.error("Error al guardar el informe completo")}finally{C(!1)}}),disabled:j,variant:"primary",children:[l.jsxDEV("i",{className:"fas fa-save mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:422,columnNumber:13},void 0),j?"Guardando...":"Guardar Informe"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:417,columnNumber:11},void 0),l.jsxDEV(m,{onClick:()=>{const e=document.querySelectorAll(".print-hide");e.forEach(e=>e.style.display="none");const i=document.querySelector(".container"),a=document.body,r=document.documentElement,l=null==i?void 0:i.className,o=null==a?void 0:a.className,s=null==r?void 0:r.className;i&&(i.className+=" print-optimize"),a&&(a.className+=" print-optimize"),r&&(r.className+=" print-optimize");const t=document.createElement("style");t.textContent="\n      @media print {\n        * {\n          -webkit-print-color-adjust: exact !important;\n          print-color-adjust: exact !important;\n        }\n        .space-y-6 > * + * {\n          margin-top: 0.5rem !important;\n        }\n        .mb-6 {\n          margin-bottom: 0.5rem !important;\n        }\n        .py-6 {\n          padding-top: 0.5rem !important;\n          padding-bottom: 0.5rem !important;\n        }\n      }\n    ",document.head.appendChild(t);const m=document.title;document.title=`Informe_${null==N?void 0:N.nombre}_${null==N?void 0:N.apellido}_${(new Date).toLocaleDateString("es-ES").replace(/\//g,"-")}`,window.print(),setTimeout(()=>{e.forEach(e=>e.style.display=""),i&&l&&(i.className=l),a&&o&&(a.className=o),r&&s&&(r.className=s),document.head.removeChild(t),document.title=m},1e3)},variant:"secondary",children:[l.jsxDEV("i",{className:"fas fa-file-pdf mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:429,columnNumber:13},void 0),"Generar PDF"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:425,columnNumber:11},void 0),l.jsxDEV(m,{onClick:()=>window.print(),variant:"outline",children:[l.jsxDEV("i",{className:"fas fa-print mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:436,columnNumber:13},void 0),"Imprimir"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:432,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:409,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:398,columnNumber:7},void 0),l.jsxDEV(s,{className:"mb-6 print-keep-together shadow-lg border-l-4 border-blue-500",children:[l.jsxDEV(n,{className:"bg-gradient-to-r from-blue-50 to-green-50 border-b-2 border-blue-200",children:l.jsxDEV("div",{className:"flex items-center",children:[l.jsxDEV("div",{className:`w-12 h-12 bg-${"masculino"===(null==N?void 0:N.genero)?"blue":"pink"}-100 rounded-full flex items-center justify-center mr-4`,children:l.jsxDEV("i",{className:`fas ${"masculino"===(null==N?void 0:N.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} text-xl`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:447,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:446,columnNumber:13},void 0),l.jsxDEV("h2",{className:"text-xl font-semibold text-blue-800",children:[l.jsxDEV("i",{className:"fas fa-user mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:450,columnNumber:15},void 0),"Información del Paciente"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:449,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:445,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:444,columnNumber:9},void 0),l.jsxDEV(t,{className:"bg-white",children:[l.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 print:hidden",children:[l.jsxDEV("div",{className:"space-y-4",children:[l.jsxDEV("div",{className:"bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400",children:[l.jsxDEV("p",{className:"text-xs font-medium text-blue-600 uppercase tracking-wide mb-1",children:"Nombre Completo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:461,columnNumber:17},void 0),l.jsxDEV("p",{className:"text-lg font-bold text-gray-900",children:[null==N?void 0:N.nombre," ",null==N?void 0:N.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:462,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:460,columnNumber:15},void 0),(null==N?void 0:N.documento)&&l.jsxDEV("div",{className:"bg-gray-50 p-4 rounded-lg border-l-4 border-gray-400",children:[l.jsxDEV("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide mb-1",children:"Documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:469,columnNumber:19},void 0),l.jsxDEV("p",{className:"text-base font-semibold text-gray-900",children:N.documento},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:470,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:468,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:459,columnNumber:13},void 0),l.jsxDEV("div",{className:"space-y-4",children:[(null==N?void 0:N.fecha_nacimiento)&&l.jsxDEV("div",{className:"bg-green-50 p-4 rounded-lg border-l-4 border-green-400",children:[l.jsxDEV("p",{className:"text-xs font-medium text-green-600 uppercase tracking-wide mb-1",children:"Fecha de Nacimiento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:479,columnNumber:19},void 0),l.jsxDEV("p",{className:"text-base font-semibold text-gray-900",children:new Date(N.fecha_nacimiento).toLocaleDateString("es-ES")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:480,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:478,columnNumber:17},void 0),l.jsxDEV("div",{className:"bg-purple-50 p-4 rounded-lg border-l-4 border-purple-400",children:[l.jsxDEV("p",{className:"text-xs font-medium text-purple-600 uppercase tracking-wide mb-1",children:"Edad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:487,columnNumber:17},void 0),l.jsxDEV("p",{className:"text-base font-semibold text-gray-900",children:[V(null==N?void 0:N.fecha_nacimiento)," años"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:488,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:486,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:476,columnNumber:13},void 0),l.jsxDEV("div",{className:"space-y-4",children:[l.jsxDEV("div",{className:`bg-${"masculino"===(null==N?void 0:N.genero)?"blue":"pink"}-50 p-4 rounded-lg border-l-4 border-${"masculino"===(null==N?void 0:N.genero)?"blue":"pink"}-400`,children:[l.jsxDEV("p",{className:`text-xs font-medium text-${"masculino"===(null==N?void 0:N.genero)?"blue":"pink"}-600 uppercase tracking-wide mb-1`,children:"Género"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:497,columnNumber:17},void 0),l.jsxDEV("p",{className:"text-base font-semibold text-gray-900 capitalize flex items-center",children:[l.jsxDEV("i",{className:`fas ${"masculino"===(null==N?void 0:N.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} mr-2`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:499,columnNumber:19},void 0),null==N?void 0:N.genero]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:498,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:496,columnNumber:15},void 0),(null==N?void 0:N.email)&&l.jsxDEV("div",{className:"bg-orange-50 p-4 rounded-lg border-l-4 border-orange-400",children:[l.jsxDEV("p",{className:"text-xs font-medium text-orange-600 uppercase tracking-wide mb-1",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:506,columnNumber:19},void 0),l.jsxDEV("p",{className:"text-sm font-semibold text-gray-900 break-all",children:N.email},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:507,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:505,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:495,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:457,columnNumber:11},void 0),l.jsxDEV("div",{className:"hidden print:block",children:l.jsxDEV("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[l.jsxDEV("div",{children:[l.jsxDEV("div",{className:"mb-2",children:[l.jsxDEV("span",{className:"font-medium text-blue-600",children:"Nombre:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:518,columnNumber:19},void 0),l.jsxDEV("span",{className:"ml-2 font-bold",children:[null==N?void 0:N.nombre," ",null==N?void 0:N.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:519,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:517,columnNumber:17},void 0),(null==N?void 0:N.documento)&&l.jsxDEV("div",{className:"mb-2",children:[l.jsxDEV("span",{className:"font-medium text-gray-600",children:"Documento:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:523,columnNumber:21},void 0),l.jsxDEV("span",{className:"ml-2",children:N.documento},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:524,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:522,columnNumber:19},void 0),(null==N?void 0:N.email)&&l.jsxDEV("div",{className:"mb-2",children:[l.jsxDEV("span",{className:"font-medium text-orange-600",children:"Email:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:529,columnNumber:21},void 0),l.jsxDEV("span",{className:"ml-2 text-xs",children:N.email},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:530,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:528,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:516,columnNumber:15},void 0),l.jsxDEV("div",{children:[(null==N?void 0:N.fecha_nacimiento)&&l.jsxDEV("div",{className:"mb-2",children:[l.jsxDEV("span",{className:"font-medium text-green-600",children:"Fecha de Nacimiento:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:537,columnNumber:21},void 0),l.jsxDEV("span",{className:"ml-2",children:new Date(N.fecha_nacimiento).toLocaleDateString("es-ES")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:538,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:536,columnNumber:19},void 0),l.jsxDEV("div",{className:"mb-2",children:[l.jsxDEV("span",{className:"font-medium text-purple-600",children:"Edad:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:542,columnNumber:19},void 0),l.jsxDEV("span",{className:"ml-2",children:[V(null==N?void 0:N.fecha_nacimiento)," años"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:543,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:541,columnNumber:17},void 0),l.jsxDEV("div",{className:"mb-2",children:[l.jsxDEV("span",{className:`font-medium text-${"masculino"===(null==N?void 0:N.genero)?"blue":"pink"}-600`,children:"Género:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:546,columnNumber:19},void 0),l.jsxDEV("span",{className:"ml-2 capitalize",children:[l.jsxDEV("i",{className:`fas ${"masculino"===(null==N?void 0:N.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} mr-1`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:548,columnNumber:21},void 0),null==N?void 0:N.genero]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:547,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:545,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:534,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:515,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:514,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:455,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:443,columnNumber:7},void 0),l.jsxDEV(s,{className:"mb-6",children:[l.jsxDEV(n,{className:"bg-green-50 border-b",children:l.jsxDEV("h2",{className:"text-xl font-semibold text-blue-800",children:[l.jsxDEV("i",{className:"fas fa-chart-pie mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:562,columnNumber:13},void 0),"Resumen General"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:561,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:560,columnNumber:9},void 0),l.jsxDEV(t,{className:"print-compact",children:[l.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 print-grid-horizontal gap-6 mb-6",children:[l.jsxDEV("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[l.jsxDEV("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:x.length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:570,columnNumber:15},void 0),l.jsxDEV("div",{className:"text-sm font-medium text-blue-700",children:"Tests Completados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:573,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:569,columnNumber:13},void 0),l.jsxDEV("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[l.jsxDEV("div",{className:"text-3xl font-bold text-green-600 mb-2",children:D||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:579,columnNumber:15},void 0),l.jsxDEV("div",{className:"text-sm font-medium text-green-700",children:"Percentil Promedio"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:582,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:578,columnNumber:13},void 0),l.jsxDEV("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[l.jsxDEV("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:x.filter(e=>e.percentil&&e.percentil>=75).length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:588,columnNumber:15},void 0),l.jsxDEV("div",{className:"text-sm font-medium text-purple-700",children:"Aptitudes Altas (≥75)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:591,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:587,columnNumber:13},void 0),l.jsxDEV("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[l.jsxDEV("div",{className:"text-3xl font-bold text-orange-600 mb-2",children:x.filter(e=>e.percentil&&e.percentil<=25).length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:597,columnNumber:15},void 0),l.jsxDEV("div",{className:"text-sm font-medium text-orange-700",children:"Aptitudes a Reforzar (≤25)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:600,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:596,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:568,columnNumber:11},void 0),l.jsxDEV("div",{className:"border-t pt-6",children:[l.jsxDEV("h3",{className:"text-lg font-semibold text-gray-800 mb-4 text-center",children:"Índices Especializados BAT-7"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:608,columnNumber:13},void 0),l.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 print-grid-horizontal gap-6",children:[l.jsxDEV("div",{className:"text-center p-4 bg-indigo-50 rounded-lg border-2 border-indigo-200",children:[l.jsxDEV("div",{className:"text-2xl font-bold text-indigo-600 mb-2",children:D||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:611,columnNumber:17},void 0),l.jsxDEV("div",{className:"text-sm font-medium text-indigo-700",children:"Total BAT"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:614,columnNumber:17},void 0),l.jsxDEV("div",{className:"text-xs text-indigo-600 mt-1",children:"Capacidad General"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:617,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:610,columnNumber:15},void 0),l.jsxDEV("div",{className:"text-center p-4 bg-cyan-50 rounded-lg border-2 border-cyan-200",children:[l.jsxDEV("div",{className:"text-2xl font-bold text-cyan-600 mb-2",children:S||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:623,columnNumber:17},void 0),l.jsxDEV("div",{className:"text-sm font-medium text-cyan-700",children:"Índice g"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:626,columnNumber:17},void 0),l.jsxDEV("div",{className:"text-xs text-cyan-600 mt-1",children:"Capacidad General"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:629,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:622,columnNumber:15},void 0),l.jsxDEV("div",{className:"text-center p-4 bg-teal-50 rounded-lg border-2 border-teal-200",children:[l.jsxDEV("div",{className:"text-2xl font-bold text-teal-600 mb-2",children:_||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:635,columnNumber:17},void 0),l.jsxDEV("div",{className:"text-sm font-medium text-teal-700",children:"Índice Gf"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:638,columnNumber:17},void 0),l.jsxDEV("div",{className:"text-xs text-teal-600 mt-1",children:"Inteligencia Fluida"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:641,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:634,columnNumber:15},void 0),l.jsxDEV("div",{className:"text-center p-4 bg-emerald-50 rounded-lg border-2 border-emerald-200",children:[l.jsxDEV("div",{className:"text-2xl font-bold text-emerald-600 mb-2",children:P||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:647,columnNumber:17},void 0),l.jsxDEV("div",{className:"text-sm font-medium text-emerald-700",children:"Índice Gc"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:650,columnNumber:17},void 0),l.jsxDEV("div",{className:"text-xs text-emerald-600 mt-1",children:"Inteligencia Cristalizada"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:653,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:646,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:609,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:607,columnNumber:11},void 0),x.length>0&&l.jsxDEV("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[l.jsxDEV("div",{children:[l.jsxDEV("span",{className:"text-gray-500",children:"Primera evaluación:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:663,columnNumber:17},void 0),l.jsxDEV("span",{className:"ml-2 font-medium",children:new Date(x[x.length-1].created_at).toLocaleDateString("es-ES")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:664,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:662,columnNumber:15},void 0),l.jsxDEV("div",{children:[l.jsxDEV("span",{className:"text-gray-500",children:"Última evaluación:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:669,columnNumber:17},void 0),l.jsxDEV("span",{className:"ml-2 font-medium",children:new Date(x[0].created_at).toLocaleDateString("es-ES")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:670,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:668,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:661,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:566,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:559,columnNumber:7},void 0),l.jsxDEV(s,{className:"mb-6 print-keep-together",children:[l.jsxDEV(n,{className:"bg-gray-50 border-b",children:l.jsxDEV("h2",{className:"text-xl font-semibold text-blue-800",children:[l.jsxDEV("i",{className:"fas fa-list-alt mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:683,columnNumber:13},void 0),"Resultados Detallados por Aptitud"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:682,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:681,columnNumber:9},void 0),l.jsxDEV(t,{className:"p-0",children:0===x.length?l.jsxDEV("div",{className:"py-8 text-center",children:[l.jsxDEV("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:690,columnNumber:15},void 0),l.jsxDEV("p",{className:"text-gray-500",children:"No hay resultados de tests disponibles para este paciente."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:691,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:689,columnNumber:13},void 0):l.jsxDEV("div",{className:"overflow-x-auto",children:l.jsxDEV("table",{className:"w-full",children:[l.jsxDEV("thead",{children:l.jsxDEV("tr",{className:"bg-slate-800 text-white",children:[l.jsxDEV("th",{className:"px-4 py-3 text-left font-semibold",children:"S"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:699,columnNumber:21},void 0),l.jsxDEV("th",{className:"px-4 py-3 text-left font-semibold",children:"APTITUDES EVALUADAS"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:700,columnNumber:21},void 0),l.jsxDEV("th",{className:"px-4 py-3 text-center font-semibold",children:"PD"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:701,columnNumber:21},void 0),l.jsxDEV("th",{className:"px-4 py-3 text-center font-semibold",children:"PC"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:702,columnNumber:21},void 0),l.jsxDEV("th",{className:"px-4 py-3 text-left font-semibold",children:"PERFIL DE LAS APTITUDES"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:703,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:698,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:697,columnNumber:17},void 0),l.jsxDEV("tbody",{children:x.map((e,i)=>{var a,r,o;null==(a=e.aptitudes)||a.codigo;const s=e.percentil||0;let t="bg-blue-500";return t=s>=80?"bg-orange-500":s>=60?"bg-blue-500":s>=40?"bg-blue-400":"bg-blue-300",l.jsxDEV("tr",{className:i%2==0?"bg-white":"bg-gray-50",children:[l.jsxDEV("td",{className:"px-4 py-3",children:l.jsxDEV("div",{className:"w-8 h-8 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:null==(r=e.aptitudes)?void 0:r.codigo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:721,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:720,columnNumber:25},void 0),l.jsxDEV("td",{className:"px-4 py-3",children:l.jsxDEV("div",{className:"font-medium text-gray-900",children:null==(o=e.aptitudes)?void 0:o.nombre},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:726,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:725,columnNumber:25},void 0),l.jsxDEV("td",{className:"px-4 py-3 text-center font-bold text-gray-900",children:e.puntaje_directo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:728,columnNumber:25},void 0),l.jsxDEV("td",{className:"px-4 py-3 text-center font-bold text-gray-900",children:e.percentil||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:731,columnNumber:25},void 0),l.jsxDEV("td",{className:"px-4 py-3",children:l.jsxDEV("div",{className:"flex items-center",children:l.jsxDEV("div",{className:"flex-1 bg-gray-200 rounded-full h-6 mr-3",children:l.jsxDEV("div",{className:`${t} h-6 rounded-full flex items-center justify-end pr-2`,style:{width:`${Math.max(s,5)}%`},children:l.jsxDEV("span",{className:"text-white text-xs font-bold",children:s>0?s:""},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:741,columnNumber:33},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:737,columnNumber:31},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:736,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:735,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:734,columnNumber:25},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:719,columnNumber:23},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:706,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:696,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:694,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:687,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:680,columnNumber:7},void 0),x.length>0&&l.jsxDEV(s,{className:"mb-6 print-keep-together",children:[l.jsxDEV(n,{className:"bg-purple-50 border-b",children:l.jsxDEV("h2",{className:"text-xl font-semibold text-blue-800",children:[l.jsxDEV("i",{className:"fas fa-brain mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:763,columnNumber:15},void 0),"Interpretación Cualitativa de Aptitudes"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:762,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:761,columnNumber:11},void 0),l.jsxDEV(t,{children:l.jsxDEV("div",{className:"space-y-6",children:x.map((e,i)=>{var a,r,o;const s=p(null==(a=e.aptitudes)?void 0:a.codigo,e.percentil||0);return s?l.jsxDEV("div",{className:"border-l-4 border-blue-500 pl-6 py-4 bg-gray-50 rounded-r-lg",children:[l.jsxDEV("div",{className:"flex items-center mb-3",children:[l.jsxDEV("div",{className:"w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold mr-3",children:null==(r=e.aptitudes)?void 0:r.codigo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:777,columnNumber:23},void 0),l.jsxDEV("div",{children:[l.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:[null==(o=e.aptitudes)?void 0:o.nombre," - Nivel ",s.nivel]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:781,columnNumber:25},void 0),l.jsxDEV("p",{className:"text-sm text-gray-600",children:["Percentil: ",e.percentil||"N/A"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:784,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:780,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:776,columnNumber:21},void 0),l.jsxDEV("div",{className:"mb-4",children:[l.jsxDEV("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:791,columnNumber:23},void 0),l.jsxDEV("p",{className:"text-gray-700 text-sm leading-relaxed",children:s.descripcion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:792,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:790,columnNumber:21},void 0),l.jsxDEV("div",{children:[l.jsxDEV("h4",{className:"font-medium text-gray-800 mb-2",children:["Características ","Alto"===s.nivel?"Fortalezas":"Áreas de Mejora",":"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:798,columnNumber:23},void 0),l.jsxDEV("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:s.caracteristicas.map((e,i)=>l.jsxDEV("li",{className:"leading-relaxed",children:e},i,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:803,columnNumber:27},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:801,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:797,columnNumber:21},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:775,columnNumber:19},void 0):null})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:768,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:767,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:760,columnNumber:9},void 0),l.jsxDEV(s,{className:"mb-6 print-keep-together",children:[l.jsxDEV(n,{className:"bg-indigo-50 border-b",children:l.jsxDEV("h2",{className:"text-xl font-semibold text-blue-800",children:[l.jsxDEV("i",{className:"fas fa-chart-line mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:821,columnNumber:13},void 0),"Interpretación Cualitativa de Índices Especializados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:820,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:819,columnNumber:9},void 0),l.jsxDEV(t,{children:l.jsxDEV("div",{className:"space-y-6",children:[D&&l.jsxDEV("div",{className:"border-l-4 border-indigo-500 pl-6 py-4 bg-indigo-50 rounded-r-lg",children:[l.jsxDEV("div",{className:"flex items-center mb-3",children:[l.jsxDEV("div",{className:"w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"g"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:831,columnNumber:19},void 0),l.jsxDEV("div",{children:[l.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice g - Capacidad General: ",G("g",S).nivel]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:835,columnNumber:21},void 0),l.jsxDEV("p",{className:"text-sm text-gray-600",children:["Percentil: ",S]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:838,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:834,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:830,columnNumber:17},void 0),l.jsxDEV("div",{className:"mb-4",children:[l.jsxDEV("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:845,columnNumber:19},void 0),l.jsxDEV("p",{className:"text-gray-700 text-sm leading-relaxed",children:G("g",S).descripcion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:846,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:844,columnNumber:17},void 0),l.jsxDEV("div",{children:[l.jsxDEV("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:852,columnNumber:19},void 0),l.jsxDEV("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:G("g",S).caracteristicas.map((e,i)=>l.jsxDEV("li",{className:"leading-relaxed",children:e},i,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:855,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:853,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:851,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:829,columnNumber:15},void 0),_&&l.jsxDEV("div",{className:"border-l-4 border-teal-500 pl-6 py-4 bg-teal-50 rounded-r-lg",children:[l.jsxDEV("div",{className:"flex items-center mb-3",children:[l.jsxDEV("div",{className:"w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"Gf"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:868,columnNumber:19},void 0),l.jsxDEV("div",{children:[l.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice Gf - Inteligencia Fluida: ",G("Gf",_).nivel]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:872,columnNumber:21},void 0),l.jsxDEV("p",{className:"text-sm text-gray-600",children:["Percentil: ",_," (basado en R + N)"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:875,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:871,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:867,columnNumber:17},void 0),l.jsxDEV("div",{className:"mb-4",children:[l.jsxDEV("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:882,columnNumber:19},void 0),l.jsxDEV("p",{className:"text-gray-700 text-sm leading-relaxed",children:G("Gf",_).descripcion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:883,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:881,columnNumber:17},void 0),l.jsxDEV("div",{children:[l.jsxDEV("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:889,columnNumber:19},void 0),l.jsxDEV("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:G("Gf",_).caracteristicas.map((e,i)=>l.jsxDEV("li",{className:"leading-relaxed",children:e},i,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:892,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:890,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:888,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:866,columnNumber:15},void 0),P&&l.jsxDEV("div",{className:"border-l-4 border-emerald-500 pl-6 py-4 bg-emerald-50 rounded-r-lg",children:[l.jsxDEV("div",{className:"flex items-center mb-3",children:[l.jsxDEV("div",{className:"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"Gc"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:905,columnNumber:19},void 0),l.jsxDEV("div",{children:[l.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice Gc - Inteligencia Cristalizada: ",G("Gc",P).nivel]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:909,columnNumber:21},void 0),l.jsxDEV("p",{className:"text-sm text-gray-600",children:["Percentil: ",P," (basado en V + O)"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:912,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:908,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:904,columnNumber:17},void 0),l.jsxDEV("div",{className:"mb-4",children:[l.jsxDEV("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:919,columnNumber:19},void 0),l.jsxDEV("p",{className:"text-gray-700 text-sm leading-relaxed",children:G("Gc",P).descripcion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:920,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:918,columnNumber:17},void 0),l.jsxDEV("div",{children:[l.jsxDEV("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:926,columnNumber:19},void 0),l.jsxDEV("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:G("Gc",P).caracteristicas.map((e,i)=>l.jsxDEV("li",{className:"leading-relaxed",children:e},i,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:929,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:927,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:925,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:903,columnNumber:15},void 0),_&&P&&Math.abs(_-P)>15&&l.jsxDEV("div",{className:"border-l-4 border-yellow-500 pl-6 py-4 bg-yellow-50 rounded-r-lg",children:[l.jsxDEV("div",{className:"flex items-center mb-3",children:[l.jsxDEV("div",{className:"w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:l.jsxDEV("i",{className:"fas fa-balance-scale"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:943,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:942,columnNumber:19},void 0),l.jsxDEV("div",{children:l.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:"Análisis de Disparidad entre Índices"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:946,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:945,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:941,columnNumber:17},void 0),l.jsxDEV("div",{children:l.jsxDEV("p",{className:"text-gray-700 text-sm leading-relaxed",children:["Se observa una diferencia significativa entre la Inteligencia Fluida (Gf: ",_,") y la Inteligencia Cristalizada (Gc: ",P,"). Esta disparidad sugiere un perfil cognitivo heterogéneo que requiere consideración especial en las recomendaciones de intervención.",_>P?" El evaluado muestra mayor fortaleza en razonamiento abstracto que en conocimientos adquiridos.":" El evaluado muestra mayor fortaleza en conocimientos adquiridos que en razonamiento abstracto."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:953,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:952,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:940,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:826,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:825,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:818,columnNumber:7},void 0),l.jsxDEV("div",{className:"text-center text-sm text-gray-500 border-t pt-4",children:[l.jsxDEV("p",{children:["Informe completo generado el ",(new Date).toLocaleDateString("es-ES")," a las ",(new Date).toLocaleTimeString("es-ES")]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:971,columnNumber:9},void 0),l.jsxDEV("p",{className:"mt-1",children:"Sistema de Evaluación Psicológica - BAT-7 - Panel de Administración"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:972,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:970,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/CompleteReport.jsx",lineNumber:396,columnNumber:5},void 0)};export{u as default};
