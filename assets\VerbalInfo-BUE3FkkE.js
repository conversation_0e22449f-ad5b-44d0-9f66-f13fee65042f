import{u as e,j as a}from"./vendor-CIyllXGj.js";import{C as i,b as l,a as r,B as n}from"./index-CrXvaDRr.js";const s=()=>{const s=e(),o=()=>{s("/test/instructions/verbal")};return a.jsxDEV("div",{className:"container mx-auto py-8 px-4 max-w-6xl",children:[a.jsxDEV("div",{className:"text-center mb-12",children:[a.jsxDEV("h1",{className:"text-3xl md:text-4xl font-bold text-gray-800 mb-4",children:"Test de Aptitud Verbal"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:20,columnNumber:9},void 0),a.jsxDEV("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Evalúa tu capacidad para comprender, razonar y comunicarte efectivamente a través del lenguaje"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:21,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:19,columnNumber:7},void 0),a.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12",children:[a.jsxDEV("div",{className:"lg:col-span-2",children:a.jsxDEV(i,{children:[a.jsxDEV(l,{children:a.jsxDEV("h2",{className:"text-2xl font-semibold text-gray-800",children:"¿Qué es la Aptitud Verbal?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:30,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:29,columnNumber:13},void 0),a.jsxDEV(r,{className:"space-y-6",children:[a.jsxDEV("p",{className:"text-gray-700",children:"La aptitud verbal es la capacidad para comprender ideas expresadas en palabras. Es una habilidad fundamental que implica el dominio del lenguaje, la comprensión de conceptos verbales, el razonamiento con palabras y la capacidad para expresar ideas de manera efectiva."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:33,columnNumber:15},void 0),a.jsxDEV("div",{children:[a.jsxDEV("h3",{className:"text-lg font-medium text-gray-800 mb-3",children:"La aptitud verbal incluye:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:38,columnNumber:17},void 0),a.jsxDEV("ul",{className:"space-y-2 text-gray-700",children:[a.jsxDEV("li",{className:"flex items-start",children:[a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsxDEV("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:42,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:41,columnNumber:21},void 0),a.jsxDEV("span",{children:[a.jsxDEV("strong",{children:"Comprensión verbal:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:44,columnNumber:27},void 0)," Entender el significado de palabras, frases y textos."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:44,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:40,columnNumber:19},void 0),a.jsxDEV("li",{className:"flex items-start",children:[a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsxDEV("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:48,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:47,columnNumber:21},void 0),a.jsxDEV("span",{children:[a.jsxDEV("strong",{children:"Razonamiento verbal:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:50,columnNumber:27},void 0)," Capacidad para analizar relaciones entre conceptos expresados en palabras."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:50,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:46,columnNumber:19},void 0),a.jsxDEV("li",{className:"flex items-start",children:[a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsxDEV("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:54,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:53,columnNumber:21},void 0),a.jsxDEV("span",{children:[a.jsxDEV("strong",{children:"Vocabulario:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:56,columnNumber:27},void 0)," Conocimiento de palabras, sus significados y usos adecuados."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:56,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:52,columnNumber:19},void 0),a.jsxDEV("li",{className:"flex items-start",children:[a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsxDEV("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:60,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:59,columnNumber:21},void 0),a.jsxDEV("span",{children:[a.jsxDEV("strong",{children:"Comprensión lectora:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:62,columnNumber:27},void 0)," Habilidad para entender textos completos, extraer información y hacer inferencias."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:62,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:58,columnNumber:19},void 0),a.jsxDEV("li",{className:"flex items-start",children:[a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsxDEV("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:66,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:65,columnNumber:21},void 0),a.jsxDEV("span",{children:[a.jsxDEV("strong",{children:"Expresión verbal:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:68,columnNumber:27},void 0)," Capacidad para comunicar ideas de manera clara y precisa."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:68,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:64,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:39,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:37,columnNumber:15},void 0),a.jsxDEV("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-100",children:[a.jsxDEV("h3",{className:"text-lg font-medium text-blue-800 mb-2",children:"¿Por qué es importante?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:74,columnNumber:17},void 0),a.jsxDEV("p",{className:"text-blue-700",children:"La aptitud verbal es esencial en casi todos los ámbitos académicos y profesionales. Permite expresar ideas con claridad, comprender instrucciones complejas, interpretar información, participar en discusiones productivas y resolver problemas que involucran el uso del lenguaje."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:75,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:73,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:32,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:28,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:27,columnNumber:9},void 0),a.jsxDEV("div",{children:[a.jsxDEV(i,{className:"mb-6 bg-gradient-to-br from-blue-50 to-indigo-50",children:a.jsxDEV(r,{className:"text-center p-6",children:[a.jsxDEV("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mx-auto mb-4",children:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:88,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:87,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:86,columnNumber:15},void 0),a.jsxDEV("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Test de Aptitud Verbal"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:91,columnNumber:15},void 0),a.jsxDEV("p",{className:"text-gray-600 mb-6",children:"Evalúa tus habilidades verbales con este test completo y recibe un análisis detallado de tus fortalezas y áreas de mejora."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:92,columnNumber:15},void 0),a.jsxDEV("div",{className:"space-y-3",children:[a.jsxDEV("div",{className:"flex items-center text-gray-700",children:[a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsxDEV("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:98,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:97,columnNumber:19},void 0),a.jsxDEV("span",{children:"Duración: 30 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:100,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:96,columnNumber:17},void 0),a.jsxDEV("div",{className:"flex items-center text-gray-700",children:[a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:[a.jsxDEV("path",{d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:104,columnNumber:21},void 0),a.jsxDEV("path",{fillRule:"evenodd",d:"M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:105,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:103,columnNumber:19},void 0),a.jsxDEV("span",{children:"20 preguntas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:107,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:102,columnNumber:17},void 0),a.jsxDEV("div",{className:"flex items-center text-gray-700",children:[a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsxDEV("path",{fillRule:"evenodd",d:"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:111,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:110,columnNumber:19},void 0),a.jsxDEV("span",{children:"Resultados inmediatos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:113,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:109,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:95,columnNumber:15},void 0),a.jsxDEV(n,{variant:"primary",className:"w-full mt-6",onClick:o,children:"Iniciar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:116,columnNumber:15},void 0),a.jsxDEV("button",{className:"mt-3 text-blue-600 hover:text-blue-800 text-sm font-medium",onClick:()=>{s("/test/verbal")},children:"Ver ejemplo del test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:123,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:85,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:84,columnNumber:11},void 0),a.jsxDEV(i,{children:[a.jsxDEV(l,{children:a.jsxDEV("h3",{className:"text-lg font-medium text-gray-800",children:"Relevancia Profesional"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:134,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:133,columnNumber:13},void 0),a.jsxDEV(r,{children:a.jsxDEV("ul",{className:"space-y-3",children:[a.jsxDEV("li",{className:"flex items-start",children:[a.jsxDEV("div",{className:"w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 flex-shrink-0 mt-0.5",children:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsxDEV("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:141,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:140,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:139,columnNumber:19},void 0),a.jsxDEV("div",{children:[a.jsxDEV("span",{className:"font-medium",children:"Derecho"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:145,columnNumber:21},void 0),a.jsxDEV("p",{className:"text-sm text-gray-600",children:"Esencial para interpretar textos legales, argumentar y redactar documentos."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:146,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:144,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:138,columnNumber:17},void 0),a.jsxDEV("li",{className:"flex items-start",children:[a.jsxDEV("div",{className:"w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 flex-shrink-0 mt-0.5",children:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsxDEV("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:152,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:151,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:150,columnNumber:19},void 0),a.jsxDEV("div",{children:[a.jsxDEV("span",{className:"font-medium",children:"Educación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:156,columnNumber:21},void 0),a.jsxDEV("p",{className:"text-sm text-gray-600",children:"Fundamental para transmitir conocimientos con claridad y precisión."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:157,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:155,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:149,columnNumber:17},void 0),a.jsxDEV("li",{className:"flex items-start",children:[a.jsxDEV("div",{className:"w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 flex-shrink-0 mt-0.5",children:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsxDEV("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:163,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:162,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:161,columnNumber:19},void 0),a.jsxDEV("div",{children:[a.jsxDEV("span",{className:"font-medium",children:"Comunicación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:167,columnNumber:21},void 0),a.jsxDEV("p",{className:"text-sm text-gray-600",children:"Indispensable para periodistas, escritores y profesionales del marketing."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:168,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:166,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:160,columnNumber:17},void 0),a.jsxDEV("li",{className:"flex items-start",children:[a.jsxDEV("div",{className:"w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3 flex-shrink-0 mt-0.5",children:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsxDEV("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:174,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:173,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:172,columnNumber:19},void 0),a.jsxDEV("div",{children:[a.jsxDEV("span",{className:"font-medium",children:"Psicología"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:178,columnNumber:21},void 0),a.jsxDEV("p",{className:"text-sm text-gray-600",children:"Importante para la comunicación terapéutica y el análisis de casos."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:179,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:177,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:171,columnNumber:17},void 0),a.jsxDEV("li",{className:"flex items-start",children:[a.jsxDEV("div",{className:"w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3 flex-shrink-0 mt-0.5",children:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsxDEV("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:185,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:184,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:183,columnNumber:19},void 0),a.jsxDEV("div",{children:[a.jsxDEV("span",{className:"font-medium",children:"Ciencias"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:189,columnNumber:21},void 0),a.jsxDEV("p",{className:"text-sm text-gray-600",children:"Útil para comunicar conceptos complejos y resultados de investigación."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:190,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:188,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:182,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:137,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:136,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:132,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:83,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:26,columnNumber:7},void 0),a.jsxDEV("div",{className:"mb-12",children:a.jsxDEV(i,{children:[a.jsxDEV(l,{children:a.jsxDEV("h2",{className:"text-2xl font-semibold text-gray-800",children:"Componentes del Test de Aptitud Verbal"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:202,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:201,columnNumber:11},void 0),a.jsxDEV(r,{children:a.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[a.jsxDEV("div",{className:"bg-white p-5 rounded-lg border border-gray-200 shadow-sm",children:[a.jsxDEV("div",{className:"w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center text-blue-600 mb-4",children:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:209,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:208,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:207,columnNumber:17},void 0),a.jsxDEV("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"Sinónimos y Antónimos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:212,columnNumber:17},void 0),a.jsxDEV("p",{className:"text-gray-600",children:"Evalúa tu vocabulario y comprensión de las relaciones entre palabras, midiendo tu capacidad para identificar significados similares u opuestos."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:213,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:206,columnNumber:15},void 0),a.jsxDEV("div",{className:"bg-white p-5 rounded-lg border border-gray-200 shadow-sm",children:[a.jsxDEV("div",{className:"w-12 h-12 rounded-lg bg-indigo-100 flex items-center justify-center text-indigo-600 mb-4",children:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:221,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:220,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:219,columnNumber:17},void 0),a.jsxDEV("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"Analogías Verbales"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:224,columnNumber:17},void 0),a.jsxDEV("p",{className:"text-gray-600",children:"Mide tu capacidad para identificar relaciones lógicas entre pares de palabras, evaluando tu pensamiento abstracto y razonamiento verbal."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:225,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:218,columnNumber:15},void 0),a.jsxDEV("div",{className:"bg-white p-5 rounded-lg border border-gray-200 shadow-sm",children:[a.jsxDEV("div",{className:"w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mb-4",children:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:233,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:232,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:231,columnNumber:17},void 0),a.jsxDEV("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"Comprensión Lectora"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:236,columnNumber:17},void 0),a.jsxDEV("p",{className:"text-gray-600",children:"Evalúa tu capacidad para entender textos, extraer información relevante, hacer inferencias y comprender ideas centrales y secundarias."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:237,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:230,columnNumber:15},void 0),a.jsxDEV("div",{className:"bg-white p-5 rounded-lg border border-gray-200 shadow-sm",children:[a.jsxDEV("div",{className:"w-12 h-12 rounded-lg bg-yellow-100 flex items-center justify-center text-yellow-600 mb-4",children:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:245,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:244,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:243,columnNumber:17},void 0),a.jsxDEV("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"Completar Oraciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:248,columnNumber:17},void 0),a.jsxDEV("p",{className:"text-gray-600",children:"Evalúa tu sentido del contexto lingüístico y la coherencia textual, midiendo tu capacidad para seleccionar palabras adecuadas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:249,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:242,columnNumber:15},void 0),a.jsxDEV("div",{className:"bg-white p-5 rounded-lg border border-gray-200 shadow-sm",children:[a.jsxDEV("div",{className:"w-12 h-12 rounded-lg bg-red-100 flex items-center justify-center text-red-600 mb-4",children:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:257,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:256,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:255,columnNumber:17},void 0),a.jsxDEV("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"Definiciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:260,columnNumber:17},void 0),a.jsxDEV("p",{className:"text-gray-600",children:"Evalúa tu precisión en la comprensión de términos y tu capacidad para identificar definiciones exactas de conceptos."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:261,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:254,columnNumber:15},void 0),a.jsxDEV("div",{className:"bg-white p-5 rounded-lg border border-gray-200 shadow-sm",children:[a.jsxDEV("div",{className:"w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center text-purple-600 mb-4",children:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:269,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:268,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:267,columnNumber:17},void 0),a.jsxDEV("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"Expresiones y Refranes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:272,columnNumber:17},void 0),a.jsxDEV("p",{className:"text-gray-600",children:"Mide tu comprensión del lenguaje figurado y las expresiones idiomáticas, evaluando tu familiaridad con el uso cultural del lenguaje."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:273,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:266,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:205,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:204,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:200,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:199,columnNumber:7},void 0),a.jsxDEV("div",{className:"text-center mb-8",children:[a.jsxDEV("h2",{className:"text-2xl font-semibold text-gray-800 mb-4",children:"¿Listo para evaluar tu aptitud verbal?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:283,columnNumber:9},void 0),a.jsxDEV("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto mb-6",children:"Descubre tus fortalezas y áreas de mejora en comprensión y razonamiento verbal con nuestro test completo."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:284,columnNumber:9},void 0),a.jsxDEV(n,{variant:"primary",size:"lg",className:"px-8",onClick:o,children:"Iniciar Test de Aptitud Verbal"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:287,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:282,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/landing/VerbalInfo.jsx",lineNumber:18,columnNumber:5},void 0)};export{s as default};
