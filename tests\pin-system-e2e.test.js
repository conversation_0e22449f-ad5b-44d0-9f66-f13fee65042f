/**
 * Pruebas End-to-End del Sistema de Pines
 * Verifica el flujo completo: Solicitar → Aprobar → Verificar Persistencia
 */

import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import PinRechargeRequestsAPI from '../src/api/endpoints/pinRechargeRequests.js';
import PinNotificationsAPI from '../src/api/endpoints/pinNotifications.js';
import { supabase } from '../src/api/supabaseClient.js';

// IDs de prueba
const TEST_PSYCHOLOGIST_ID = '74c8230e-6f01-4b5d-ae72-cf5ac61db33e';
const TEST_ADMIN_ID = 'admin-test-id';

describe('Sistema de Pines - Pruebas End-to-End', () => {
  let testRequestId = null;
  let testNotificationId = null;

  beforeAll(async () => {
    console.log('🧪 Iniciando pruebas E2E del sistema de pines...');
    
    // Limpiar datos de prueba previos
    await cleanupTestData();
  });

  afterAll(async () => {
    console.log('🧹 Limpiando datos de prueba...');
    await cleanupTestData();
  });

  describe('📝 Flujo de Solicitudes de Recarga', () => {
    
    test('1. Crear solicitud de recarga', async () => {
      console.log('📋 Creando solicitud de recarga...');
      
      const requestData = {
        psychologist_id: TEST_PSYCHOLOGIST_ID,
        requested_pins: 50,
        urgency: 'normal',
        reason: 'Prueba E2E - Necesito pines para testing',
        metadata: {
          test: true,
          created_by: 'e2e-test'
        }
      };

      const result = await PinRechargeRequestsAPI.createRequest(requestData);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.id).toBeDefined();
      expect(result.data.status).toBe('pending');
      expect(result.data.requested_pins).toBe(50);
      expect(result.data.urgency).toBe('normal');
      
      testRequestId = result.data.id;
      console.log(`✅ Solicitud creada con ID: ${testRequestId}`);
    });

    test('2. Verificar solicitud en base de datos', async () => {
      console.log('🔍 Verificando solicitud en base de datos...');
      
      const { data: request, error } = await supabase
        .from('pin_recharge_requests')
        .select('*')
        .eq('id', testRequestId)
        .single();

      expect(error).toBeNull();
      expect(request).toBeDefined();
      expect(request.id).toBe(testRequestId);
      expect(request.status).toBe('pending');
      expect(request.psychologist_id).toBe(TEST_PSYCHOLOGIST_ID);
      
      console.log('✅ Solicitud verificada en base de datos');
    });

    test('3. Obtener solicitudes pendientes', async () => {
      console.log('📋 Obteniendo solicitudes pendientes...');
      
      const result = await PinRechargeRequestsAPI.getRequests({
        status: 'pending',
        limit: 10
      });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      
      // Verificar que nuestra solicitud está en la lista
      const ourRequest = result.data.find(req => req.id === testRequestId);
      expect(ourRequest).toBeDefined();
      expect(ourRequest.status).toBe('pending');
      
      console.log(`✅ Encontradas ${result.data.length} solicitudes pendientes`);
    });

    test('4. Aprobar solicitud', async () => {
      console.log('✅ Aprobando solicitud...');
      
      const processData = {
        action: 'approve',
        admin_id: TEST_ADMIN_ID,
        admin_notes: 'Aprobado por prueba E2E',
        approved_pins: 45 // Aprobar menos de lo solicitado
      };

      const result = await PinRechargeRequestsAPI.processRequest(testRequestId, processData);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.status).toBe('approved');
      expect(result.data.approved_pins).toBe(45);
      expect(result.data.processed_by).toBe(TEST_ADMIN_ID);
      expect(result.data.admin_notes).toBe('Aprobado por prueba E2E');
      
      console.log('✅ Solicitud aprobada exitosamente');
    });

    test('5. Verificar aprobación en base de datos', async () => {
      console.log('🔍 Verificando aprobación en base de datos...');
      
      const { data: request, error } = await supabase
        .from('pin_recharge_requests')
        .select('*')
        .eq('id', testRequestId)
        .single();

      expect(error).toBeNull();
      expect(request).toBeDefined();
      expect(request.status).toBe('approved');
      expect(request.approved_pins).toBe(45);
      expect(request.processed_by).toBe(TEST_ADMIN_ID);
      expect(request.processed_at).toBeDefined();
      
      console.log('✅ Aprobación verificada en base de datos');
    });

    test('6. Verificar estadísticas de solicitudes', async () => {
      console.log('📊 Verificando estadísticas...');
      
      const result = await PinRechargeRequestsAPI.getRequestStats({
        psychologist_id: TEST_PSYCHOLOGIST_ID
      });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.total_requests).toBeGreaterThan(0);
      expect(result.data.by_status.approved).toBeGreaterThan(0);
      expect(result.data.pins.total_approved).toBeGreaterThan(0);
      
      console.log('✅ Estadísticas verificadas');
    });
  });

  describe('🔔 Flujo de Notificaciones', () => {
    
    test('1. Crear notificación', async () => {
      console.log('🔔 Creando notificación...');
      
      const notificationData = {
        user_id: TEST_PSYCHOLOGIST_ID,
        type: 'test_notification',
        title: 'Prueba E2E',
        message: 'Esta es una notificación de prueba end-to-end',
        severity: 'info',
        metadata: {
          test: true,
          created_by: 'e2e-test'
        }
      };

      const result = await PinNotificationsAPI.createNotification(notificationData);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.id).toBeDefined();
      expect(result.data.user_id).toBe(TEST_PSYCHOLOGIST_ID);
      expect(result.data.type).toBe('test_notification');
      expect(result.data.read).toBe(false);
      
      testNotificationId = result.data.id;
      console.log(`✅ Notificación creada con ID: ${testNotificationId}`);
    });

    test('2. Obtener notificaciones del usuario', async () => {
      console.log('📋 Obteniendo notificaciones del usuario...');
      
      const result = await PinNotificationsAPI.getUserNotifications(TEST_PSYCHOLOGIST_ID, {
        limit: 10
      });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.unread_count).toBeGreaterThan(0);
      
      // Verificar que nuestra notificación está en la lista
      const ourNotification = result.data.find(notif => notif.id === testNotificationId);
      expect(ourNotification).toBeDefined();
      expect(ourNotification.read).toBe(false);
      
      console.log(`✅ Encontradas ${result.data.length} notificaciones, ${result.unread_count} no leídas`);
    });

    test('3. Marcar notificación como leída', async () => {
      console.log('👁️ Marcando notificación como leída...');
      
      const result = await PinNotificationsAPI.markAsRead(testNotificationId, TEST_PSYCHOLOGIST_ID);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.read).toBe(true);
      expect(result.data.read_at).toBeDefined();
      
      console.log('✅ Notificación marcada como leída');
    });

    test('4. Verificar estadísticas de notificaciones', async () => {
      console.log('📊 Verificando estadísticas de notificaciones...');
      
      const result = await PinNotificationsAPI.getNotificationStats(TEST_PSYCHOLOGIST_ID);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.total).toBeGreaterThan(0);
      expect(result.data.read).toBeGreaterThan(0);
      expect(result.data.by_severity).toBeDefined();
      
      console.log('✅ Estadísticas de notificaciones verificadas');
    });

    test('5. Crear notificación de pines bajos', async () => {
      console.log('⚠️ Creando notificación de pines bajos...');
      
      const result = await PinNotificationsAPI.createLowPinNotification(TEST_PSYCHOLOGIST_ID, 2, 5);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.type).toBe('low_pins');
      expect(result.data.severity).toBe('warning');
      
      console.log('✅ Notificación de pines bajos creada');
    });
  });

  describe('🔄 Flujo Completo Integrado', () => {
    
    test('1. Flujo completo: Solicitar → Aprobar → Notificar', async () => {
      console.log('🔄 Ejecutando flujo completo integrado...');
      
      // 1. Crear solicitud
      const requestResult = await PinRechargeRequestsAPI.createRequest({
        psychologist_id: TEST_PSYCHOLOGIST_ID,
        requested_pins: 30,
        urgency: 'high',
        reason: 'Prueba de flujo completo E2E'
      });
      
      expect(requestResult.success).toBe(true);
      const requestId = requestResult.data.id;
      
      // 2. Aprobar solicitud
      const approveResult = await PinRechargeRequestsAPI.processRequest(requestId, {
        action: 'approve',
        admin_id: TEST_ADMIN_ID,
        approved_pins: 30
      });
      
      expect(approveResult.success).toBe(true);
      expect(approveResult.data.status).toBe('approved');
      
      // 3. Verificar que se crearon notificaciones automáticamente
      const notificationsResult = await PinNotificationsAPI.getUserNotifications(TEST_PSYCHOLOGIST_ID, {
        type: 'recharge_request_approved',
        limit: 5
      });
      
      expect(notificationsResult.success).toBe(true);
      
      console.log('✅ Flujo completo ejecutado exitosamente');
      
      // Limpiar esta solicitud de prueba
      await supabase
        .from('pin_recharge_requests')
        .delete()
        .eq('id', requestId);
    });
  });
});

// Funciones auxiliares
async function cleanupTestData() {
  try {
    // Limpiar solicitudes de prueba
    await supabase
      .from('pin_recharge_requests')
      .delete()
      .eq('psychologist_id', TEST_PSYCHOLOGIST_ID)
      .contains('metadata', { test: true });

    // Limpiar notificaciones de prueba
    await supabase
      .from('pin_notifications')
      .delete()
      .eq('user_id', TEST_PSYCHOLOGIST_ID)
      .contains('metadata', { test: true });

    console.log('🧹 Datos de prueba limpiados');
  } catch (error) {
    console.warn('⚠️ Error limpiando datos de prueba:', error.message);
  }
}
