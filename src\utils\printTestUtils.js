/**
 * Utilidades para probar y verificar la funcionalidad de impresión
 * Ayuda a diagnosticar problemas con react-to-print y estilos CSS
 */

/**
 * Verifica que los estilos de impresión estén aplicados correctamente
 * @param {HTMLElement} element - Elemento a verificar
 * @returns {Object} Resultado de la verificación
 */
export const verifyPrintStyles = (element) => {
  if (!element) {
    return { success: false, error: 'Elemento no encontrado' };
  }

  const issues = [];
  const computedStyle = window.getComputedStyle(element);

  // Verificar que el elemento tenga la clase print-content
  if (!element.classList.contains('print-content')) {
    issues.push('Falta la clase print-content en el elemento principal');
  }

  // Verificar que los estilos de fuente estén aplicados
  const fontFamily = computedStyle.fontFamily;
  if (!fontFamily.includes('Arial')) {
    issues.push(`Fuente incorrecta: ${fontFamily} (debería incluir Arial)`);
  }

  // Verificar elementos del header
  const header = element.querySelector('.print-header');
  if (header) {
    const headerStyle = window.getComputedStyle(header);
    const backgroundColor = headerStyle.backgroundColor;
    
    // Verificar que el header tenga el color correcto
    if (!backgroundColor.includes('rgb(30, 64, 175)') && !backgroundColor.includes('#1e40af')) {
      issues.push(`Color de header incorrecto: ${backgroundColor}`);
    }
  } else {
    issues.push('No se encontró el header con clase print-header');
  }

  // Verificar tarjetas de aptitudes
  const aptitudeCards = element.querySelectorAll('.print-aptitude-card');
  if (aptitudeCards.length === 0) {
    issues.push('No se encontraron tarjetas de aptitudes con clase print-aptitude-card');
  }

  // Verificar tarjetas de inteligencia
  const intelligenceCards = element.querySelectorAll('.print-intelligence-card');
  if (intelligenceCards.length === 0) {
    issues.push('No se encontraron tarjetas de inteligencia con clase print-intelligence-card');
  }

  return {
    success: issues.length === 0,
    issues: issues,
    elementInfo: {
      hasContent: element.children.length > 0,
      className: element.className,
      fontFamily: computedStyle.fontFamily,
      fontSize: computedStyle.fontSize,
      color: computedStyle.color,
      backgroundColor: computedStyle.backgroundColor
    }
  };
};

/**
 * Aplica estilos de impresión temporalmente para pruebas
 * @param {HTMLElement} element - Elemento al que aplicar estilos
 */
export const applyTestPrintStyles = (element) => {
  if (!element) return;

  // Aplicar estilos inline para asegurar que se mantengan en impresión
  element.style.fontFamily = 'Arial, sans-serif';
  element.style.fontSize = '12pt';
  element.style.lineHeight = '1.4';
  element.style.color = 'black';
  element.style.backgroundColor = 'white';
  element.style.webkitPrintColorAdjust = 'exact';
  element.style.printColorAdjust = 'exact';

  // Aplicar estilos al header
  const header = element.querySelector('.print-header');
  if (header) {
    header.style.backgroundColor = '#1e40af';
    header.style.color = 'white';
    header.style.webkitPrintColorAdjust = 'exact';
    header.style.printColorAdjust = 'exact';
    
    // Aplicar estilos a todos los elementos del header
    const headerElements = header.querySelectorAll('*');
    headerElements.forEach(el => {
      el.style.color = 'white';
    });
  }

  // Aplicar estilos a las tarjetas
  const cards = element.querySelectorAll('.print-aptitude-card, .print-intelligence-card');
  cards.forEach(card => {
    card.style.backgroundColor = 'white';
    card.style.border = '1px solid #e5e7eb';
    card.style.borderRadius = '8px';
    card.style.padding = '1.5rem';
    card.style.marginBottom = '1rem';
    card.style.pageBreakInside = 'avoid';
  });

  console.log('✅ Estilos de prueba aplicados al elemento de impresión');
};

/**
 * Genera un reporte de diagnóstico para problemas de impresión
 * @param {HTMLElement} element - Elemento a diagnosticar
 * @returns {string} Reporte de diagnóstico
 */
export const generatePrintDiagnostic = (element) => {
  const verification = verifyPrintStyles(element);
  
  let report = '🖨️ DIAGNÓSTICO DE IMPRESIÓN\n';
  report += '================================\n\n';
  
  if (verification.success) {
    report += '✅ ESTADO: Todos los estilos están correctamente aplicados\n\n';
  } else {
    report += '❌ ESTADO: Se encontraron problemas\n\n';
    report += 'PROBLEMAS DETECTADOS:\n';
    verification.issues.forEach((issue, index) => {
      report += `${index + 1}. ${issue}\n`;
    });
    report += '\n';
  }
  
  report += 'INFORMACIÓN DEL ELEMENTO:\n';
  report += `- Tiene contenido: ${verification.elementInfo.hasContent}\n`;
  report += `- Clases CSS: ${verification.elementInfo.className}\n`;
  report += `- Fuente: ${verification.elementInfo.fontFamily}\n`;
  report += `- Tamaño de fuente: ${verification.elementInfo.fontSize}\n`;
  report += `- Color de texto: ${verification.elementInfo.color}\n`;
  report += `- Color de fondo: ${verification.elementInfo.backgroundColor}\n\n`;
  
  report += 'RECOMENDACIONES:\n';
  if (verification.issues.length > 0) {
    report += '1. Verificar que el CSS de impresión esté cargado correctamente\n';
    report += '2. Asegurar que las clases print-* estén aplicadas\n';
    report += '3. Verificar que react-to-print esté configurado con pageStyle\n';
    report += '4. Comprobar que -webkit-print-color-adjust: exact esté aplicado\n';
  } else {
    report += '1. Los estilos están correctos, el problema puede ser del navegador\n';
    report += '2. Probar en diferentes navegadores (Chrome, Firefox, Safari)\n';
    report += '3. Verificar configuración de impresión del navegador\n';
  }
  
  return report;
};

/**
 * Función de utilidad para probar la impresión en desarrollo
 * @param {HTMLElement} printElement - Elemento a imprimir
 */
export const testPrintInDevelopment = (printElement) => {
  console.group('🖨️ PRUEBA DE IMPRESIÓN');
  
  const diagnostic = generatePrintDiagnostic(printElement);
  console.log(diagnostic);
  
  // Aplicar estilos de prueba
  applyTestPrintStyles(printElement);
  
  // Mostrar información adicional
  console.log('📊 Elemento de impresión:', printElement);
  console.log('🎨 Estilos computados:', window.getComputedStyle(printElement));
  
  console.groupEnd();
};
