import { createClient } from '@supabase/supabase-js';

// Configuración directa de Supabase para el script
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Versión simplificada del servicio para testing
class TestPinControlService {
  static async getPsychologistsWithPinInfo() {
    const { data: psychologists, error } = await supabase
      .from('psicologos')
      .select(`
        id,
        nombre,
        apellido,
        email,
        psychologist_usage_control!left (
          total_uses,
          used_uses,
          is_unlimited,
          is_active,
          plan_type,
          updated_at
        )
      `)
      .order('nombre', { ascending: true });

    if (error) throw error;

    return (psychologists || []).map(psy => {
      const control = psy.psychologist_usage_control?.[0];
      const hasControl = !!control;

      return {
        id: psy.id,
        nombre: psy.nombre,
        apellido: psy.apellido,
        email: psy.email || 'Sin email',
        fullName: `${psy.nombre} ${psy.apellido}`.trim(),
        hasControl,
        totalPins: hasControl ? (control.total_uses || 0) : 0,
        usedPins: hasControl ? (control.used_uses || 0) : 0,
        remainingPins: hasControl ? Math.max(0, (control.total_uses || 0) - (control.used_uses || 0)) : 0,
        isUnlimited: hasControl ? (control.is_unlimited || false) : false,
        isActive: hasControl ? (control.is_active || false) : false,
        status: hasControl ? (control.is_active ? 'Activo' : 'Inactivo') : 'Sin pines',
        statusColor: hasControl ? (control.is_active ? 'green' : 'yellow') : 'red'
      };
    });
  }

  static async assignPins(psychologistId, pins, isUnlimited = false) {
    // Verificar si ya existe un control para este psicólogo
    const { data: existingControl } = await supabase
      .from('psychologist_usage_control')
      .select('id')
      .eq('psychologist_id', psychologistId)
      .single();

    const controlData = {
      psychologist_id: psychologistId,
      total_uses: isUnlimited ? 999999 : pins,
      used_uses: 0,
      // remaining_uses es una columna generada, no la incluimos
      is_unlimited: isUnlimited,
      is_active: true,
      plan_type: isUnlimited ? 'unlimited' : 'assigned',
      updated_at: new Date().toISOString()
    };

    let data, error;

    if (existingControl) {
      // Actualizar registro existente
      const result = await supabase
        .from('psychologist_usage_control')
        .update(controlData)
        .eq('psychologist_id', psychologistId)
        .select()
        .single();

      data = result.data;
      error = result.error;
    } else {
      // Crear nuevo registro
      const result = await supabase
        .from('psychologist_usage_control')
        .insert([controlData])
        .select()
        .single();

      data = result.data;
      error = result.error;
    }

    if (error) throw error;

    return {
      success: true,
      message: `Pines ${isUnlimited ? 'ilimitados' : pins} asignados exitosamente`,
      data
    };
  }

  static async consumePins(psychologistId, pinsToConsume = 1, metadata = {}) {
    const { data: control, error: controlError } = await supabase
      .from('psychologist_usage_control')
      .select('*')
      .eq('psychologist_id', psychologistId)
      .eq('is_active', true)
      .single();

    if (controlError || !control) {
      throw new Error('No se encontró control de pines activo');
    }

    if (!control.is_unlimited) {
      const remainingPins = (control.total_uses || 0) - (control.used_uses || 0);
      if (remainingPins < pinsToConsume) {
        throw new Error(`Pines insuficientes. Disponibles: ${remainingPins}`);
      }
    }

    const newUsedPins = (control.used_uses || 0) + pinsToConsume;
    const newRemainingPins = control.is_unlimited ? 999999 : Math.max(0, (control.total_uses || 0) - newUsedPins);

    const { error: updateError } = await supabase
      .from('psychologist_usage_control')
      .update({
        used_uses: newUsedPins,
        // remaining_uses es calculada automáticamente
        updated_at: new Date().toISOString()
      })
      .eq('psychologist_id', psychologistId);

    if (updateError) throw updateError;

    return {
      success: true,
      remainingPins: newRemainingPins,
      isUnlimited: control.is_unlimited
    };
  }

  static async getSystemStats() {
    const { data: psychStats } = await supabase
      .from('psychologist_usage_control')
      .select('total_uses, used_uses, is_unlimited, is_active');

    const stats = {
      totalPsychologists: psychStats?.length || 0,
      activePsychologists: psychStats?.filter(p => p.is_active)?.length || 0,
      totalPinsAssigned: psychStats?.reduce((sum, p) => sum + (p.total_uses || 0), 0) || 0,
      totalPinsUsed: psychStats?.reduce((sum, p) => sum + (p.used_uses || 0), 0) || 0,
      unlimitedPlans: psychStats?.filter(p => p.is_unlimited)?.length || 0,
      recentConsumption: 0
    };

    stats.remainingPins = stats.totalPinsAssigned - stats.totalPinsUsed;
    stats.usagePercentage = stats.totalPinsAssigned > 0 ?
      Math.round((stats.totalPinsUsed / stats.totalPinsAssigned) * 100) : 0;

    return stats;
  }

  static async subtractPins(psychologistId, pinsToSubtract) {
    const { data: control, error: controlError } = await supabase
      .from('psychologist_usage_control')
      .select('*')
      .eq('psychologist_id', psychologistId)
      .eq('is_active', true)
      .single();

    if (controlError || !control) {
      throw new Error('No se encontró control de pines activo');
    }

    if (control.is_unlimited) {
      throw new Error('No se pueden restar pines de un plan ilimitado');
    }

    const newTotalPins = Math.max(0, (control.total_uses || 0) - pinsToSubtract);
    const currentUsedPins = control.used_uses || 0;
    const newUsedPins = Math.min(currentUsedPins, newTotalPins);

    const { error: updateError } = await supabase
      .from('psychologist_usage_control')
      .update({
        total_uses: newTotalPins,
        used_uses: newUsedPins,
        updated_at: new Date().toISOString()
      })
      .eq('psychologist_id', psychologistId);

    if (updateError) throw updateError;

    return {
      success: true,
      message: `${pinsToSubtract} pines restados exitosamente`,
      data: {
        previousTotal: control.total_uses,
        newTotal: newTotalPins,
        usedPins: newUsedPins,
        remainingPins: Math.max(0, newTotalPins - newUsedPins)
      }
    };
  }

  static async removePsychologistPins(psychologistId) {
    const { data: psychologist, error: psychError } = await supabase
      .from('psicologos')
      .select('id, nombre, apellido')
      .eq('id', psychologistId)
      .single();

    if (psychError || !psychologist) {
      throw new Error('Psicólogo no encontrado');
    }

    const { data: control } = await supabase
      .from('psychologist_usage_control')
      .select('*')
      .eq('psychologist_id', psychologistId)
      .single();

    const { error: deleteError } = await supabase
      .from('psychologist_usage_control')
      .delete()
      .eq('psychologist_id', psychologistId);

    if (deleteError) throw deleteError;

    return {
      success: true,
      message: `Control de pines eliminado para ${psychologist.nombre} ${psychologist.apellido}`,
      data: {
        psychologist: psychologist,
        removedControl: control
      }
    };
  }
}

/**
 * Script para probar el sistema completo de pines
 */

async function testPinSystem() {
  console.log('🧪 Iniciando pruebas del sistema de pines...\n');

  try {
    // 1. Obtener psicólogos
    console.log('1️⃣ Obteniendo lista de psicólogos...');
    const psychologists = await TestPinControlService.getPsychologistsWithPinInfo();
    
    console.log(`✅ ${psychologists.length} psicólogos encontrados:`);
    psychologists.forEach((psy, index) => {
      console.log(`   ${index + 1}. ${psy.fullName} (${psy.email})`);
      console.log(`      Pines: ${psy.isUnlimited ? '∞' : psy.totalPins} total, ${psy.usedPins} usados, ${psy.isUnlimited ? '∞' : psy.remainingPins} restantes`);
      console.log(`      Estado: ${psy.status} | Control: ${psy.hasControl ? 'Sí' : 'No'}`);
    });

    if (psychologists.length === 0) {
      console.log('❌ No hay psicólogos para probar');
      return;
    }

    // 2. Asignar pines a un psicólogo de prueba
    const testPsychologist = psychologists[0];
    console.log(`\n2️⃣ Asignando 50 pines a ${testPsychologist.fullName}...`);
    
    const assignResult = await TestPinControlService.assignPins(
      testPsychologist.id,
      50,
      false
    );

    console.log('✅ Resultado de asignación:', assignResult.message);

    // 3. Verificar la asignación
    console.log('\n3️⃣ Verificando asignación...');
    const updatedPsychologists = await TestPinControlService.getPsychologistsWithPinInfo();
    const updatedPsy = updatedPsychologists.find(p => p.id === testPsychologist.id);
    
    if (updatedPsy) {
      console.log(`✅ ${updatedPsy.fullName} ahora tiene:`);
      console.log(`   - Pines totales: ${updatedPsy.totalPins}`);
      console.log(`   - Pines usados: ${updatedPsy.usedPins}`);
      console.log(`   - Pines restantes: ${updatedPsy.remainingPins}`);
      console.log(`   - Estado: ${updatedPsy.status}`);
    }

    // 4. Consumir algunos pines
    console.log(`\n4️⃣ Consumiendo 5 pines de ${testPsychologist.fullName}...`);
    
    const consumeResult = await TestPinControlService.consumePins(
      testPsychologist.id,
      5,
      {
        test_action: 'script_test',
        consumed_by: 'test_script'
      }
    );

    console.log('✅ Pines consumidos exitosamente');
    console.log(`   - Pines restantes: ${consumeResult.remainingPins}`);
    console.log(`   - Es ilimitado: ${consumeResult.isUnlimited ? 'Sí' : 'No'}`);

    // 5. Verificar el consumo
    console.log('\n5️⃣ Verificando consumo...');
    const finalPsychologists = await TestPinControlService.getPsychologistsWithPinInfo();
    const finalPsy = finalPsychologists.find(p => p.id === testPsychologist.id);
    
    if (finalPsy) {
      console.log(`✅ ${finalPsy.fullName} después del consumo:`);
      console.log(`   - Pines totales: ${finalPsy.totalPins}`);
      console.log(`   - Pines usados: ${finalPsy.usedPins}`);
      console.log(`   - Pines restantes: ${finalPsy.remainingPins}`);
      console.log(`   - Estado: ${finalPsy.status}`);
    }

    // 6. Obtener estadísticas del sistema
    console.log('\n6️⃣ Obteniendo estadísticas del sistema...');
    const stats = await TestPinControlService.getSystemStats();
    
    console.log('📊 Estadísticas del sistema:');
    console.log(`   - Total psicólogos: ${stats.totalPsychologists}`);
    console.log(`   - Psicólogos activos: ${stats.activePsychologists}`);
    console.log(`   - Pines asignados: ${stats.totalPinsAssigned}`);
    console.log(`   - Pines usados: ${stats.totalPinsUsed}`);
    console.log(`   - Pines restantes: ${stats.remainingPins}`);
    console.log(`   - Planes ilimitados: ${stats.unlimitedPlans}`);
    console.log(`   - Porcentaje de uso: ${stats.usagePercentage}%`);
    console.log(`   - Consumo reciente (30 días): ${stats.recentConsumption}`);

    // 7. Probar plan ilimitado
    if (psychologists.length > 1) {
      const testPsy2 = psychologists[1];
      console.log(`\n7️⃣ Asignando plan ilimitado a ${testPsy2.fullName}...`);
      
      const unlimitedResult = await TestPinControlService.assignPins(
        testPsy2.id,
        0,
        true
      );

      console.log('✅ Plan ilimitado asignado:', unlimitedResult.message);

      // Verificar plan ilimitado
      const unlimitedPsychologists = await TestPinControlService.getPsychologistsWithPinInfo();
      const unlimitedPsy = unlimitedPsychologists.find(p => p.id === testPsy2.id);
      
      if (unlimitedPsy) {
        console.log(`✅ ${unlimitedPsy.fullName} con plan ilimitado:`);
        console.log(`   - Es ilimitado: ${unlimitedPsy.isUnlimited ? 'Sí' : 'No'}`);
        console.log(`   - Pines mostrados: ${unlimitedPsy.isUnlimited ? '∞' : unlimitedPsy.totalPins}`);
        console.log(`   - Estado: ${unlimitedPsy.status}`);
      }
    }

    // 8. Probar nuevas funcionalidades: Resta de pines
    console.log('\n8️⃣ Probando resta de pines...');
    const psychsForSubtract = await TestPinControlService.getPsychologistsWithPinInfo();
    const psychWithPinsToSubtract = psychsForSubtract.find(p => p.hasControl && !p.isUnlimited && p.totalPins > 10);

    if (psychWithPinsToSubtract) {
      console.log(`Restando 10 pines de ${psychWithPinsToSubtract.fullName}...`);
      console.log(`Estado antes: ${psychWithPinsToSubtract.totalPins} total, ${psychWithPinsToSubtract.usedPins} usados`);

      const subtractResult = await TestPinControlService.subtractPins(
        psychWithPinsToSubtract.id,
        10
      );

      console.log('✅ Resultado de resta:', subtractResult.message);
      console.log(`   Pines anteriores: ${subtractResult.data.previousTotal}`);
      console.log(`   Pines nuevos: ${subtractResult.data.newTotal}`);

      // Verificar la resta
      const verifyAfterSubtract = await TestPinControlService.getPsychologistsWithPinInfo();
      const verifiedPsy = verifyAfterSubtract.find(p => p.id === psychWithPinsToSubtract.id);

      if (verifiedPsy) {
        console.log(`✅ ${verifiedPsy.fullName} después de la resta:`);
        console.log(`   - Pines totales: ${verifiedPsy.totalPins}`);
        console.log(`   - Pines usados: ${verifiedPsy.usedPins}`);
        console.log(`   - Pines restantes: ${verifiedPsy.remainingPins}`);
      }
    }

    // 9. Probar eliminación de psicólogo
    console.log('\n9️⃣ Probando eliminación de control de pines...');
    const psychsForRemoval = await TestPinControlService.getPsychologistsWithPinInfo();
    const psychToRemove = psychsForRemoval.find(p => p.hasControl && !p.isUnlimited);

    if (psychToRemove) {
      console.log(`Eliminando control de pines de ${psychToRemove.fullName}...`);

      const removeResult = await TestPinControlService.removePsychologistPins(
        psychToRemove.id
      );

      console.log('✅ Resultado de eliminación:', removeResult.message);

      // Verificar la eliminación
      const verifyAfterRemoval = await TestPinControlService.getPsychologistsWithPinInfo();
      const verifiedRemovedPsy = verifyAfterRemoval.find(p => p.id === psychToRemove.id);

      if (verifiedRemovedPsy) {
        console.log(`✅ ${verifiedRemovedPsy.fullName} después de la eliminación:`);
        console.log(`   - Tiene control: ${verifiedRemovedPsy.hasControl ? 'Sí' : 'No'}`);
        console.log(`   - Estado: ${verifiedRemovedPsy.status}`);
      }
    }

    console.log('\n🎉 ¡Todas las pruebas completadas exitosamente!');
    console.log('\n📋 Resumen:');
    console.log('✅ Carga de psicólogos: OK');
    console.log('✅ Asignación de pines: OK');
    console.log('✅ Consumo de pines: OK');
    console.log('✅ Estadísticas del sistema: OK');
    console.log('✅ Plan ilimitado: OK');
    console.log('✅ Resta de pines: OK');
    console.log('✅ Eliminación de control: OK');

  } catch (error) {
    console.error('❌ Error en las pruebas:', error);
    console.error('Stack:', error.stack);
  }
}

// Ejecutar pruebas
testPinSystem();
