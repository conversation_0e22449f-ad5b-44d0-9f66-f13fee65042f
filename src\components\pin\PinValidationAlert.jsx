import React from 'react';
import { 
  FaExclamationTriangle, 
  FaTimesCircle, 
  FaCheckCircle, 
  FaInfoCircle,
  FaCoins,
  FaUsers,
  FaFileAlt
} from 'react-icons/fa';

/**
 * Componente para mostrar alertas de validación de pines
 * Muestra información detallada sobre el estado de validación
 */
const PinValidationAlert = ({ 
  validationResult, 
  showDetails = true, 
  className = '',
  onClose = null 
}) => {
  if (!validationResult) return null;

  const {
    canProceed,
    severity,
    userMessage,
    details,
    suggestions = [],
    batchInfo
  } = validationResult;

  // Configuración de estilos por severidad
  const severityConfig = {
    success: {
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      textColor: 'text-green-800',
      iconColor: 'text-green-600',
      icon: FaCheckCircle
    },
    warning: {
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      textColor: 'text-yellow-800',
      iconColor: 'text-yellow-600',
      icon: FaExclamationTriangle
    },
    critical: {
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      textColor: 'text-red-800',
      iconColor: 'text-red-600',
      icon: FaExclamationTriangle
    },
    error: {
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      textColor: 'text-red-800',
      iconColor: 'text-red-600',
      icon: FaTimesCircle
    },
    info: {
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      textColor: 'text-blue-800',
      iconColor: 'text-blue-600',
      icon: FaInfoCircle
    }
  };

  const config = severityConfig[severity] || severityConfig.info;
  const IconComponent = config.icon;

  return (
    <div className={`${config.bgColor} ${config.borderColor} border rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <IconComponent className={`h-5 w-5 ${config.iconColor}`} />
        </div>
        
        <div className="ml-3 flex-1">
          {/* Mensaje principal */}
          <div className="flex items-center justify-between">
            <h3 className={`text-sm font-medium ${config.textColor}`}>
              {canProceed ? 'Operación Permitida' : 'Operación Bloqueada'}
            </h3>
            {onClose && (
              <button
                onClick={onClose}
                className={`${config.textColor} hover:opacity-75`}
              >
                <FaTimesCircle className="h-4 w-4" />
              </button>
            )}
          </div>
          
          <div className={`mt-1 text-sm ${config.textColor}`}>
            {userMessage}
          </div>

          {/* Detalles adicionales */}
          {showDetails && details && (
            <div className="mt-3 space-y-2">
              {/* Información de pines */}
              {!details.isUnlimited && (
                <div className="flex items-center space-x-4 text-xs">
                  <div className="flex items-center">
                    <FaCoins className={`h-3 w-3 ${config.iconColor} mr-1`} />
                    <span>Disponibles: {details.currentPins || 0}</span>
                  </div>
                  <div className="flex items-center">
                    <FaFileAlt className={`h-3 w-3 ${config.iconColor} mr-1`} />
                    <span>Requeridos: {details.requiredPins || 1}</span>
                  </div>
                  {details.pinsAfter !== undefined && (
                    <div className="flex items-center">
                      <FaCoins className={`h-3 w-3 ${config.iconColor} mr-1`} />
                      <span>Quedarán: {details.pinsAfter}</span>
                    </div>
                  )}
                </div>
              )}

              {/* Información de lote */}
              {batchInfo && (
                <div className="flex items-center text-xs">
                  <FaUsers className={`h-3 w-3 ${config.iconColor} mr-1`} />
                  <span>Generación en lote: {batchInfo.patientCount} pacientes</span>
                </div>
              )}

              {/* Plan ilimitado */}
              {details.isUnlimited && (
                <div className="flex items-center text-xs">
                  <FaCheckCircle className="h-3 w-3 text-green-600 mr-1" />
                  <span>Plan ilimitado activo</span>
                </div>
              )}
            </div>
          )}

          {/* Sugerencias */}
          {suggestions.length > 0 && (
            <div className="mt-3">
              <h4 className={`text-xs font-medium ${config.textColor} mb-1`}>
                Sugerencias:
              </h4>
              <ul className={`text-xs ${config.textColor} space-y-1`}>
                {suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start">
                    <span className="mr-1">•</span>
                    <span>{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * Componente compacto para mostrar solo el estado básico
 */
export const PinValidationBadge = ({ validationResult, className = '' }) => {
  if (!validationResult) return null;

  const { canProceed, severity, details } = validationResult;

  const badgeConfig = {
    success: 'bg-green-100 text-green-800 border-green-200',
    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    critical: 'bg-red-100 text-red-800 border-red-200',
    error: 'bg-red-100 text-red-800 border-red-200',
    info: 'bg-blue-100 text-blue-800 border-blue-200'
  };

  const config = badgeConfig[severity] || badgeConfig.info;

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config} ${className}`}>
      <FaCoins className="h-3 w-3 mr-1" />
      {details?.isUnlimited ? 'Ilimitado' : `${details?.currentPins || 0} pines`}
      {!canProceed && (
        <FaTimesCircle className="h-3 w-3 ml-1" />
      )}
    </span>
  );
};

/**
 * Componente para mostrar confirmación antes de proceder
 */
export const PinValidationConfirmation = ({ 
  validationResult, 
  onConfirm, 
  onCancel,
  confirmText = "Continuar",
  cancelText = "Cancelar"
}) => {
  if (!validationResult || !validationResult.canProceed) return null;

  const { severity, userMessage, details, hasWarning } = validationResult;

  // Solo mostrar confirmación si hay advertencias
  if (!hasWarning && severity === 'success') {
    return null;
  }

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div className="flex items-start">
        <FaExclamationTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-yellow-800">
            Confirmar Operación
          </h3>
          <p className="mt-1 text-sm text-yellow-700">
            {userMessage}
          </p>
          
          {details && !details.isUnlimited && (
            <p className="mt-2 text-xs text-yellow-600">
              Después de esta operación quedarán {details.pinsAfter} pines disponibles.
            </p>
          )}
          
          <div className="mt-4 flex space-x-3">
            <button
              onClick={onConfirm}
              className="bg-yellow-600 text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-yellow-700 transition-colors"
            >
              {confirmText}
            </button>
            <button
              onClick={onCancel}
              className="bg-gray-300 text-gray-700 px-3 py-1.5 rounded text-sm font-medium hover:bg-gray-400 transition-colors"
            >
              {cancelText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PinValidationAlert;
