import fs from 'fs';
import path from 'path';

/**
 * Script para cambiar todos los imports de AuthContext a SimpleAuthContextTemp
 */

function updateAuthImports(dir, results = []) {
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // Ignorar node_modules, .git y scripts
      if (!file.startsWith('.') && file !== 'node_modules' && file !== 'scripts') {
        updateAuthImports(filePath, results);
      }
    } else if (file.endsWith('.jsx') || file.endsWith('.js')) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Buscar imports de AuthContext
        if (content.includes("from '../../context/AuthContext'") || 
            content.includes("from '../context/AuthContext'")) {
          
          let updatedContent = content;
          
          // Reemplazar imports
          updatedContent = updatedContent.replace(
            /from '\.\.\/\.\.\/context\/AuthContext'/g,
            "from '../../context/SimpleAuthContextTemp'"
          );
          
          updatedContent = updatedContent.replace(
            /from '\.\.\/context\/AuthContext'/g,
            "from '../context/SimpleAuthContextTemp'"
          );
          
          // Solo escribir si hubo cambios
          if (updatedContent !== content) {
            fs.writeFileSync(filePath, updatedContent, 'utf8');
            results.push(filePath);
            console.log(`✅ Actualizado: ${filePath}`);
          }
        }
      } catch (error) {
        console.error(`❌ Error procesando ${filePath}:`, error.message);
      }
    }
  }

  return results;
}

function main() {
  console.log('🔄 Cambiando imports de AuthContext a SimpleAuthContextTemp...\n');

  const srcDir = path.join(process.cwd(), 'src');
  const results = updateAuthImports(srcDir);

  if (results.length === 0) {
    console.log('ℹ️ No se encontraron archivos para actualizar');
  } else {
    console.log(`\n🎉 Se actualizaron ${results.length} archivos:`);
    results.forEach((file, index) => {
      console.log(`${index + 1}. ${file}`);
    });
  }

  console.log('\n✨ Proceso completado. La aplicación ahora usa el contexto temporal.');
}

main();
