import{r as e,j as t,V as s,L as a}from"./vendor-BqMjyOVw.js";import{C as r,a as n,b as l,B as c,s as i,c as d}from"./index-Bdl1jgS_.js";import{u as o}from"./useToast-Du4vz6Q_.js";import{P as x}from"./PageHeader-DzW86ZOX.js";const m=()=>{const[m,u]=e.useState([]),[p,h]=e.useState(!0),{showToast:f}=o();e.useEffect(()=>{var e,t,s;e=null,t=null,s=function*(){try{h(!0);const{data:e,error:t}=yield i.from("resultados").select("\n            id,\n            puntaje_directo,\n            percentil,\n            errores,\n            tiempo_segundos,\n            concentracion,\n            created_at,\n            pacientes:paciente_id (\n              id,\n              nombre,\n              apellido,\n              documento,\n              genero\n            ),\n            aptitudes:aptitud_id (\n              codigo,\n              nombre,\n              descripcion\n            )\n          ").not("puntaje_directo","is",null).not("percentil","is",null).order("created_at",{ascending:!1});if(t)return void f("Error al cargar los resultados","error");const s=new Map;let a=0;e.forEach(e=>{var t,r;if(!(null==(t=e.pacientes)?void 0:t.id)||!(null==(r=e.aptitudes)?void 0:r.codigo)||null===e.puntaje_directo||null===e.percentil)return;const n=`${e.pacientes.id}-${e.aptitudes.codigo}`,l=s.get(n);if(l){const t=new Date(l.created_at);new Date(e.created_at)>t?(s.set(n,e),a++):a++}else s.set(n,e)});const r=Array.from(s.values()).reduce((e,t)=>{var s,a,r;const n=null==(s=t.pacientes)?void 0:s.id;if(!n)return e;e[n]||(e[n]={paciente:t.pacientes,resultados:[],fechaUltimaEvaluacion:t.created_at});const l=t.percentil?d.obtenerInterpretacionPC(t.percentil):{nivel:"Pendiente",color:"text-gray-600",bg:"bg-gray-100"};return e[n].resultados.push({id:t.id,test:(null==(a=t.aptitudes)?void 0:a.codigo)||"N/A",testName:(null==(r=t.aptitudes)?void 0:r.nombre)||"Test Desconocido",puntajePD:t.puntaje_directo||0,puntajePC:t.percentil||"N/A",errores:t.errores||0,tiempo:t.tiempo_segundos?`${Math.round(t.tiempo_segundos/60)}:${String(t.tiempo_segundos%60).padStart(2,"0")}`:"N/A",concentracion:t.concentracion?`${t.concentracion.toFixed(1)}%`:"N/A",fecha:new Date(t.created_at).toLocaleDateString("es-ES"),interpretacion:l.nivel,interpretacionColor:l.color,interpretacionBg:l.bg}),new Date(t.created_at)>new Date(e[n].fechaUltimaEvaluacion)&&(e[n].fechaUltimaEvaluacion=t.created_at),e},{}),n=Object.values(r).sort((e,t)=>new Date(t.fechaUltimaEvaluacion)-new Date(e.fechaUltimaEvaluacion));u(n),h(!1)}catch(e){f("Error al cargar los resultados","error"),h(!1)}},new Promise((a,r)=>{var n=e=>{try{c(s.next(e))}catch(t){r(t)}},l=e=>{try{c(s.throw(e))}catch(t){r(t)}},c=e=>e.done?a(e.value):Promise.resolve(e.value).then(n,l);c((s=s.apply(e,t)).next())})},[f]);const j=e=>({V:"fas fa-comments",E:"fas fa-cube",A:"fas fa-eye",R:"fas fa-puzzle-piece",N:"fas fa-calculator",M:"fas fa-cogs",O:"fas fa-spell-check"}[e]||"fas fa-clipboard-list");return t.jsxs("div",{children:[t.jsx(x,{title:"Resultados de Tests",subtitle:`${m.length} paciente${1!==m.length?"s":""} con resultados disponibles`,icon:s}),t.jsx("div",{className:"container mx-auto py-6",children:p?t.jsxs("div",{className:"py-16 text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),t.jsx("p",{className:"text-gray-500",children:"Cargando resultados..."})]}):t.jsx(t.Fragment,{children:0===m.length?t.jsx(r,{children:t.jsx(n,{children:t.jsxs("div",{className:"py-8 text-center",children:[t.jsx("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 mb-4"}),t.jsx("p",{className:"text-gray-500",children:"No hay resultados de tests disponibles."}),t.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Los resultados aparecerán aquí una vez que se completen los tests."})]})})}):t.jsx("div",{className:"space-y-6",children:m.map((e,s)=>{var i,d,o,x;return t.jsxs(r,{className:"overflow-hidden shadow-lg border border-blue-200",children:[t.jsx(l,{className:"bg-gradient-to-r from-blue-500 to-blue-600 border-b border-blue-300",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx("div",{className:"w-14 h-14 rounded-full flex items-center justify-center text-white text-xl font-bold mr-4 shadow-lg "+(null==(i=e.paciente)||i.genero,"bg-white bg-opacity-20 border-2 border-white border-opacity-30"),children:t.jsx("i",{className:"fas "+("masculino"===(null==(d=e.paciente)?void 0:d.genero)?"fa-mars text-blue-100":"fa-venus text-pink-200")})}),t.jsxs("div",{children:[t.jsxs("h3",{className:"text-xl font-bold text-white",children:[null==(o=e.paciente)?void 0:o.nombre," ",null==(x=e.paciente)?void 0:x.apellido]}),t.jsxs("p",{className:"text-blue-100 text-sm",children:[t.jsx("i",{className:"fas fa-clipboard-check mr-1"}),e.resultados.length," test",1!==e.resultados.length?"s":""," completado",1!==e.resultados.length?"s":""]})]})]}),t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsxs("div",{className:"text-right",children:[t.jsx("p",{className:"text-blue-100 text-sm",children:"Última evaluación"}),t.jsx("p",{className:"text-white font-semibold",children:new Date(e.fechaUltimaEvaluacion).toLocaleDateString("es-ES")})]}),t.jsxs(c,{as:a,to:`/student/informe-completo/${e.paciente.id}`,className:"bg-white text-blue-600 hover:bg-blue-50 border-white shadow-lg",size:"sm",children:[t.jsx("i",{className:"fas fa-file-alt mr-2"}),"Ver Informe Completo"]})]})]})}),t.jsx(n,{className:"p-0",children:t.jsx("div",{className:"overflow-x-auto",children:t.jsxs("table",{className:"w-full",children:[t.jsx("thead",{className:"bg-blue-50 border-b border-blue-200",children:t.jsxs("tr",{children:[t.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Test"}),t.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Puntaje PD"}),t.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Puntaje PC"}),t.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Errores"}),t.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Tiempo"}),t.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Fecha Test"})]})}),t.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.resultados.map((e,s)=>{return t.jsxs("tr",{className:s%2==0?"bg-white":"bg-gray-50",children:[t.jsx("td",{className:"px-4 py-4 text-center",children:t.jsxs("div",{className:"flex items-center justify-center",children:[t.jsx("div",{className:`w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-2 ${a=e.test,{V:"text-blue-600",E:"text-indigo-600",A:"text-red-600",R:"text-amber-600",N:"text-teal-600",M:"text-slate-600",O:"text-green-600"}[a]||"text-gray-600"}`,children:t.jsx("i",{className:j(e.test)})}),t.jsxs("div",{className:"text-left",children:[t.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.test}),t.jsx("div",{className:"text-xs text-gray-500",children:e.testName})]})]})}),t.jsx("td",{className:"px-4 py-4 text-center",children:t.jsx("span",{className:"text-lg font-bold text-orange-600 bg-orange-100 px-3 py-1 rounded-full",children:e.puntajePD})}),t.jsx("td",{className:"px-4 py-4 text-center",children:"N/A"!==e.puntajePC?t.jsxs("div",{className:"flex flex-col items-center",children:[t.jsx("span",{className:"text-lg font-bold text-blue-600 bg-blue-100 px-3 py-1 rounded-full mb-1",children:e.puntajePC}),t.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${e.interpretacionBg} ${e.interpretacionColor}`,children:e.interpretacion})]}):t.jsx("span",{className:"text-gray-400 text-sm",children:"Pendiente"})}),t.jsx("td",{className:"px-4 py-4 text-center",children:t.jsx("span",{className:"text-sm font-medium text-gray-700",children:e.errores})}),t.jsx("td",{className:"px-4 py-4 text-center",children:t.jsx("span",{className:"text-sm font-medium text-gray-700",children:e.tiempo})}),t.jsx("td",{className:"px-4 py-4 text-center",children:t.jsx("span",{className:"text-sm text-gray-500",children:e.fecha})})]},e.id);var a})})]})})})]},e.paciente.id)})})})})]})};export{m as default};
