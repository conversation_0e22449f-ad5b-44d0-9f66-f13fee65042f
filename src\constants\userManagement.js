/**
 * Constantes para el módulo de gestión de usuarios
 * Evita "magic strings" y centraliza la configuración
 */

// Roles de usuario
export const USER_ROLES = {
  ADMIN: 'administrador',
  PSYCHOLOGIST: 'psicologo',
  PATIENT: 'paciente'
};

// Estados de usuario
export const USER_STATUS = {
  ACTIVE: true,
  INACTIVE: false
};

// Opciones de filtros
export const FILTER_OPTIONS = {
  ALL: 'all',
  ACTIVE: 'active',
  INACTIVE: 'inactive'
};

// Configuración de paginación
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [5, 10, 25, 50],
  MAX_PAGE_BUTTONS: 5
};

// Configuración de búsqueda
export const SEARCH_CONFIG = {
  DEBOUNCE_DELAY: 300, // ms
  MIN_SEARCH_LENGTH: 2
};

// Mensajes de error personalizados
export const ERROR_MESSAGES = {
  EMAIL_ALREADY_EXISTS: 'Ya existe un usuario con ese email',
  REQUIRED_FIELDS: 'Por favor completa todos los campos requeridos',
  PASSWORD_TOO_SHORT: 'La contraseña debe tener al menos 6 caracteres',
  PASSWORDS_DONT_MATCH: 'Las contraseñas no coinciden',
  LOAD_USERS_ERROR: 'Error al cargar usuarios',
  CREATE_USER_ERROR: 'Error al crear usuario',
  UPDATE_USER_ERROR: 'Error al actualizar usuario',
  DELETE_USER_ERROR: 'Error al eliminar usuario',
  TOGGLE_STATUS_ERROR: 'Error al cambiar estado del usuario',
  NETWORK_ERROR: 'Error de conexión. Verifica tu conexión a internet.',
  UNAUTHORIZED: 'No tienes permisos para realizar esta acción',
  USER_NOT_FOUND: 'Usuario no encontrado'
};

// Mensajes de éxito
export const SUCCESS_MESSAGES = {
  USER_CREATED: 'Usuario creado exitosamente',
  USER_UPDATED: 'Usuario actualizado exitosamente',
  USER_DELETED: 'Usuario eliminado exitosamente',
  USER_ACTIVATED: 'Usuario activado exitosamente',
  USER_DEACTIVATED: 'Usuario desactivado exitosamente',
  PASSWORD_RESET: 'Contraseña restablecida exitosamente'
};

// Configuración de validación
export const VALIDATION_CONFIG = {
  MIN_PASSWORD_LENGTH: 6,
  MAX_NAME_LENGTH: 50,
  MAX_EMAIL_LENGTH: 100,
  MAX_DOCUMENT_LENGTH: 20
};

// Etiquetas de roles para mostrar en UI
export const ROLE_LABELS = {
  [USER_ROLES.ADMIN]: 'Administrador',
  [USER_ROLES.PSYCHOLOGIST]: 'Psicólogo',
  [USER_ROLES.PATIENT]: 'Paciente'
};

// Colores para badges de roles
export const ROLE_COLORS = {
  [USER_ROLES.ADMIN]: 'bg-purple-100 text-purple-800',
  [USER_ROLES.PSYCHOLOGIST]: 'bg-indigo-100 text-indigo-800',
  [USER_ROLES.PATIENT]: 'bg-teal-100 text-teal-800'
};

// Colores para badges de estado
export const STATUS_COLORS = {
  [USER_STATUS.ACTIVE]: 'bg-green-100 text-green-800',
  [USER_STATUS.INACTIVE]: 'bg-red-100 text-red-800'
};

// Configuración de columnas de la tabla
export const TABLE_COLUMNS = {
  USER: 'Usuario',
  EMAIL: 'Email',
  DOCUMENT: 'Documento',
  ROLE: 'Rol',
  STATUS: 'Estado',
  ACTIONS: 'Acciones'
};

// Configuración de acciones
export const USER_ACTIONS = {
  CREATE: 'create',
  EDIT: 'edit',
  DELETE: 'delete',
  TOGGLE_STATUS: 'toggle_status',
  RESET_PASSWORD: 'reset_password'
};

// Configuración de modales
export const MODAL_CONFIG = {
  CREATE_TITLE: 'Crear Nuevo Usuario',
  EDIT_TITLE: 'Editar Usuario',
  DELETE_TITLE: 'Confirmar Eliminación',
  CONFIRM_DELETE_MESSAGE: '¿Estás seguro de que quieres eliminar este usuario?',
  CONFIRM_DELETE_SUBTITLE: 'Esta acción no se puede deshacer.',
  CONFIRM_ACTIVATE_MESSAGE: '¿Estás seguro de que quieres activar este usuario?',
  CONFIRM_DEACTIVATE_MESSAGE: '¿Estás seguro de que quieres desactivar este usuario?'
};

// Configuración de formularios
export const FORM_CONFIG = {
  INITIAL_USER_DATA: {
    email: '',
    password: '',
    confirmPassword: '',
    nombre: '',
    apellido: '',
    documento: '',
    rol: USER_ROLES.PATIENT,
    activo: USER_STATUS.ACTIVE
  }
};

// Configuración de ordenamiento
export const SORT_CONFIG = {
  DEFAULT_COLUMN: 'fecha_creacion',
  DEFAULT_ORDER: 'desc',
  OPTIONS: {
    NAME_ASC: { column: 'nombre', order: 'asc', label: 'Nombre A-Z' },
    NAME_DESC: { column: 'nombre', order: 'desc', label: 'Nombre Z-A' },
    EMAIL_ASC: { column: 'email', order: 'asc', label: 'Email A-Z' },
    EMAIL_DESC: { column: 'email', order: 'desc', label: 'Email Z-A' },
    CREATED_ASC: { column: 'fecha_creacion', order: 'asc', label: 'Más antiguos' },
    CREATED_DESC: { column: 'fecha_creacion', order: 'desc', label: 'Más recientes' },
    ROLE_ASC: { column: 'rol', order: 'asc', label: 'Rol A-Z' },
    ROLE_DESC: { column: 'rol', order: 'desc', label: 'Rol Z-A' }
  }
};

// Configuración de exportación
export const EXPORT_CONFIG = {
  FILENAME_PREFIX: 'usuarios_bat7',
  DATE_FORMAT: 'YYYY-MM-DD',
  SUPPORTED_FORMATS: ['csv', 'xlsx'],
  CSV_HEADERS: [
    'Nombre',
    'Apellido', 
    'Email',
    'Documento',
    'Rol',
    'Estado',
    'Fecha Creación',
    'Último Acceso'
  ]
};

export default {
  USER_ROLES,
  USER_STATUS,
  FILTER_OPTIONS,
  PAGINATION_CONFIG,
  SEARCH_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  VALIDATION_CONFIG,
  ROLE_LABELS,
  ROLE_COLORS,
  STATUS_COLORS,
  TABLE_COLUMNS,
  USER_ACTIONS,
  MODAL_CONFIG,
  FORM_CONFIG,
  SORT_CONFIG,
  EXPORT_CONFIG
};
