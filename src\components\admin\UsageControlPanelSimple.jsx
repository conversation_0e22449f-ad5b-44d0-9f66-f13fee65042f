import React, { useState, useEffect } from 'react';
import { 
  FaChartBar, 
  FaUsers, 
  FaCoins, 
  FaExclamationTriangle,
  FaBell,
  FaHistory,
  FaPlus,
  FaCheck,
  FaTimes,
  FaEye
} from 'react-icons/fa';
import { supabase } from '../../api/supabaseClient';
import PinRechargeRequestsAPI from '../../api/endpoints/pinRechargeRequests';
import PinNotificationsAPI from '../../api/endpoints/pinNotifications';

const UsageControlPanelSimple = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [requests, setRequests] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [stats, setStats] = useState({
    totalRequests: 0,
    pendingRequests: 0,
    totalNotifications: 0,
    unreadNotifications: 0
  });

  // Pestañas disponibles
  const tabs = [
    {
      id: 'overview',
      name: 'Resumen',
      icon: FaChartBar,
      description: 'Vista general del sistema de pines'
    },
    {
      id: 'requests',
      name: 'Solicitudes de Recarga',
      icon: FaCoins,
      description: 'Gestionar solicitudes de recarga de pines'
    },
    {
      id: 'notifications',
      name: 'Centro de Notificaciones',
      icon: FaBell,
      description: 'Gestionar notificaciones del sistema'
    }
  ];

  // Cargar datos iniciales
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // Cargar solicitudes
      const requestsResult = await PinRechargeRequestsAPI.getRequests({
        limit: 10
      });
      
      if (requestsResult.success) {
        setRequests(requestsResult.data || []);
      }

      // Cargar notificaciones (usando un ID de prueba)
      const notificationsResult = await PinNotificationsAPI.getUserNotifications(
        '74c8230e-6f01-4b5d-ae72-cf5ac61db33e',
        { limit: 10 }
      );
      
      if (notificationsResult.success) {
        setNotifications(notificationsResult.data || []);
      }

      // Calcular estadísticas
      setStats({
        totalRequests: requestsResult.data?.length || 0,
        pendingRequests: requestsResult.data?.filter(r => r.status === 'pending').length || 0,
        totalNotifications: notificationsResult.data?.length || 0,
        unreadNotifications: notificationsResult.unread_count || 0
      });

    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApproveRequest = async (requestId) => {
    try {
      setLoading(true);
      const result = await PinRechargeRequestsAPI.processRequest(requestId, {
        action: 'approve',
        admin_id: 'admin-test-id',
        admin_notes: 'Aprobado desde panel de control'
      });

      if (result.success) {
        await loadData(); // Recargar datos
        alert('Solicitud aprobada exitosamente');
      } else {
        alert('Error al aprobar solicitud: ' + result.error);
      }
    } catch (error) {
      console.error('Error approving request:', error);
      alert('Error al aprobar solicitud');
    } finally {
      setLoading(false);
    }
  };

  const handleRejectRequest = async (requestId) => {
    try {
      setLoading(true);
      const result = await PinRechargeRequestsAPI.processRequest(requestId, {
        action: 'reject',
        admin_id: 'admin-test-id',
        admin_notes: 'Rechazado desde panel de control'
      });

      if (result.success) {
        await loadData(); // Recargar datos
        alert('Solicitud rechazada');
      } else {
        alert('Error al rechazar solicitud: ' + result.error);
      }
    } catch (error) {
      console.error('Error rejecting request:', error);
      alert('Error al rechazar solicitud');
    } finally {
      setLoading(false);
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">Total Solicitudes</p>
              <p className="text-3xl font-bold text-gray-900">{stats.totalRequests}</p>
            </div>
            <FaCoins className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">Solicitudes Pendientes</p>
              <p className="text-3xl font-bold text-orange-600">{stats.pendingRequests}</p>
            </div>
            <FaExclamationTriangle className="w-8 h-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">Total Notificaciones</p>
              <p className="text-3xl font-bold text-gray-900">{stats.totalNotifications}</p>
            </div>
            <FaBell className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">No Leídas</p>
              <p className="text-3xl font-bold text-red-600">{stats.unreadNotifications}</p>
            </div>
            <FaBell className="w-8 h-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Estado del sistema eliminado - No es necesario según solicitud del usuario */}
    </div>
  );

  const renderRequests = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Solicitudes de Recarga</h3>
          <p className="text-sm text-gray-600 mt-1">Gestiona las solicitudes de recarga de pines</p>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Psicólogo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pines Solicitados
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Urgencia
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fecha
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {requests.map((request) => (
                <tr key={request.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {request.psychologist_id || 'N/A'}
                    </div>
                    <div className="text-sm text-gray-500">
                      ID: {request.psychologist_id?.slice(0, 8)}...
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {request.requested_pins}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      request.urgency === 'urgent' ? 'bg-red-100 text-red-800' :
                      request.urgency === 'high' ? 'bg-orange-100 text-orange-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {request.urgency}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      request.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      request.status === 'approved' ? 'bg-green-100 text-green-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {request.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(request.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {request.status === 'pending' && (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleApproveRequest(request.id)}
                          disabled={loading}
                          className="text-green-600 hover:text-green-900 disabled:opacity-50"
                        >
                          <FaCheck className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleRejectRequest(request.id)}
                          disabled={loading}
                          className="text-red-600 hover:text-red-900 disabled:opacity-50"
                        >
                          <FaTimes className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderNotifications = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Centro de Notificaciones</h3>
          <p className="text-sm text-gray-600 mt-1">Gestiona las notificaciones del sistema</p>
        </div>
        
        <div className="p-6">
          {notifications.length === 0 ? (
            <div className="text-center py-8">
              <FaBell className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No hay notificaciones</h3>
              <p className="mt-1 text-sm text-gray-500">
                Las notificaciones aparecerán aquí cuando se generen.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 rounded-lg border ${
                    notification.read ? 'bg-gray-50 border-gray-200' : 'bg-blue-50 border-blue-200'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">
                        {notification.title}
                      </h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        {new Date(notification.created_at).toLocaleString()}
                      </p>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      notification.severity === 'success' ? 'bg-green-100 text-green-800' :
                      notification.severity === 'warning' ? 'bg-orange-100 text-orange-800' :
                      notification.severity === 'error' ? 'bg-red-100 text-red-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {notification.severity}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'requests':
        return renderRequests();
      case 'notifications':
        return renderNotifications();
      default:
        return <div>Pestaña no encontrada</div>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Navegación de pestañas */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Contenido de la pestaña activa */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        renderTabContent()
      )}
    </div>
  );
};

export default UsageControlPanelSimple;
