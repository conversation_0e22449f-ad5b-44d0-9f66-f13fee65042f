/**
 * Script para agregar la columna updated_at a la tabla usuarios
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function addUpdatedAtColumn() {
  console.log('🔧 AGREGANDO COLUMNA UPDATED_AT');
  console.log('==============================\n');
  
  try {
    // Verificar estructura actual
    console.log('📋 Verificando estructura actual...');
    const { data: users, error } = await supabase
      .from('usuarios')
      .select('*')
      .limit(1);

    if (error) {
      console.error('❌ Error obteniendo usuarios:', error);
      return;
    }

    if (users.length > 0) {
      const columns = Object.keys(users[0]);
      console.log('📋 Columnas actuales:', columns);
      
      if (columns.includes('updated_at')) {
        console.log('✅ La columna updated_at ya existe');
        return;
      }
    }

    console.log('\n💡 SOLUCIÓN MANUAL REQUERIDA:');
    console.log('La columna updated_at no existe y no se puede agregar automáticamente.');
    console.log('Por favor, ejecuta manualmente en el Editor SQL de Supabase:');
    console.log('');
    console.log('ALTER TABLE public.usuarios ADD COLUMN updated_at TIMESTAMPTZ DEFAULT now();');
    console.log('');
    console.log('O ejecuta el archivo completo: src/sql/add_updated_at_column.sql');

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

addUpdatedAtColumn();
