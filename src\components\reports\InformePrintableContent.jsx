/**
 * @file InformePrintableContent.jsx
 * @description Componente separado para el contenido imprimible del informe
 * Diseñado específicamente para ser usado con react-to-print
 */

import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import { FaUser, FaCalendar, FaIdCard, FaChartBar, FaBrain, FaGraduationCap, FaClipboardList, FaFileAlt, FaCogs, FaBirthdayCake } from 'react-icons/fa';
import {
  HiOutlineChatAlt2,
  HiOutlineCube,
  HiOutlineEye,
  HiOutlineCalculator,
  HiOutlineCog,
  HiOutlineBookOpen
} from 'react-icons/hi';
import { FaBullseye, FaPuzzlePiece } from 'react-icons/fa';
import { formatDateLong } from '../../utils/dateUtils';
import { useInterpretacionesReales } from '../../hooks/useInterpretacionesReales';
import {
  INDICES_INTELIGENCIA,
  getLevelConfigByPercentile,
  getLevelNameByPercentile,
} from '../../pages/reports/constants/reportConstants';
import {
  INTERPRETACIONES_INDICES,
  obtenerNivel,
} from '../../pages/reports/constants/interpretacionesCualitativas';

/**
 * Componente de contenido imprimible del informe
 * Usa forwardRef para que react-to-print pueda referenciar el componente
 */
const InformePrintableContent = forwardRef(({
  patientData,
  normalizedResultsData,
  intelligenceIndices,
  generalStats,
  getAptitudeConfig,
  getPercentilLevel,
  reportContent,
  interpretacionesLoading, // Prop para el estado de carga
  obtenerInterpretacion,   // Prop para la función de obtención
}, ref) => {

  // Mapeo de iconos para usar en el render
  const iconMap = {
    'HiOutlineChatAlt2': HiOutlineChatAlt2,
    'HiOutlineCube': HiOutlineCube,
    'HiOutlineEye': HiOutlineEye,
    'HiOutlineCalculator': HiOutlineCalculator,
    'HiOutlineCog': HiOutlineCog,
    'HiOutlineBookOpen': HiOutlineBookOpen,
    'FaBullseye': FaBullseye,
    'FaPuzzlePiece': FaPuzzlePiece
  };

  return (
    <div
      ref={ref}
      className="print-content bg-white p-8"
    >
      {/* Header - Professional Clinical Design */}
      <div className="bg-white rounded-lg shadow-lg border-l-4 border-blue-600 px-6 py-6 mb-8 text-center">
        <div className="flex items-center justify-center mb-2">
          <FaFileAlt className="text-blue-600 text-2xl mr-3" />
          <h1 className="text-3xl font-bold text-gray-900 tracking-tight">
            INFORME PSICOLÓGICO
          </h1>
        </div>
        <p className="text-blue-700 text-lg font-medium">
          Batería de Aptitudes Diferenciales y Generales - BAT-7
        </p>
        <p className="text-gray-600 text-sm mt-1">
          Evaluación Psicológica Integral
        </p>
      </div>

      {/* 1. Información del Evaluado */}
      <div className="print-patient-info bg-white rounded-lg shadow-sm overflow-hidden mb-8">
        <div
          className="print-header bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4"
        >
          <div className="flex items-center">
            <FaUser className="text-white text-2xl mr-3" />
            <div>
              <h2 className="text-2xl font-bold text-white">Información del Evaluado</h2>
              <p className="text-blue-100 text-sm">Datos personales y demográficos</p>
            </div>
          </div>
        </div>

        <div className="p-6 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Datos Personales */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center mb-4">
                <FaIdCard className="text-blue-600 text-lg mr-2" />
                <h3 className="text-lg font-semibold text-blue-600">Datos Personales</h3>
              </div>

              <div className="space-y-4">
                <div className="flex items-start">
                  <FaUser className="text-blue-500 mt-1 mr-3 flex-shrink-0" />
                  <div className="flex-1">
                    <label className="text-sm font-medium text-gray-600">Nombre Completo:</label>
                    <p className="text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50">
                      {patientData.nombre} {patientData.apellido}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <FaIdCard className="text-blue-500 mt-1 mr-3 flex-shrink-0" />
                  <div className="flex-1">
                    <label className="text-sm font-medium text-gray-600">Documento de Identidad:</label>
                    <p className="text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50">
                      {patientData.documento || patientData.numero_documento}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-4 h-4 mt-1 mr-3 flex-shrink-0 rounded-full bg-pink-500 flex items-center justify-center" style={{
                    backgroundColor: patientData.genero?.toLowerCase() === 'femenino' ? '#ec4899' : patientData.genero?.toLowerCase() === 'masculino' ? '#3b82f6' : '#6b7280',
                    WebkitPrintColorAdjust: 'exact',
                    printColorAdjust: 'exact',
                    colorAdjust: 'exact'
                  }}>
                    <span className="text-white text-xs font-bold" style={{
                      WebkitPrintColorAdjust: 'exact',
                      printColorAdjust: 'exact',
                      colorAdjust: 'exact',
                      color: 'white'
                    }}>
                      {patientData.genero?.toLowerCase() === 'femenino' ? 'F' : 
                       patientData.genero?.toLowerCase() === 'masculino' ? 'M' : 'N'}
                    </span>
                  </div>
                  <div className="flex-1">
                    <label className="text-sm font-medium text-gray-600">Género:</label>
                    <p className="text-lg font-semibold text-gray-900 mt-1 capitalize p-2 border border-gray-200 rounded bg-gray-50">
                      {patientData.genero}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Datos Demográficos */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center mb-4">
                <FaCalendar className="text-blue-600 text-lg mr-2" />
                <h3 className="text-lg font-semibold text-blue-600">Datos Demográficos</h3>
              </div>

              <div className="space-y-4">
                <div className="flex items-start">
                  <FaCalendar className="text-blue-500 mt-1 mr-3 flex-shrink-0" />
                  <div className="flex-1">
                    <label className="text-sm font-medium text-gray-600">Fecha de Nacimiento:</label>
                    <p className="text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50">
                      {formatDateLong(patientData.fecha_nacimiento) || 'viernes, 27 de julio de 2012'}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-1 mr-3" style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    backgroundColor: '#3b82f6',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    WebkitPrintColorAdjust: 'exact',
                    printColorAdjust: 'exact',
                    colorAdjust: 'exact'
                  }}>
                    <span style={{
                      color: 'white',
                      fontSize: '10px',
                      fontWeight: 'bold',
                      lineHeight: '1',
                      WebkitPrintColorAdjust: 'exact',
                      printColorAdjust: 'exact',
                      colorAdjust: 'exact'
                    }}>
                      E
                    </span>
                  </div>
                  <div className="flex-1">
                    <label className="text-sm font-medium text-gray-600">Edad:</label>
                    <p className="text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50">
                      {patientData.edad || '13 años'}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <FaCalendar className="text-blue-500 mt-1 mr-3 flex-shrink-0" />
                  <div className="flex-1">
                    <label className="text-sm font-medium text-gray-600">Fecha de Evaluación:</label>
                    <p className="text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50">
                      {formatDateLong(reportContent?.fecha_generacion || Date.now())}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 2. Resumen General */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm mb-8">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center">
            <FaChartBar className="text-gray-700 text-lg mr-3" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Resumen General</h2>
              <p className="text-sm text-gray-600">Estadísticas generales de la evaluación</p>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">{generalStats.testsCompletados}</div>
                <div className="text-sm text-gray-600 font-medium">Tests Completados</div>
              </div>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">{generalStats.percentilPromedio}</div>
                <div className="text-sm text-gray-600 font-medium">Percentil Promedio</div>
              </div>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-700 mb-1">{generalStats.aptitudesAltas}</div>
                <div className="text-sm text-gray-600 font-medium">Aptitudes Altas</div>
              </div>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-700 mb-1">{generalStats.aptitudesAReforzar}</div>
                <div className="text-sm text-gray-600 font-medium">A Reforzar</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 3. Resultados Gráficos por Aptitud */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
              <FaChartBar className="text-white text-lg" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">Resultados Gráficos por Aptitud</h2>
              <p className="text-blue-100 text-sm">Visualización detallada de puntuaciones y niveles</p>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          {normalizedResultsData.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-gray-500 text-lg mb-2">No hay datos de evaluación disponibles</div>
              <div className="text-gray-400 text-sm">Los resultados aparecerán aquí una vez que se completen las evaluaciones</div>
            </div>
          ) : (
            <table className="w-full">
              <thead className="bg-gray-800 text-white">
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-bold uppercase tracking-wider">APTITUDES EVALUADAS</th>
                  <th className="px-4 py-3 text-center text-sm font-bold uppercase tracking-wider">PD</th>
                  <th className="px-4 py-3 text-center text-sm font-bold uppercase tracking-wider">PC</th>
                  <th className="px-4 py-3 text-center text-sm font-bold uppercase tracking-wider">PERFIL DE LAS APTITUDES</th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {normalizedResultsData.map((result, index) => {
                  const percentil = result.percentil;
                  const puntajeDirecto = result.puntaje_directo;
                  const testLetter = result.aptitudes?.codigo || 'T';
                  const aptitudeName = result.aptitudes?.nombre || 'N/A';
                  const aptitudeConfig = getAptitudeConfig(testLetter, aptitudeName);
                  const barInfo = getPercentilLevel(percentil);
                  const IconComponent = iconMap[aptitudeConfig.icon] || FaPuzzlePiece;

                  return (
                    <tr key={index} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                      <td className="px-4 py-4">
                        <div className="flex items-center">
                          <div
                            className="w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0"
                            style={{ backgroundColor: aptitudeConfig.color }}
                          >
                            <IconComponent className="text-white text-lg" />
                          </div>
                          <div>
                            <p className="text-base font-semibold text-gray-900">{aptitudeConfig.name}</p>
                            <p className="text-sm text-gray-500">{testLetter}</p>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-center">
                        <span className="text-lg font-bold text-gray-900">{puntajeDirecto}</span>
                      </td>
                      <td className="px-4 py-4 text-center">
                        <span className="text-lg font-bold text-gray-900">{percentil}</span>
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 mr-4">
                            <div className="w-full bg-gray-200 rounded-full h-6 relative overflow-hidden" style={{ WebkitPrintColorAdjust: 'exact', printColorAdjust: 'exact' }}>
                              <div
                                className="h-6 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-300"
                                style={{
                                  WebkitPrintColorAdjust: 'exact',
                                  printColorAdjust: 'exact',
                                  colorAdjust: 'exact',
                                  width: `${Math.max(Math.min(percentil, 100), 8)}%`,
                                  backgroundColor: barInfo.color,
                                  position: 'relative',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}
                              >
                                {percentil > 15 && (
                                  <span
                                    className="text-white font-bold"
                                    style={{
                                      position: 'absolute',
                                      left: '50%',
                                      top: '50%',
                                      transform: 'translate(-50%, -50%)',
                                      whiteSpace: 'nowrap',
                                      fontSize: '0.875rem',
                                      fontWeight: '700',
                                      lineHeight: '1'
                                    }}
                                  >
                                    {percentil}
                                  </span>
                                )}
                              </div>
                              {percentil <= 15 && (
                                <div className="absolute inset-0 flex items-center justify-start pl-2">
                                  <span className="text-xs font-bold text-gray-700">{percentil}</span>
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="text-right min-w-[80px]">
                            <span className="text-sm font-medium text-gray-700">{barInfo.level}</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}
        </div>

        {/* Leyenda de niveles */}
        <div className="px-6 py-4 bg-white border-t border-gray-200">
          <h4 className="text-sm font-semibold text-gray-700 mb-3">Leyenda de Niveles:</h4>
          <div className="flex flex-wrap gap-4 text-xs">
            <div className="flex items-center">
              <div style={{
                width: '16px',
                height: '16px',
                backgroundColor: '#ef4444',
                borderRadius: '4px',
                marginRight: '8px',
                WebkitPrintColorAdjust: 'exact',
                printColorAdjust: 'exact',
                colorAdjust: 'exact'
              }}></div>
              <span className="font-medium">Muy bajo (≤5)</span>
            </div>
            <div className="flex items-center">
              <div style={{
                width: '16px',
                height: '16px',
                backgroundColor: '#f97316',
                borderRadius: '4px',
                marginRight: '8px',
                WebkitPrintColorAdjust: 'exact',
                printColorAdjust: 'exact',
                colorAdjust: 'exact'
              }}></div>
              <span className="font-medium">Bajo (6-20)</span>
            </div>
            <div className="flex items-center">
              <div style={{
                width: '16px',
                height: '16px',
                backgroundColor: '#eab308',
                borderRadius: '4px',
                marginRight: '8px',
                WebkitPrintColorAdjust: 'exact',
                printColorAdjust: 'exact',
                colorAdjust: 'exact'
              }}></div>
              <span className="font-medium">Medio-bajo (21-40)</span>
            </div>
            <div className="flex items-center">
              <div style={{
                width: '16px',
                height: '16px',
                backgroundColor: '#6b7280',
                borderRadius: '4px',
                marginRight: '8px',
                WebkitPrintColorAdjust: 'exact',
                printColorAdjust: 'exact',
                colorAdjust: 'exact'
              }}></div>
              <span className="font-medium">Medio (41-60)</span>
            </div>
            <div className="flex items-center">
              <div style={{
                width: '16px',
                height: '16px',
                backgroundColor: '#3b82f6',
                borderRadius: '4px',
                marginRight: '8px',
                WebkitPrintColorAdjust: 'exact',
                printColorAdjust: 'exact',
                colorAdjust: 'exact'
              }}></div>
              <span className="font-medium">Medio-alto (61-80)</span>
            </div>
            <div className="flex items-center">
              <div style={{
                width: '16px',
                height: '16px',
                backgroundColor: '#22c55e',
                borderRadius: '4px',
                marginRight: '8px',
                WebkitPrintColorAdjust: 'exact',
                printColorAdjust: 'exact',
                colorAdjust: 'exact'
              }}></div>
              <span className="font-medium">Alto (81-95)</span>
            </div>
            <div className="flex items-center">
              <div style={{
                width: '16px',
                height: '16px',
                backgroundColor: '#a855f7',
                borderRadius: '4px',
                marginRight: '8px',
                WebkitPrintColorAdjust: 'exact',
                printColorAdjust: 'exact',
                colorAdjust: 'exact'
              }}></div>
              <span className="font-medium">Muy alto (&gt;95)</span>
            </div>
          </div>
        </div>
      </div>

      {/* 4. Índices de Inteligencia */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
        <div className="print-header bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4" style={{ WebkitPrintColorAdjust: 'exact', printColorAdjust: 'exact', colorAdjust: 'exact' }}>
          <div className="flex items-center">
            <FaBrain className="text-white text-2xl mr-3" />
            <div>
              <h2 className="text-2xl font-bold text-white">Índices de Inteligencia</h2>
              <p className="text-blue-100 text-sm">Interpretación profesional de aptitudes e índices de inteligencia</p>
            </div>
          </div>
        </div>

        <div className="p-6">

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {Object.entries(intelligenceIndices || {}).length === 0 ? (
              <div className="col-span-3 text-center py-8">
                <div className="text-gray-500 text-lg mb-2">No hay índices de inteligencia disponibles</div>
                <div className="text-gray-400 text-sm">Los índices aparecerán aquí una vez que se completen las evaluaciones necesarias</div>
              </div>
            ) : (
              Object.entries(intelligenceIndices).map(([codigo, percentil]) => {
              const indiceConfig = INDICES_INTELIGENCIA[codigo];
              const nivelConfig = getLevelConfigByPercentile(percentil);
              const nivelNombre = getLevelNameByPercentile(percentil);
              const nivel = obtenerNivel(percentil);
              const interpretacionIndice = INTERPRETACIONES_INDICES[codigo];
              const interpretacionNivel = interpretacionIndice?.interpretaciones[nivel];

              if (!indiceConfig) return null;

              return (
                <div key={codigo} className={`print-intelligence-card bg-white p-6 rounded-lg shadow-lg border-l-4 ${nivelConfig.borderColor}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <i className={`fas ${indiceConfig.icon} text-2xl ${nivelConfig.textColor} mr-3`}></i>
                      <div>
                        <h4 className="font-bold text-gray-800 text-lg">{indiceConfig.nombre}</h4>
                        <p className="text-sm text-gray-600">{codigo}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-2xl font-bold ${nivelConfig.textColor}`}>
                        {percentil}
                      </div>
                      <div className="text-xs text-gray-500">Percentil</div>
                    </div>
                  </div>

                  <div className={`px-3 py-2 rounded-lg ${nivelConfig.color} text-white text-center font-semibold mb-4`} style={{ WebkitPrintColorAdjust: 'exact', printColorAdjust: 'exact' }}>
                    {nivelNombre}
                  </div>

                  {interpretacionNivel && (
                    <div className="space-y-3 text-xs">
                      <div>
                        <p className="font-bold text-gray-700">Definición:</p>
                        <p className="text-gray-600">
                          {interpretacionIndice?.descripcion || indiceConfig.descripcion}
                        </p>
                      </div>
                      <div>
                        <p className="font-bold text-gray-700">Interpretación Integrada:</p>
                        <p className="text-gray-600 leading-relaxed">{interpretacionNivel.integrada}</p>
                      </div>
                      <div>
                        <p className="font-bold text-gray-700">Implicaciones Generales:</p>
                        <p className="text-gray-600 leading-relaxed">{interpretacionNivel.implicaciones}</p>
                      </div>
                    </div>
                  )}

                  <div className="mt-4 pt-3 border-t border-gray-200">
                    <p className="text-xs text-gray-500">
                      <strong className="font-bold">Componentes:</strong> {indiceConfig.componentes.join(', ')}
                    </p>
                  </div>
                </div>
              );
            })
            )}
          </div>
        </div>
      </div>

      {/* 5. Interpretación por Aptitudes */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
          <div className="flex items-center">
            <FaClipboardList className="text-white text-2xl mr-3" />
            <div>
              <h2 className="text-2xl font-bold text-white">Interpretación por Aptitudes</h2>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {normalizedResultsData.length === 0 ? (
              <div className="col-span-2 p-8 text-center">
                <div className="text-gray-500 text-lg mb-2">No hay interpretaciones disponibles</div>
                <div className="text-gray-400 text-sm">Las interpretaciones aparecerán aquí una vez que se completen las evaluaciones</div>
              </div>
            ) : (
              normalizedResultsData.map((result, index) => {
                const percentil = result.percentil;
                const puntajeDirecto = result.puntaje_directo;
                const testLetter = result.aptitudes?.codigo || 'T';
                const aptitudeName = result.aptitudes?.nombre || 'N/A';
                const aptitudeConfig = getAptitudeConfig(testLetter, aptitudeName);
                const levelInfo = getPercentilLevel(percentil);
                const IconComponent = iconMap[aptitudeConfig.icon] || FaPuzzlePiece;

                // Obtener configuración de nivel para el borde lateral de color
                // Esto aplicará colores como: rojo (muy bajo), naranja (bajo), amarillo (medio-bajo),
                // verde (medio-alto), azul (alto), gris (superior)
                const nivelConfig = getLevelConfigByPercentile(percentil);

                // Obtener interpretación real desde Supabase
                const interpretacionReal = obtenerInterpretacion(testLetter);

                return (
                  <div key={index} className={`print-aptitude-card bg-white rounded-lg p-6 shadow-lg border-l-4 ${nivelConfig.borderColor}`}>
                    <div className="flex items-center mb-4">
                      <div
                        className="w-12 h-12 rounded-full flex items-center justify-center mr-4"
                        style={{
                          backgroundColor: aptitudeConfig.color,
                          WebkitPrintColorAdjust: 'exact',
                          printColorAdjust: 'exact',
                          colorAdjust: 'exact'
                        }}
                      >
                        <IconComponent className="text-white text-xl" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900">{aptitudeConfig.name}</h3>
                      </div>
                      <div className="text-right">
                        <div className="text-4xl font-bold text-gray-900 mb-1">{percentil}</div>
                        <div className="text-sm text-gray-600">Percentil</div>
                      </div>
                    </div>

                    <div className="space-y-3 text-xs">
                      <div>
                        <p className="font-bold text-gray-700">Descripción:</p>
                        <p className="text-gray-600">{aptitudeConfig.description}</p>
                      </div>

                      <div className="mb-4">
                        <div
                          className="text-white text-center py-2 rounded font-bold text-sm"
                          style={{
                            backgroundColor: levelInfo.color,
                            WebkitPrintColorAdjust: 'exact', printColorAdjust: 'exact', colorAdjust: 'exact'
                          }}>
                          {levelInfo.level}
                        </div>
                      </div>

                      <div>
                        <p className="font-bold text-gray-700">Interpretación del Rendimiento:</p>
                        <p className="text-gray-600">
                          {interpretacionesLoading ? (
                            'Cargando interpretación oficial...'
                          ) : interpretacionReal?.rendimiento ? (
                            interpretacionReal.rendimiento
                          ) : (
                            `Rendimiento en nivel ${levelInfo.level} para ${testLetter}. Interpretación oficial cargándose...`
                          )}
                        </p>
                      </div>

                      <div>
                        <p className="font-bold text-gray-700">Implicaciones Académicas:</p>
                        <p className="text-gray-600">
                          {interpretacionesLoading ? (
                            'Cargando implicaciones académicas...'
                          ) : interpretacionReal?.academico ? (
                            interpretacionReal.academico
                          ) : (
                            `Implicaciones académicas para nivel ${levelInfo.level} en ${testLetter}. Consulte con el profesional para detalles específicos.`
                          )}
                        </p>
                      </div>

                      <div>
                        <p className="font-bold text-gray-700">Implicaciones Vocacionales:</p>
                        <p className="text-gray-600">
                          {interpretacionesLoading ? (
                            'Cargando implicaciones vocacionales...'
                          ) : interpretacionReal?.vocacional ? (
                            interpretacionReal.vocacional
                          ) : (
                            `Implicaciones vocacionales para nivel ${levelInfo.level} en ${testLetter}. Consulte con el profesional para orientación específica.`
                          )}
                        </p>
                      </div>

                      <div className="grid grid-cols-3 gap-2 pt-3 border-t border-gray-100 text-center">
                        <div>
                          <div className="text-sm font-bold text-gray-900">{percentil}</div>
                          <div className="text-xs text-gray-500">PC</div>
                        </div>
                        <div>
                          <div className="text-sm font-bold text-gray-900">{result.errores || 0}</div>
                          <div className="text-xs text-gray-500">Errores</div>
                        </div>
                        <div>
                          <div className="text-sm font-bold text-gray-900">
                            {(() => {
                              const tiempoSegundos = parseInt(result.tiempo_segundos) || 0;
                              console.log(`🕰️ [InformePrintable] ${result.aptitudes?.codigo}: tiempo_segundos=${result.tiempo_segundos} -> ${tiempoSegundos} segundos`);
                              return tiempoSegundos > 0 ? `${Math.round(tiempoSegundos / 60)} min` : '0 min';
                            })()}
                          </div>
                          <div className="text-xs text-gray-500">Tiempo</div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
    </div>
  );
});

InformePrintableContent.displayName = 'InformePrintableContent';

InformePrintableContent.propTypes = {
  patientData: PropTypes.object.isRequired,
  normalizedResultsData: PropTypes.array.isRequired,
  intelligenceIndices: PropTypes.object.isRequired,
  generalStats: PropTypes.object.isRequired,
  getAptitudeConfig: PropTypes.func.isRequired,
  getPercentilLevel: PropTypes.func.isRequired,
  reportContent: PropTypes.object
};

export default InformePrintableContent;

// ✅ Títulos en negrita aplicados: Descripción, Interpretación del Rendimiento, Implicaciones Académicas, Implicaciones Vocacionales, Definición, Interpretación Integrada, Implicaciones Generales, Componentes
