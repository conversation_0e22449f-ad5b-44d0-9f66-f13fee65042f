import { supabase } from '../../api/supabaseClient.js';
import { PIN_CONSTANTS } from './PinConstants.js';
import { PinLogger } from './PinLogger.js';
import { toast } from 'react-toastify';
import PinNotificationsAPI from '../../api/endpoints/pinNotifications.js';

/**
 * Servicio mejorado de notificaciones para el sistema de pines
 * Incluye notificaciones por correo, en plataforma y configuración personalizable
 */
export class EnhancedNotificationService {
  
  /**
   * Configuración de umbrales de notificación
   */
  static NOTIFICATION_THRESHOLDS = {
    CRITICAL: 2,    // Notificación crítica
    LOW: 5,         // Notificación de advertencia
    MODERATE: 10,   // Notificación informativa
    EXHAUSTED: 0    // Sin pines
  };

  /**
   * Tipos de notificación
   */
  static NOTIFICATION_TYPES = {
    LOW_PINS: 'low_pins',
    CRITICAL_PINS: 'critical_pins',
    EXHAUSTED_PINS: 'exhausted_pins',
    PIN_CONSUMED: 'pin_consumed',
    PINS_ASSIGNED: 'pins_assigned',
    USAGE_REPORT: 'usage_report'
  };

  /**
   * Canales de notificación
   */
  static NOTIFICATION_CHANNELS = {
    IN_APP: 'in_app',
    EMAIL: 'email',
    TOAST: 'toast',
    SYSTEM: 'system'
  };

  /**
   * Crear notificación de pines bajos con configuración avanzada
   * @param {string} psychologistId - ID del psicólogo
   * @param {number} remainingPins - Pines restantes
   * @param {Object} options - Opciones de notificación
   */
  static async createLowPinNotification(psychologistId, remainingPins, options = {}) {
    const {
      channels = [this.NOTIFICATION_CHANNELS.IN_APP, this.NOTIFICATION_CHANNELS.TOAST],
      priority = 'normal',
      includeUsageStats = true
    } = options;

    try {
      PinLogger.logInfo('Creating low pin notification', { psychologistId, remainingPins, channels });

      // Determinar el tipo y severidad de la notificación
      const notificationData = this._determineNotificationType(remainingPins);
      
      // Obtener información del psicólogo
      const psychologistInfo = await this._getPsychologistInfo(psychologistId);
      
      // Crear notificación en la base de datos
      const notificationId = await this._createDatabaseNotification(
        psychologistId,
        notificationData,
        psychologistInfo,
        includeUsageStats
      );

      // Enviar notificaciones por los canales especificados
      const results = await Promise.allSettled([
        // Notificación en la aplicación
        channels.includes(this.NOTIFICATION_CHANNELS.IN_APP) 
          ? this._sendInAppNotification(psychologistId, notificationData, psychologistInfo)
          : Promise.resolve(),
        
        // Notificación toast
        channels.includes(this.NOTIFICATION_CHANNELS.TOAST)
          ? this._sendToastNotification(notificationData, psychologistInfo)
          : Promise.resolve(),
        
        // Notificación por correo (si está configurado)
        channels.includes(this.NOTIFICATION_CHANNELS.EMAIL)
          ? this._sendEmailNotification(psychologistId, notificationData, psychologistInfo)
          : Promise.resolve()
      ]);

      // Log de resultados
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      PinLogger.logInfo(`Notification sent through ${successCount}/${channels.length} channels`);

      return {
        notificationId,
        channelsUsed: channels,
        successCount,
        results
      };

    } catch (error) {
      PinLogger.logError('Error creating low pin notification', error);
      throw error;
    }
  }

  /**
   * Crear notificación de consumo de pin
   * @param {string} psychologistId - ID del psicólogo
   * @param {number} remainingPins - Pines restantes después del consumo
   * @param {Object} consumptionData - Datos del consumo
   */
  static async createPinConsumptionNotification(psychologistId, remainingPins, consumptionData = {}) {
    try {
      const { patientId, reportId, testSessionId } = consumptionData;
      
      // Solo notificar si quedan pocos pines
      if (remainingPins > this.NOTIFICATION_THRESHOLDS.LOW) {
        return null;
      }

      const notificationData = {
        type: this.NOTIFICATION_TYPES.PIN_CONSUMED,
        title: 'Pin Consumido',
        message: `Se ha consumido 1 pin. Quedan ${remainingPins} pines disponibles.`,
        severity: remainingPins <= this.NOTIFICATION_THRESHOLDS.CRITICAL ? 'critical' : 'warning',
        metadata: {
          remainingPins,
          patientId,
          reportId,
          testSessionId,
          consumedAt: new Date().toISOString()
        }
      };

      // Enviar notificación toast
      await this._sendToastNotification(notificationData);

      return notificationData;

    } catch (error) {
      PinLogger.logError('Error creating pin consumption notification', error);
      throw error;
    }
  }

  /**
   * Crear notificación de asignación de pines
   * @param {string} psychologistId - ID del psicólogo
   * @param {number} assignedPins - Pines asignados
   * @param {number} totalPins - Total de pines después de la asignación
   */
  static async createPinAssignmentNotification(psychologistId, assignedPins, totalPins) {
    try {
      const notificationData = {
        type: this.NOTIFICATION_TYPES.PINS_ASSIGNED,
        title: 'Pines Asignados',
        message: `Se han asignado ${assignedPins} pines. Total disponible: ${totalPins}`,
        severity: 'success',
        metadata: {
          assignedPins,
          totalPins,
          assignedAt: new Date().toISOString()
        }
      };

      // Enviar notificaciones usando la API real
      await Promise.all([
        this._sendToastNotification(notificationData),
        PinNotificationsAPI.createPinAssignmentNotification(psychologistId, assignedPins, totalPins > 0)
      ]);

      return notificationData;

    } catch (error) {
      PinLogger.logError('Error creating pin assignment notification', error);
      throw error;
    }
  }

  /**
   * Obtener notificaciones no leídas de un psicólogo
   * @param {string} psychologistId - ID del psicólogo
   * @returns {Promise<Array>} Lista de notificaciones
   */
  static async getUnreadNotifications(psychologistId) {
    try {
      const { data, error } = await supabase
        .from('pin_notifications')
        .select('*')
        .eq('psychologist_id', psychologistId)
        .eq('read', false)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data || [];

    } catch (error) {
      PinLogger.logError('Error getting unread notifications', error);
      throw error;
    }
  }

  /**
   * Marcar notificación como leída
   * @param {string} notificationId - ID de la notificación
   */
  static async markAsRead(notificationId) {
    try {
      const { error } = await supabase
        .from('pin_notifications')
        .update({ 
          read: true, 
          read_at: new Date().toISOString() 
        })
        .eq('id', notificationId);

      if (error) throw error;

    } catch (error) {
      PinLogger.logError('Error marking notification as read', error);
      throw error;
    }
  }

  /**
   * Determinar el tipo de notificación basado en pines restantes
   * @private
   */
  static _determineNotificationType(remainingPins) {
    if (remainingPins === 0) {
      return {
        type: this.NOTIFICATION_TYPES.EXHAUSTED_PINS,
        title: 'Pines Agotados',
        message: 'No tienes pines disponibles. Contacta al administrador para obtener más.',
        severity: 'error'
      };
    } else if (remainingPins <= this.NOTIFICATION_THRESHOLDS.CRITICAL) {
      return {
        type: this.NOTIFICATION_TYPES.CRITICAL_PINS,
        title: 'Pines Críticos',
        message: `Solo quedan ${remainingPins} pines disponibles. Es urgente recargar.`,
        severity: 'critical'
      };
    } else if (remainingPins <= this.NOTIFICATION_THRESHOLDS.LOW) {
      return {
        type: this.NOTIFICATION_TYPES.LOW_PINS,
        title: 'Pines Bajos',
        message: `Quedan ${remainingPins} pines disponibles. Considera recargar pronto.`,
        severity: 'warning'
      };
    } else {
      return {
        type: this.NOTIFICATION_TYPES.LOW_PINS,
        title: 'Estado de Pines',
        message: `Tienes ${remainingPins} pines disponibles.`,
        severity: 'info'
      };
    }
  }

  /**
   * Obtener información del psicólogo
   * @private
   */
  static async _getPsychologistInfo(psychologistId) {
    try {
      const { data, error } = await supabase
        .from('psicologos')
        .select('id, email, nombre, apellido')
        .eq('id', psychologistId)
        .single();

      if (error) throw error;

      return data;

    } catch (error) {
      PinLogger.logError('Error getting psychologist info', error);
      return { id: psychologistId, email: null, nombre: 'Usuario', apellido: '' };
    }
  }

  /**
   * Crear notificación en la base de datos
   * @private
   */
  static async _createDatabaseNotification(psychologistId, notificationData, psychologistInfo = null, includeUsageStats = false) {
    try {
      let usageStats = null;
      
      if (includeUsageStats) {
        // Obtener estadísticas de uso
        const { data: stats } = await supabase
          .from('psychologist_usage_control')
          .select('total_uses, used_uses')
          .eq('psychologist_id', psychologistId)
          .eq('is_active', true)
          .single();
        
        usageStats = stats;
      }

      const { data, error } = await supabase
        .from('pin_notifications')
        .insert({
          psychologist_id: psychologistId,
          type: notificationData.type,
          title: notificationData.title,
          message: notificationData.message,
          severity: notificationData.severity,
          metadata: {
            ...notificationData.metadata,
            usageStats,
            psychologistInfo
          },
          read: false
        })
        .select()
        .single();

      if (error) throw error;

      return data.id;

    } catch (error) {
      PinLogger.logError('Error creating database notification', error);
      throw error;
    }
  }

  /**
   * Enviar notificación en la aplicación
   * @private
   */
  static async _sendInAppNotification(psychologistId, notificationData, psychologistInfo) {
    // Implementar lógica de notificación en tiempo real
    // Esto podría usar WebSockets, Server-Sent Events, etc.
    PinLogger.logInfo('In-app notification sent', { psychologistId, type: notificationData.type });
    return true;
  }

  /**
   * Enviar notificación toast
   * @private
   */
  static async _sendToastNotification(notificationData, psychologistInfo = null) {
    const { severity, title, message, type } = notificationData;

    // Generar ID único para evitar duplicados
    const toastId = `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    const toastOptions = {
      toastId, // ID único para evitar duplicados
      position: "top-right",
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true
    };

    switch (severity) {
      case 'error':
      case 'critical':
        toast.error(`${title}: ${message}`, {
          ...toastOptions,
          autoClose: 8000
        });
        break;
      case 'warning':
        toast.warning(`${title}: ${message}`, {
          ...toastOptions,
          autoClose: 6000
        });
        break;
      case 'success':
        toast.success(`${title}: ${message}`, {
          ...toastOptions,
          autoClose: 4000
        });
        break;
      default:
        toast.info(`${title}: ${message}`, {
          ...toastOptions,
          autoClose: 5000
        });
    }

    PinLogger.logInfo('Toast notification sent', {
      toastId,
      severity,
      title,
      psychologist: psychologistInfo?.name || 'Unknown'
    });

    return true;
  }

  /**
   * Enviar notificación por correo
   * @private
   */
  static async _sendEmailNotification(psychologistId, notificationData, psychologistInfo) {
    try {
      // Implementar envío de correo electrónico
      // Esto requeriría configurar un servicio de email como SendGrid, AWS SES, etc.
      
      PinLogger.logInfo('Email notification would be sent', { 
        psychologistId, 
        email: psychologistInfo?.email,
        type: notificationData.type 
      });
      
      return true;

    } catch (error) {
      PinLogger.logError('Error sending email notification', error);
      return false;
    }
  }
}

export default EnhancedNotificationService;
