import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { Button } from '../ui/Button';
import { 
  FaUsers, 
  FaCoins, 
  FaShieldAlt, 
  FaRocket, 
  FaBalanceScale,
  FaInfoCircle,
  FaCheckCircle,
  FaExclamationTriangle
} from 'react-icons/fa';
import { BatchProcessingService } from '../../services/pin/BatchProcessingService';
import { useAdvancedPinValidation } from '../../hooks/useAdvancedPinValidation';

/**
 * Componente para seleccionar estrategia de procesamiento en lote
 * Permite al usuario elegir cómo procesar múltiples informes
 */
const BatchStrategySelector = ({ 
  psychologistId,
  patientIds = [],
  onStrategySelect,
  onCancel,
  className = ''
}) => {
  const [selectedStrategy, setSelectedStrategy] = useState(null);
  const [recommendedStrategy, setRecommendedStrategy] = useState(null);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const {
    validationResult
  } = useAdvancedPinValidation(psychologistId, {
    showToastAlerts: false,
    autoValidate: true
  });

  // Calcular estrategia recomendada
  useEffect(() => {
    if (validationResult && patientIds.length > 0) {
      const availablePins = validationResult.remainingPins || 0;
      const recommended = BatchProcessingService.getRecommendedStrategy(
        patientIds.length,
        availablePins,
        { reliability: 0.95, riskTolerance: 'medium' }
      );
      
      setRecommendedStrategy(recommended);
      setSelectedStrategy(recommended);
    }
  }, [validationResult, patientIds.length]);

  // Configuración de estrategias
  const strategies = [
    {
      id: BatchProcessingService.BATCH_STRATEGIES.INDIVIDUAL,
      name: 'Procesamiento Individual',
      icon: FaShieldAlt,
      color: 'blue',
      description: 'Valida y descuenta pines uno por uno',
      pros: [
        'Máxima seguridad',
        'Control granular',
        'Fácil de rastrear errores'
      ],
      cons: [
        'Más lento',
        'Puede interrumpirse si se agotan pines'
      ],
      recommended: 'Para lotes pequeños o cuando la seguridad es prioritaria'
    },
    {
      id: BatchProcessingService.BATCH_STRATEGIES.BULK_UPFRONT,
      name: 'Descuento Anticipado',
      icon: FaRocket,
      color: 'green',
      description: 'Descuenta todos los pines al inicio',
      pros: [
        'Procesamiento rápido',
        'Sin interrupciones por pines',
        'Eficiente para lotes grandes'
      ],
      cons: [
        'Consume pines aunque falle',
        'Menos flexible'
      ],
      recommended: 'Cuando tienes muchos pines y alta confianza en el éxito'
    },
    {
      id: BatchProcessingService.BATCH_STRATEGIES.BULK_SUCCESS,
      name: 'Pago por Éxito',
      icon: FaBalanceScale,
      color: 'purple',
      description: 'Solo descuenta pines por informes exitosos',
      pros: [
        'No desperdicias pines',
        'Justo y transparente',
        'Ideal para procesos inciertos'
      ],
      cons: [
        'Complejidad adicional',
        'Requiere dos fases'
      ],
      recommended: 'Cuando los pines son limitados y hay riesgo de fallos'
    },
    {
      id: BatchProcessingService.BATCH_STRATEGIES.OPTIMISTIC,
      name: 'Procesamiento Optimista',
      icon: FaRocket,
      color: 'orange',
      description: 'Procesa todo y hace rollback si es necesario',
      pros: [
        'Máxima velocidad',
        'Rollback automático',
        'Optimizado para éxito'
      ],
      cons: [
        'Complejo de implementar',
        'Puede requerir limpieza'
      ],
      recommended: 'Para usuarios avanzados con alta tasa de éxito'
    }
  ];

  const getStrategyConfig = (strategyId) => {
    return strategies.find(s => s.id === strategyId);
  };

  const handleStrategySelect = (strategyId) => {
    setSelectedStrategy(strategyId);
  };

  const handleConfirm = () => {
    if (selectedStrategy && onStrategySelect) {
      onStrategySelect(selectedStrategy);
    }
  };

  const availablePins = validationResult?.remainingPins || 0;
  const requiredPins = patientIds.length;
  const isUnlimited = validationResult?.isUnlimited || false;

  return (
    <Card className={className}>
      <CardHeader className="bg-blue-50 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FaUsers className="text-blue-600 mr-3" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Seleccionar Estrategia de Procesamiento
              </h3>
              <p className="text-sm text-gray-600">
                {patientIds.length} informes a procesar
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <div className="flex items-center text-sm text-gray-600 mb-1">
              <FaCoins className="mr-1" />
              <span>
                {isUnlimited ? 'Ilimitado' : `${availablePins} disponibles`}
              </span>
            </div>
            <div className="text-xs text-gray-500">
              Requeridos: {requiredPins}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardBody className="space-y-6">
        {/* Estrategia recomendada */}
        {recommendedStrategy && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FaCheckCircle className="text-green-600 mr-2" />
              <span className="text-sm font-medium text-green-900">
                Estrategia Recomendada
              </span>
            </div>
            <p className="text-sm text-green-800">
              {getStrategyConfig(recommendedStrategy)?.name} - {getStrategyConfig(recommendedStrategy)?.recommended}
            </p>
          </div>
        )}

        {/* Lista de estrategias */}
        <div className="space-y-4">
          {strategies.map((strategy) => {
            const isSelected = selectedStrategy === strategy.id;
            const isRecommended = recommendedStrategy === strategy.id;
            const IconComponent = strategy.icon;

            return (
              <div
                key={strategy.id}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  isSelected
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
                onClick={() => handleStrategySelect(strategy.id)}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-lg mr-3 ${
                      strategy.color === 'blue' ? 'bg-blue-100 text-blue-600' :
                      strategy.color === 'green' ? 'bg-green-100 text-green-600' :
                      strategy.color === 'purple' ? 'bg-purple-100 text-purple-600' :
                      'bg-orange-100 text-orange-600'
                    }`}>
                      <IconComponent className="h-5 w-5" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 flex items-center">
                        {strategy.name}
                        {isRecommended && (
                          <span className="ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                            Recomendada
                          </span>
                        )}
                      </h4>
                      <p className="text-sm text-gray-600">{strategy.description}</p>
                    </div>
                  </div>
                  
                  <div className={`w-4 h-4 rounded-full border-2 ${
                    isSelected
                      ? 'border-blue-500 bg-blue-500'
                      : 'border-gray-300'
                  }`}>
                    {isSelected && (
                      <div className="w-full h-full rounded-full bg-white scale-50"></div>
                    )}
                  </div>
                </div>

                {/* Detalles expandidos */}
                {(isSelected || showAdvanced) && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 pt-4 border-t border-gray-200">
                    <div>
                      <h5 className="text-sm font-medium text-green-700 mb-2">Ventajas:</h5>
                      <ul className="text-sm text-green-600 space-y-1">
                        {strategy.pros.map((pro, index) => (
                          <li key={index} className="flex items-center">
                            <FaCheckCircle className="h-3 w-3 mr-2" />
                            {pro}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h5 className="text-sm font-medium text-red-700 mb-2">Consideraciones:</h5>
                      <ul className="text-sm text-red-600 space-y-1">
                        {strategy.cons.map((con, index) => (
                          <li key={index} className="flex items-center">
                            <FaExclamationTriangle className="h-3 w-3 mr-2" />
                            {con}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Toggle para mostrar detalles avanzados */}
        <div className="flex items-center justify-center">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
          >
            <FaInfoCircle className="mr-1" />
            {showAdvanced ? 'Ocultar detalles' : 'Mostrar detalles avanzados'}
          </button>
        </div>

        {/* Información adicional */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <FaInfoCircle className="text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <h5 className="font-medium mb-2">Información Importante:</h5>
              <ul className="space-y-1 list-disc list-inside">
                <li>La estrategia recomendada se basa en tu historial y pines disponibles</li>
                <li>Puedes cambiar la estrategia en cualquier momento</li>
                <li>Todas las estrategias incluyen validación y manejo de errores</li>
                <li>Los resultados detallados estarán disponibles al finalizar</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Botones de acción */}
        <div className="flex space-x-3 pt-4 border-t border-gray-200">
          <Button
            onClick={handleConfirm}
            disabled={!selectedStrategy}
            className="bg-blue-600 hover:bg-blue-700 text-white flex-1"
          >
            Continuar con {selectedStrategy ? getStrategyConfig(selectedStrategy)?.name : 'Estrategia Seleccionada'}
          </Button>
          
          <Button
            onClick={onCancel}
            variant="outline"
            className="px-6"
          >
            Cancelar
          </Button>
        </div>
      </CardBody>
    </Card>
  );
};

export default BatchStrategySelector;
