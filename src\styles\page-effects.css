/* Efectos personalizados para las páginas BAT-7 */

/* Efectos para iconos de la sidebar */
.sidebar-icon-hover {
  transition: all 0.3s ease-in-out;
}

.sidebar-icon-hover:hover {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 4px 8px rgba(245, 158, 11, 0.3));
}



/* Efectos de hover para cards */
.card-hover-effect {
  transition: all 0.3s ease-in-out;
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Efectos para botones */
.button-glow {
  position: relative;
  overflow: hidden;
}

.button-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.button-glow:hover::before {
  left: 100%;
}



/* Responsive adjustments */
@media (max-width: 768px) {
  .page-header-effect {
    padding: 1rem;
  }
  
  .floating-particle {
    display: none;
  }
}
