-- Script SQL para limpiar usuarios de prueba y preparar para usuario administrador real
-- EJECUTAR ESTE SCRIPT EN EL EDITOR SQL DE SUPABASE

-- =====================================================
-- PASO 1: ELIMINAR USUARIOS DE PRUEBA
-- =====================================================

-- Mostrar usuarios de prueba antes de eliminar
SELECT 
  'USUARIOS DE PRUEBA A ELIMINAR:' as info,
  u.id,
  au.email,
  u.documento,
  u.nombre,
  u.apellido,
  u.tipo_usuario,
  u.activo
FROM public.usuarios u
JOIN auth.users au ON u.id = au.id
WHERE au.email IN (
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
)
ORDER BY au.email;

-- Eliminar usuarios de prueba de la tabla usuarios
-- (Los usuarios en auth.users se eliminarán por CASCADE o manualmente)
DELETE FROM public.usuarios 
WHERE id IN (
  SELECT u.id 
  FROM public.usuarios u
  JOIN auth.users au ON u.id = au.id
  WHERE au.email IN (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  )
);

-- =====================================================
-- PASO 2: VERIFICAR LIMPIEZA
-- =====================================================

-- Mostrar usuarios restantes después de la limpieza
SELECT 
  'USUARIOS RESTANTES DESPUÉS DE LIMPIEZA:' as info,
  COUNT(*) as total_usuarios
FROM public.usuarios;

-- Mostrar detalles de usuarios restantes
SELECT 
  u.id,
  au.email,
  u.documento,
  u.nombre,
  u.apellido,
  u.tipo_usuario,
  u.activo,
  u.fecha_creacion
FROM public.usuarios u
JOIN auth.users au ON u.id = au.id
ORDER BY u.fecha_creacion DESC;

-- =====================================================
-- PASO 3: PREPARAR PARA USUARIO ADMINISTRADOR REAL
-- =====================================================

-- Verificar si ya existe el usuario administrador real
SELECT 
  'VERIFICANDO USUARIO ADMINISTRADOR REAL:' as info,
  u.id,
  au.email,
  u.documento,
  u.nombre,
  u.apellido,
  u.tipo_usuario,
  u.activo
FROM public.usuarios u
JOIN auth.users au ON u.id = au.id
WHERE au.email = '<EMAIL>';

-- =====================================================
-- PASO 4: FUNCIÓN PARA INSERTAR USUARIO ADMINISTRADOR
-- =====================================================

-- Crear función para insertar el usuario administrador real
-- (Solo se ejecutará después de crear el usuario en auth.users)
CREATE OR REPLACE FUNCTION insertar_admin_real(
  p_user_id UUID,
  p_documento TEXT DEFAULT '13716261',
  p_nombre TEXT DEFAULT 'Administrador',
  p_apellido TEXT DEFAULT 'Principal'
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Insertar o actualizar el usuario administrador
  INSERT INTO public.usuarios (
    id,
    documento,
    nombre,
    apellido,
    tipo_usuario,
    activo,
    fecha_creacion,
    ultimo_acceso
  )
  VALUES (
    p_user_id,
    p_documento,
    p_nombre,
    p_apellido,
    'administrador',
    true,
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    documento = EXCLUDED.documento,
    nombre = EXCLUDED.nombre,
    apellido = EXCLUDED.apellido,
    tipo_usuario = EXCLUDED.tipo_usuario,
    activo = EXCLUDED.activo,
    ultimo_acceso = NOW();
  
  RETURN true;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error al insertar usuario administrador: %', SQLERRM;
    RETURN false;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- PASO 5: INSTRUCCIONES PARA COMPLETAR EL PROCESO
-- =====================================================

-- Mostrar instrucciones
SELECT 
  'INSTRUCCIONES PARA COMPLETAR:' as info,
  '1. Crear usuario en auth.users desde el panel de Supabase o usando el script JS' as paso_1,
  '2. Email: <EMAIL>' as email,
  '3. Contraseña: 13716261' as password,
  '4. Ejecutar: SELECT insertar_admin_real(''USER_ID_AQUI'');' as paso_4,
  '5. Verificar con: SELECT * FROM usuarios WHERE documento = ''13716261'';' as paso_5;

-- =====================================================
-- PASO 6: VERIFICACIÓN FINAL
-- =====================================================

-- Crear función de verificación final
CREATE OR REPLACE FUNCTION verificar_configuracion_usuarios()
RETURNS TABLE (
  info TEXT,
  tipo_usuario TEXT,
  cantidad BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    'RESUMEN DE USUARIOS POR TIPO:' as info,
    COALESCE(u.tipo_usuario, 'SIN_TIPO') as tipo_usuario,
    COUNT(*) as cantidad
  FROM public.usuarios u
  GROUP BY u.tipo_usuario
  ORDER BY u.tipo_usuario;
END;
$$ LANGUAGE plpgsql;

-- Ejecutar verificación
SELECT * FROM verificar_configuracion_usuarios();

-- =====================================================
-- PASO 7: LIMPIEZA DE FUNCIONES TEMPORALES
-- =====================================================

-- Las funciones se pueden eliminar después de usar:
-- DROP FUNCTION IF EXISTS insertar_admin_real(UUID, TEXT, TEXT, TEXT);
-- DROP FUNCTION IF EXISTS verificar_configuracion_usuarios();

-- =====================================================
-- NOTAS IMPORTANTES
-- =====================================================

/*
NOTAS IMPORTANTES:

1. USUARIOS EN AUTH.USERS:
   - Los usuarios de prueba en auth.users pueden requerir eliminación manual
   - Ir a Authentication > Users en el panel de Supabase
   - Eliminar manualmente los usuarios de prueba

2. CREAR USUARIO ADMINISTRADOR:
   - Usar el panel de Supabase: Authentication > Users > Add User
   - O usar el script JavaScript: cleanupAndCreateRealAdmin.js
   - Email: <EMAIL>
   - Contraseña: 13716261

3. DESPUÉS DE CREAR EN AUTH.USERS:
   - Obtener el UUID del usuario creado
   - Ejecutar: SELECT insertar_admin_real('UUID_AQUI');

4. VERIFICACIÓN:
   - SELECT * FROM usuarios WHERE email = '<EMAIL>';
   - Verificar que tipo_usuario = 'administrador'
   - Verificar que activo = true

5. SEGURIDAD:
   - Cambiar la contraseña después del primer login
   - Configurar autenticación de dos factores si está disponible
   - Revisar permisos y políticas RLS
*/
