/**
 * @file InformesService.js
 * @description Servicio para gestionar informes de evaluaciones psicológicas
 */

import supabase from '../api/supabaseClient';
import { toast } from 'react-toastify';
import { calculateAge } from '../utils/dateUtils';
import InterpretacionesService from './InterpretacionesService';
import pinControlService from './pin/ImprovedPinControlService';

const InformesService = {
  /**
   * Ejecuta una operación de base de datos
   * @param {Function} operation - Función que ejecuta la operación
   * @param {string} operationName - Nombre de la operación para logging
   * @returns {Promise} - Resultado de la operación
   */
  async executeOperation(operation, operationName) {
    try {
      console.log(`🔄 [InformesService] Ejecutando operación: ${operationName}`);
      const result = await operation();
      
      if (result.error) {
        console.error(`❌ [InformesService] Error en ${operationName}:`, result.error);
        throw result.error;
      }
      
      return result;
    } catch (error) {
      console.error(`❌ [InformesService] Error en ${operationName}:`, error.message);
      throw error;
    }
  },

  /**
   * Generar informe completo para un paciente
   */
  async generarInformeCompleto(pacienteId, titulo = null, descripcion = null, incluirInterpretaciones = true, skipValidation = false) {
    try {
      console.log('📊 [InformesService] Generando informe completo para paciente:', pacienteId);

      // Obtener datos del paciente
      const { data: paciente, error: pacienteError } = await supabase
        .from('pacientes')
        .select('*')
        .eq('id', pacienteId)
        .single();

      if (pacienteError) {
        throw pacienteError;
      }

      // Validación previa de pines (si no se omite)
      if (!skipValidation && paciente?.psicologo_id) {
        try {
          // Obtener usuario actual para verificar rol de administrador
          const { data: { user } } = await supabase.auth.getUser();
          let currentUser = null;

          if (user) {
            const { data: userData } = await supabase
              .from('usuarios')
              .select('id, rol, nombre, apellido')
              .eq('id', user.id)
              .single();
            currentUser = userData;
          }

          console.log('👤 [InformesService] Usuario actual:', currentUser?.rol || 'No identificado');

          // Bypass for admin users
          if (currentUser && currentUser.rol === 'admin') {
            console.log('✅ [InformesService] Bypass de administrador aplicado');
          } else {
            const usageStatus = await pinControlService.checkPsychologistUsage(paciente.psicologo_id);

            if (!usageStatus.canUse) {
              console.error('❌ [InformesService] Validación de pines falló:', usageStatus.reason);
              throw new Error(`No se puede generar el informe: ${usageStatus.reason}`);
            }
            console.log('✅ [InformesService] Validación de pines exitosa:', usageStatus.reason);
          }
        } catch (validationError) {
          console.error('❌ [InformesService] Error en validación de pines:', validationError);
          throw new Error(`Error al validar permisos: ${validationError.message}`);
        }
      }

      // Calcular edad si existe fecha de nacimiento
      if (paciente && paciente.fecha_nacimiento) {
        paciente.edad = calculateAge(paciente.fecha_nacimiento);
      }

      // Obtener resultados del paciente
      const { data: resultados, error: resultadosError } = await supabase
        .from('resultados')
        .select('*, aptitudes(id, codigo, nombre, descripcion)')
        .eq('paciente_id', pacienteId)
        .order('created_at', { ascending: false });

      if (resultadosError) {
        throw resultadosError;
      }

      // Generar interpretaciones cualitativas si se solicita
      let interpretacionesCualitativas = null;
      if (incluirInterpretaciones && resultados && resultados.length > 0) {
        try {
          interpretacionesCualitativas = await this._generarInterpretacionesCualitativas(resultados);
        } catch (error) {
          console.warn('⚠️ [InformesService] Error generando interpretaciones cualitativas:', error);
          // Continuar sin interpretaciones cualitativas
        }
      }

      // Estructurar contenido del informe
      const contenido = {
        paciente: paciente,
        resultados: resultados || [],
        estadisticas: this._calcularEstadisticas(resultados || []),
        evaluacion: this._generarEvaluacion(resultados || []),
        interpretacionesCualitativas,
        concentracion: this._calcularDatosConcentracion(resultados || []),
        graficos: this._generarDatosGraficos(resultados || [])
      };

      // Crear registro del informe
      const informeResult = await this.executeOperation(async () => {
        return await supabase
          .from('informes_generados')
          .insert({
            paciente_id: pacienteId,
            tipo_informe: 'completo',
            titulo: titulo || `Informe Completo - ${paciente.nombre} ${paciente.apellido}`,
            descripcion: descripcion || 'Informe completo de evaluación psicológica',
            contenido: contenido,
            estado: 'generado',
            fecha_generacion: new Date().toISOString(),
            metadatos: {
              version: '2.0',
              generado_por: 'sistema',
              total_resultados: resultados?.length || 0,
              incluye_interpretaciones: !!interpretacionesCualitativas
            }
          })
          .select()
          .single();
      }, 'INSERT_INFORME');

      if (!informeResult.data) {
        throw new Error('No se pudo crear el informe en la base de datos');
      }

      const informe = informeResult.data;

      // Consumir pin automáticamente si el paciente tiene psicólogo asignado
      try {
        if (paciente.psicologo_id) {
          // Usar el nuevo servicio de control de sesiones
          const SessionControlService = (await import('./pin/SessionControlService.js')).default;
          const pendingSessions = await SessionControlService.getPendingSessions(paciente.psicologo_id);

          // Buscar sesión para este paciente específico
          const patientSession = pendingSessions.find(s => s.paciente_id === pacienteId);

          if (patientSession) {
            // Validar que la sesión puede consumir un pin
            const validation = await SessionControlService.validateSessionForPinConsumption(
              patientSession.id,
              paciente.psicologo_id
            );

            if (validation.canProceed) {
              // Consumir el pin
              await pinControlService.consumePin(
                paciente.psicologo_id,
                pacienteId,
                patientSession.id,
                informe.id
              );

              // Marcar la sesión como que ya consumió pin
              await SessionControlService.markSessionPinConsumed(
                patientSession.id,
                'pin-consumption-id',
                informe.id
              );

              console.log('✅ [InformesService] Pin consumido automáticamente para informe:', informe.id);
              toast.info('Se ha consumido 1 pin para generar el informe.');
            } else {
              console.warn('⚠️ [InformesService] Validación de sesión falló:', validation.message);
            }
          } else {
            console.warn('⚠️ [InformesService] No se encontró sesión pendiente para paciente:', pacienteId);
          }
        }
      } catch (pinError) {
        console.error('❌ [InformesService] Error al consumir pin para informe:', pinError);
        // Si no hay pines disponibles, eliminar el informe creado y lanzar error
        if (pinError.message.includes('No hay pines disponibles') || pinError.message.includes('Sin pines')) {
          await supabase.from('informes_generados').delete().eq('id', informe.id);
          throw new Error(`No se puede generar el informe: ${pinError.message}`);
        }
        // Para otros errores, solo advertir pero continuar
        console.warn('⚠️ [InformesService] Error al consumir pin, pero continuando con la generación del informe');
      }

      console.log('✅ [InformesService] Informe completo generado:', informe.id);
      toast.success('Informe completo generado exitosamente');
      return informe.id;

    } catch (error) {
      console.error('❌ [InformesService] Error generando informe completo:', error);
      toast.error(error.message || 'Error al generar el informe completo');
      throw error;
    }
  },

  /**
   * Calcular estadísticas básicas de los resultados
   * @private
   */
  _calcularEstadisticas(resultados) {
    if (!resultados || resultados.length === 0) {
      return {
        total_evaluaciones: 0,
        promedio_percentiles: {},
        aptitudes_destacadas: [],
        areas_mejora: []
      };
    }

    // Extraer percentiles de cada resultado
    const percentiles = resultados.map(r => ({
      aptitud: r.aptitudes?.codigo || 'N/A',
      percentil: r.percentil,
      fecha: r.created_at
    }));

    // Calcular promedios por aptitud
    const promediosPorAptitud = {};
    percentiles.forEach(({ aptitud, percentil }) => {
      if (!promediosPorAptitud[aptitud]) {
        promediosPorAptitud[aptitud] = [];
      }
      promediosPorAptitud[aptitud].push(percentil);
    });

    // Calcular promedios finales
    const promediosFinales = {};
    Object.entries(promediosPorAptitud).forEach(([aptitud, valores]) => {
      promediosFinales[aptitud] = Math.round(
        valores.reduce((sum, val) => sum + val, 0) / valores.length
      );
    });

    // Identificar aptitudes destacadas y áreas de mejora
    const aptitudesDestacadas = Object.entries(promediosFinales)
      .filter(([_, promedio]) => promedio >= 75)
      .map(([aptitud, promedio]) => ({ aptitud, promedio }));

    const areasMejora = Object.entries(promediosFinales)
      .filter(([_, promedio]) => promedio <= 25)
      .map(([aptitud, promedio]) => ({ aptitud, promedio }));

    return {
      total_evaluaciones: resultados.length,
      promedio_percentiles: promediosFinales,
      aptitudes_destacadas: aptitudesDestacadas,
      areas_mejora: areasMejora,
      fecha_primera_evaluacion: resultados[resultados.length - 1]?.created_at,
      fecha_ultima_evaluacion: resultados[0]?.created_at
    };
  },

  /**
   * Genera una evaluación cualitativa básica
   * @private
   */
  _generarEvaluacion(resultados) {
    if (!resultados || resultados.length === 0) {
      return {
        resumen: 'No hay resultados disponibles para evaluar.',
        recomendaciones: [],
        observaciones: []
      };
    }

    const estadisticas = this._calcularEstadisticas(resultados);
    const recomendaciones = [];
    const observaciones = [];

    // Generar recomendaciones basadas en estadísticas
    if (estadisticas.aptitudes_destacadas.length > 0) {
      recomendaciones.push(
        `Potenciar las aptitudes destacadas: ${estadisticas.aptitudes_destacadas.map(a => a.aptitud).join(', ')}`
      );
    }

    if (estadisticas.areas_mejora.length > 0) {
      recomendaciones.push(
        `Trabajar en las áreas de mejora: ${estadisticas.areas_mejora.map(a => a.aptitud).join(', ')}`
      );
    }

    // Generar observaciones
    if (resultados.length > 1) {
      observaciones.push('Se observa un historial de evaluaciones que permite analizar la evolución.');
    }

    const resumen = `Evaluación basada en ${estadisticas.total_evaluaciones} evaluación${estadisticas.total_evaluaciones !== 1 ? 'es' : ''}. 
${estadisticas.aptitudes_destacadas.length > 0 ? `Aptitudes destacadas: ${estadisticas.aptitudes_destacadas.length}. ` : ''}
${estadisticas.areas_mejora.length > 0 ? `Áreas de mejora identificadas: ${estadisticas.areas_mejora.length}.` : ''}`;

    return {
      resumen: resumen.trim(),
      recomendaciones,
      observaciones
    };
  },

  /**
   * Calcula datos específicos de concentración
   * @private
   */
  _calcularDatosConcentracion(resultados) {
    // Buscar resultado de atención
    const atencionResult = resultados.find(r => r.aptitudes?.codigo === 'A');

    if (!atencionResult) {
      return {
        disponible: false,
        mensaje: 'No se encontraron datos de atención para calcular concentración'
      };
    }

    const pd = atencionResult.puntaje_directo || 0;
    const errores = atencionResult.errores || 0;
    const concentracion = pd + errores > 0 ? Math.round((pd / (pd + errores)) * 100) : 0;

    return {
      disponible: true,
      atencion: {
        puntaje_directo: pd,
        errores: errores,
        percentil: atencionResult.percentil
      },
      concentracion: {
        valor: concentracion,
        interpretacion: this._interpretarConcentracion(concentracion),
        formula_aplicada: `CON = (${pd} / (${pd} + ${errores})) × 100 = ${concentracion}%`
      }
    };
  },

  /**
   * Interpreta el nivel de concentración
   * @private
   */
  _interpretarConcentracion(valor) {
    if (valor >= 90) return 'Muy Alto';
    if (valor >= 80) return 'Alto';
    if (valor >= 65) return 'Medio';
    if (valor >= 50) return 'Bajo';
    return 'Muy Bajo';
  },

  /**
   * Genera datos para gráficos del informe
   * @private
   */
  _generarDatosGraficos(resultados) {
    if (!resultados || resultados.length === 0) {
      return {
        disponible: false,
        mensaje: 'No hay datos suficientes para generar gráficos'
      };
    }

    // Preparar datos para gráfico de percentiles
    const datosPercentiles = resultados
      .filter(r => r.percentil !== null && r.percentil !== undefined)
      .map(r => ({
        aptitud: r.aptitudes?.codigo || 'N/A',
        nombre: r.aptitudes?.nombre || 'Desconocido',
        percentil: r.percentil,
        puntaje_directo: r.puntaje_directo
      }))
      .sort((a, b) => b.percentil - a.percentil);

    // Preparar datos para gráfico de concentración
    const concentracionData = this._calcularDatosConcentracion(resultados);
    let graficoConcentracion = null;

    if (concentracionData.disponible) {
      graficoConcentracion = {
        valor: concentracionData.concentracion.valor,
        interpretacion: concentracionData.concentracion.interpretacion,
        color: this._getColorByPercentil(concentracionData.concentracion.valor),
        datos_comparativos: [
          { categoria: 'Muy Bajo', rango: '0-49%', color: '#e74c3c' },
          { categoria: 'Bajo', rango: '50-64%', color: '#f39c12' },
          { categoria: 'Medio', rango: '65-79%', color: '#f1c40f' },
          { categoria: 'Alto', rango: '80-89%', color: '#2ecc71' },
          { categoria: 'Muy Alto', rango: '90-100%', color: '#27ae60' }
        ]
      };
    }

    return {
      disponible: true,
      percentiles: {
        datos: datosPercentiles,
        titulo: 'Percentiles por Aptitud',
        tipo: 'bar'
      },
      concentracion: graficoConcentracion,
      resumen: {
        total_aptitudes: datosPercentiles.length,
        promedio_percentil: Math.round(
          datosPercentiles.reduce((sum, d) => sum + d.percentil, 0) / datosPercentiles.length
        ),
        aptitud_mas_alta: datosPercentiles[0]?.nombre || 'N/A',
        aptitud_mas_baja: datosPercentiles[datosPercentiles.length - 1]?.nombre || 'N/A'
      }
    };
  },

  /**
   * Obtener color según percentil
   * @private
   */
  _getColorByPercentil(percentil) {
    if (percentil >= 90) return '#27ae60'; // Verde oscuro
    if (percentil >= 75) return '#2ecc71'; // Verde
    if (percentil >= 50) return '#f1c40f'; // Amarillo
    if (percentil >= 25) return '#f39c12'; // Naranja
    return '#e74c3c'; // Rojo
  },

  /**
   * Generar interpretaciones cualitativas
   * @private
   */
  async _generarInterpretacionesCualitativas(resultados) {
    try {
      // Preparar datos para el servicio de interpretaciones
      const resultadosAptitudes = resultados.map(resultado => ({
        aptitud_codigo: resultado.aptitudes?.codigo || resultado.aptitud_id,
        percentil: resultado.percentil,
        puntaje_directo: resultado.puntaje_directo
      }));

      // Generar resumen cualitativo completo
      const resumenCualitativo = await InterpretacionesService.generarResumenCualitativo(
        resultadosAptitudes
      );

      return resumenCualitativo;
    } catch (error) {
      console.error('Error al generar interpretaciones cualitativas:', error);
      throw error;
    }
  }
};

export default InformesService;
