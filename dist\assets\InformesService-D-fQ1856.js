const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/PinValidationService-Ki4hIVgd.js","assets/vendor-BqMjyOVw.js","assets/vendor-B4zyQOk2.css","assets/PinLogger-C2v3yGM1.js","assets/index-Bdl1jgS_.js","assets/index-Csy2uUlu.css","assets/SessionControlService-CcWSYZik.js","assets/BatchProcessingService-oBS5r6Eb.js","assets/ImprovedPinControlService-BUPGzexy.js","assets/NotificationService-DiDbKBbI.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,a=Object.defineProperties,n=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,t=(a,n,i)=>n in a?e(a,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):a[n]=i,c=(e,a)=>{for(var n in a||(a={}))r.call(a,n)&&t(e,n,a[n]);if(i)for(var n of i(a))o.call(a,n)&&t(e,n,a[n]);return e},s=(e,i)=>a(e,n(i)),l=(e,a,n)=>new Promise((i,r)=>{var o=e=>{try{c(n.next(e))}catch(a){r(a)}},t=e=>{try{c(n.throw(e))}catch(a){r(a)}},c=e=>e.done?i(e.value):Promise.resolve(e.value).then(o,t);c((n=n.apply(e,a)).next())});import{_ as d,Q as u}from"./vendor-BqMjyOVw.js";import{s as p,d as m}from"./index-Bdl1jgS_.js";import{p as f}from"./ImprovedPinControlService-BUPGzexy.js";import"./PinLogger-C2v3yGM1.js";import"./NotificationService-DiDbKBbI.js";const g={obtenerNivelPorPercentil:e=>e<=5?{id:1,nombre:"Muy Bajo",color:"text-red-600"}:e<=20?{id:2,nombre:"Bajo",color:"text-red-500"}:e<=40?{id:3,nombre:"Medio-Bajo",color:"text-orange-500"}:e<=60?{id:4,nombre:"Medio",color:"text-yellow-600"}:e<=80?{id:5,nombre:"Medio-Alto",color:"text-blue-500"}:e<=95?{id:6,nombre:"Alto",color:"text-green-600"}:{id:7,nombre:"Muy Alto",color:"text-green-700"},interpretaciones:{V:{1:{rendimiento:"Presenta dificultades significativas en la comprensión y manejo de conceptos verbales.",academico:"Puede presentar dificultades en asignaturas como Lengua y Literatura, Historia, Filosofía.",vocacional:"Se beneficiaría de actividades profesionales que no dependan del procesamiento verbal complejo."},2:{rendimiento:"Muestra un rendimiento por debajo del promedio en tareas verbales.",academico:"Requiere apoyo adicional en materias con alta carga verbal.",vocacional:"Puede desenvolverse en profesiones que combinen habilidades verbales básicas con otras competencias."},3:{rendimiento:"Demuestra una capacidad verbal ligeramente por debajo del promedio.",academico:"Puede manejar contenido verbal básico pero requiere apoyo en textos complejos.",vocacional:"Adecuado para profesiones con demandas verbales moderadas."},4:{rendimiento:"Demuestra una capacidad verbal dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en materias con componente verbal.",vocacional:"Posee las competencias verbales necesarias para una amplia gama de profesiones."},5:{rendimiento:"Muestra una capacidad verbal por encima del promedio.",academico:"Destaca en materias que requieren comprensión verbal y puede abordar textos complejos.",vocacional:"Tiene potencial para profesiones que requieren habilidades verbales avanzadas."},6:{rendimiento:"Demuestra una capacidad verbal superior.",academico:"Destaca significativamente en materias verbales y puede manejar contenido académico avanzado.",vocacional:"Posee las competencias para destacar en profesiones altamente verbales."},7:{rendimiento:"Presenta una capacidad verbal excepcional.",academico:"Puede destacar significativamente en todas las materias con componente verbal.",vocacional:"Posee el potencial para sobresalir en las profesiones más exigentes verbalmente."}},E:{4:{rendimiento:"Demuestra una capacidad espacial dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en materias con componente espacial.",vocacional:"Posee las competencias espaciales necesarias para diversas profesiones."},5:{rendimiento:"Muestra una capacidad espacial por encima del promedio.",academico:"Destaca en materias que requieren visualización espacial.",vocacional:"Tiene potencial para profesiones técnicas y de diseño."},6:{rendimiento:"Demuestra una capacidad espacial superior.",academico:"Destaca en matemáticas, física y materias técnicas.",vocacional:"Excelente para ingeniería, arquitectura y diseño."}},R:{4:{rendimiento:"Demuestra una capacidad de razonamiento dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en materias que requieren razonamiento.",vocacional:"Posee las competencias de razonamiento necesarias para una amplia gama de profesiones."},5:{rendimiento:"Muestra una capacidad de razonamiento por encima del promedio.",academico:"Destaca en materias que requieren análisis lógico.",vocacional:"Tiene potencial para profesiones analíticas y de resolución de problemas."},6:{rendimiento:"Demuestra una capacidad de razonamiento superior.",academico:"Destaca en materias que requieren razonamiento complejo.",vocacional:"Excelente para investigación, análisis y consultoría."}},N:{4:{rendimiento:"Demuestra una capacidad numérica dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en matemáticas.",vocacional:"Posee las competencias numéricas necesarias para diversas profesiones."},5:{rendimiento:"Muestra una capacidad numérica por encima del promedio.",academico:"Destaca en matemáticas y materias con componente numérico.",vocacional:"Tiene potencial para profesiones que requieren habilidades matemáticas."},6:{rendimiento:"Demuestra una capacidad numérica superior.",academico:"Destaca significativamente en matemáticas y ciencias exactas.",vocacional:"Excelente para ingeniería, finanzas y ciencias."}},A:{4:{rendimiento:"Demuestra una capacidad atencional dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en tareas que requieren concentración.",vocacional:"Posee las competencias atencionales necesarias para diversas profesiones."},5:{rendimiento:"Muestra una capacidad atencional por encima del promedio.",academico:"Destaca en tareas que requieren concentración sostenida.",vocacional:"Tiene potencial para profesiones que demandan alta concentración."},6:{rendimiento:"Demuestra una capacidad atencional superior.",academico:"Destaca en todas las actividades académicas que requieren concentración.",vocacional:"Excelente para profesiones de precisión y control de calidad."}},M:{4:{rendimiento:"Demuestra una comprensión mecánica dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en materias técnicas.",vocacional:"Posee las competencias mecánicas necesarias para diversas profesiones técnicas."},5:{rendimiento:"Muestra una comprensión mecánica por encima del promedio.",academico:"Destaca en materias técnicas y de ciencias aplicadas.",vocacional:"Tiene potencial para profesiones de ingeniería y tecnología."},6:{rendimiento:"Demuestra una comprensión mecánica superior.",academico:"Destaca significativamente en todas las materias técnicas.",vocacional:"Excelente para ingeniería mecánica y desarrollo tecnológico."}},O:{4:{rendimiento:"Demuestra un conocimiento ortográfico dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en escritura y comunicación.",vocacional:"Posee las competencias ortográficas necesarias para diversas profesiones."},5:{rendimiento:"Muestra un conocimiento ortográfico por encima del promedio.",academico:"Destaca en materias de lengua y comunicación escrita.",vocacional:"Tiene potencial para profesiones que requieren comunicación escrita precisa."},6:{rendimiento:"Demuestra un conocimiento ortográfico superior.",academico:"Destaca significativamente en todas las materias de comunicación.",vocacional:"Excelente para periodismo, edición y comunicación profesional."}}},obtenerInterpretacionAptitud:(e,a)=>{var n;const i=g.obtenerNivelPorPercentil(a),r=null==(n=g.interpretaciones[e])?void 0:n[i.id];return r?{nivel_nombre:i.nombre,rendimiento:r.rendimiento,academico:r.academico,vocacional:r.vocacional}:{nivel_nombre:i.nombre,rendimiento:"Interpretación no disponible para este nivel.",academico:"Se requiere evaluación adicional.",vocacional:"Consulte con un profesional para orientación específica."}}};class v{static obtenerNivelPorPercentil(e){return l(this,null,function*(){try{return g.obtenerNivelPorPercentil(e)}catch(a){throw a}})}static obtenerInterpretacionAptitud(e,a){return l(this,null,function*(){try{return g.obtenerInterpretacionAptitud(e,a)}catch(n){throw n}})}static obtenerInterpretacionIndice(e,a){return l(this,null,function*(){try{const{data:n,error:i}=yield p.rpc("obtener_interpretacion_indice",{p_indice_codigo:e,p_percentil:a});if(i)throw i;return n}catch(n){throw n}})}static obtenerInterpretacionesMultiples(e){return l(this,null,function*(){try{return yield Promise.all(e.map(e=>l(this,null,function*(){const a=yield this.obtenerInterpretacionAptitud(e.aptitud_codigo,e.percentil);return s(c({},e),{interpretacion:a})})))}catch(a){throw a}})}static generarResumenCualitativo(e){return l(this,arguments,function*(e,a=[]){try{const n=yield this.obtenerInterpretacionesMultiples(e),i=yield Promise.all(a.map(e=>l(this,null,function*(){const a=yield this.obtenerInterpretacionIndice(e.indice_codigo,e.percentil);return s(c({},e),{interpretacion:a})}))),r=n.filter(e=>e.percentil>=75).map(e=>{var a,n;return{aptitud:e.aptitud_codigo,percentil:e.percentil,nivel:null==(a=e.interpretacion)?void 0:a.nivel_nombre,descripcion:null==(n=e.interpretacion)?void 0:n.rendimiento}}),o=n.filter(e=>e.percentil<=25).map(e=>{var a,n;return{aptitud:e.aptitud_codigo,percentil:e.percentil,nivel:null==(a=e.interpretacion)?void 0:a.nivel_nombre,descripcion:null==(n=e.interpretacion)?void 0:n.rendimiento}}),t=this._generarRecomendacionesAcademicas(n);return{interpretacionesAptitudes:n,interpretacionesIndices:i,fortalezas:r,debilidades:o,recomendacionesAcademicas:t,orientacionVocacional:this._generarOrientacionVocacional(n),resumenGeneral:this._generarResumenGeneral(n,i)}}catch(n){throw n}})}static _generarRecomendacionesAcademicas(e){const a=[];return e.forEach(e=>{var n;(null==(n=e.interpretacion)?void 0:n.academico)&&a.push({aptitud:e.aptitud_codigo,nivel:e.interpretacion.nivel_nombre,recomendacion:e.interpretacion.academico})}),a}static _generarOrientacionVocacional(e){const a=[];return e.forEach(e=>{var n;(null==(n=e.interpretacion)?void 0:n.vocacional)&&a.push({aptitud:e.aptitud_codigo,nivel:e.interpretacion.nivel_nombre,orientacion:e.interpretacion.vocacional})}),a}static _generarResumenGeneral(e,a){const n=e.length>0?e.reduce((e,a)=>e+a.percentil,0)/e.length:50;let i="Promedio";n>=85?i="Superior":n>=75?i="Por encima del promedio":n<=15?i="Por debajo del promedio":n<=25&&(i="Ligeramente por debajo del promedio");const r=e.filter(e=>e.percentil>=75).map(e=>e.aptitud_codigo),o=e.filter(e=>e.percentil<=25).map(e=>e.aptitud_codigo);return{nivelGeneral:i,promedioPercentil:Math.round(n),aptitudesDestacadas:r,aptitudesAMejorar:o,perfilCognitivo:this._determinarPerfilCognitivo(e)}}static _determinarPerfilCognitivo(e){const a={verbal:["V","O"],numerico:["N","R"],espacial:["E","M"],atencion:["A"]},n={};Object.keys(a).forEach(i=>{const r=e.filter(e=>a[i].includes(e.aptitud_codigo));r.length>0&&(n[i]=r.reduce((e,a)=>e+a.percentil,0)/r.length)});const i=Object.keys(n);return{perfilDominante:i.length>0?i.reduce((e,a)=>n[e]>n[a]?e:a):"equilibrado",promedios:n}}static obtenerAptitudes(){return l(this,null,function*(){try{const{data:e,error:a}=yield p.from("aptitudes_interpretacion").select("*").order("codigo");if(a)throw a;return e}catch(e){throw e}})}static obtenerIndices(){return l(this,null,function*(){try{const{data:e,error:a}=yield p.from("indices_inteligencia").select("*").order("codigo");if(a)throw a;return e}catch(e){throw e}})}static obtenerNiveles(){return l(this,null,function*(){try{const{data:e,error:a}=yield p.from("niveles_rendimiento").select("*").order("percentil_minimo");if(a)throw a;return e}catch(e){throw e}})}}const h={obtenerInformesPaciente(e){return l(this,null,function*(){try{const{data:a,error:n}=yield p.from("informes_generados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento,\n            fecha_nacimiento\n          )\n        ").eq("paciente_id",e).order("fecha_generacion",{ascending:!1});if(n)throw n;return a||[]}catch(a){throw u.error("Error al cargar los informes del paciente"),a}})},obtenerInforme(e){return l(this,null,function*(){try{const{data:a,error:n}=yield p.from("informes_generados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento,\n            fecha_nacimiento,\n            genero,\n            institucion_id\n          )\n        ").eq("id",e).single();if(n)throw n;return a&&a.pacientes&&a.pacientes.fecha_nacimiento&&(a.pacientes.edad=m(a.pacientes.fecha_nacimiento)),a}catch(a){throw u.error("Error al cargar el informe"),a}})},generarInformeCompleto(e,a=null,n=null,i=!0,r=!1){return l(this,null,function*(){try{const{data:s,error:g}=yield p.from("pacientes").select("*").eq("id",e).single();if(g)throw g;if(!r&&(null==s?void 0:s.psicologo_id))try{const e=(yield d(()=>l(null,null,function*(){const{default:e}=yield import("./PinValidationService-Ki4hIVgd.js");return{default:e}}),__vite__mapDeps([0,1,2,3,4,5]))).default,a=yield e.validateReportGeneration(s.psicologo_id,1);if(!a.canProceed)throw new Error(`No se puede generar el informe: ${a.userMessage}`)}catch(o){throw new Error(`Error al validar permisos: ${o.message}`)}s&&s.fecha_nacimiento&&(s.edad=m(s.fecha_nacimiento));const{data:v,error:h}=yield p.from("resultados").select("*").eq("paciente_id",e).order("created_at",{ascending:!1});if(h)throw h;let _=null;if(i&&v&&v.length>0)try{_=yield this._generarInterpretacionesCualitativas(v)}catch(t){}const y={paciente:s,resultados:v||[],estadisticas:this._calcularEstadisticas(v||[]),evaluacion:this._generarEvaluacion(v||[]),interpretacionesCualitativas:_,concentracion:this._calcularDatosConcentracion(v||[]),graficos:this._generarDatosGraficos(v||[])},{data:b,error:P}=yield p.from("informes_generados").insert({paciente_id:e,tipo_informe:"completo",titulo:a||`Informe Completo - ${s.nombre} ${s.apellido}`,descripcion:n||"Informe completo de evaluación psicológica",contenido:y,estado:"generado",fecha_generacion:(new Date).toISOString(),metadatos:{version:"2.0",generado_por:"sistema",total_resultados:(null==v?void 0:v.length)||0,incluye_interpretaciones:!!_}}).select().single();if(P)throw P;try{if(s.psicologo_id){const a=(yield d(()=>l(this,null,function*(){const{default:e}=yield import("./SessionControlService-CcWSYZik.js");return{default:e}}),__vite__mapDeps([6,4,1,2,5,3]))).default,n=(yield a.getPendingSessions(s.psicologo_id)).find(a=>a.paciente_id===e);if(n){(yield a.validateSessionForPinConsumption(n.id,s.psicologo_id)).canProceed&&(yield f.consumePin(s.psicologo_id,e,n.id,b.id),yield a.markSessionPinConsumed(n.id,"pin-consumption-id",b.id),u.info("Se ha consumido 1 pin para generar el informe."))}}}catch(c){if(c.message.includes("No hay pines disponibles")||c.message.includes("Sin pines"))throw yield p.from("informes_generados").delete().eq("id",b.id),new Error(`No se puede generar el informe: ${c.message}`)}return u.success("Informe completo generado exitosamente"),b.id}catch(t){throw u.error("Error al generar el informe completo"),t}})},generarInformeIndividual(e,a=null,n=null){return l(this,null,function*(){try{const{data:r,error:o}=yield p.from("resultados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento,\n            fecha_nacimiento,\n            genero,\n            institucion\n          )\n        ").eq("id",e).single();if(o)throw o;r&&r.pacientes&&r.pacientes.fecha_nacimiento&&(r.pacientes.edad=m(r.pacientes.fecha_nacimiento));const t={paciente:r.pacientes,resultados:[r],estadisticas:this._calcularEstadisticas([r]),evaluacion:this._generarEvaluacion([r])},{data:c,error:s}=yield p.from("informes_generados").insert({paciente_id:r.paciente_id,tipo_informe:"individual",titulo:a||`Informe Individual - ${r.pacientes.nombre} ${r.pacientes.apellido}`,descripcion:n||"Informe individual de evaluación psicológica",contenido:t,estado:"generado",fecha_generacion:(new Date).toISOString(),metadatos:{version:"1.0",generado_por:"sistema",resultado_id:e}}).select().single();if(s)throw s;try{const{data:e,error:a}=yield p.from("pacientes").select("psicologo_id").eq("id",r.paciente_id).single();if(!a&&(null==e?void 0:e.psicologo_id)){const{data:a,error:n}=yield p.from("test_sessions").select("id, paciente_id, fecha_fin").eq("paciente_id",r.paciente_id).eq("estado","finalizado").is("pin_consumed_at",null).order("fecha_fin",{ascending:!1}).limit(1).single();a&&(yield f.consumePin(e.psicologo_id,r.paciente_id,a.id,c.id),yield p.from("test_sessions").update({pin_consumed_at:(new Date).toISOString()}).eq("id",a.id),u.info("Se ha consumido 1 pin para generar el informe."))}}catch(i){if(i.message.includes("No hay pines disponibles")||i.message.includes("Sin pines"))throw yield p.from("informes_generados").delete().eq("id",c.id),new Error(`No se puede generar el informe: ${i.message}`)}return u.success("Informe individual generado exitosamente"),c.id}catch(r){throw u.error("Error al generar el informe individual"),r}})},archivarInforme(e){return l(this,null,function*(){try{const{error:a}=yield p.from("informes_generados").update({estado:"archivado",fecha_archivado:(new Date).toISOString()}).eq("id",e);if(a)throw a;u.success("Informe archivado exitosamente")}catch(a){throw u.error("Error al archivar el informe"),a}})},eliminarInforme(e){return l(this,null,function*(){try{const{error:a}=yield p.from("informes_generados").delete().eq("id",e);if(a)throw a;u.success("Informe eliminado exitosamente")}catch(a){throw u.error("Error al eliminar el informe"),a}})},obtenerTodosLosInformes(){return l(this,null,function*(){try{const{data:e,error:a}=yield p.from("informes_generados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento\n          )\n        ").order("fecha_generacion",{ascending:!1});if(a)throw a;return e||[]}catch(e){throw u.error("Error al cargar los informes"),e}})},_calcularEstadisticas(e){var a,n;if(!e||0===e.length)return{total_evaluaciones:0,promedio_percentiles:{},aptitudes_destacadas:[],areas_mejora:[]};const i={};["verbal","espacial","atencion_concentracion","razonamiento","numerica","mecanica","ortografia"].forEach(a=>{const n=e.map(e=>{var n;return null==(n=e.percentiles)?void 0:n[a]}).filter(e=>null!=e&&!isNaN(e));n.length>0&&(i[a]=n.reduce((e,a)=>e+a,0)/n.length,n.length)});const r=Object.entries(i).filter(([e,a])=>a>75).map(([e,a])=>({aptitud:e,promedio:Math.round(a)})),o=Object.entries(i).filter(([e,a])=>a<25).map(([e,a])=>({aptitud:e,promedio:Math.round(a)}));return{total_evaluaciones:e.length,promedio_percentiles:Object.fromEntries(Object.entries(i).map(([e,a])=>[e,Math.round(a)])),aptitudes_destacadas:r,areas_mejora:o,fecha_primera_evaluacion:null==(a=e[e.length-1])?void 0:a.fecha_evaluacion,fecha_ultima_evaluacion:null==(n=e[0])?void 0:n.fecha_evaluacion}},_generarInterpretacionesCualitativas(e){return l(this,null,function*(){try{const a=e.map(e=>{var a;return{aptitud_codigo:e.aptitud_id,percentil:(null==(a=e.percentiles)?void 0:a.verbal)||e.percentil,puntaje_directo:e.puntaje_directo,interpretacion:e.interpretacion}});return yield v.generarResumenCualitativo(a)}catch(a){throw a}})},_generarEvaluacion(e){if(!e||0===e.length)return{resumen:"No hay resultados disponibles para evaluar.",recomendaciones:[],observaciones:[]};const a=this._calcularEstadisticas(e),n=[],i=[];a.aptitudes_destacadas.length>0&&n.push(`Potenciar las aptitudes destacadas: ${a.aptitudes_destacadas.map(e=>e.aptitud).join(", ")}`),a.areas_mejora.length>0&&n.push(`Trabajar en las áreas de mejora: ${a.areas_mejora.map(e=>e.aptitud).join(", ")}`),e.length>1&&i.push("Se observa un historial de evaluaciones que permite analizar la evolución.");return{resumen:`Evaluación basada en ${a.total_evaluaciones} evaluación${1!==a.total_evaluaciones?"es":""}. \n${a.aptitudes_destacadas.length>0?`Aptitudes destacadas: ${a.aptitudes_destacadas.length}. `:""}\n${a.areas_mejora.length>0?`Áreas de mejora identificadas: ${a.areas_mejora.length}.`:""}`.trim(),recomendaciones:n,observaciones:i}},generarInformeAutomatico(e,a=null){return l(this,null,function*(){try{const{data:a,error:n}=yield p.from("informes_generados").select("id").eq("paciente_id",e).eq("tipo_informe","completo").eq("estado","generado").single();if(n&&"PGRST116"!==n.code)throw n;if(a)return null;const{data:i,error:r}=yield p.from("pacientes").select("*").eq("id",e).single();if(r)throw r;return yield this.generarInformeCompleto(e,`Informe Automático BAT-7 - ${i.nombre} ${i.apellido}`,"Informe generado automáticamente al completar la evaluación")}catch(a){return null}})},necesitaInformeAutomatico(e){return l(this,null,function*(){try{const{data:a,error:n}=yield p.from("resultados").select("id").eq("paciente_id",e);if(n)throw n;if(!a||0===a.length)return!1;const{data:i,error:r}=yield p.from("informes_generados").select("id").eq("paciente_id",e).eq("tipo_informe","completo").eq("estado","generado").single();if(r&&"PGRST116"!==r.code)throw r;return!i}catch(a){return!1}})},_calcularDatosConcentracion(e){const a=e.find(e=>{var a;return"A"===(null==(a=e.aptitudes)?void 0:a.codigo)}),n=e.find(e=>{var a;return"C"===(null==(a=e.aptitudes)?void 0:a.codigo)});if(!a&&!n)return{disponible:!1,mensaje:"No se encontraron datos de atención para calcular concentración"};const i={disponible:!0,atencion:a?{puntaje_directo:a.puntaje_directo,errores:a.errores||0,percentil:a.percentil}:null,concentracion:n?{valor:n.puntaje_directo,percentil:n.percentil,interpretacion:n.interpretacion,formula_aplicada:`CON = (${(null==a?void 0:a.puntaje_directo)||0} / (${(null==a?void 0:a.puntaje_directo)||0} + ${(null==a?void 0:a.errores)||0})) × 100`}:null};if(a&&!n){const e=a.puntaje_directo||0,n=a.errores||0,r=e+n>0?Math.round(e/(e+n)*100):0;i.concentracion={valor:r,percentil:null,interpretacion:"Calculado automáticamente",formula_aplicada:`CON = (${e} / (${e} + ${n})) × 100 = ${r}%`}}return i},_generarDatosGraficos(e){var a,n;if(!e||0===e.length)return{disponible:!1,mensaje:"No hay datos suficientes para generar gráficos"};const i=e.filter(e=>null!==e.percentil&&void 0!==e.percentil).map(e=>{var a,n;return{aptitud:(null==(a=e.aptitudes)?void 0:a.codigo)||"N/A",nombre:(null==(n=e.aptitudes)?void 0:n.nombre)||"Desconocido",percentil:e.percentil,puntaje_directo:e.puntaje_directo,interpretacion:e.interpretacion}}).sort((e,a)=>a.percentil-e.percentil),r=this._calcularDatosConcentracion(e);let o=null;return r.disponible&&r.concentracion&&(o={valor:r.concentracion.valor,percentil:r.concentracion.percentil,interpretacion:r.concentracion.interpretacion,color:this._getColorByPercentil(r.concentracion.percentil),datos_comparativos:[{categoria:"Muy Bajo",rango:"0-27%",color:"#e74c3c"},{categoria:"Bajo",rango:"28-66%",color:"#f39c12"},{categoria:"Medio",rango:"67-81%",color:"#f1c40f"},{categoria:"Alto",rango:"82-90%",color:"#2ecc71"},{categoria:"Muy Alto",rango:"91-100%",color:"#27ae60"}]}),{disponible:!0,percentiles:{datos:i,titulo:"Percentiles por Aptitud",tipo:"bar"},concentracion:o,resumen:{total_aptitudes:e.length,promedio_percentil:Math.round(i.reduce((e,a)=>e+a.percentil,0)/i.length),aptitud_mas_alta:(null==(a=i[0])?void 0:a.aptitud)||"N/A",aptitud_mas_baja:(null==(n=i[i.length-1])?void 0:n.aptitud)||"N/A"}}},_getColorByPercentil:e=>e>=90?"#27ae60":e>=75?"#2ecc71":e>=50?"#f1c40f":e>=25?"#f39c12":"#e74c3c",generarInformesEnLote(e,a){return l(this,arguments,function*(e,a,n={}){var i;const{titulo:r=null,descripcion:o=null,incluirInterpretaciones:t=!0,strategy:c=null,skipValidation:s=!1}=n;try{const n=(yield d(()=>l(null,null,function*(){const{default:e}=yield import("./BatchProcessingService-oBS5r6Eb.js");return{default:e}}),__vite__mapDeps([7,1,2,3,4,5,0,8,9]))).default;let s=c;if(!s){const r=(yield d(()=>l(null,null,function*(){const{default:e}=yield import("./PinValidationService-Ki4hIVgd.js");return{default:e}}),__vite__mapDeps([0,1,2,3,4,5]))).default,o=(null==(i=(yield r.validateBatchReportGeneration(a,e)).pinStatus)?void 0:i.remainingPins)||0;s=n.getRecommendedStrategy(e.length,o,{reliability:.95,riskTolerance:"medium"})}return yield n.processBatchReports(a,e,{strategy:s,title:r,description:o,incluirInterpretaciones:t,continueOnError:!0,maxConcurrent:3})}catch(u){throw u}})}};export{h as default};
