import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../api/supabaseClient';
import { 
  ERROR_MESSAGES, 
  SUCCESS_MESSAGES, 
  USER_ROLES,
  SORT_CONFIG 
} from '../constants/userManagement';
import { toast } from 'react-toastify';

/**
 * Hook personalizado para manejar operaciones de usuarios con Supabase
 * Implementa paginación del lado del servidor y filtrado optimizado
 */
export const useSupabaseUsers = () => {
  // Estados principales
  const [users, setUsers] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Estados para estadísticas
  const [stats, setStats] = useState({
    total: 0,
    activos: 0,
    inactivos: 0,
    administradores: 0,
    psicologos: 0,
    pacientes: 0
  });

  /**
   * Función para cargar usuarios con filtros y paginación del servidor
   */
  const fetchUsers = useCallback(async (options = {}) => {
    const {
      page = 1,
      pageSize = 10,
      filters = {},
      sortColumn = SORT_CONFIG.DEFAULT_COLUMN,
      sortOrder = SORT_CONFIG.DEFAULT_ORDER
    } = options;

    try {
      setLoading(true);
      setError(null);

      // Construir consulta base
      let query = supabase
        .from('usuarios')
        .select('*', { count: 'exact' });

      // Aplicar filtros de búsqueda
      if (filters.search) {
        query = query.or(`nombre.ilike.%${filters.search}%,apellido.ilike.%${filters.search}%,email.ilike.%${filters.search}%,documento.ilike.%${filters.search}%`);
      }

      // Aplicar filtro de rol
      if (filters.rol) {
        query = query.eq('rol', filters.rol);
      }

      // Aplicar filtro de estado
      if (filters.activo !== undefined) {
        query = query.eq('activo', filters.activo);
      }

      // Aplicar ordenamiento
      query = query.order(sortColumn, { ascending: sortOrder === 'asc' });

      // Aplicar paginación
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);

      // Ejecutar consulta
      const { data, error: queryError, count } = await query;

      if (queryError) {
        throw queryError;
      }

      setUsers(data || []);
      setTotalCount(count || 0);

    } catch (error) {
      console.error('Error fetching users:', error);
      const errorMessage = getErrorMessage(error);
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Función para cargar estadísticas de usuarios
   */
  const fetchStats = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('usuarios')
        .select('rol, activo');

      if (error) {
        throw error;
      }

      const total = data.length;
      const activos = data.filter(u => u.activo).length;
      const inactivos = total - activos;
      const administradores = data.filter(u => u.rol === USER_ROLES.ADMIN).length;
      const psicologos = data.filter(u => u.rol === USER_ROLES.PSYCHOLOGIST).length;
      const pacientes = data.filter(u => u.rol === USER_ROLES.PATIENT).length;

      setStats({
        total,
        activos,
        inactivos,
        administradores,
        psicologos,
        pacientes
      });

    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  }, []);

  /**
   * Función para crear usuario con transacción
   */
  const createUser = useCallback(async (userData) => {
    try {
      setLoading(true);

      // Validar email único
      const { data: existingUser } = await supabase
        .from('usuarios')
        .select('id')
        .eq('email', userData.email)
        .single();

      if (existingUser) {
        throw new Error(ERROR_MESSAGES.EMAIL_ALREADY_EXISTS);
      }

      // Crear usuario en auth.users
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true,
        user_metadata: {
          nombre: userData.nombre,
          apellido: userData.apellido,
          documento: userData.documento,
          rol: userData.rol
        }
      });

      if (authError) {
        throw authError;
      }

      // Crear perfil en tabla usuarios
      const { error: profileError } = await supabase
        .from('usuarios')
        .insert([{
          id: authData.user.id,
          email: userData.email,
          nombre: userData.nombre,
          apellido: userData.apellido,
          documento: userData.documento,
          rol: userData.rol,
          activo: userData.activo,
          fecha_creacion: new Date().toISOString()
        }]);

      if (profileError) {
        // Si falla la creación del perfil, intentar limpiar el usuario de auth
        try {
          await supabase.auth.admin.deleteUser(authData.user.id);
        } catch (cleanupError) {
          console.error('Error cleaning up auth user:', cleanupError);
        }
        throw profileError;
      }

      toast.success(SUCCESS_MESSAGES.USER_CREATED);
      return { success: true, user: authData.user };

    } catch (error) {
      console.error('Error creating user:', error);
      const errorMessage = getErrorMessage(error);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Función para actualizar usuario
   */
  const updateUser = useCallback(async (userId, userData) => {
    try {
      setLoading(true);

      // Actualizar en tabla usuarios
      const { error } = await supabase
        .from('usuarios')
        .update({
          nombre: userData.nombre,
          apellido: userData.apellido,
          documento: userData.documento,
          rol: userData.rol,
          activo: userData.activo,
          fecha_actualizacion: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      // Si se cambió la contraseña, actualizar en auth
      if (userData.password) {
        const { error: authError } = await supabase.auth.admin.updateUserById(
          userId,
          { password: userData.password }
        );

        if (authError) {
          console.warn('Error updating password:', authError);
          // No lanzar error, solo advertir
        }
      }

      toast.success(SUCCESS_MESSAGES.USER_UPDATED);
      return { success: true };

    } catch (error) {
      console.error('Error updating user:', error);
      const errorMessage = getErrorMessage(error);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Función para cambiar estado del usuario
   */
  const toggleUserStatus = useCallback(async (userId, currentStatus) => {
    try {
      setLoading(true);
      const newStatus = !currentStatus;

      const { error } = await supabase
        .from('usuarios')
        .update({
          activo: newStatus,
          fecha_actualizacion: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      const message = newStatus ? SUCCESS_MESSAGES.USER_ACTIVATED : SUCCESS_MESSAGES.USER_DEACTIVATED;
      toast.success(message);
      return { success: true };

    } catch (error) {
      console.error('Error toggling user status:', error);
      const errorMessage = getErrorMessage(error);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Función para eliminar usuario (soft delete)
   */
  const deleteUser = useCallback(async (userId) => {
    try {
      setLoading(true);

      // Soft delete: marcar como inactivo y agregar timestamp de eliminación
      const { error } = await supabase
        .from('usuarios')
        .update({
          activo: false,
          fecha_eliminacion: new Date().toISOString(),
          fecha_actualizacion: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      toast.success(SUCCESS_MESSAGES.USER_DELETED);
      return { success: true };

    } catch (error) {
      console.error('Error deleting user:', error);
      const errorMessage = getErrorMessage(error);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Función para interpretar errores de Supabase
   */
  const getErrorMessage = (error) => {
    if (error.message?.includes('duplicate key')) {
      return ERROR_MESSAGES.EMAIL_ALREADY_EXISTS;
    }
    if (error.message?.includes('network')) {
      return ERROR_MESSAGES.NETWORK_ERROR;
    }
    if (error.message?.includes('unauthorized')) {
      return ERROR_MESSAGES.UNAUTHORIZED;
    }
    return error.message || ERROR_MESSAGES.LOAD_USERS_ERROR;
  };

  return {
    // Estados
    users,
    totalCount,
    loading,
    error,
    stats,

    // Funciones
    fetchUsers,
    fetchStats,
    createUser,
    updateUser,
    toggleUserStatus,
    deleteUser,

    // Utilidades
    refreshData: () => {
      fetchUsers();
      fetchStats();
    }
  };
};
