/**
 * Script simplificado para arreglar el problema de login del administrador
 * 
 * Este script:
 * 1. Verifica que el usuario existe en la tabla usuarios
 * 2. Actualiza el email en auth.users si es necesario
 * 3. Prueba la autenticación
 * 
 * Ejecutar con: node src/scripts/fixAdminLoginSimple.js
 */

import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Datos del administrador
const ADMIN_DATA = {
  email: '<EMAIL>',
  password: '13716261',
  documento: '13716261',
  nombre: 'Administrador',
  apellido: 'Principal',
  tipo_usuario: 'administrador'
};

/**
 * Verifica si el usuario administrador existe en la tabla usuarios
 */
async function verifyUserInTable() {
  console.log('\n🔍 Verificando usuario en tabla usuarios...');
  
  try {
    const { data: userProfile, error: profileError } = await supabase
      .from('usuarios')
      .select('*')
      .eq('documento', ADMIN_DATA.documento)
      .single();
    
    if (profileError && profileError.code !== 'PGRST116') {
      console.error('❌ Error buscando usuario:', profileError);
      return null;
    }
    
    if (!userProfile) {
      console.log('⚠️  Usuario administrador no encontrado en tabla usuarios');
      return null;
    }
    
    console.log('✅ Usuario encontrado en tabla usuarios:', {
      id: userProfile.id,
      nombre: userProfile.nombre,
      apellido: userProfile.apellido,
      documento: userProfile.documento,
      tipo_usuario: userProfile.tipo_usuario || userProfile.rol || 'sin_rol',
      activo: userProfile.activo
    });
    
    return userProfile;
    
  } catch (error) {
    console.error('❌ Error verificando usuario:', error);
    return null;
  }
}

/**
 * Prueba la autenticación del administrador
 */
async function testAdminLogin() {
  console.log('\n🧪 Probando autenticación del administrador...');
  
  try {
    // Intentar login con email
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: ADMIN_DATA.email,
      password: ADMIN_DATA.password
    });
    
    if (loginError) {
      console.error('❌ Error en autenticación:', loginError.message);
      return false;
    }
    
    if (!loginData.user) {
      console.error('❌ No se obtuvo usuario en la respuesta');
      return false;
    }
    
    console.log('✅ Autenticación exitosa:', {
      id: loginData.user.id,
      email: loginData.user.email,
      confirmed_at: loginData.user.email_confirmed_at
    });
    
    // Cerrar sesión para limpiar
    await supabase.auth.signOut();
    console.log('✅ Sesión cerrada correctamente');
    
    return loginData.user;
    
  } catch (error) {
    console.error('❌ Error probando autenticación:', error);
    return false;
  }
}

/**
 * Crea el usuario en auth.users si no existe
 */
async function createUserInAuth() {
  console.log('\n👤 Creando usuario en auth.users...');
  
  try {
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: ADMIN_DATA.email,
      password: ADMIN_DATA.password,
      options: {
        data: {
          nombre: ADMIN_DATA.nombre,
          apellido: ADMIN_DATA.apellido,
          documento: ADMIN_DATA.documento
        }
      }
    });
    
    if (signUpError) {
      if (signUpError.message.includes('already registered')) {
        console.log('✅ Usuario ya existe en auth.users');
        return true;
      } else {
        console.error('❌ Error creando usuario:', signUpError.message);
        return false;
      }
    }
    
    if (signUpData.user) {
      console.log('✅ Usuario creado en auth.users:', signUpData.user.id);
      return true;
    }
    
    return false;
    
  } catch (error) {
    console.error('❌ Error creando usuario en auth:', error);
    return false;
  }
}

/**
 * Actualiza el ID en la tabla usuarios si es necesario
 */
async function updateUserTableId(userProfile, authUser) {
  console.log('\n🔄 Verificando sincronización de IDs...');
  
  if (userProfile.id === authUser.id) {
    console.log('✅ IDs ya están sincronizados');
    return true;
  }
  
  console.log(`⚠️  IDs no coinciden. Tabla: ${userProfile.id}, Auth: ${authUser.id}`);
  console.log('🔄 Actualizando ID en tabla usuarios...');
  
  try {
    // Actualizar el ID en la tabla usuarios
    const { error: updateError } = await supabase
      .from('usuarios')
      .update({ id: authUser.id })
      .eq('documento', ADMIN_DATA.documento);
    
    if (updateError) {
      console.error('❌ Error actualizando ID:', updateError);
      return false;
    }
    
    console.log('✅ ID actualizado en tabla usuarios');
    return true;
    
  } catch (error) {
    console.error('❌ Error actualizando ID:', error);
    return false;
  }
}

/**
 * Función principal
 */
async function main() {
  console.log('🔧 ARREGLANDO PROBLEMA DE LOGIN DEL ADMINISTRADOR (VERSIÓN SIMPLE)');
  console.log('================================================================\n');
  
  try {
    // Paso 1: Verificar usuario en tabla
    console.log('📋 PASO 1: Verificando usuario en tabla usuarios');
    const userProfile = await verifyUserInTable();
    
    if (!userProfile) {
      console.error('❌ Usuario no encontrado en tabla usuarios');
      console.log('\n💡 SOLUCIÓN MANUAL:');
      console.log('1. Ve al panel de Supabase');
      console.log('2. Ejecuta el script SQL: src/sql/fix_admin_user_complete.sql');
      process.exit(1);
    }
    
    // Paso 2: Crear usuario en auth.users si no existe
    console.log('\n📋 PASO 2: Verificando usuario en auth.users');
    const authCreated = await createUserInAuth();
    
    if (!authCreated) {
      console.error('❌ No se pudo crear usuario en auth.users');
      process.exit(1);
    }
    
    // Paso 3: Probar autenticación
    console.log('\n📋 PASO 3: Probando autenticación');
    const authUser = await testAdminLogin();
    
    if (!authUser) {
      console.error('❌ La autenticación no funciona');
      process.exit(1);
    }
    
    // Paso 4: Sincronizar IDs si es necesario
    console.log('\n📋 PASO 4: Sincronizando datos');
    const syncSuccess = await updateUserTableId(userProfile, authUser);
    
    if (!syncSuccess) {
      console.error('❌ Error sincronizando datos');
      process.exit(1);
    }
    
    // Paso 5: Prueba final
    console.log('\n📋 PASO 5: Prueba final de autenticación');
    const finalTest = await testAdminLogin();
    
    if (!finalTest) {
      console.error('❌ La prueba final falló');
      process.exit(1);
    }
    
    console.log('\n🎉 ¡PROBLEMA DE LOGIN SOLUCIONADO!');
    console.log('==================================');
    console.log('✅ Usuario administrador verificado');
    console.log('✅ Usuario existe en auth.users');
    console.log('✅ Usuario existe en tabla usuarios');
    console.log('✅ IDs sincronizados correctamente');
    console.log('✅ Autenticación funcionando');
    console.log('\n🔑 CREDENCIALES DE ACCESO:');
    console.log(`   Email: ${ADMIN_DATA.email}`);
    console.log(`   Contraseña: ${ADMIN_DATA.password}`);
    console.log(`   Documento: ${ADMIN_DATA.documento}`);
    console.log('\n🚀 ¡Ya puedes iniciar sesión en el sistema!');
    
  } catch (error) {
    console.error('\n❌ Error fatal durante el proceso:', error);
    process.exit(1);
  }
}

// Ejecutar el script
main();
