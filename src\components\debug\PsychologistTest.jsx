import React, { useState, useEffect } from 'react';
import supabaseService from '../../services/supabaseService';
import usePsychologists from '../../hooks/usePsychologists';

/**
 * Componente de prueba para verificar la carga de psicólogos
 */
const PsychologistTest = () => {
  const [directData, setDirectData] = useState([]);
  const [directLoading, setDirectLoading] = useState(false);
  const [directError, setDirectError] = useState(null);
  
  // Usar el hook personalizado
  const { psychologists: hookData, loading: hookLoading, error: hookError } = usePsychologists();

  // Función para cargar datos directamente
  const loadDirectData = async () => {
    try {
      setDirectLoading(true);
      setDirectError(null);
      
      const { data, error } = await supabaseService.getPsychologists();
      
      if (error) {
        throw error;
      }
      
      console.log('✅ Datos directos de Supabase:', data);
      setDirectData(data || []);
    } catch (error) {
      console.error('❌ Error cargando datos directos:', error);
      setDirectError(error.message);
    } finally {
      setDirectLoading(false);
    }
  };

  useEffect(() => {
    loadDirectData();
  }, []);

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Prueba de Carga de Psicólogos</h2>
      
      {/* Datos directos de Supabase */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Datos Directos de Supabase</h3>
        {directLoading && <p className="text-blue-600">Cargando datos directos...</p>}
        {directError && <p className="text-red-600">Error: {directError}</p>}
        {!directLoading && !directError && (
          <div>
            <p className="mb-2">Total de psicólogos: {directData.length}</p>
            <div className="bg-gray-100 p-4 rounded max-h-40 overflow-y-auto">
              <pre>{JSON.stringify(directData, null, 2)}</pre>
            </div>
          </div>
        )}
      </div>

      {/* Datos del hook personalizado */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Datos del Hook usePsychologists</h3>
        {hookLoading && <p className="text-blue-600">Cargando datos del hook...</p>}
        {hookError && <p className="text-red-600">Error: {hookError}</p>}
        {!hookLoading && !hookError && (
          <div>
            <p className="mb-2">Total de psicólogos transformados: {hookData.length}</p>
            <div className="bg-gray-100 p-4 rounded max-h-40 overflow-y-auto">
              <pre>{JSON.stringify(hookData, null, 2)}</pre>
            </div>
          </div>
        )}
      </div>

      {/* Lista desplegable de prueba */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Lista Desplegable de Prueba</h3>
        <select className="w-full px-4 py-2 border border-gray-300 rounded-lg">
          <option value="">Seleccionar psicólogo...</option>
          {hookData.map((psychologist) => (
            <option key={psychologist.id} value={psychologist.id}>
              {psychologist.nombre} {psychologist.apellido} - {psychologist.email}
            </option>
          ))}
        </select>
        {hookData.length === 0 && !hookLoading && (
          <p className="text-red-600 mt-2">⚠️ No hay psicólogos disponibles en la lista</p>
        )}
      </div>

      {/* Botón para recargar */}
      <button
        onClick={loadDirectData}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
      >
        Recargar Datos
      </button>
    </div>
  );
};

export default PsychologistTest;
