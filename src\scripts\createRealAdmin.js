/**
 * Script simplificado para crear únicamente el usuario administrador real
 * Ejecutar con: node src/scripts/createRealAdmin.js
 */

import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Datos del administrador real
const ADMIN_DATA = {
  email: '<EMAIL>',
  password: '13716261',
  documento: '13716261',
  nombre: 'Administrador',
  apellido: 'Principal',
  tipo_usuario: 'administrador'
};

async function createRealAdmin() {
  console.log('🚀 Creando usuario administrador real...');
  console.log(`📧 Email: ${ADMIN_DATA.email}`);
  console.log(`📄 Documento: ${ADMIN_DATA.documento}`);
  
  try {
    // Paso 1: Verificar si el usuario ya existe
    console.log('\n🔍 Verificando si el usuario ya existe...');
    
    const { data: existingAuth, error: authCheckError } = await supabase.auth.signInWithPassword({
      email: ADMIN_DATA.email,
      password: ADMIN_DATA.password
    });

    if (!authCheckError && existingAuth.user) {
      console.log('✅ Usuario ya existe en auth.users');
      
      // Verificar en tabla usuarios
      const { data: existingUser, error: userCheckError } = await supabase
        .from('usuarios')
        .select('*')
        .eq('id', existingAuth.user.id)
        .single();

      if (userCheckError && userCheckError.code !== 'PGRST116') {
        console.error('❌ Error al verificar usuario en tabla usuarios:', userCheckError);
        return false;
      }

      if (existingUser) {
        console.log('✅ Usuario ya existe en tabla usuarios');
        console.log('📋 Datos actuales:', {
          email: ADMIN_DATA.email,
          documento: existingUser.documento,
          nombre: existingUser.nombre,
          apellido: existingUser.apellido,
          tipo_usuario: existingUser.tipo_usuario,
          activo: existingUser.activo
        });
        
        // Actualizar datos si es necesario
        const { error: updateError } = await supabase
          .from('usuarios')
          .update({
            documento: ADMIN_DATA.documento,
            nombre: ADMIN_DATA.nombre,
            apellido: ADMIN_DATA.apellido,
            tipo_usuario: ADMIN_DATA.tipo_usuario,
            activo: true,
            ultimo_acceso: new Date().toISOString()
          })
          .eq('id', existingAuth.user.id);

        if (updateError) {
          console.error('❌ Error al actualizar usuario:', updateError);
          return false;
        }

        console.log('✅ Usuario actualizado correctamente');
        return true;
      } else {
        // Crear registro en tabla usuarios
        console.log('📝 Creando registro en tabla usuarios...');
        const { error: insertError } = await supabase
          .from('usuarios')
          .insert({
            id: existingAuth.user.id,
            documento: ADMIN_DATA.documento,
            nombre: ADMIN_DATA.nombre,
            apellido: ADMIN_DATA.apellido,
            tipo_usuario: ADMIN_DATA.tipo_usuario,
            activo: true,
            fecha_creacion: new Date().toISOString()
          });

        if (insertError) {
          console.error('❌ Error al crear registro en tabla usuarios:', insertError);
          return false;
        }

        console.log('✅ Registro creado en tabla usuarios');
        return true;
      }
    }

    // Paso 2: Crear nuevo usuario
    console.log('👤 Creando nuevo usuario en auth.users...');
    
    const { data: authData, error: signUpError } = await supabase.auth.signUp({
      email: ADMIN_DATA.email,
      password: ADMIN_DATA.password,
      options: {
        data: {
          nombre: ADMIN_DATA.nombre,
          apellido: ADMIN_DATA.apellido,
          documento: ADMIN_DATA.documento,
          tipo_usuario: ADMIN_DATA.tipo_usuario
        }
      }
    });

    if (signUpError) {
      console.error('❌ Error al crear usuario en auth.users:', signUpError);
      return false;
    }

    if (!authData.user) {
      console.error('❌ No se pudo crear el usuario');
      return false;
    }

    console.log('✅ Usuario creado en auth.users');
    console.log('🆔 ID del usuario:', authData.user.id);

    // Paso 3: Crear registro en tabla usuarios
    console.log('📝 Creando registro en tabla usuarios...');
    
    const { error: insertError } = await supabase
      .from('usuarios')
      .insert({
        id: authData.user.id,
        documento: ADMIN_DATA.documento,
        nombre: ADMIN_DATA.nombre,
        apellido: ADMIN_DATA.apellido,
        tipo_usuario: ADMIN_DATA.tipo_usuario,
        activo: true,
        fecha_creacion: new Date().toISOString()
      });

    if (insertError) {
      console.error('❌ Error al crear registro en tabla usuarios:', insertError);
      return false;
    }

    console.log('✅ Registro creado en tabla usuarios');
    return true;

  } catch (error) {
    console.error('❌ Error inesperado:', error);
    return false;
  }
}

async function verifyAdmin() {
  console.log('\n🔍 Verificando configuración del administrador...');
  
  try {
    // Verificar login
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: ADMIN_DATA.email,
      password: ADMIN_DATA.password
    });

    if (loginError) {
      console.error('❌ Error al verificar login:', loginError);
      return false;
    }

    console.log('✅ Login verificado correctamente');

    // Verificar datos en tabla usuarios
    const { data: userData, error: userError } = await supabase
      .from('usuarios')
      .select('*')
      .eq('id', loginData.user.id)
      .single();

    if (userError) {
      console.error('❌ Error al obtener datos del usuario:', userError);
      return false;
    }

    console.log('✅ Datos del usuario verificados:');
    console.log('📋 Información completa:');
    console.log(`   ID: ${userData.id}`);
    console.log(`   Email: ${ADMIN_DATA.email}`);
    console.log(`   Documento: ${userData.documento}`);
    console.log(`   Nombre: ${userData.nombre} ${userData.apellido}`);
    console.log(`   Tipo: ${userData.tipo_usuario}`);
    console.log(`   Activo: ${userData.activo ? 'Sí' : 'No'}`);
    console.log(`   Creado: ${userData.fecha_creacion}`);

    return true;
  } catch (error) {
    console.error('❌ Error durante la verificación:', error);
    return false;
  }
}

async function main() {
  console.log('🔐 CREACIÓN DE USUARIO ADMINISTRADOR REAL');
  console.log('==========================================\n');
  
  try {
    // Crear administrador
    const createSuccess = await createRealAdmin();
    if (!createSuccess) {
      console.error('\n❌ Falló la creación del administrador');
      process.exit(1);
    }

    // Verificar configuración
    const verifySuccess = await verifyAdmin();
    if (!verifySuccess) {
      console.error('\n❌ Falló la verificación del administrador');
      process.exit(1);
    }

    console.log('\n🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!');
    console.log('=====================================');
    console.log('🔑 CREDENCIALES DE ACCESO:');
    console.log(`   Email: ${ADMIN_DATA.email}`);
    console.log(`   Contraseña: ${ADMIN_DATA.password}`);
    console.log(`   Documento: ${ADMIN_DATA.documento}`);
    console.log('\n📝 NOTAS IMPORTANTES:');
    console.log('   - Cambia la contraseña después del primer login');
    console.log('   - El usuario tiene permisos de administrador completo');
    console.log('   - Puedes acceder con email o documento');
    console.log('\n🚀 ¡Ya puedes iniciar sesión en el sistema!');

  } catch (error) {
    console.error('\n❌ Error fatal:', error);
    process.exit(1);
  }
}

// Ejecutar el script
main();
