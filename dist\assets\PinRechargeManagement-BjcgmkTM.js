var e=Object.defineProperty,s=Object.defineProperties,a=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,n=(s,a,r)=>a in s?e(s,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):s[a]=r,i=(e,s)=>{for(var a in s||(s={}))t.call(s,a)&&n(e,a,s[a]);if(r)for(var a of r(s))l.call(s,a)&&n(e,a,s[a]);return e},c=(e,r)=>s(e,a(r)),d=(e,s,a)=>new Promise((r,t)=>{var l=e=>{try{i(a.next(e))}catch(s){t(s)}},n=e=>{try{i(a.throw(e))}catch(s){t(s)}},i=e=>e.done?r(e.value):Promise.resolve(e.value).then(l,n);i((a=a.apply(e,s)).next())});import{r as o,Q as x,j as m,M as h,t as u,N as g,O as p,h as j,z as y,P as b,R as N,S as v,T as f,J as w,U as P,D as C}from"./vendor-BqMjyOVw.js";import{B as A,C as S,a as k,b as z}from"./index-Bdl1jgS_.js";const M=()=>{const[e,s]=o.useState([]),[a,r]=o.useState([]),[t,l]=o.useState(!1),[n,N]=o.useState("pending"),[v,f]=o.useState(null),[w,P]=o.useState(!1),[C,z]=o.useState({search:"",urgency:"all",dateRange:"all"});o.useEffect(()=>{M()},[]);const M=()=>d(null,null,function*(){try{l(!0);const e=[{id:"1",psychologistId:"psy-001",psychologistName:"Dr. Juan Pérez",psychologistEmail:"<EMAIL>",requestedPins:100,currentPins:5,urgency:"high",reason:"Necesito generar informes urgentes para mis pacientes",status:"pending",createdAt:(new Date).toISOString(),metadata:{lastActivity:"2024-01-15",totalReports:45,averageMonthly:25}},{id:"2",psychologistId:"psy-002",psychologistName:"Dra. María García",psychologistEmail:"<EMAIL>",requestedPins:50,currentPins:0,urgency:"urgent",reason:"Se agotaron mis pines y tengo pacientes esperando",status:"pending",createdAt:new Date(Date.now()-864e5).toISOString(),metadata:{lastActivity:"2024-01-14",totalReports:78,averageMonthly:35}}],a=[{id:"psy-001",name:"Dr. Juan Pérez",email:"<EMAIL>",currentPins:5},{id:"psy-002",name:"Dra. María García",email:"<EMAIL>",currentPins:0},{id:"psy-003",name:"Dr. Carlos López",email:"<EMAIL>",currentPins:25}];s(e),r(a)}catch(e){x.error("Error al cargar los datos")}finally{l(!1)}}),O=(e,a)=>d(null,null,function*(){try{s(s=>s.map(s=>s.id===e?c(i({},s),{status:"approved",approvedPins:a,processedAt:(new Date).toISOString()}):s)),x.success(`Solicitud aprobada: ${a} pines asignados`),f(null)}catch(r){x.error("Error al aprobar la solicitud")}}),E=(e,a)=>d(null,null,function*(){try{s(s=>s.map(s=>s.id===e?c(i({},s),{status:"rejected",rejectionReason:a,processedAt:(new Date).toISOString()}):s)),x.success("Solicitud rechazada"),f(null)}catch(r){x.error("Error al rechazar la solicitud")}}),I=(e,s,a)=>d(null,null,function*(){try{x.success(`${s} pines asignados manualmente`),P(!1),r(a=>a.map(a=>a.id===e?c(i({},a),{currentPins:a.currentPins+s}):a))}catch(a){x.error("Error en la asignación manual")}}),B=e.filter(e=>("pending"!==n||"pending"===e.status)&&(("processed"!==n||"pending"!==e.status)&&(!(C.search&&!e.psychologistName.toLowerCase().includes(C.search.toLowerCase()))&&("all"===C.urgency||e.urgency===C.urgency))));return m.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-7xl",children:[m.jsxs("div",{className:"mb-8",children:[m.jsxs("div",{className:"flex items-center justify-between mb-4",children:[m.jsxs("div",{children:[m.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Gestión de Recargas de Pines"}),m.jsx("p",{className:"text-gray-600",children:"Administrar solicitudes y asignaciones de pines"})]}),m.jsxs(A,{onClick:()=>P(!0),className:"bg-blue-600 hover:bg-blue-700 text-white",children:[m.jsx(h,{className:"mr-2"}),"Asignación Manual"]})]}),m.jsxs("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg",children:[m.jsxs("button",{onClick:()=>N("pending"),className:"px-4 py-2 text-sm font-medium rounded-md transition-colors "+("pending"===n?"bg-white text-blue-700 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[m.jsx(u,{className:"inline mr-2"}),"Pendientes (",e.filter(e=>"pending"===e.status).length,")"]}),m.jsxs("button",{onClick:()=>N("processed"),className:"px-4 py-2 text-sm font-medium rounded-md transition-colors "+("processed"===n?"bg-white text-blue-700 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[m.jsx(g,{className:"inline mr-2"}),"Procesadas (",e.filter(e=>"pending"!==e.status).length,")"]}),m.jsxs("button",{onClick:()=>N("manual"),className:"px-4 py-2 text-sm font-medium rounded-md transition-colors "+("manual"===n?"bg-white text-blue-700 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[m.jsx(p,{className:"inline mr-2"}),"Psicólogos (",a.length,")"]})]})]}),("pending"===n||"processed"===n)&&m.jsx(S,{className:"mb-6",children:m.jsx(k,{className:"py-4",children:m.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[m.jsxs("div",{className:"flex items-center space-x-2",children:[m.jsx(j,{className:"text-gray-400"}),m.jsx("input",{type:"text",placeholder:"Buscar psicólogo...",value:C.search,onChange:e=>z(s=>c(i({},s),{search:e.target.value})),className:"px-3 py-1 border border-gray-300 rounded text-sm"})]}),m.jsxs("div",{className:"flex items-center space-x-2",children:[m.jsx(y,{className:"text-gray-400"}),m.jsxs("select",{value:C.urgency,onChange:e=>z(s=>c(i({},s),{urgency:e.target.value})),className:"px-3 py-1 border border-gray-300 rounded text-sm",children:[m.jsx("option",{value:"all",children:"Todas las urgencias"}),m.jsx("option",{value:"low",children:"Baja"}),m.jsx("option",{value:"normal",children:"Normal"}),m.jsx("option",{value:"high",children:"Alta"}),m.jsx("option",{value:"urgent",children:"Urgente"})]})]}),m.jsxs(A,{size:"sm",variant:"outline",children:[m.jsx(b,{className:"mr-1"}),"Exportar"]})]})})}),"pending"===n&&m.jsx(D,{requests:B,onApprove:O,onReject:E,onViewDetails:f,loading:t}),"processed"===n&&m.jsx(R,{requests:B,loading:t}),"manual"===n&&m.jsx(q,{psychologists:a,onManualAssign:I,loading:t}),v&&m.jsx(H,{request:v,onClose:()=>f(null),onApprove:O,onReject:E}),w&&m.jsx(V,{psychologists:a,onClose:()=>P(!1),onAssign:I})]})},D=({requests:e,onApprove:s,onReject:a,onViewDetails:r,loading:t})=>t?m.jsx("div",{className:"space-y-4",children:[...Array(3)].map((e,s)=>m.jsx(S,{children:m.jsxs(k,{className:"animate-pulse",children:[m.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),m.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})},s))}):0===e.length?m.jsx(S,{children:m.jsxs(k,{className:"text-center py-12",children:[m.jsx(u,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),m.jsx("p",{className:"text-gray-500",children:"No hay solicitudes pendientes"})]})}):m.jsx("div",{className:"space-y-4",children:e.map(e=>m.jsx(O,{request:e,onApprove:s,onReject:a,onViewDetails:r},e.id))}),R=({requests:e,loading:s})=>s?m.jsx("div",{className:"text-center py-8",children:"Cargando..."}):0===e.length?m.jsx(S,{children:m.jsxs(k,{className:"text-center py-12",children:[m.jsx(g,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),m.jsx("p",{className:"text-gray-500",children:"No hay solicitudes procesadas"})]})}):m.jsx("div",{className:"space-y-4",children:e.map(e=>m.jsx(E,{request:e},e.id))}),q=({psychologists:e,onManualAssign:s,loading:a})=>a?m.jsx("div",{className:"text-center py-8",children:"Cargando..."}):m.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.map(e=>m.jsx(I,{psychologist:e,onManualAssign:s},e.id))}),O=({request:e,onApprove:s,onReject:a,onViewDetails:r})=>m.jsx(S,{children:m.jsxs(k,{children:[m.jsx("div",{className:"flex items-start justify-between mb-4",children:m.jsxs("div",{className:"flex-1",children:[m.jsxs("div",{className:"flex items-center space-x-3 mb-2",children:[m.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:e.psychologistName}),m.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full border ${(e=>{switch(e){case"urgent":return"bg-red-100 text-red-800 border-red-200";case"high":return"bg-orange-100 text-orange-800 border-orange-200";case"normal":return"bg-blue-100 text-blue-800 border-blue-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(e.urgency)}`,children:(e=>{switch(e){case"urgent":return"Urgente";case"high":return"Alta";case"normal":default:return"Normal";case"low":return"Baja"}})(e.urgency)})]}),m.jsx("p",{className:"text-sm text-gray-600 mb-2",children:e.psychologistEmail}),m.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500 mb-3",children:[m.jsxs("div",{className:"flex items-center",children:[m.jsx(w,{className:"mr-1"}),m.jsxs("span",{children:["Actual: ",e.currentPins]})]}),m.jsxs("div",{className:"flex items-center",children:[m.jsx(h,{className:"mr-1"}),m.jsxs("span",{children:["Solicita: ",e.requestedPins]})]}),m.jsxs("div",{className:"flex items-center",children:[m.jsx(P,{className:"mr-1"}),m.jsx("span",{children:v(new Date(e.createdAt),"dd/MM/yyyy HH:mm",{locale:f})})]})]}),m.jsx("p",{className:"text-sm text-gray-700 bg-gray-50 p-3 rounded-lg",children:e.reason})]})}),m.jsxs("div",{className:"flex space-x-3",children:[m.jsxs(A,{onClick:()=>r(e),size:"sm",variant:"outline",children:[m.jsx(C,{className:"mr-1"}),"Ver Detalles"]}),m.jsxs(A,{onClick:()=>s(e.id,e.requestedPins),size:"sm",className:"bg-green-600 hover:bg-green-700 text-white",children:[m.jsx(g,{className:"mr-1"}),"Aprobar"]}),m.jsxs(A,{onClick:()=>a(e.id,"Rechazado por administrador"),size:"sm",className:"bg-red-600 hover:bg-red-700 text-white",children:[m.jsx(N,{className:"mr-1"}),"Rechazar"]})]})]})}),E=({request:e})=>{const s="approved"===e.status;return m.jsx(S,{children:m.jsx(k,{children:m.jsx("div",{className:"flex items-start justify-between",children:m.jsxs("div",{className:"flex-1",children:[m.jsxs("div",{className:"flex items-center space-x-3 mb-2",children:[m.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:e.psychologistName}),m.jsx("span",{className:"px-2 py-1 text-xs font-medium rounded-full "+(s?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:s?"Aprobado":"Rechazado"})]}),m.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500 mb-2",children:[m.jsxs("span",{children:["Solicitó: ",e.requestedPins," pines"]}),s&&m.jsxs("span",{children:["Aprobado: ",e.approvedPins," pines"]}),m.jsxs("span",{children:["Procesado: ",v(new Date(e.processedAt),"dd/MM/yyyy HH:mm",{locale:f})]})]}),e.rejectionReason&&m.jsxs("p",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:["Motivo: ",e.rejectionReason]})]})})})})},I=({psychologist:e,onManualAssign:s})=>{const[a,r]=o.useState(!1),[t,l]=o.useState(50),[n,i]=o.useState("");return m.jsx(S,{children:m.jsxs(k,{children:[m.jsxs("div",{className:"text-center mb-4",children:[m.jsx("h3",{className:"font-semibold text-gray-900 mb-1",children:e.name}),m.jsx("p",{className:"text-sm text-gray-600 mb-3",children:e.email}),m.jsxs("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[m.jsx(w,{className:"text-blue-600"}),m.jsx("span",{className:"text-2xl font-bold text-blue-600",children:e.currentPins}),m.jsx("span",{className:"text-sm text-gray-500",children:"pines"})]})]}),a?m.jsxs("div",{className:"space-y-3",children:[m.jsx("input",{type:"number",value:t,onChange:e=>l(parseInt(e.target.value)||0),placeholder:"Cantidad de pines",className:"w-full px-3 py-2 border border-gray-300 rounded text-sm",min:"1"}),m.jsx("textarea",{value:n,onChange:e=>i(e.target.value),placeholder:"Motivo (opcional)",className:"w-full px-3 py-2 border border-gray-300 rounded text-sm",rows:"2"}),m.jsxs("div",{className:"flex space-x-2",children:[m.jsx(A,{onClick:()=>{t>0&&(s(e.id,t,n),r(!1),l(50),i(""))},size:"sm",className:"flex-1 bg-green-600 hover:bg-green-700 text-white",children:"Asignar"}),m.jsx(A,{onClick:()=>r(!1),size:"sm",variant:"outline",className:"flex-1",children:"Cancelar"})]})]}):m.jsxs(A,{onClick:()=>r(!0),size:"sm",className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:[m.jsx(h,{className:"mr-1"}),"Asignar Pines"]})]})})},H=({request:e,onClose:s,onApprove:a,onReject:r})=>{var t,l,n;const[i,c]=o.useState(e.requestedPins),[d,x]=o.useState(""),[h,u]=o.useState(!1);return m.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:m.jsx("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto",children:m.jsxs(S,{children:[m.jsx(z,{className:"bg-blue-50 border-b",children:m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Detalles de Solicitud"}),m.jsx("button",{onClick:s,className:"text-gray-400 hover:text-gray-600",children:m.jsx(N,{})})]})}),m.jsxs(k,{className:"space-y-6",children:[m.jsxs("div",{children:[m.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"Información del Psicólogo"}),m.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg space-y-2",children:[m.jsxs("p",{children:[m.jsx("strong",{children:"Nombre:"})," ",e.psychologistName]}),m.jsxs("p",{children:[m.jsx("strong",{children:"Email:"})," ",e.psychologistEmail]}),m.jsxs("p",{children:[m.jsx("strong",{children:"Pines actuales:"})," ",e.currentPins]}),m.jsxs("p",{children:[m.jsx("strong",{children:"Última actividad:"})," ",null==(t=e.metadata)?void 0:t.lastActivity]}),m.jsxs("p",{children:[m.jsx("strong",{children:"Total informes:"})," ",null==(l=e.metadata)?void 0:l.totalReports]}),m.jsxs("p",{children:[m.jsx("strong",{children:"Promedio mensual:"})," ",null==(n=e.metadata)?void 0:n.averageMonthly]})]})]}),m.jsxs("div",{children:[m.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"Detalles de la Solicitud"}),m.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg space-y-2",children:[m.jsxs("p",{children:[m.jsx("strong",{children:"Pines solicitados:"})," ",e.requestedPins]}),m.jsxs("p",{children:[m.jsx("strong",{children:"Urgencia:"})," ",e.urgency]}),m.jsxs("p",{children:[m.jsx("strong",{children:"Fecha:"})," ",v(new Date(e.createdAt),"dd/MM/yyyy HH:mm",{locale:f})]}),m.jsx("p",{children:m.jsx("strong",{children:"Motivo:"})}),m.jsx("p",{className:"bg-white p-3 rounded border",children:e.reason})]})]}),m.jsx("div",{className:"space-y-4",children:h?m.jsxs("div",{className:"space-y-3",children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Motivo del rechazo"}),m.jsx("textarea",{value:d,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded",rows:"3",placeholder:"Explique el motivo del rechazo..."}),m.jsxs("div",{className:"flex space-x-3",children:[m.jsx(A,{onClick:()=>r(e.id,d),className:"bg-red-600 hover:bg-red-700 text-white",disabled:!d.trim(),children:"Confirmar Rechazo"}),m.jsx(A,{onClick:()=>u(!1),variant:"outline",children:"Cancelar"})]})]}):m.jsxs("div",{className:"flex space-x-3",children:[m.jsxs("div",{className:"flex-1",children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Pines a aprobar"}),m.jsx("input",{type:"number",value:i,onChange:e=>c(parseInt(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 rounded",min:"1"})]}),m.jsxs("div",{className:"flex flex-col justify-end space-y-2",children:[m.jsxs(A,{onClick:()=>a(e.id,i),className:"bg-green-600 hover:bg-green-700 text-white",children:[m.jsx(g,{className:"mr-1"}),"Aprobar"]}),m.jsxs(A,{onClick:()=>u(!0),className:"bg-red-600 hover:bg-red-700 text-white",children:[m.jsx(N,{className:"mr-1"}),"Rechazar"]})]})]})})]})]})})})},V=({psychologists:e,onClose:s,onAssign:a})=>{const[r,t]=o.useState(""),[l,n]=o.useState(50),[i,c]=o.useState("");return m.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:m.jsx("div",{className:"bg-white rounded-lg max-w-md w-full",children:m.jsxs(S,{children:[m.jsx(z,{className:"bg-blue-50 border-b",children:m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Asignación Manual de Pines"}),m.jsx("button",{onClick:s,className:"text-gray-400 hover:text-gray-600",children:m.jsx(N,{})})]})}),m.jsxs(k,{className:"space-y-4",children:[m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Psicólogo"}),m.jsxs("select",{value:r,onChange:e=>t(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded",children:[m.jsx("option",{value:"",children:"Seleccionar psicólogo..."}),e.map(e=>m.jsxs("option",{value:e.id,children:[e.name," (",e.currentPins," pines actuales)"]},e.id))]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cantidad de pines"}),m.jsx("input",{type:"number",value:l,onChange:e=>n(parseInt(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 rounded",min:"1"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Motivo"}),m.jsx("textarea",{value:i,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded",rows:"3",placeholder:"Motivo de la asignación..."})]}),m.jsxs("div",{className:"flex space-x-3",children:[m.jsx(A,{onClick:()=>{r&&l>0&&a(r,l,i)},className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white",disabled:!r||l<=0,children:"Asignar Pines"}),m.jsx(A,{onClick:s,variant:"outline",className:"flex-1",children:"Cancelar"})]})]})]})})})};export{M as default};
