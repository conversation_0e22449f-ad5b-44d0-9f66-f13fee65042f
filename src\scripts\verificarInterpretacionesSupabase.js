/**
 * @file verificarInterpretacionesSupabase.js
 * @description Script para verificar que las interpretaciones oficiales se hayan insertado correctamente en Supabase
 */

import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://smiwggxvxswrgycghcux.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNtaXdnZ3h2eHN3cmd5Y2doY3V4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzI5NzQsImV4cCI6MjA1MDU0ODk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Función principal para verificar las interpretaciones en Supabase
 */
async function verificarInterpretaciones() {
  console.log('🔍 Verificando interpretaciones oficiales en Supabase...\n');
  
  try {
    // 1. Verificar conteo total de interpretaciones
    const { count: totalInterpretaciones, error: errorTotal } = await supabase
      .from('interpretaciones_oficiales')
      .select('*', { count: 'exact', head: true });

    if (errorTotal) {
      console.error('❌ Error obteniendo conteo total:', errorTotal);
      return;
    }

    console.log(`📊 Total de interpretaciones en Supabase: ${totalInterpretaciones}`);
    console.log(`📈 Esperadas: 49 (7 aptitudes × 7 niveles)`);
    console.log(`✅ Completitud: ${Math.round((totalInterpretaciones / 49) * 100)}%\n`);

    // 2. Verificar conteo por aptitud
    const { data: conteoPorAptitud, error: errorConteo } = await supabase
      .from('interpretaciones_oficiales')
      .select('aptitud_codigo')
      .eq('es_oficial', true);

    if (errorConteo) {
      console.error('❌ Error obteniendo conteo por aptitud:', errorConteo);
      return;
    }

    // Agrupar por aptitud
    const conteos = {};
    conteoPorAptitud.forEach(item => {
      conteos[item.aptitud_codigo] = (conteos[item.aptitud_codigo] || 0) + 1;
    });

    console.log('📋 Interpretaciones por aptitud:');
    const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
    aptitudes.forEach(aptitud => {
      const count = conteos[aptitud] || 0;
      const status = count === 7 ? '✅' : '⚠️';
      console.log(`${status} ${aptitud}: ${count}/7 interpretaciones`);
    });

    // 3. Probar función RPC obtener_interpretacion_oficial
    console.log('\n🧪 Probando función obtener_interpretacion_oficial...');
    
    const pruebasRPC = [
      { aptitud: 'V', percentil: 85 },
      { aptitud: 'E', percentil: 25 },
      { aptitud: 'A', percentil: 95 },
      { aptitud: 'R', percentil: 50 }
    ];

    for (const prueba of pruebasRPC) {
      try {
        const { data, error } = await supabase
          .rpc('obtener_interpretacion_oficial', {
            aptitud_codigo_param: prueba.aptitud,
            percentil_valor: prueba.percentil
          });

        if (error) {
          console.log(`❌ ${prueba.aptitud}-${prueba.percentil}: Error - ${error.message}`);
        } else if (data && data.length > 0) {
          const resultado = data[0];
          console.log(`✅ ${prueba.aptitud}-${prueba.percentil}: ${resultado.nivel_nombre} (${resultado.percentil_rango})`);
        } else {
          console.log(`⚠️ ${prueba.aptitud}-${prueba.percentil}: Sin resultados`);
        }
      } catch (err) {
        console.log(`❌ ${prueba.aptitud}-${prueba.percentil}: Error - ${err.message}`);
      }
    }

    // 4. Verificar función obtener_nivel_por_percentil
    console.log('\n🧪 Probando función obtener_nivel_por_percentil...');
    
    const pruebasNivel = [5, 25, 50, 85, 98];
    
    for (const percentil of pruebasNivel) {
      try {
        const { data, error } = await supabase
          .rpc('obtener_nivel_por_percentil', {
            percentil_valor: percentil
          });

        if (error) {
          console.log(`❌ Percentil ${percentil}: Error - ${error.message}`);
        } else if (data && data.length > 0) {
          const nivel = data[0];
          console.log(`✅ Percentil ${percentil}: ${nivel.nivel_nombre} (${nivel.rango_descripcion})`);
        } else {
          console.log(`⚠️ Percentil ${percentil}: Sin resultados`);
        }
      } catch (err) {
        console.log(`❌ Percentil ${percentil}: Error - ${err.message}`);
      }
    }

    // 5. Mostrar ejemplo de interpretación completa
    console.log('\n📄 Ejemplo de interpretación completa:');
    
    try {
      const { data: ejemplo, error: errorEjemplo } = await supabase
        .rpc('obtener_interpretacion_oficial', {
          aptitud_codigo_param: 'V',
          percentil_valor: 90
        });

      if (errorEjemplo) {
        console.error('❌ Error obteniendo ejemplo:', errorEjemplo);
      } else if (ejemplo && ejemplo.length > 0) {
        const interp = ejemplo[0];
        console.log(`\n🎯 Aptitud: ${interp.aptitud_nombre} (${interp.aptitud_codigo})`);
        console.log(`📊 Percentil: 90 → ${interp.nivel_nombre} (${interp.percentil_rango})`);
        console.log(`\n📝 Rendimiento:`);
        console.log(`   ${interp.rendimiento.substring(0, 100)}...`);
        console.log(`\n🎓 Académico:`);
        console.log(`   ${interp.academico.substring(0, 100)}...`);
        console.log(`\n💼 Vocacional:`);
        console.log(`   ${interp.vocacional.substring(0, 100)}...`);
      }
    } catch (err) {
      console.error('❌ Error obteniendo ejemplo:', err);
    }

    // 6. Verificar estructura de tablas
    console.log('\n🏗️ Verificando estructura de tablas...');
    
    const tablas = ['niveles_percentil', 'aptitudes', 'interpretaciones_oficiales'];
    
    for (const tabla of tablas) {
      try {
        const { count, error } = await supabase
          .from(tabla)
          .select('*', { count: 'exact', head: true });

        if (error) {
          console.log(`❌ ${tabla}: Error - ${error.message}`);
        } else {
          console.log(`✅ ${tabla}: ${count} registros`);
        }
      } catch (err) {
        console.log(`❌ ${tabla}: Error - ${err.message}`);
      }
    }

    console.log('\n🎉 Verificación completada!');

  } catch (error) {
    console.error('💥 Error durante la verificación:', error);
  }
}

/**
 * Función para mostrar estadísticas detalladas
 */
async function mostrarEstadisticas() {
  console.log('\n📊 Estadísticas detalladas de interpretaciones:');
  
  try {
    // Obtener interpretaciones con información completa
    const { data: interpretaciones, error } = await supabase
      .from('interpretaciones_oficiales')
      .select(`
        aptitud_codigo,
        nivel_id,
        niveles_percentil!nivel_id(nombre, rango_descripcion),
        aptitudes!aptitud_codigo(nombre)
      `)
      .eq('es_oficial', true)
      .order('aptitud_codigo')
      .order('nivel_id');

    if (error) {
      console.error('❌ Error obteniendo estadísticas:', error);
      return;
    }

    // Agrupar por aptitud
    const porAptitud = {};
    interpretaciones.forEach(item => {
      if (!porAptitud[item.aptitud_codigo]) {
        porAptitud[item.aptitud_codigo] = {
          nombre: item.aptitudes.nombre,
          niveles: []
        };
      }
      porAptitud[item.aptitud_codigo].niveles.push({
        id: item.nivel_id,
        nombre: item.niveles_percentil.nombre,
        rango: item.niveles_percentil.rango_descripcion
      });
    });

    // Mostrar estadísticas por aptitud
    Object.keys(porAptitud).sort().forEach(codigo => {
      const aptitud = porAptitud[codigo];
      console.log(`\n📋 ${codigo} - ${aptitud.nombre}:`);
      aptitud.niveles.forEach(nivel => {
        console.log(`   ${nivel.id}. ${nivel.nombre} (${nivel.rango})`);
      });
    });

  } catch (error) {
    console.error('💥 Error mostrando estadísticas:', error);
  }
}

// Ejecutar verificación si se ejecuta directamente
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🎯 Verificación de interpretaciones oficiales en Supabase');
  console.log('📄 Fuente: Interpretacion de aptitudes y Generalidaes.txt\n');
  
  await verificarInterpretaciones();
  
  const args = process.argv.slice(2);
  if (args.includes('--stats')) {
    await mostrarEstadisticas();
  }
}

export { verificarInterpretaciones, mostrarEstadisticas };
