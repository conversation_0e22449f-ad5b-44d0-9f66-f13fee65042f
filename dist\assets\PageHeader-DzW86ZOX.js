import{j as e}from"./vendor-BqMjyOVw.js";const s=({title:s,subtitle:t,icon:l,className:x="",showTransitions:a=!0})=>e.jsx("div",{className:`bg-gradient-to-r from-blue-900 to-blue-800 text-white ${x}`,children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center mb-3",children:[l&&e.jsx("div",{className:"w-12 h-12 bg-[#f59e0b] rounded-full flex items-center justify-center mr-4 shadow-lg",children:e.jsx(l,{className:"text-white text-xl"})}),e.jsx("h1",{className:"text-3xl font-bold",children:s})]}),t&&e.jsx("p",{className:"text-blue-100 text-lg",children:t})]})})});export{s as P};
