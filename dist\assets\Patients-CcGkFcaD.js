var e=Object.defineProperty,t=Object.defineProperties,s=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,l=(t,s,a)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[s]=a,i=(e,t)=>{for(var s in t||(t={}))r.call(t,s)&&l(e,s,t[s]);if(a)for(var s of a(t))n.call(t,s)&&l(e,s,t[s]);return e},o=(e,a)=>t(e,s(a)),c=(e,t,s)=>new Promise((a,r)=>{var n=e=>{try{i(s.next(e))}catch(t){r(t)}},l=e=>{try{i(s.throw(e))}catch(t){r(t)}},i=e=>e.done?a(e.value):Promise.resolve(e.value).then(n,l);i((s=s.apply(e,t)).next())});import{r as d,j as m,O as x,R as u,f as p,F as h,m as g,h as b,M as f,B as y,Y as j,Q as v}from"./vendor-BqMjyOVw.js";import{u as N,s as w}from"./index-Bdl1jgS_.js";import{P as C}from"./PageHeader-DzW86ZOX.js";const k=({isOpen:e,onClose:t,patient:s,onSave:a,title:r="Editar Paciente",psychologists:n=[],institutions:l=[]})=>{const[h,g]=d.useState({nombre:"",apellido:"",email:"",documento:"",genero:"",fecha_nacimiento:"",nivel_educativo:"",ocupacion:"",psicologo_id:"",institucion_id:""}),[b,f]=d.useState({}),[y,j]=d.useState(!1);d.useEffect(()=>{g(s?{nombre:s.nombre||"",apellido:s.apellido||"",email:s.email||"",documento:s.documento||"",genero:s.genero||"",fecha_nacimiento:s.fecha_nacimiento||"",nivel_educativo:s.nivel_educativo||"",ocupacion:s.ocupacion||"",psicologo_id:s.psicologo_id||"",institucion_id:s.institucion_id||""}:{nombre:"",apellido:"",email:"",documento:"",genero:"",fecha_nacimiento:"",nivel_educativo:"",ocupacion:"",psicologo_id:"",institucion_id:""}),f({})},[s,e]);const v=e=>{const{name:t,value:s}=e.target;g(e=>o(i({},e),{[t]:s})),b[t]&&f(e=>o(i({},e),{[t]:""}))};return e?m.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:m.jsxs("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[m.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[m.jsxs("div",{className:"flex items-center",children:[m.jsx(x,{className:"text-blue-600 mr-3"}),m.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:r})]}),m.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:m.jsx(u,{size:20})})]}),m.jsxs("form",{onSubmit:e=>c(null,null,function*(){if(e.preventDefault(),(()=>{const e={};return h.nombre.trim()||(e.nombre="El nombre es requerido"),h.apellido.trim()||(e.apellido="El apellido es requerido"),h.email.trim()&&!/\S+@\S+\.\S+/.test(h.email)&&(e.email="El email no es válido"),f(e),0===Object.keys(e).length})()){j(!0);try{yield a(h),t()}catch(s){}finally{j(!1)}}}),className:"p-6",children:[m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre *"}),m.jsx("input",{type:"text",name:"nombre",value:h.nombre,onChange:v,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent "+(b.nombre?"border-red-500":"border-gray-300"),placeholder:"Ingrese el nombre"}),b.nombre&&m.jsx("p",{className:"text-red-500 text-xs mt-1",children:b.nombre})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido *"}),m.jsx("input",{type:"text",name:"apellido",value:h.apellido,onChange:v,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent "+(b.apellido?"border-red-500":"border-gray-300"),placeholder:"Ingrese el apellido"}),b.apellido&&m.jsx("p",{className:"text-red-500 text-xs mt-1",children:b.apellido})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),m.jsx("input",{type:"email",name:"email",value:h.email,onChange:v,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent "+(b.email?"border-red-500":"border-gray-300"),placeholder:"<EMAIL>"}),b.email&&m.jsx("p",{className:"text-red-500 text-xs mt-1",children:b.email})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"}),m.jsx("input",{type:"text",name:"documento",value:h.documento,onChange:v,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Número de documento"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Género"}),m.jsxs("select",{name:"genero",value:h.genero,onChange:v,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[m.jsx("option",{value:"",children:"Seleccionar género"}),m.jsx("option",{value:"masculino",children:"Masculino"}),m.jsx("option",{value:"femenino",children:"Femenino"}),m.jsx("option",{value:"otro",children:"Otro"})]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fecha de Nacimiento"}),m.jsx("input",{type:"date",name:"fecha_nacimiento",value:h.fecha_nacimiento,onChange:v,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nivel Educativo"}),m.jsxs("select",{name:"nivel_educativo",value:h.nivel_educativo,onChange:v,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[m.jsx("option",{value:"",children:"Seleccionar nivel educativo"}),m.jsx("option",{value:"primaria",children:"Primaria"}),m.jsx("option",{value:"secundaria",children:"Secundaria"}),m.jsx("option",{value:"tecnico",children:"Técnico"}),m.jsx("option",{value:"universitario",children:"Universitario"}),m.jsx("option",{value:"posgrado",children:"Posgrado"})]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ocupación"}),m.jsx("input",{type:"text",name:"ocupacion",value:h.ocupacion,onChange:v,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ocupación del paciente"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Psicólogo Asignado"}),m.jsxs("select",{name:"psicologo_id",value:h.psicologo_id,onChange:v,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[m.jsx("option",{value:"",children:"Seleccionar psicólogo"}),n.map(e=>m.jsxs("option",{value:e.id,children:[e.nombre," ",e.apellido]},e.id))]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Institución"}),m.jsxs("select",{name:"institucion_id",value:h.institucion_id,onChange:v,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[m.jsx("option",{value:"",children:"Seleccionar institución"}),l.map(e=>m.jsx("option",{value:e.id,children:e.nombre},e.id))]})]})]}),m.jsxs("div",{className:"flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200",children:[m.jsx("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",disabled:y,children:"Cancelar"}),m.jsx("button",{type:"submit",disabled:y,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:y?m.jsxs(m.Fragment,{children:[m.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Guardando..."]}):m.jsxs(m.Fragment,{children:[m.jsx(p,{className:"mr-2"}),"Guardar"]})})]})]})]})}):null},S=()=>{const{user:e,isAdmin:t,isPsicologo:s,loading:a}=N(),[r,n]=d.useState([]),[l,i]=d.useState(!1),[o,x]=d.useState(""),[u,p]=d.useState(!1),[S,_]=d.useState(null),[P,E]=d.useState(""),[O,A]=d.useState([]),[D,G]=d.useState([]);if(a)return m.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:m.jsxs("div",{className:"flex items-center",children:[m.jsx(h,{className:"animate-spin text-blue-500 text-2xl mr-3"}),m.jsx("span",{className:"text-gray-600",children:"Cargando..."})]})});if(!t&&!s)return m.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:m.jsxs("div",{className:"text-center p-8 bg-white rounded-lg shadow-lg max-w-md",children:[m.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:m.jsx(g,{className:"text-red-600 text-2xl"})}),m.jsx("h2",{className:"text-2xl font-bold text-red-600 mb-4",children:"Acceso Denegado"}),m.jsx("p",{className:"text-gray-600 mb-6",children:"Solo los administradores y psicólogos pueden gestionar pacientes."}),m.jsx("button",{onClick:()=>window.history.back(),className:"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors",children:"Volver"})]})});const q=()=>c(null,null,function*(){i(!0);try{const{data:e,error:t}=yield w.from("pacientes").select("*").order("created_at",{ascending:!1});if(t)throw t;n(e||[])}catch(e){v.error("Error al cargar los pacientes")}finally{i(!1)}});d.useEffect(()=>{q(),c(null,null,function*(){try{const{data:e,error:t}=yield w.from("psicologos").select("id, nombre, apellido").order("nombre",{ascending:!0});if(t)throw t;A(e||[])}catch(e){}}),c(null,null,function*(){try{const{data:e,error:t}=yield w.from("instituciones").select("id, nombre").order("nombre",{ascending:!0});if(t)throw t;G(e||[])}catch(e){}});c(null,null,function*(){try{const{count:e}=yield w.from("pacientes").select("*",{count:"exact",head:!0}),{count:t}=yield w.from("resultados").select("*",{count:"exact",head:!0})}catch(e){}})},[]);const F=r.filter(e=>{var t,s,a,r;if(!o)return!0;const n=o.toLowerCase();return(null==(t=e.nombre)?void 0:t.toLowerCase().includes(n))||(null==(s=e.apellido)?void 0:s.toLowerCase().includes(n))||(null==(a=e.documento)?void 0:a.toLowerCase().includes(n))||(null==(r=e.email)?void 0:r.toLowerCase().includes(n))}),L=e=>c(null,null,function*(){try{i(!0);const{error:t}=yield w.from("pacientes").insert([e]);if(t)throw t;v.success("Paciente creado correctamente"),q()}catch(t){throw v.error("Error al crear el paciente"),t}finally{i(!1)}}),z=(e,t)=>c(null,null,function*(){try{i(!0);const{error:s}=yield w.from("pacientes").update(t).eq("id",e);if(s)throw s;v.success("Paciente actualizado correctamente"),q()}catch(s){v.error("Error al actualizar el paciente")}finally{i(!1)}});return m.jsxs("div",{className:"min-h-screen bg-gray-50",children:[m.jsx(C,{title:"Gestión de Pacientes",subtitle:"Administra la información y el historial de tus pacientes registrados en la plataforma",icon:g}),m.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[m.jsx("div",{className:"mb-6",children:m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsxs("div",{children:[m.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Gestión de Pacientes"}),m.jsxs("p",{className:"text-gray-600 mt-1",children:["Administre los pacientes registrados en el sistema (",F.length," pacientes)"]})]}),m.jsxs("div",{className:"flex items-center space-x-3",children:[m.jsxs("div",{className:"relative",children:[m.jsx(b,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),m.jsx("input",{type:"text",placeholder:"Buscar paciente...",value:o,onChange:e=>x(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),t&&m.jsxs("button",{onClick:()=>{_(null),E("Nuevo Paciente"),p(!0)},className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[m.jsx(f,{className:"mr-2"}),"Nuevo Paciente"]})]})]})}),m.jsx("div",{className:"bg-white shadow-sm rounded-lg overflow-hidden",children:m.jsx("div",{className:"overflow-x-auto",children:m.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsx("thead",{className:"bg-blue-600 text-white",children:m.jsxs("tr",{children:[m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Nombre Completo"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Email"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Documento"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Género"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Fecha de Nacimiento"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Nivel Educativo"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Ocupación"}),t&&m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Acciones"})]})}),m.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:l?m.jsx("tr",{children:m.jsx("td",{colSpan:t?8:7,className:"px-6 py-12 text-center",children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx(h,{className:"animate-spin text-blue-500 text-2xl mr-3"}),m.jsx("span",{className:"text-gray-600",children:"Cargando pacientes..."})]})})}):0===F.length?m.jsx("tr",{children:m.jsx("td",{colSpan:t?8:7,className:"px-6 py-12 text-center text-gray-500",children:0===r.length?"No hay pacientes registrados":"No se encontraron pacientes que coincidan con la búsqueda"})}):F.map(e=>{var s,a;return m.jsxs("tr",{className:"hover:bg-gray-50",children:[m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsxs("div",{className:"flex items-center",children:[m.jsx("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3",children:m.jsx("span",{className:"text-white text-sm font-medium",children:null==(a=null==(s=e.nombre)?void 0:s.charAt(0))?void 0:a.toUpperCase()})}),m.jsxs("div",{children:[m.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]}),m.jsx("div",{className:"text-sm text-gray-500",children:e.telefono&&`Tel: ${e.telefono}`})]})]})}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.email||"-"}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:m.jsx("span",{className:"px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full",children:e.documento||"-"})}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:m.jsx("span",{className:"px-2 py-1 text-xs font-medium rounded-full "+("masculino"===e.genero?"bg-blue-100 text-blue-800":"femenino"===e.genero?"bg-pink-100 text-pink-800":"bg-gray-100 text-gray-800"),children:e.genero?e.genero.charAt(0).toUpperCase()+e.genero.slice(1):"-"})}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.fecha_nacimiento?new Date(e.fecha_nacimiento).toLocaleDateString("es-ES"):"-"}),m.jsx("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate",children:e.nivel_educativo||"-"}),m.jsx("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate",children:e.ocupacion||"-"}),t&&m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:m.jsxs("div",{className:"flex space-x-2",children:[m.jsx("button",{onClick:()=>(e=>{_(e),E("Editar Paciente"),p(!0)})(e),className:"text-blue-600 hover:text-blue-900 transition-colors",title:"Editar paciente",children:m.jsx(y,{})}),m.jsx("button",{onClick:()=>(e=>c(null,null,function*(){if(window.confirm(`¿Está seguro de eliminar al paciente ${e.nombre} ${e.apellido}?`))try{i(!0);const{error:t}=yield w.from("pacientes").delete().eq("id",e.id);if(t)throw t;v.success("Paciente eliminado correctamente"),q()}catch(t){v.error("Error al eliminar el paciente")}finally{i(!1)}}))(e),className:"text-red-600 hover:text-red-900 transition-colors",title:"Eliminar paciente",children:m.jsx(j,{})})]})})]},e.id)})})]})})}),m.jsx("div",{className:"mt-8 text-center text-sm text-gray-500",children:"© 2025 Sistema de Gestión Psicológica - Panel de Administración"})]}),m.jsx(k,{isOpen:u,onClose:()=>{p(!1),_(null),E("")},patient:S,onSave:e=>c(null,null,function*(){S?yield z(S.id,e):yield L(e)}),title:P,psychologists:O,institutions:D})]})};export{S as default};
