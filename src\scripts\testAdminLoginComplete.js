/**
 * <PERSON>ript para probar completamente el login del administrador
 * 
 * Este script:
 * 1. Prueba la autenticación
 * 2. Verifica los datos del usuario
 * 3. Simula el proceso de login completo
 * 4. Verifica la redirección correcta
 * 
 * Ejecutar con: node src/scripts/testAdminLoginComplete.js
 */

import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Datos del administrador
const ADMIN_DATA = {
  email: '<EMAIL>',
  password: '13716261',
  documento: '13716261'
};

/**
 * Simula el proceso completo de login como lo hace UnifiedAuthService
 */
async function simulateCompleteLogin() {
  console.log('🔐 Simulando proceso completo de login...');
  
  try {
    const identifier = ADMIN_DATA.email;
    const password = ADMIN_DATA.password;
    const isEmail = identifier.includes('@');
    
    console.log(`📧 Autenticando con ${isEmail ? 'email' : 'documento'}: ${identifier}`);
    
    // Paso 1: Autenticar con Supabase Auth
    const { data: loginData, error: authError } = await supabase.auth.signInWithPassword({
      email: identifier.trim(),
      password: password
    });
    
    if (authError) {
      console.error('❌ Error en autenticación:', authError);
      return false;
    }
    
    console.log('✅ Autenticación exitosa en auth.users:', {
      id: loginData.user.id,
      email: loginData.user.email,
      confirmed_at: loginData.user.email_confirmed_at
    });
    
    // Paso 2: Obtener perfil del usuario
    const { data: profile, error: profileError } = await supabase
      .from('usuarios')
      .select('*')
      .eq('id', loginData.user.id)
      .single();
    
    if (profileError) {
      console.error('❌ Error obteniendo perfil:', profileError);
      await supabase.auth.signOut();
      return false;
    }
    
    console.log('✅ Perfil obtenido de tabla usuarios:', {
      id: profile.id,
      nombre: profile.nombre,
      apellido: profile.apellido,
      documento: profile.documento,
      tipo_usuario: profile.tipo_usuario || profile.rol || 'sin_rol',
      activo: profile.activo
    });
    
    // Paso 3: Verificar que el usuario esté activo
    if (!profile.activo) {
      console.error('❌ Usuario inactivo');
      await supabase.auth.signOut();
      return false;
    }
    
    // Paso 4: Preparar datos del usuario (como lo hace UnifiedAuthService)
    const userData = {
      id: profile.id,
      email: loginData.user.email,
      documento: profile.documento,
      nombre: profile.nombre,
      apellido: profile.apellido,
      rol: profile.tipo_usuario || profile.rol || 'candidato',
      tipo_usuario: profile.tipo_usuario || profile.rol || 'candidato',
      activo: profile.activo
    };
    
    console.log('✅ Datos del usuario preparados:', userData);
    
    // Paso 5: Determinar redirección según el rol
    const userRole = userData.rol;
    let redirectPath;
    
    switch (userRole) {
      case 'administrador':
        redirectPath = '/admin/administration';
        break;
      case 'psicologo':
        redirectPath = '/admin/candidates';
        break;
      case 'paciente':
      case 'estudiante':
        redirectPath = '/student/questionnaire';
        break;
      default:
        redirectPath = '/home';
        break;
    }
    
    console.log('✅ Redirección determinada:', {
      rol: userRole,
      ruta: redirectPath
    });
    
    // Paso 6: Simular guardado en localStorage
    const sessionData = {
      isLoggedIn: 'true',
      userRole: userData.rol,
      userData: JSON.stringify(userData),
      userEmail: userData.email
    };
    
    console.log('✅ Datos de sesión que se guardarían:', sessionData);
    
    // Cerrar sesión para limpiar
    await supabase.auth.signOut();
    console.log('✅ Sesión cerrada correctamente');
    
    return {
      success: true,
      userData,
      redirectPath,
      sessionData
    };
    
  } catch (error) {
    console.error('❌ Error en simulación de login:', error);
    return false;
  }
}

/**
 * Verifica que el usuario tenga el rol correcto
 */
async function verifyUserRole() {
  console.log('\n🔍 Verificando rol del usuario...');
  
  try {
    const { data: profile, error } = await supabase
      .from('usuarios')
      .select('*')
      .eq('documento', ADMIN_DATA.documento)
      .single();
    
    if (error) {
      console.error('❌ Error obteniendo usuario:', error);
      return false;
    }
    
    const userRole = profile.tipo_usuario || profile.rol;
    
    console.log('📋 Información del usuario:', {
      id: profile.id,
      nombre: profile.nombre,
      apellido: profile.apellido,
      documento: profile.documento,
      tipo_usuario: profile.tipo_usuario,
      rol: profile.rol,
      rol_detectado: userRole,
      activo: profile.activo
    });
    
    if (userRole !== 'administrador') {
      console.error(`❌ Rol incorrecto. Esperado: 'administrador', Encontrado: '${userRole}'`);
      
      // Intentar corregir el rol
      console.log('🔧 Intentando corregir el rol...');
      const { error: updateError } = await supabase
        .from('usuarios')
        .update({ tipo_usuario: 'administrador' })
        .eq('id', profile.id);
      
      if (updateError) {
        console.error('❌ Error corrigiendo rol:', updateError);
        return false;
      }
      
      console.log('✅ Rol corregido a "administrador"');
    } else {
      console.log('✅ Rol correcto: administrador');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Error verificando rol:', error);
    return false;
  }
}

/**
 * Función principal
 */
async function main() {
  console.log('🧪 PRUEBA COMPLETA DE LOGIN DEL ADMINISTRADOR');
  console.log('==============================================\n');
  
  try {
    // Paso 1: Verificar rol del usuario
    console.log('📋 PASO 1: Verificando rol del usuario');
    const roleOK = await verifyUserRole();
    
    if (!roleOK) {
      console.error('❌ Error en verificación de rol');
      process.exit(1);
    }
    
    // Paso 2: Simular login completo
    console.log('\n📋 PASO 2: Simulando login completo');
    const loginResult = await simulateCompleteLogin();
    
    if (!loginResult) {
      console.error('❌ Error en simulación de login');
      process.exit(1);
    }
    
    console.log('\n🎉 ¡PRUEBA COMPLETADA EXITOSAMENTE!');
    console.log('===================================');
    console.log('✅ Usuario autenticado correctamente');
    console.log('✅ Perfil obtenido de la base de datos');
    console.log('✅ Rol verificado: administrador');
    console.log('✅ Redirección correcta: /admin/administration');
    console.log('✅ Datos de sesión preparados');
    
    console.log('\n🔑 CREDENCIALES VERIFICADAS:');
    console.log(`   Email: ${ADMIN_DATA.email}`);
    console.log(`   Contraseña: ${ADMIN_DATA.password}`);
    console.log(`   Documento: ${ADMIN_DATA.documento}`);
    
    console.log('\n📋 DATOS DEL USUARIO:');
    console.log(`   ID: ${loginResult.userData.id}`);
    console.log(`   Nombre: ${loginResult.userData.nombre} ${loginResult.userData.apellido}`);
    console.log(`   Email: ${loginResult.userData.email}`);
    console.log(`   Documento: ${loginResult.userData.documento}`);
    console.log(`   Rol: ${loginResult.userData.rol}`);
    console.log(`   Activo: ${loginResult.userData.activo}`);
    
    console.log('\n🚀 REDIRECCIÓN:');
    console.log(`   Ruta: ${loginResult.redirectPath}`);
    console.log('   Descripción: Panel de administración');
    
    console.log('\n💡 PRÓXIMOS PASOS:');
    console.log('1. Intenta hacer login en la aplicación');
    console.log('2. Deberías ser redirigido a /admin/administration');
    console.log('3. Si aún hay problemas, revisa la consola del navegador');
    
  } catch (error) {
    console.error('\n❌ Error fatal durante la prueba:', error);
    process.exit(1);
  }
}

// Ejecutar el script
main();
