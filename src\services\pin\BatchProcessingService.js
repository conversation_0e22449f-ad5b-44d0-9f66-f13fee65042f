import { PinLogger } from './PinLogger.js';
import PinValidationService from './PinValidationService.js';
import SessionControlService from './SessionControlService.js';
import InformesService from '../InformesService.js';

/**
 * Servicio especializado para el procesamiento en lote de informes
 * Optimiza el consumo de pines y maneja diferentes estrategias de descuento
 */
export class BatchProcessingService {
  
  /**
   * Estrategias de procesamiento en lote
   */
  static BATCH_STRATEGIES = {
    INDIVIDUAL: 'individual',     // Descuento individual por cada informe
    BULK_UPFRONT: 'bulk_upfront', // Descuento total al inicio
    BULK_SUCCESS: 'bulk_success', // Descuento solo por informes exitosos
    OPTIMISTIC: 'optimistic'      // Procesamiento optimista con rollback
  };

  /**
   * Procesar informes en lote con estrategia optimizada
   * @param {string} psychologistId - ID del psicólogo
   * @param {Array<string>} patientIds - IDs de pacientes
   * @param {Object} options - Opciones de procesamiento
   * @returns {Promise<Object>} Resultado del procesamiento
   */
  static async processBatchReports(psychologistId, patientIds, options = {}) {
    const {
      strategy = this.BATCH_STRATEGIES.INDIVIDUAL,
      title = null,
      description = null,
      incluirInterpretaciones = true,
      maxConcurrent = 3,
      continueOnError = true
    } = options;

    try {
      PinLogger.logInfo('Starting batch processing', { 
        psychologistId, 
        patientCount: patientIds.length,
        strategy 
      });

      // Validación previa completa
      const validation = await PinValidationService.validateBatchReportGeneration(
        psychologistId, 
        patientIds
      );

      if (!validation.canProceed) {
        throw new Error(`Validación de lote falló: ${validation.userMessage}`);
      }

      // Ejecutar estrategia seleccionada
      switch (strategy) {
        case this.BATCH_STRATEGIES.INDIVIDUAL:
          return await this._processIndividualStrategy(
            psychologistId, patientIds, options
          );
        
        case this.BATCH_STRATEGIES.BULK_UPFRONT:
          return await this._processBulkUpfrontStrategy(
            psychologistId, patientIds, options
          );
        
        case this.BATCH_STRATEGIES.BULK_SUCCESS:
          return await this._processBulkSuccessStrategy(
            psychologistId, patientIds, options
          );
        
        case this.BATCH_STRATEGIES.OPTIMISTIC:
          return await this._processOptimisticStrategy(
            psychologistId, patientIds, options
          );
        
        default:
          throw new Error(`Estrategia no soportada: ${strategy}`);
      }

    } catch (error) {
      PinLogger.logError('Error in batch processing', error);
      throw error;
    }
  }

  /**
   * Estrategia individual: valida y descuenta pin por cada informe
   * @private
   */
  static async _processIndividualStrategy(psychologistId, patientIds, options) {
    const results = {
      strategy: this.BATCH_STRATEGIES.INDIVIDUAL,
      successful: [],
      failed: [],
      totalProcessed: 0,
      totalSuccessful: 0,
      totalFailed: 0,
      pinsConsumed: 0,
      startTime: new Date().toISOString()
    };

    for (const patientId of patientIds) {
      try {
        results.totalProcessed++;
        
        // Validar individualmente antes de procesar
        const validation = await PinValidationService.validateReportGeneration(psychologistId, 1);
        
        if (!validation.canProceed) {
          results.failed.push({
            patientId,
            error: validation.userMessage,
            reason: 'validation_failed',
            timestamp: new Date().toISOString()
          });
          results.totalFailed++;
          continue;
        }

        // Generar informe (incluye validación y consumo automático)
        const informeId = await InformesService.generarInformeCompleto(
          patientId,
          options.title,
          options.description,
          options.incluirInterpretaciones,
          false // No saltar validación
        );

        results.successful.push({
          patientId,
          informeId,
          timestamp: new Date().toISOString()
        });
        
        results.totalSuccessful++;
        results.pinsConsumed++;

      } catch (error) {
        PinLogger.logError(`Error processing patient ${patientId}`, error);
        
        results.failed.push({
          patientId,
          error: error.message,
          reason: 'processing_error',
          timestamp: new Date().toISOString()
        });
        
        results.totalFailed++;

        // Decidir si continuar o parar
        if (!options.continueOnError) {
          break;
        }
      }
    }

    results.endTime = new Date().toISOString();
    results.duration = new Date(results.endTime) - new Date(results.startTime);

    PinLogger.logInfo('Individual strategy completed', {
      successful: results.totalSuccessful,
      failed: results.totalFailed,
      pinsConsumed: results.pinsConsumed
    });

    return results;
  }

  /**
   * Estrategia bulk upfront: descuenta todos los pines al inicio
   * @private
   */
  static async _processBulkUpfrontStrategy(psychologistId, patientIds, options) {
    const results = {
      strategy: this.BATCH_STRATEGIES.BULK_UPFRONT,
      successful: [],
      failed: [],
      totalProcessed: 0,
      totalSuccessful: 0,
      totalFailed: 0,
      pinsConsumed: patientIds.length,
      startTime: new Date().toISOString()
    };

    try {
      // Consumir todos los pines al inicio
      const PinControlService = (await import('./PinControlService.js')).default;
      
      // Simular consumo en lote (en producción, implementar lógica real)
      for (let i = 0; i < patientIds.length; i++) {
        await PinControlService.consumePin(
          psychologistId,
          patientIds[i],
          null, // sessionId se manejará internamente
          null  // reportId se asignará después
        );
      }

      // Procesar informes sin validación adicional de pines
      for (const patientId of patientIds) {
        try {
          results.totalProcessed++;
          
          const informeId = await InformesService.generarInformeCompleto(
            patientId,
            options.title,
            options.description,
            options.incluirInterpretaciones,
            true // Saltar validación de pines
          );

          results.successful.push({
            patientId,
            informeId,
            timestamp: new Date().toISOString()
          });
          
          results.totalSuccessful++;

        } catch (error) {
          PinLogger.logError(`Error processing patient ${patientId}`, error);
          
          results.failed.push({
            patientId,
            error: error.message,
            reason: 'processing_error',
            timestamp: new Date().toISOString()
          });
          
          results.totalFailed++;
        }
      }

    } catch (error) {
      PinLogger.logError('Error in bulk upfront strategy', error);
      throw error;
    }

    results.endTime = new Date().toISOString();
    results.duration = new Date(results.endTime) - new Date(results.startTime);

    return results;
  }

  /**
   * Estrategia bulk success: descuenta pines solo por informes exitosos
   * @private
   */
  static async _processBulkSuccessStrategy(psychologistId, patientIds, options) {
    const results = {
      strategy: this.BATCH_STRATEGIES.BULK_SUCCESS,
      successful: [],
      failed: [],
      totalProcessed: 0,
      totalSuccessful: 0,
      totalFailed: 0,
      pinsConsumed: 0,
      startTime: new Date().toISOString()
    };

    // Procesar informes sin consumir pines inicialmente
    for (const patientId of patientIds) {
      try {
        results.totalProcessed++;
        
        const informeId = await InformesService.generarInformeCompleto(
          patientId,
          options.title,
          options.description,
          options.incluirInterpretaciones,
          true // Saltar validación de pines temporalmente
        );

        results.successful.push({
          patientId,
          informeId,
          timestamp: new Date().toISOString()
        });
        
        results.totalSuccessful++;

      } catch (error) {
        PinLogger.logError(`Error processing patient ${patientId}`, error);
        
        results.failed.push({
          patientId,
          error: error.message,
          reason: 'processing_error',
          timestamp: new Date().toISOString()
        });
        
        results.totalFailed++;
      }
    }

    // Consumir pines solo por los informes exitosos
    if (results.totalSuccessful > 0) {
      try {
        const PinControlService = (await import('./PinControlService.js')).default;
        
        for (const success of results.successful) {
          await PinControlService.consumePin(
            psychologistId,
            success.patientId,
            null,
            success.informeId
          );
          results.pinsConsumed++;
        }
      } catch (error) {
        PinLogger.logError('Error consuming pins for successful reports', error);
        // En caso de error, podríamos hacer rollback de los informes
      }
    }

    results.endTime = new Date().toISOString();
    results.duration = new Date(results.endTime) - new Date(results.startTime);

    return results;
  }

  /**
   * Estrategia optimista: procesa todo y hace rollback si es necesario
   * @private
   */
  static async _processOptimisticStrategy(psychologistId, patientIds, options) {
    const results = {
      strategy: this.BATCH_STRATEGIES.OPTIMISTIC,
      successful: [],
      failed: [],
      totalProcessed: 0,
      totalSuccessful: 0,
      totalFailed: 0,
      pinsConsumed: 0,
      rollbackPerformed: false,
      startTime: new Date().toISOString()
    };

    const createdReports = [];

    try {
      // Procesar todos los informes optimistamente
      for (const patientId of patientIds) {
        try {
          results.totalProcessed++;
          
          const informeId = await InformesService.generarInformeCompleto(
            patientId,
            options.title,
            options.description,
            options.incluirInterpretaciones,
            true // Saltar validación inicial
          );

          createdReports.push({ patientId, informeId });
          
          results.successful.push({
            patientId,
            informeId,
            timestamp: new Date().toISOString()
          });
          
          results.totalSuccessful++;

        } catch (error) {
          results.failed.push({
            patientId,
            error: error.message,
            reason: 'processing_error',
            timestamp: new Date().toISOString()
          });
          
          results.totalFailed++;
        }
      }

      // Validar si tenemos suficientes pines para todos los exitosos
      const validation = await PinValidationService.validateReportGeneration(
        psychologistId, 
        results.totalSuccessful
      );

      if (validation.canProceed) {
        // Consumir pines para todos los exitosos
        const PinControlService = (await import('./PinControlService.js')).default;
        
        for (const success of results.successful) {
          await PinControlService.consumePin(
            psychologistId,
            success.patientId,
            null,
            success.informeId
          );
          results.pinsConsumed++;
        }
      } else {
        // Rollback: eliminar informes creados
        results.rollbackPerformed = true;
        
        for (const report of createdReports) {
          try {
            // Aquí implementarías la lógica de rollback
            // Por ejemplo, marcar informes como cancelados
            PinLogger.logInfo(`Rolling back report ${report.informeId}`);
          } catch (rollbackError) {
            PinLogger.logError('Error during rollback', rollbackError);
          }
        }

        throw new Error(`Rollback realizado: ${validation.userMessage}`);
      }

    } catch (error) {
      if (!results.rollbackPerformed) {
        // Realizar rollback si no se hizo ya
        for (const report of createdReports) {
          try {
            PinLogger.logInfo(`Emergency rollback for report ${report.informeId}`);
          } catch (rollbackError) {
            PinLogger.logError('Error during emergency rollback', rollbackError);
          }
        }
      }
      
      throw error;
    }

    results.endTime = new Date().toISOString();
    results.duration = new Date(results.endTime) - new Date(results.startTime);

    return results;
  }

  /**
   * Obtener recomendación de estrategia basada en el contexto
   * @param {number} batchSize - Tamaño del lote
   * @param {number} availablePins - Pines disponibles
   * @param {Object} psychologistProfile - Perfil del psicólogo
   * @returns {string} Estrategia recomendada
   */
  static getRecommendedStrategy(batchSize, availablePins, psychologistProfile = {}) {
    const { reliability = 0.95, riskTolerance = 'medium' } = psychologistProfile;

    // Lotes pequeños: individual es más seguro
    if (batchSize <= 5) {
      return this.BATCH_STRATEGIES.INDIVIDUAL;
    }

    // Si hay pines justos: bulk success para no desperdiciar
    if (availablePins < batchSize * 1.2) {
      return this.BATCH_STRATEGIES.BULK_SUCCESS;
    }

    // Alta confiabilidad y tolerancia al riesgo: optimistic
    if (reliability > 0.9 && riskTolerance === 'high') {
      return this.BATCH_STRATEGIES.OPTIMISTIC;
    }

    // Muchos pines disponibles: bulk upfront
    if (availablePins > batchSize * 2) {
      return this.BATCH_STRATEGIES.BULK_UPFRONT;
    }

    // Por defecto: individual (más seguro)
    return this.BATCH_STRATEGIES.INDIVIDUAL;
  }
}

export default BatchProcessingService;
