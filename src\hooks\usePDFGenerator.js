/**
 * ========================================
 * HOOK PERSONALIZADO PARA GENERACIÓN DE PDF
 * Simplifica el uso del generador ultra-preciso
 * ========================================
 */

import { useState, useCallback } from 'react';
import { generateInformePDF, generateInformePDFScaled, generatePrecisePDF } from '../utils/pdfGenerator';
import { applyAllInlineStyles } from '../utils/printStylesInline';
import { toast } from 'react-toastify';

/**
 * Hook personalizado para generar PDFs con máxima fidelidad visual
 * @param {Object} options - Opciones de configuración
 * @returns {Object} Estado y funciones para generar PDFs
 */
export const usePDFGenerator = (options = {}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState(null);
  const [lastGeneratedFile, setLastGeneratedFile] = useState(null);

  /**
   * Genera un PDF de informe psicológico
   */
  const generateInformePDFHook = useCallback(async (element, patientData, customOptions = {}) => {
    if (!element || isGenerating) {
      console.warn('⚠️ [usePDFGenerator] Elemento no válido o generación en progreso');
      return false;
    }

    setIsGenerating(true);
    setError(null);

    try {
      console.log('🎯 [usePDFGenerator] Iniciando generación de informe PDF...');

      // Aplicar estilos ultra-robustos antes de la captura
      if (applyAllInlineStyles) {
        applyAllInlineStyles(element);
      }

      // Configuración por defecto (SIN escalado automático)
      const defaultOptions = {
        margin: 0, // Sin márgenes para tamaño natural
        html2canvasOptions: {
          scale: 1, // Escala 1:1 para tamaño real
          useCORS: true,
          backgroundColor: '#ffffff',
          logging: process.env.NODE_ENV === 'development'
        },
        onBeforeCapture: async (captureElement) => {
          console.log('📸 [usePDFGenerator] Preparando captura...');
          
          // Diagnóstico automático
          const diagnostics = {
            headers: captureElement.querySelectorAll('.print-header, .bg-blue-600, .bg-gradient-to-r'),
            icons: captureElement.querySelectorAll('.bg-orange-500, .bg-red-500, .bg-green-500, .bg-yellow-500, .bg-gray-600'),
            bars: captureElement.querySelectorAll('.h-6[style*="backgroundColor"]')
          };
          
          console.log('🔍 [usePDFGenerator] Elementos encontrados:', {
            headers: diagnostics.headers.length,
            icons: diagnostics.icons.length,
            bars: diagnostics.bars.length
          });
        },
        ...options,
        ...customOptions
      };

      // Generar PDF
      await generateInformePDF(element, patientData, defaultOptions);

      // Actualizar estado de éxito
      const filename = `Informe_BAT7_${patientData?.nombre || 'Paciente'}_${new Date().toISOString().split('T')[0]}.pdf`;
      setLastGeneratedFile(filename);

      // Mostrar notificación de éxito
      if (options.showToast !== false) {
        toast.success('✅ PDF generado exitosamente', {
          position: "top-right",
          autoClose: 3000,
        });
      }

      console.log('✅ [usePDFGenerator] PDF generado exitosamente');
      return true;

    } catch (err) {
      console.error('❌ [usePDFGenerator] Error generando PDF:', err);
      setError(err);

      // Mostrar notificación de error
      if (options.showToast !== false) {
        toast.error(`❌ Error generando PDF: ${err.message}`, {
          position: "top-right",
          autoClose: 5000,
        });
      }

      return false;
    } finally {
      setIsGenerating(false);
    }
  }, [isGenerating, options]);

  /**
   * Genera un PDF de informe CON escalado automático (legacy)
   */
  const generateInformePDFScaledHook = useCallback(async (element, patientData, customOptions = {}) => {
    if (!element || isGenerating) {
      console.warn('⚠️ [usePDFGenerator] Elemento no válido o generación en progreso');
      return false;
    }

    setIsGenerating(true);
    setError(null);

    try {
      console.log('🎯 [usePDFGenerator] Iniciando generación de informe PDF ESCALADO...');

      // Configuración con escalado automático
      const scaledOptions = {
        margin: 10,
        html2canvasOptions: {
          scale: 2, // Alta resolución
          useCORS: true,
          backgroundColor: '#ffffff',
          logging: process.env.NODE_ENV === 'development'
        },
        ...options,
        ...customOptions
      };

      // Generar PDF escalado
      await generateInformePDFScaled(element, patientData, scaledOptions);

      const filename = `Informe_BAT7_${patientData?.nombre || 'Paciente'}_${new Date().toISOString().split('T')[0]}_escalado.pdf`;
      setLastGeneratedFile(filename);

      if (options.showToast !== false) {
        toast.success('✅ PDF escalado generado exitosamente', {
          position: "top-right",
          autoClose: 3000,
        });
      }

      console.log('✅ [usePDFGenerator] PDF escalado generado exitosamente');
      return true;

    } catch (err) {
      console.error('❌ [usePDFGenerator] Error generando PDF escalado:', err);
      setError(err);

      if (options.showToast !== false) {
        toast.error(`❌ Error generando PDF escalado: ${err.message}`, {
          position: "top-right",
          autoClose: 5000,
        });
      }

      return false;
    } finally {
      setIsGenerating(false);
    }
  }, [isGenerating, options]);

  /**
   * Genera un PDF genérico de cualquier elemento
   */
  const generateGenericPDF = useCallback(async (element, filename = 'documento.pdf', customOptions = {}) => {
    if (!element || isGenerating) {
      console.warn('⚠️ [usePDFGenerator] Elemento no válido o generación en progreso');
      return false;
    }

    setIsGenerating(true);
    setError(null);

    try {
      console.log('🎯 [usePDFGenerator] Iniciando generación de PDF genérico...');

      // Configuración por defecto
      const defaultOptions = {
        margin: 10,
        html2canvasOptions: {
          scale: 2,
          useCORS: true,
          backgroundColor: '#ffffff',
          logging: process.env.NODE_ENV === 'development'
        },
        ...options,
        ...customOptions
      };

      // Generar PDF
      await generatePrecisePDF(element, filename, defaultOptions);

      setLastGeneratedFile(filename);

      // Mostrar notificación de éxito
      if (options.showToast !== false) {
        toast.success('✅ PDF generado exitosamente', {
          position: "top-right",
          autoClose: 3000,
        });
      }

      console.log('✅ [usePDFGenerator] PDF genérico generado exitosamente');
      return true;

    } catch (err) {
      console.error('❌ [usePDFGenerator] Error generando PDF genérico:', err);
      setError(err);

      // Mostrar notificación de error
      if (options.showToast !== false) {
        toast.error(`❌ Error generando PDF: ${err.message}`, {
          position: "top-right",
          autoClose: 5000,
        });
      }

      return false;
    } finally {
      setIsGenerating(false);
    }
  }, [isGenerating, options]);

  /**
   * Resetea el estado del hook
   */
  const reset = useCallback(() => {
    setIsGenerating(false);
    setError(null);
    setLastGeneratedFile(null);
  }, []);

  /**
   * Verifica si el navegador soporta las funcionalidades necesarias
   */
  const isSupported = useCallback(() => {
    try {
      // Verificar soporte para html2canvas y jsPDF
      return (
        typeof window !== 'undefined' &&
        window.HTMLCanvasElement &&
        window.CanvasRenderingContext2D &&
        typeof document.createElement === 'function'
      );
    } catch {
      return false;
    }
  }, []);

  return {
    // Estado
    isGenerating,
    error,
    lastGeneratedFile,

    // Funciones principales
    generateInformePDF: generateInformePDFHook, // Tamaño natural (SIN escalado)
    generateInformePDFScaled: generateInformePDFScaledHook, // Con escalado automático
    generateGenericPDF,

    // Utilidades
    reset,
    isSupported,

    // Información de estado
    canGenerate: !isGenerating && isSupported(),
    hasError: !!error
  };
};

/**
 * Hook simplificado específico para informes
 */
export const useInformePDF = (patientData, options = {}) => {
  const pdfGenerator = usePDFGenerator(options);

  const generatePDF = useCallback(async (element, customOptions = {}) => {
    return pdfGenerator.generateInformePDF(element, patientData, customOptions);
  }, [pdfGenerator, patientData]);

  return {
    ...pdfGenerator,
    generatePDF
  };
};

export default usePDFGenerator;
