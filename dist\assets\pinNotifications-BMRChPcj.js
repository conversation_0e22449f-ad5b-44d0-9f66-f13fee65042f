var e=(e,t,r)=>new Promise((a,i)=>{var n=e=>{try{o(r.next(e))}catch(t){i(t)}},s=e=>{try{o(r.throw(e))}catch(t){i(t)}},o=e=>e.done?a(e.value):Promise.resolve(e.value).then(n,s);o((r=r.apply(e,t)).next())});import{s as t}from"./index-Bdl1jgS_.js";class r{static getUserNotifications(r){return e(this,arguments,function*(e,r={}){try{const{read:a=null,type:i=null,limit:n=50,offset:s=0,include_expired:o=!1}=r;let c=t.from("pin_notifications").select("*").eq("user_id",e).order("created_at",{ascending:!1}).range(s,s+n-1);null!==a&&(c=c.eq("read",a)),i&&(c=c.eq("type",i)),o||(c=c.or("expires_at.is.null,expires_at.gt."+(new Date).toISOString()));const{data:l,error:d}=yield c;if(d)throw new Error(`Error al obtener notificaciones: ${d.message}`);const{count:u,error:g}=yield t.from("pin_notifications").select("*",{count:"exact",head:!0}).eq("user_id",e).eq("read",!1).or("expires_at.is.null,expires_at.gt."+(new Date).toISOString());return{success:!0,data:l||[],unread_count:u||0,pagination:{limit:n,offset:s,has_more:((null==l?void 0:l.length)||0)===n}}}catch(a){return{success:!1,error:a.message,message:"Error al obtener las notificaciones"}}})}static createNotification(r){return e(this,null,function*(){try{const{user_id:e,type:a,title:i,message:n,severity:s="info",channels:o=["toast"],metadata:c={},related_entity_type:l=null,related_entity_id:d=null,expires_at:u=null}=r;if(!(e&&a&&i&&n))throw new Error("Faltan campos requeridos: user_id, type, title, message");if(!["info","success","warning","error","critical"].includes(s))throw new Error("Severidad debe ser: info, success, warning, error, o critical");const{data:g,error:f}=yield t.from("usuarios").select("id").eq("id",e).single();if(f||!g)throw new Error("Usuario no encontrado");const{data:m,error:p}=yield t.from("pin_notifications").insert([{user_id:e,type:a,title:i,message:n,severity:s,channels:JSON.stringify(o),metadata:c,related_entity_type:l,related_entity_id:d,expires_at:u}]).select("*").single();if(p)throw new Error(`Error al crear notificación: ${p.message}`);return o.includes("toast")&&(yield this._sendRealtimeNotification(m)),{success:!0,data:m,message:"Notificación creada exitosamente"}}catch(e){return{success:!1,error:e.message,message:"Error al crear la notificación"}}})}static markAsRead(r,a=null){return e(this,null,function*(){try{let e=t.from("pin_notifications").update({read:!0,read_at:(new Date).toISOString()}).eq("id",r);a&&(e=e.eq("user_id",a));const{data:i,error:n}=yield e.select("*").single();if(n)throw new Error(`Error al marcar como leída: ${n.message}`);return{success:!0,data:i,message:"Notificación marcada como leída"}}catch(e){return{success:!1,error:e.message,message:"Error al marcar la notificación como leída"}}})}static markAllAsRead(r){return e(this,null,function*(){try{const{data:e,error:a}=yield t.from("pin_notifications").update({read:!0,read_at:(new Date).toISOString()}).eq("user_id",r).eq("read",!1).select("*");if(a)throw new Error(`Error al marcar todas como leídas: ${a.message}`);return{success:!0,data:e||[],count:(null==e?void 0:e.length)||0,message:`${(null==e?void 0:e.length)||0} notificaciones marcadas como leídas`}}catch(e){return{success:!1,error:e.message,message:"Error al marcar todas las notificaciones como leídas"}}})}static deleteNotification(r,a=null){return e(this,null,function*(){try{let e=t.from("pin_notifications").delete().eq("id",r);a&&(e=e.eq("user_id",a));const{data:i,error:n}=yield e.select("*").single();if(n)throw new Error(`Error al eliminar notificación: ${n.message}`);return{success:!0,data:i,message:"Notificación eliminada exitosamente"}}catch(e){return{success:!1,error:e.message,message:"Error al eliminar la notificación"}}})}static cleanupExpiredNotifications(){return e(this,null,function*(){try{const{data:e,error:r}=yield t.from("pin_notifications").delete().lt("expires_at",(new Date).toISOString()).select("*");if(r)throw new Error(`Error al limpiar notificaciones: ${r.message}`);return{success:!0,count:(null==e?void 0:e.length)||0,message:`${(null==e?void 0:e.length)||0} notificaciones expiradas eliminadas`}}catch(e){return{success:!1,error:e.message,message:"Error al limpiar notificaciones expiradas"}}})}static getNotificationStats(r){return e(this,arguments,function*(e,r={}){try{const{date_from:a=null,date_to:i=null}=r;let n=t.from("pin_notifications").select("type, severity, read, created_at").eq("user_id",e);a&&(n=n.gte("created_at",a)),i&&(n=n.lte("created_at",i));const{data:s,error:o}=yield n;if(o)throw new Error(`Error al obtener estadísticas: ${o.message}`);const c={total:s.length,unread:s.filter(e=>!e.read).length,read:s.filter(e=>e.read).length,by_type:{},by_severity:{info:s.filter(e=>"info"===e.severity).length,success:s.filter(e=>"success"===e.severity).length,warning:s.filter(e=>"warning"===e.severity).length,error:s.filter(e=>"error"===e.severity).length,critical:s.filter(e=>"critical"===e.severity).length},recent_activity:{today:s.filter(e=>new Date(e.created_at).toDateString()===(new Date).toDateString()).length,this_week:s.filter(e=>{const t=new Date(e.created_at),r=new Date;return r.setDate(r.getDate()-7),t>=r}).length}};return s.forEach(e=>{c.by_type[e.type]=(c.by_type[e.type]||0)+1}),{success:!0,data:c}}catch(a){return{success:!1,error:a.message,message:"Error al obtener estadísticas de notificaciones"}}})}static createLowPinNotification(t,r,a=5){return e(this,null,function*(){try{const e=0===r?"critical":r<=2?"error":"warning",i=0===r?"urgent":r<=2?"high":"normal",n=0===r?"Sin pines disponibles":"Pines bajos",s=0===r?"No tienes pines disponibles. Solicita una recarga para continuar generando informes.":`Te quedan solo ${r} pines disponibles. Considera solicitar una recarga.`;return yield this.createNotification({user_id:t,type:"low_pins",title:n,message:s,severity:e,channels:["toast","in_app"],metadata:{remaining_pins:r,threshold:a,urgency:i,auto_generated:!0},expires_at:new Date(Date.now()+864e5).toISOString()})}catch(e){return{success:!1,error:e.message,message:"Error al crear notificación de pines bajos"}}})}static createPinAssignmentNotification(t,r,a=!1){return e(this,null,function*(){try{const e="Pines asignados",i=a?"Se te ha asignado un plan ilimitado de pines":`Se han asignado ${r} pines a tu cuenta`;return yield this.createNotification({user_id:t,type:"pin_assignment",title:e,message:i,severity:"success",channels:["toast","in_app"],metadata:{assigned_pins:r,is_unlimited:a,auto_generated:!0}})}catch(e){return{success:!1,error:e.message,message:"Error al crear notificación de asignación"}}})}static _sendRealtimeNotification(r){return e(this,null,function*(){try{t.channel(`notifications:${r.user_id}`).send({type:"broadcast",event:"new_notification",payload:r})}catch(e){}})}static _validateMetadata(e){try{return"string"==typeof e&&JSON.parse(e),!0}catch(t){return!1}}}export{r as P};
