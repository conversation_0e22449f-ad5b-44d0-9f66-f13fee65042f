import { supabase } from '../supabaseClient.js';

/**
 * API Endpoints para Solicitudes de Recarga de Pines
 * Implementa CRUD completo para el flujo de solicitudes
 */
export class PinRechargeRequestsAPI {

  /**
   * Crear nueva solicitud de recarga
   * POST /api/pin-recharge-requests
   */
  static async createRequest(requestData) {
    try {
      const {
        psychologist_id,
        requested_pins,
        urgency = 'normal',
        reason,
        metadata = {}
      } = requestData;

      // Validaciones
      if (!psychologist_id || !requested_pins || !reason) {
        throw new Error('Faltan campos requeridos: psychologist_id, requested_pins, reason');
      }

      if (!['normal', 'high', 'urgent'].includes(urgency)) {
        throw new Error('Urgencia debe ser: normal, high, o urgent');
      }

      if (requested_pins <= 0 || requested_pins > 1000) {
        throw new Error('Cantidad de pines debe estar entre 1 y 1000');
      }

      // Verificar que el psicólogo existe
      const { data: psychologist, error: psychError } = await supabase
        .from('psicologos')
        .select('id, nombre, email')
        .eq('id', psychologist_id)
        .single();

      if (psychError || !psychologist) {
        throw new Error('Psicólogo no encontrado');
      }

      // Obtener estadísticas actuales del psicólogo
      const { data: currentStats } = await supabase
        .from('psychologist_usage_control')
        .select('total_uses, used_uses, is_unlimited, is_active')
        .eq('psicologo_id', psychologist_id)
        .single();

      // Enriquecer metadata con estadísticas actuales
      const enrichedMetadata = {
        ...metadata,
        usage_stats: {
          current_pins: currentStats ? (currentStats.total_uses - currentStats.used_uses) : 0,
          total_assigned: currentStats?.total_uses || 0,
          total_consumed: currentStats?.used_uses || 0,
          is_unlimited: currentStats?.is_unlimited || false,
          is_active: currentStats?.is_active || false,
          last_assignment: currentStats?.updated_at
        },
        psychologist_info: {
          name: psychologist.name,
          email: psychologist.email
        },
        request_context: {
          user_agent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Server',
          timestamp: new Date().toISOString()
        }
      };

      // Crear la solicitud
      const { data: newRequest, error } = await supabase
        .from('pin_recharge_requests')
        .insert([{
          psychologist_id,
          requested_pins,
          urgency,
          reason,
          status: 'pending',
          metadata: enrichedMetadata
        }])
        .select('*')
        .single();

      if (error) {
        console.error('Error creating recharge request:', error);
        throw new Error(`Error al crear solicitud: ${error.message}`);
      }

      // Crear notificación para el psicólogo
      await this._createRequestNotification(newRequest, 'created');

      // Crear notificación para administradores
      await this._notifyAdministrators(newRequest, 'new_request');

      return {
        success: true,
        data: newRequest,
        message: 'Solicitud de recarga creada exitosamente'
      };

    } catch (error) {
      console.error('Error in createRequest:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al crear la solicitud de recarga'
      };
    }
  }

  /**
   * Obtener solicitudes con filtros
   * GET /api/pin-recharge-requests
   */
  static async getRequests(filters = {}) {
    try {
      const {
        status = null,
        psychologist_id = null,
        urgency = null,
        date_from = null,
        date_to = null,
        limit = 50,
        offset = 0,
        order_by = 'created_at',
        order_direction = 'desc'
      } = filters;

      let query = supabase
        .from('pin_recharge_requests')
        .select('*')
        .range(offset, offset + limit - 1)
        .order(order_by, { ascending: order_direction === 'asc' });

      // Aplicar filtros
      if (status) {
        query = query.eq('status', status);
      }

      if (psychologist_id) {
        query = query.eq('psychologist_id', psychologist_id);
      }

      if (urgency) {
        query = query.eq('urgency', urgency);
      }

      if (date_from) {
        query = query.gte('created_at', date_from);
      }

      if (date_to) {
        query = query.lte('created_at', date_to);
      }

      const { data: requests, error } = await query;

      if (error) {
        throw new Error(`Error al obtener solicitudes: ${error.message}`);
      }

      // Obtener conteo total para paginación
      let countQuery = supabase
        .from('pin_recharge_requests')
        .select('*', { count: 'exact', head: true });

      if (status) countQuery = countQuery.eq('status', status);
      if (psychologist_id) countQuery = countQuery.eq('psychologist_id', psychologist_id);
      if (urgency) countQuery = countQuery.eq('urgency', urgency);
      if (date_from) countQuery = countQuery.gte('created_at', date_from);
      if (date_to) countQuery = countQuery.lte('created_at', date_to);

      const { count, error: countError } = await countQuery;

      if (countError) {
        console.warn('Error getting count:', countError);
      }

      return {
        success: true,
        data: requests || [],
        pagination: {
          total: count || 0,
          limit,
          offset,
          has_more: (count || 0) > offset + limit
        }
      };

    } catch (error) {
      console.error('Error in getRequests:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al obtener las solicitudes'
      };
    }
  }

  /**
   * Procesar solicitud (aprobar/rechazar)
   * PUT /api/pin-recharge-requests/:id
   */
  static async processRequest(requestId, processData) {
    try {
      const {
        action, // 'approve' | 'reject'
        admin_id,
        admin_notes = '',
        approved_pins = null // Solo para approve
      } = processData;

      if (!['approve', 'reject'].includes(action)) {
        throw new Error('Acción debe ser "approve" o "reject"');
      }

      if (!admin_id) {
        throw new Error('ID del administrador es requerido');
      }

      // Obtener la solicitud actual
      const { data: currentRequest, error: fetchError } = await supabase
        .from('pin_recharge_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      if (fetchError || !currentRequest) {
        throw new Error('Solicitud no encontrada');
      }

      if (currentRequest.status !== 'pending') {
        throw new Error(`Solicitud ya fue procesada con estado: ${currentRequest.status}`);
      }

      const newStatus = action === 'approve' ? 'approved' : 'rejected';
      const finalApprovedPins = action === 'approve' 
        ? (approved_pins || currentRequest.requested_pins)
        : null;

      // Actualizar la solicitud
      const { data: updatedRequest, error: updateError } = await supabase
        .from('pin_recharge_requests')
        .update({
          status: newStatus,
          processed_by: admin_id,
          processed_at: new Date().toISOString(),
          admin_notes,
          approved_pins: finalApprovedPins
        })
        .eq('id', requestId)
        .select('*')
        .single();

      if (updateError) {
        throw new Error(`Error al actualizar solicitud: ${updateError.message}`);
      }

      // Si se aprueba, asignar los pines
      if (action === 'approve' && finalApprovedPins > 0) {
        await this._assignPinsToUser(
          currentRequest.psychologist_id, 
          finalApprovedPins,
          `Recarga aprobada - Solicitud #${requestId.slice(0, 8)}`
        );
      }

      // Crear notificaciones
      await this._createRequestNotification(updatedRequest, action);

      return {
        success: true,
        data: updatedRequest,
        message: action === 'approve' 
          ? `Solicitud aprobada y ${finalApprovedPins} pines asignados`
          : 'Solicitud rechazada'
      };

    } catch (error) {
      console.error('Error in processRequest:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al procesar la solicitud'
      };
    }
  }

  /**
   * Obtener estadísticas de solicitudes
   * GET /api/pin-recharge-requests/stats
   */
  static async getRequestStats(filters = {}) {
    try {
      const { psychologist_id = null, date_from = null, date_to = null } = filters;

      let query = supabase
        .from('pin_recharge_requests')
        .select('status, urgency, requested_pins, approved_pins, created_at');

      if (psychologist_id) {
        query = query.eq('psychologist_id', psychologist_id);
      }

      if (date_from) {
        query = query.gte('created_at', date_from);
      }

      if (date_to) {
        query = query.lte('created_at', date_to);
      }

      const { data: requests, error } = await query;

      if (error) {
        throw new Error(`Error al obtener estadísticas: ${error.message}`);
      }

      // Calcular estadísticas
      const stats = {
        total_requests: requests.length,
        by_status: {
          pending: requests.filter(r => r.status === 'pending').length,
          approved: requests.filter(r => r.status === 'approved').length,
          rejected: requests.filter(r => r.status === 'rejected').length
        },
        by_urgency: {
          normal: requests.filter(r => r.urgency === 'normal').length,
          high: requests.filter(r => r.urgency === 'high').length,
          urgent: requests.filter(r => r.urgency === 'urgent').length
        },
        pins: {
          total_requested: requests.reduce((sum, r) => sum + r.requested_pins, 0),
          total_approved: requests
            .filter(r => r.status === 'approved')
            .reduce((sum, r) => sum + (r.approved_pins || 0), 0),
          average_request: requests.length > 0 
            ? Math.round(requests.reduce((sum, r) => sum + r.requested_pins, 0) / requests.length)
            : 0
        }
      };

      return {
        success: true,
        data: stats
      };

    } catch (error) {
      console.error('Error in getRequestStats:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error al obtener estadísticas'
      };
    }
  }

  // =====================================================
  // MÉTODOS PRIVADOS
  // =====================================================

  /**
   * Asignar pines a un usuario
   * @private
   */
  static async _assignPinsToUser(psychologistId, pins, reason) {
    try {
      // Usar el servicio existente de asignación de pines
      const PinManagementService = (await import('../../services/pin/PinManagementService.js')).default;
      
      await PinManagementService.assignPins(
        psychologistId,
        pins,
        false, // no unlimited
        'recharge_approval',
        reason
      );

      return true;
    } catch (error) {
      console.error('Error assigning pins:', error);
      throw error;
    }
  }

  /**
   * Crear notificación para una solicitud
   * @private
   */
  static async _createRequestNotification(request, action) {
    try {
      const notificationData = this._getNotificationData(request, action);
      
      const { error } = await supabase
        .from('pin_notifications')
        .insert([{
          user_id: request.psychologist_id,
          type: notificationData.type,
          title: notificationData.title,
          message: notificationData.message,
          severity: notificationData.severity,
          related_entity_type: 'pin_recharge_request',
          related_entity_id: request.id,
          metadata: {
            request_id: request.id,
            action,
            pins: request.approved_pins || request.requested_pins
          }
        }]);

      if (error) {
        console.error('Error creating notification:', error);
      }
    } catch (error) {
      console.error('Error in _createRequestNotification:', error);
    }
  }

  /**
   * Notificar a administradores sobre nueva solicitud
   * @private
   */
  static async _notifyAdministrators(request, type) {
    try {
      // Obtener administradores
      const { data: admins } = await supabase
        .from('usuarios')
        .select('id')
        .eq('rol', 'administrador');

      if (!admins || admins.length === 0) return;

      // Crear notificaciones para cada admin
      const notifications = admins.map(admin => ({
        user_id: admin.id,
        type: 'new_recharge_request',
        title: 'Nueva solicitud de recarga',
        message: `${request.psychologist?.name || 'Un psicólogo'} solicita ${request.requested_pins} pines (${request.urgency})`,
        severity: request.urgency === 'urgent' ? 'warning' : 'info',
        related_entity_type: 'pin_recharge_request',
        related_entity_id: request.id,
        metadata: {
          request_id: request.id,
          psychologist_id: request.psychologist_id,
          urgency: request.urgency,
          requested_pins: request.requested_pins
        }
      }));

      const { error } = await supabase
        .from('pin_notifications')
        .insert(notifications);

      if (error) {
        console.error('Error notifying administrators:', error);
      }
    } catch (error) {
      console.error('Error in _notifyAdministrators:', error);
    }
  }

  /**
   * Obtener datos de notificación según la acción
   * @private
   */
  static _getNotificationData(request, action) {
    switch (action) {
      case 'created':
        return {
          type: 'recharge_request_created',
          title: 'Solicitud enviada',
          message: `Tu solicitud de ${request.requested_pins} pines ha sido enviada y está siendo revisada`,
          severity: 'info'
        };
      case 'approve':
        return {
          type: 'recharge_request_approved',
          title: 'Solicitud aprobada',
          message: `Tu solicitud ha sido aprobada. Se han asignado ${request.approved_pins} pines a tu cuenta`,
          severity: 'success'
        };
      case 'reject':
        return {
          type: 'recharge_request_rejected',
          title: 'Solicitud rechazada',
          message: `Tu solicitud de ${request.requested_pins} pines ha sido rechazada`,
          severity: 'warning'
        };
      default:
        return {
          type: 'recharge_request_update',
          title: 'Actualización de solicitud',
          message: 'Tu solicitud ha sido actualizada',
          severity: 'info'
        };
    }
  }
}

export default PinRechargeRequestsAPI;
