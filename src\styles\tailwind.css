@tailwind base;
@tailwind components;
@tailwind utilities;

/* Estilos personalizados adicionales basados en tailwind */
@layer components {
  /* Botones */
  .btn {
    @apply px-4 py-2 rounded-md text-sm font-medium transition-colors;
  }
  
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-gray-100 text-gray-800 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
  }
  
  /* Formularios */
  .form-input {
    @apply w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm;
  }
  
  .form-select {
    @apply w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm;
  }
  
  .form-checkbox {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  /* Tarjetas */
  .card {
    @apply bg-white shadow rounded-lg overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply p-6;
  }
  
  .card-footer {
    @apply px-6 py-4 bg-gray-50 border-t border-gray-200;
  }
  
  /* Tablas */
  .table-container {
    @apply overflow-x-auto rounded-lg border border-gray-200 shadow-sm;
  }
  
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-row {
    @apply bg-white divide-y divide-gray-200 hover:bg-gray-50;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-500;
  }
  
  /* Navegación */
  .nav-link {
    @apply px-3 py-2 rounded-md text-sm font-medium;
  }
  
  .nav-link-active {
    @apply bg-blue-700 text-white;
  }
  
  .nav-link-inactive {
    @apply text-gray-300 hover:text-white hover:bg-blue-600;
  }
  
  /* Utilidades adicionales */
  .text-truncate {
    @apply whitespace-nowrap overflow-hidden text-ellipsis;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-blue {
    @apply bg-blue-100 text-blue-800;
  }
  
  .badge-green {
    @apply bg-green-100 text-green-800;
  }
  
  .badge-red {
    @apply bg-red-100 text-red-800;
  }
  
  .badge-yellow {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .badge-gray {
    @apply bg-gray-100 text-gray-800;
  }
}
