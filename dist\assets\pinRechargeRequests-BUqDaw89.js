const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/PinManagementService-DAtjQY2H.js","assets/index-Bdl1jgS_.js","assets/vendor-BqMjyOVw.js","assets/vendor-B4zyQOk2.css","assets/index-Csy2uUlu.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable,n=(t,r,s)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[r]=s,o=(e,t,r)=>new Promise((s,i)=>{var a=e=>{try{o(r.next(e))}catch(t){i(t)}},n=e=>{try{o(r.throw(e))}catch(t){i(t)}},o=e=>e.done?s(e.value):Promise.resolve(e.value).then(a,n);o((r=r.apply(e,t)).next())});import{_ as c}from"./vendor-BqMjyOVw.js";import{s as d}from"./index-Bdl1jgS_.js";class l{static createRequest(e){return o(this,null,function*(){try{const{psychologist_id:l,requested_pins:u,urgency:p="normal",reason:_,metadata:g={}}=e;if(!l||!u||!_)throw new Error("Faltan campos requeridos: psychologist_id, requested_pins, reason");if(!["normal","high","urgent"].includes(p))throw new Error("Urgencia debe ser: normal, high, o urgent");if(u<=0||u>1e3)throw new Error("Cantidad de pines debe estar entre 1 y 1000");const{data:h,error:y}=yield d.from("psicologos").select("id, nombre, email").eq("id",l).single();if(y||!h)throw new Error("Psicólogo no encontrado");const{data:m}=yield d.from("psychologist_usage_control").select("total_uses, used_uses, is_unlimited, is_active").eq("psicologo_id",l).single(),f=(o=((e,t)=>{for(var r in t||(t={}))i.call(t,r)&&n(e,r,t[r]);if(s)for(var r of s(t))a.call(t,r)&&n(e,r,t[r]);return e})({},g),c={usage_stats:{current_pins:m?m.total_uses-m.used_uses:0,total_assigned:(null==m?void 0:m.total_uses)||0,total_consumed:(null==m?void 0:m.used_uses)||0,is_unlimited:(null==m?void 0:m.is_unlimited)||!1,is_active:(null==m?void 0:m.is_active)||!1,last_assignment:null==m?void 0:m.updated_at},psychologist_info:{name:h.name,email:h.email},request_context:{user_agent:"undefined"!=typeof window?window.navigator.userAgent:"Server",timestamp:(new Date).toISOString()}},t(o,r(c))),{data:v,error:q}=yield d.from("pin_recharge_requests").insert([{psychologist_id:l,requested_pins:u,urgency:p,reason:_,status:"pending",metadata:f}]).select("*").single();if(q)throw new Error(`Error al crear solicitud: ${q.message}`);return yield this._createRequestNotification(v,"created"),yield this._notifyAdministrators(v,"new_request"),{success:!0,data:v,message:"Solicitud de recarga creada exitosamente"}}catch(l){return{success:!1,error:l.message,message:"Error al crear la solicitud de recarga"}}var o,c})}static getRequests(){return o(this,arguments,function*(e={}){try{const{status:t=null,psychologist_id:r=null,urgency:s=null,date_from:i=null,date_to:a=null,limit:n=50,offset:o=0,order_by:c="created_at",order_direction:l="desc"}=e;let u=d.from("pin_recharge_requests").select("*").range(o,o+n-1).order(c,{ascending:"asc"===l});t&&(u=u.eq("status",t)),r&&(u=u.eq("psychologist_id",r)),s&&(u=u.eq("urgency",s)),i&&(u=u.gte("created_at",i)),a&&(u=u.lte("created_at",a));const{data:p,error:_}=yield u;if(_)throw new Error(`Error al obtener solicitudes: ${_.message}`);let g=d.from("pin_recharge_requests").select("*",{count:"exact",head:!0});t&&(g=g.eq("status",t)),r&&(g=g.eq("psychologist_id",r)),s&&(g=g.eq("urgency",s)),i&&(g=g.gte("created_at",i)),a&&(g=g.lte("created_at",a));const{count:h,error:y}=yield g;return{success:!0,data:p||[],pagination:{total:h||0,limit:n,offset:o,has_more:(h||0)>o+n}}}catch(t){return{success:!1,error:t.message,message:"Error al obtener las solicitudes"}}})}static processRequest(e,t){return o(this,null,function*(){try{const{action:r,admin_id:s,admin_notes:i="",approved_pins:a=null}=t;if(!["approve","reject"].includes(r))throw new Error('Acción debe ser "approve" o "reject"');if(!s)throw new Error("ID del administrador es requerido");const{data:n,error:o}=yield d.from("pin_recharge_requests").select("*").eq("id",e).single();if(o||!n)throw new Error("Solicitud no encontrada");if("pending"!==n.status)throw new Error(`Solicitud ya fue procesada con estado: ${n.status}`);const c="approve"===r?"approved":"rejected",l="approve"===r?a||n.requested_pins:null,{data:u,error:p}=yield d.from("pin_recharge_requests").update({status:c,processed_by:s,processed_at:(new Date).toISOString(),admin_notes:i,approved_pins:l}).eq("id",e).select("*").single();if(p)throw new Error(`Error al actualizar solicitud: ${p.message}`);return"approve"===r&&l>0&&(yield this._assignPinsToUser(n.psychologist_id,l,`Recarga aprobada - Solicitud #${e.slice(0,8)}`)),yield this._createRequestNotification(u,r),{success:!0,data:u,message:"approve"===r?`Solicitud aprobada y ${l} pines asignados`:"Solicitud rechazada"}}catch(r){return{success:!1,error:r.message,message:"Error al procesar la solicitud"}}})}static getRequestStats(){return o(this,arguments,function*(e={}){try{const{psychologist_id:t=null,date_from:r=null,date_to:s=null}=e;let i=d.from("pin_recharge_requests").select("status, urgency, requested_pins, approved_pins, created_at");t&&(i=i.eq("psychologist_id",t)),r&&(i=i.gte("created_at",r)),s&&(i=i.lte("created_at",s));const{data:a,error:n}=yield i;if(n)throw new Error(`Error al obtener estadísticas: ${n.message}`);return{success:!0,data:{total_requests:a.length,by_status:{pending:a.filter(e=>"pending"===e.status).length,approved:a.filter(e=>"approved"===e.status).length,rejected:a.filter(e=>"rejected"===e.status).length},by_urgency:{normal:a.filter(e=>"normal"===e.urgency).length,high:a.filter(e=>"high"===e.urgency).length,urgent:a.filter(e=>"urgent"===e.urgency).length},pins:{total_requested:a.reduce((e,t)=>e+t.requested_pins,0),total_approved:a.filter(e=>"approved"===e.status).reduce((e,t)=>e+(t.approved_pins||0),0),average_request:a.length>0?Math.round(a.reduce((e,t)=>e+t.requested_pins,0)/a.length):0}}}}catch(t){return{success:!1,error:t.message,message:"Error al obtener estadísticas"}}})}static _assignPinsToUser(e,t,r){return o(this,null,function*(){try{const s=(yield c(()=>o(null,null,function*(){const{default:e}=yield import("./PinManagementService-DAtjQY2H.js");return{default:e}}),__vite__mapDeps([0,1,2,3,4]))).default;return yield s.assignPins(e,t,!1,"recharge_approval",r),!0}catch(s){throw s}})}static _createRequestNotification(e,t){return o(this,null,function*(){try{const r=this._getNotificationData(e,t),{error:s}=yield d.from("pin_notifications").insert([{user_id:e.psychologist_id,type:r.type,title:r.title,message:r.message,severity:r.severity,related_entity_type:"pin_recharge_request",related_entity_id:e.id,metadata:{request_id:e.id,action:t,pins:e.approved_pins||e.requested_pins}}])}catch(r){}})}static _notifyAdministrators(e,t){return o(this,null,function*(){try{const{data:t}=yield d.from("usuarios").select("id").eq("rol","administrador");if(!t||0===t.length)return;const r=t.map(t=>{var r;return{user_id:t.id,type:"new_recharge_request",title:"Nueva solicitud de recarga",message:`${(null==(r=e.psychologist)?void 0:r.name)||"Un psicólogo"} solicita ${e.requested_pins} pines (${e.urgency})`,severity:"urgent"===e.urgency?"warning":"info",related_entity_type:"pin_recharge_request",related_entity_id:e.id,metadata:{request_id:e.id,psychologist_id:e.psychologist_id,urgency:e.urgency,requested_pins:e.requested_pins}}}),{error:s}=yield d.from("pin_notifications").insert(r)}catch(t){}})}static _getNotificationData(e,t){switch(t){case"created":return{type:"recharge_request_created",title:"Solicitud enviada",message:`Tu solicitud de ${e.requested_pins} pines ha sido enviada y está siendo revisada`,severity:"info"};case"approve":return{type:"recharge_request_approved",title:"Solicitud aprobada",message:`Tu solicitud ha sido aprobada. Se han asignado ${e.approved_pins} pines a tu cuenta`,severity:"success"};case"reject":return{type:"recharge_request_rejected",title:"Solicitud rechazada",message:`Tu solicitud de ${e.requested_pins} pines ha sido rechazada`,severity:"warning"};default:return{type:"recharge_request_update",title:"Actualización de solicitud",message:"Tu solicitud ha sido actualizada",severity:"info"}}}}export{l as P};
