import{j as s}from"./vendor-BqMjyOVw.js";import{C as e,b as t,a as i}from"./index-Bdl1jgS_.js";const a=()=>s.jsxs("div",{className:"container mx-auto py-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Administración de Instituciones"}),s.jsxs(e,{children:[s.jsx(t,{children:s.jsx("h2",{className:"text-lg font-medium",children:"Lista de Instituciones"})}),s.jsx(i,{children:s.jsx("p",{className:"text-gray-600",children:"Esta sección permitirá gestionar las instituciones registradas en el sistema (componente en desarrollo)."})})]})]});export{a as default};
