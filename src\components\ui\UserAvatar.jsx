import React from 'react';
import { FaUser } from 'react-icons/fa';

/**
 * Componente de avatar de usuario con iniciales
 */
const UserAvatar = ({ 
  nombre = '', 
  apellido = '', 
  size = 'md', 
  className = '',
  showFallback = true 
}) => {
  // Generar iniciales
  const getInitials = () => {
    const firstInitial = nombre?.charAt(0)?.toUpperCase() || '';
    const lastInitial = apellido?.charAt(0)?.toUpperCase() || '';
    return `${firstInitial}${lastInitial}` || '??';
  };

  // Generar color de fondo basado en las iniciales
  const getBackgroundColor = () => {
    const initials = getInitials();
    const colors = [
      'bg-red-500',
      'bg-blue-500',
      'bg-green-500',
      'bg-yellow-500',
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-teal-500',
      'bg-orange-500',
      'bg-cyan-500'
    ];
    
    // Usar el código ASCII de las iniciales para seleccionar un color consistente
    const charCode = initials.charCodeAt(0) + (initials.charCodeAt(1) || 0);
    return colors[charCode % colors.length];
  };

  // Configuración de tamaños
  const getSizeClasses = () => {
    switch (size) {
      case 'xs':
        return 'h-6 w-6 text-xs';
      case 'sm':
        return 'h-8 w-8 text-sm';
      case 'md':
        return 'h-10 w-10 text-sm';
      case 'lg':
        return 'h-12 w-12 text-base';
      case 'xl':
        return 'h-16 w-16 text-lg';
      case '2xl':
        return 'h-20 w-20 text-xl';
      default:
        return 'h-10 w-10 text-sm';
    }
  };

  const initials = getInitials();
  const backgroundColor = getBackgroundColor();
  const sizeClasses = getSizeClasses();

  return (
    <div 
      className={`
        ${sizeClasses} 
        ${backgroundColor} 
        rounded-full 
        flex 
        items-center 
        justify-center 
        text-white 
        font-medium 
        flex-shrink-0
        ${className}
      `}
      title={`${nombre} ${apellido}`.trim() || 'Usuario'}
    >
      {initials !== '??' ? (
        <span>{initials}</span>
      ) : (
        showFallback && <FaUser className="w-1/2 h-1/2" />
      )}
    </div>
  );
};

export default UserAvatar;
