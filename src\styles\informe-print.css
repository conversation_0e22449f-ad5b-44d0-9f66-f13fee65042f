/*
 * ========================================
 * SISTEMA ULTRA-ROBUSTO DE IMPRESIÓN
 * Fidelidad Visual 100% - Cross-Browser
 * ========================================
 *
 * Este CSS garantiza que el PDF sea IDÉNTICO a la pantalla
 * Compatible con Chrome, Firefox, Safari, Edge
 * Utiliza máxima especificidad y múltiples capas de protección
 */

@media print {
  /* ========================================
     CONFIGURACIÓN DE PÁGINA OPTIMIZADA
     ======================================== */
  @page {
    margin: 0.5in !important;
    size: A4 !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* ========================================
     FORZAR PRESERVACIÓN DE COLORES GLOBAL
     Compatible con todos los navegadores
     ======================================== */
  *,
  *::before,
  *::after,
  html,
  body,
  div,
  span,
  h1, h2, h3, h4, h5, h6,
  p, a, strong, em, i, b,
  svg, path, circle, rect,
  table, tr, td, th,
  ul, ol, li,
  img, canvas {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  /* Configuración del body para impresión */
  html, body {
    background: white !important;
    color: black !important;
    font-family: Arial, sans-serif !important;
    font-size: 12pt !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: auto !important;
  }

  /* Contenedor principal de impresión - ULTRA ESPECÍFICO */
  .print-content,
  [class*="print-content"],
  div[style*="font-family"] {
    background: white !important;
    background-color: white !important;
    color: black !important;
    font-family: Arial, sans-serif !important;
    font-size: 12pt !important;
    line-height: 1.4 !important;
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 2rem !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  /* Ocultar elementos no deseados */
  .print-hide,
  .no-print,
  .sidebar,
  header,
  nav,
  .modal-overlay,
  .modal-close,
  button:not(.print-keep),
  .fixed,
  .absolute:not(.print-content) {
    display: none !important;
    visibility: hidden !important;
  }

  /* HEADERS AZULES - MÁXIMA ESPECIFICIDAD */
  .print-content .print-header,
  .print-content .bg-blue-600,
  .print-content .bg-blue-500,
  .print-content .bg-gradient-to-r,
  .print-content [class*="bg-gradient"],
  .print-content [class*="from-blue"],
  .print-content div.bg-blue-600,
  .print-content div.bg-blue-500,
  .print-content div.bg-gradient-to-r,
  .print-content div[class*="from-blue-600"],
  .print-content div[class*="to-blue-700"],
  div.print-content .print-header,
  div.print-content .bg-blue-600,
  div.print-content .bg-blue-500,
  div.print-content .bg-gradient-to-r {
    background: #2563eb !important;
    background-color: #2563eb !important;
    background-image: none !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
    display: block !important;
    visibility: visible !important;
    width: 100% !important;
    box-sizing: border-box !important;
    border: none !important;
  }

  /* TEXTO BLANCO SOLO EN HEADERS AZULES - MÁXIMA ESPECIFICIDAD */
  .print-content .print-header *,
  .print-content .bg-blue-600 *,
  .print-content .bg-blue-500 *,
  .print-content .bg-gradient-to-r *,
  .print-content [class*="bg-gradient"] *,
  .print-content [class*="from-blue"] *,
  div.print-content .print-header *,
  div.print-content .bg-blue-600 *,
  div.print-content .bg-blue-500 *,
  div.print-content .bg-gradient-to-r * {
    color: white !important;
    visibility: visible !important;
  }

  /* TEXTO ESPECÍFICO BLANCO SOLO CUANDO ESTÁ EN HEADERS */
  .print-content .print-header .text-white,
  .print-content .bg-blue-600 .text-white,
  .print-content .bg-gradient-to-r .text-white,
  .print-content .print-header h1,
  .print-content .print-header h2,
  .print-content .print-header p,
  .print-content .bg-blue-600 h1,
  .print-content .bg-blue-600 h2,
  .print-content .bg-blue-600 p,
  .print-content .bg-gradient-to-r h1,
  .print-content .bg-gradient-to-r h2,
  .print-content .bg-gradient-to-r p {
    color: white !important;
    visibility: visible !important;
  }

  /* Elementos inline en headers */
  .print-content .print-header span,
  .print-content .bg-blue-600 span,
  .print-content .bg-gradient-to-r span,
  .print-content .print-header svg,
  .print-content .bg-blue-600 svg,
  .print-content .bg-gradient-to-r svg {
    display: inline-block !important;
    color: white !important;
  }

  /* TEXTO NEGRO PARA CONTENIDO NORMAL - MÁXIMA ESPECIFICIDAD */
  .print-content p,
  .print-content span,
  .print-content div,
  .print-content h3,
  .print-content h4,
  .print-content h5,
  .print-content h6,
  .print-content .text-gray-900,
  .print-content .text-gray-800,
  .print-content .text-gray-700,
  .print-content .text-black,
  div.print-content p,
  div.print-content span,
  div.print-content div,
  div.print-content h3,
  div.print-content h4,
  div.print-content h5,
  div.print-content h6 {
    color: black !important;
  }

  /* EXCEPCIONES: Mantener blanco solo en headers azules */
  .print-content .print-header p,
  .print-content .print-header span,
  .print-content .print-header div,
  .print-content .bg-blue-600 p,
  .print-content .bg-blue-600 span,
  .print-content .bg-blue-600 div,
  .print-content .bg-gradient-to-r p,
  .print-content .bg-gradient-to-r span,
  .print-content .bg-gradient-to-r div {
    color: white !important;
  }

  /* TÍTULOS EN HEADERS - ULTRA ESPECÍFICO */
  .print-header h1,
  .print-header h2,
  .bg-blue-600 h1,
  .bg-blue-600 h2,
  .bg-gradient-to-r h1,
  .bg-gradient-to-r h2,
  [style*="background: #2563eb"] h1,
  [style*="background: #2563eb"] h2 {
    color: white !important;
    font-size: 1.875rem !important;
    font-weight: 700 !important;
    margin: 0 !important;
    text-align: left !important;
    display: block !important;
    visibility: visible !important;
    line-height: 1.2 !important;
  }

  .print-header *,
  .informe-header-main *,
  .print-only .informe-header-main * {
    color: white !important;
    visibility: visible !important;
    display: block !important;
  }

  /* PESOS DE FUENTE - MÁXIMA ESPECIFICIDAD */
  .print-content .font-bold,
  .print-content p.font-bold,
  .print-content h1.font-bold,
  .print-content h2.font-bold,
  .print-content h3.font-bold,
  .print-content h4.font-bold,
  .print-content span.font-bold,
  div.print-content .font-bold,
  div.print-content p.font-bold,
  div.print-content h1.font-bold,
  div.print-content h2.font-bold,
  div.print-content h3.font-bold {
    font-weight: 700 !important;
  }

  .print-content .font-medium,
  .print-content p.font-medium,
  div.print-content .font-medium,
  div.print-content p.font-medium {
    font-weight: 500 !important;
  }

  .print-content .font-semibold,
  .print-content p.font-semibold,
  div.print-content .font-semibold,
  div.print-content p.font-semibold {
    font-weight: 600 !important;
  }

  /* Tamaños de texto específicos para impresión */
  .text-3xl {
    font-size: 28pt !important;
    line-height: 1.2 !important;
  }

  .text-2xl {
    font-size: 24pt !important;
    line-height: 1.2 !important;
  }

  .text-xl {
    font-size: 20pt !important;
    line-height: 1.3 !important;
  }

  .text-lg {
    font-size: 16pt !important;
    line-height: 1.3 !important;
  }

  .text-base {
    font-size: 14pt !important;
    line-height: 1.4 !important;
  }

  .text-sm {
    font-size: 12pt !important;
    line-height: 1.4 !important;
  }

  .text-xs {
    font-size: 10pt !important;
    line-height: 1.4 !important;
  }

  .print-header h1,
  .informe-header-main h1,
  .print-only .informe-header-main h1 {
    color: white !important;
    font-size: 28pt !important;
    font-weight: bold !important;
    margin: 0 !important;
    text-align: left !important;
    display: block !important;
    visibility: visible !important;
  }

  /* ICONOS DE APTITUDES - MÁXIMA ESPECIFICIDAD */
  .print-content .bg-orange-500,
  .print-content .bg-orange-600,
  .print-content [style*="backgroundColor: #f97316"],
  .print-content [style*="background-color: rgb(249, 115, 22)"],
  .print-content div.bg-orange-500,
  .print-content div.bg-orange-600,
  div.print-content .bg-orange-500,
  div.print-content .bg-orange-600,
  .print-content .w-12.h-12.bg-orange-500,
  .print-content .w-12.h-12.bg-orange-600 {
    background: #f97316 !important;
    background-color: #f97316 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  .print-content .bg-blue-500,
  .print-content .bg-blue-600,
  .print-content [style*="backgroundColor: #3b82f6"],
  .print-content [style*="background-color: rgb(59, 130, 246)"],
  .print-content div.bg-blue-500,
  .print-content div.bg-blue-600,
  div.print-content .bg-blue-500,
  div.print-content .bg-blue-600,
  .print-content .w-12.h-12.bg-blue-500,
  .print-content .w-12.h-12.bg-blue-600 {
    background: #3b82f6 !important;
    background-color: #3b82f6 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  .bg-green-500,
  .bg-green-600,
  [style*="backgroundColor: #22c55e"],
  [style*="background-color: rgb(34, 197, 94)"] {
    background: #22c55e !important;
    background-color: #22c55e !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  .bg-red-500,
  .bg-red-600,
  [style*="backgroundColor: #ef4444"],
  [style*="background-color: rgb(239, 68, 68)"] {
    background: #ef4444 !important;
    background-color: #ef4444 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  .bg-yellow-500,
  .bg-yellow-600,
  [style*="backgroundColor: #eab308"],
  [style*="background-color: rgb(234, 179, 8)"] {
    background: #eab308 !important;
    background-color: #eab308 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  .bg-gray-500,
  .bg-gray-600,
  [style*="backgroundColor: #6b7280"],
  [style*="background-color: rgb(107, 114, 128)"] {
    background: #6b7280 !important;
    background-color: #6b7280 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  /* TEXTO BLANCO EN ICONOS Y NIVELES */
  .print-content .bg-orange-500 *,
  .print-content .bg-blue-500 *,
  .print-content .bg-green-500 *,
  .print-content .bg-red-500 *,
  .print-content .bg-yellow-500 *,
  .print-content .bg-gray-500 *,
  .print-content [style*="backgroundColor"] *,
  div.print-content .bg-orange-500 *,
  div.print-content .bg-blue-500 *,
  div.print-content .bg-green-500 *,
  div.print-content .bg-red-500 *,
  div.print-content .bg-yellow-500 *,
  div.print-content .bg-gray-500 * {
    color: white !important;
  }

  /* NIVELES DE RENDIMIENTO - ESPECÍFICOS */
  .print-content .text-white.text-center.py-2.rounded,
  .print-content div[class*="text-white"][class*="text-center"],
  .print-content div[class*="py-2"][class*="rounded"],
  div.print-content .text-white.text-center.py-2.rounded {
    color: white !important;
    text-align: center !important;
    padding: 0.5rem 0 !important;
    border-radius: 0.375rem !important;
    font-weight: 700 !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  .informe-header-main h2,
  .print-only .informe-header-main h2 {
    color: #e0e7ff !important;
    font-size: 16pt !important;
    margin: 0.5rem 0 0 0 !important;
    display: block !important;
    visibility: visible !important;
  }

  .informe-header-main p,
  .print-only .informe-header-main p {
    color: #c7d2fe !important;
    font-size: 12pt !important;
    margin: 0.25rem 0 0 0 !important;
    display: block !important;
    visibility: visible !important;
  }

  /* MOSTRAR ELEMENTOS SOLO EN IMPRESIÓN */
  .print-only {
    display: block !important;
    visibility: visible !important;
  }

  /* SECCIONES AZULES - INFORMACIÓN DEL EVALUADO - FORZADO AGRESIVAMENTE */
  .print-patient-info .bg-gradient-to-r,
  .print-patient-info .print-header,
  .bg-gradient-to-r.from-blue-600,
  .bg-gradient-to-r.to-indigo-700,
  .CardHeader.bg-gradient-to-r {
    background: #1e40af !important;
    background-color: #1e40af !important;
    background-image: none !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    padding: 1.5rem !important;
    border: none !important;
    display: block !important;
    visibility: visible !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  .print-patient-info .print-title,
  .bg-gradient-to-r h3,
  .bg-gradient-to-r .print-title {
    color: white !important;
    font-size: 20pt !important;
    font-weight: bold !important;
    display: block !important;
    visibility: visible !important;
    margin: 0 !important;
  }

  .print-patient-info .print-subtitle,
  .bg-gradient-to-r p,
  .bg-gradient-to-r .print-subtitle {
    color: #e0e7ff !important;
    font-size: 12pt !important;
    display: block !important;
    visibility: visible !important;
    margin: 0.25rem 0 0 0 !important;
  }

  .print-patient-info .print-icon,
  .bg-gradient-to-r .print-icon {
    background: rgba(255, 255, 255, 0.2) !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    display: inline-block !important;
    visibility: visible !important;
  }

  .print-patient-info .print-icon-color,
  .bg-gradient-to-r .print-icon-color,
  .bg-gradient-to-r i {
    color: white !important;
    display: inline-block !important;
    visibility: visible !important;
  }

  /* SECCIONES AZULES - RESUMEN GENERAL */
  .print-summary-card .bg-gradient-to-r,
  .print-summary-card .print-header {
    background: #1e40af !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    padding: 1.5rem !important;
    border: none !important;
  }

  /* SECCIONES AZULES - ANÁLISIS CUALITATIVO */
  .print-analysis-card .bg-gradient-to-r,
  .print-analysis-header {
    background: #1e40af !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    padding: 1.5rem !important;
    border: none !important;
  }

  /* ESTADÍSTICAS DEL RESUMEN GENERAL - FORZADO AGRESIVAMENTE */
  .print-stats-grid,
  .grid.grid-cols-2.md\\:grid-cols-4,
  .grid.print-stats-grid {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 1rem !important;
    padding: 1.5rem !important;
    visibility: visible !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  .print-stat-box,
  .text-center.p-4.bg-blue-50,
  .text-center.p-4.bg-green-50,
  .text-center.p-4.bg-purple-50,
  .text-center.p-4.bg-orange-50 {
    background: white !important;
    background-color: white !important;
    border: 2px solid #e5e7eb !important;
    padding: 1rem !important;
    border-radius: 8px !important;
    text-align: center !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    display: block !important;
    visibility: visible !important;
    width: 100% !important;
    box-sizing: border-box !important;
    margin: 0 !important;
  }

  /* Colores específicos para cada estadística como en la imagen */
  .print-stat-blue {
    background: #eff6ff !important;
    border-color: #3b82f6 !important;
  }

  .print-stat-green {
    background: #f0fdf4 !important;
    border-color: #22c55e !important;
  }

  .print-stat-purple {
    background: #faf5ff !important;
    border-color: #a855f7 !important;
  }

  .print-stat-orange {
    background: #fff7ed !important;
    border-color: #f97316 !important;
  }

  .print-stat-red {
    background: #fef2f2 !important;
    border-color: #ef4444 !important;
  }

  .print-stat-number,
  .text-3xl.font-bold.text-blue-600,
  .text-3xl.font-bold.text-green-600,
  .text-3xl.font-bold.text-purple-600,
  .text-3xl.font-bold.text-orange-600 {
    color: #111827 !important;
    font-weight: bold !important;
    font-size: 32pt !important;
    line-height: 1 !important;
    display: block !important;
    visibility: visible !important;
    text-align: center !important;
    margin: 0 !important;
  }

  .print-stat-label,
  .text-sm.text-gray-600.mt-1 {
    color: #6b7280 !important;
    font-weight: 600 !important;
    font-size: 12pt !important;
    margin-top: 0.5rem !important;
    display: block !important;
    visibility: visible !important;
    text-align: center !important;
  }

  /* CAJAS DE DATOS PERSONALES - FORZADO AGRESIVAMENTE */
  .print-data-box,
  .bg-white.p-4.rounded-lg.shadow-sm.border {
    background: white !important;
    background-color: white !important;
    border: 1px solid #e5e7eb !important;
    margin-bottom: 0.75rem !important;
    padding: 1rem !important;
    border-radius: 8px !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    display: block !important;
    visibility: visible !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  .print-data-icon,
  .fas.fa-user,
  .fas.fa-id-badge,
  .fas.fa-birthday-cake,
  .fas.fa-venus-mars,
  .fas.fa-phone,
  .fas.fa-envelope {
    color: #3b82f6 !important;
    display: inline-block !important;
    visibility: visible !important;
  }

  /* ETIQUETAS Y VALORES - FORZAR TEXTO NEGRO */
  .print-content .print-label,
  .print-content .font-semibold,
  .print-content .text-gray-700,
  .print-content .text-gray-800,
  .print-content .text-gray-900,
  div.print-content .print-label,
  div.print-content .font-semibold,
  div.print-content .text-gray-700,
  div.print-content .text-gray-800,
  div.print-content .text-gray-900 {
    color: #374151 !important;
    font-weight: 600 !important;
    font-size: 12pt !important;
    display: block !important;
    visibility: visible !important;
    margin: 0 !important;
  }

  .print-content .print-value,
  .print-content .font-bold,
  .print-content .text-lg,
  div.print-content .print-value,
  div.print-content .font-bold,
  div.print-content .text-lg {
    color: #111827 !important;
    font-weight: bold !important;
    font-size: 14pt !important;
    display: block !important;
    visibility: visible !important;
    margin: 0.25rem 0 0 0 !important;
  }

  /* ÍNDICES DE INTELIGENCIA */
  .print-intelligence-card {
    background: white !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    padding: 1.5rem !important;
    margin-bottom: 1rem !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  .print-intelligence-card.yellow {
    background: white !important;
    border-color: #eab308 !important;
  }

  .print-intelligence-card.orange {
    background: white !important;
    border-color: #f97316 !important;
  }

  .print-intelligence-card.gray {
    background: white !important;
    border-color: #6b7280 !important;
  }

  .print-intelligence-number {
    font-size: 36pt !important;
    font-weight: bold !important;
    color: #111827 !important;
  }

  .print-intelligence-label {
    font-size: 12pt !important;
    color: #6b7280 !important;
    font-weight: 600 !important;
  }

  .print-intelligence-level {
    font-size: 14pt !important;
    font-weight: bold !important;
    color: #111827 !important;
    background: rgba(255, 255, 255, 0.9) !important;
    padding: 0.5rem !important;
    border-radius: 4px !important;
    margin-top: 0.5rem !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* OCULTAR ELEMENTOS NO DESEADOS EN IMPRESIÓN */
  .print-hide,
  .no-print,
  .sidebar,
  header,
  nav,
  .modal-overlay,
  .modal-close,
  button:not(.print-keep) {
    display: none !important;
  }

  /* MOSTRAR ELEMENTOS SOLO EN IMPRESIÓN */
  .print-only {
    display: block !important;
  }

  /* EVITAR SALTOS DE PÁGINA EN ELEMENTOS IMPORTANTES */
  .print-keep-together,
  .print-patient-info,
  .print-summary-card,
  .print-analysis-card,
  .print-intelligence-card,
  .print-aptitude-card {
    page-break-inside: avoid !important;
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 8px !important;
    padding: 1.5rem !important;
    margin-bottom: 1rem !important;
  }

  /* Estilos específicos para tarjetas de aptitudes */
  .print-aptitude-card {
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 8px !important;
    padding: 1.5rem !important;
    margin-bottom: 1rem !important;
    page-break-inside: avoid !important;
  }

  /* Estilos específicos para tarjetas de inteligencia */
  .print-intelligence-card {
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 8px !important;
    padding: 1.5rem !important;
    margin-bottom: 1rem !important;
    page-break-inside: avoid !important;
  }

  /* AJUSTES GENERALES */
  body {
    background: white !important;
    color: black !important;
    font-size: 12pt !important;
    line-height: 1.4 !important;
  }

  .container {
    max-width: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* FORZAR VISIBILIDAD DE TODOS LOS ELEMENTOS IMPORTANTES */
  .print-content,
  .print-content *,
  .print-header,
  .print-header *,
  .print-aptitude-card,
  .print-aptitude-card *,
  .print-intelligence-card,
  .print-intelligence-card *,
  .print-value,
  .print-stat-number,
  .print-label,
  .print-stat-label,
  .print-intelligence-number,
  .print-intelligence-label,
  .print-intelligence-level,
  .print-title,
  .print-subtitle,
  .print-data-box,
  .print-stat-box,
  .print-stats-grid,
  .bg-gradient-to-r,
  .informe-header-main,
  .print-only,
  .Card,
  .CardHeader,
  .CardBody,
  h1, h2, h3, h4, h5, h6,
  p, div, span {
    opacity: 1 !important;
    visibility: visible !important; /* display: block !important; se elimina porque rompe el layout de elementos inline */
  }

  /* Asegurar que los elementos flex se muestren correctamente */
  .flex {
    display: flex !important;
  }

  .grid {
    display: grid !important;
  }

  /* Preservar espaciado y márgenes */
  .mb-1 { margin-bottom: 0.25rem !important; }
  .mb-2 { margin-bottom: 0.5rem !important; }
  .mb-3 { margin-bottom: 0.75rem !important; }
  .mb-4 { margin-bottom: 1rem !important; }
  .mb-6 { margin-bottom: 1.5rem !important; }
  .mb-8 { margin-bottom: 2rem !important; }

  .mt-1 { margin-top: 0.25rem !important; }
  .mt-2 { margin-top: 0.5rem !important; }
  .mt-3 { margin-top: 0.75rem !important; }
  .mt-4 { margin-top: 1rem !important; }

  .p-2 { padding: 0.5rem !important; }
  .p-3 { padding: 0.75rem !important; }
  .p-4 { padding: 1rem !important; }
  .p-6 { padding: 1.5rem !important; }
  .p-8 { padding: 2rem !important; }

  /* Elementos inline específicos */
  i, .fas, .fa, span.text-sm, span.text-xs {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Grids y flexbox */
  .grid, .flex {
    display: grid !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .flex {
    display: flex !important;
  }

  /* Contenedores principales */
  .container, .max-w-7xl, .mx-auto, .px-4, .sm\\:px-6, .lg\\:px-8 {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* AJUSTAR ESPACIADO PARA IMPRESIÓN */
  .mb-6 {
    margin-bottom: 1rem !important;
  }

  .p-6 {
    padding: 1rem !important;
  }

  .gap-6 {
    gap: 1rem !important;
  }

  .space-y-4 > * + * {
    margin-top: 0.75rem !important;
  }

  /* COLORES ESPECÍFICOS PARA APTITUDES - BASADO EN CONFIGURACIÓN CENTRALIZADA */

  /* Aptitud Verbal - Azul */
  .aptitude-V,
  .bg-blue-500 {
    background-color: #2563EB !important;
    color: white !important;
  }

  .text-blue-600 {
    color: #2563EB !important;
  }

  .border-blue-500 {
    border-color: #2563EB !important;
  }

  /* Aptitud Espacial - Morado */
  .aptitude-E,
  .bg-purple-500 {
    background-color: #6D28D9 !important;
    color: white !important;
  }

  .text-purple-600 {
    color: #6D28D9 !important;
  }

  .border-purple-500 {
    border-color: #6D28D9 !important;
  }

  /* Atención - Rojo */
  .aptitude-A,
  .bg-red-500 {
    background-color: #DC2626 !important;
    color: white !important;
  }

  .text-red-600 {
    color: #DC2626 !important;
  }

  .border-red-500 {
    border-color: #DC2626 !important;
  }

  /* Razonamiento - Naranja */
  .aptitude-R,
  .bg-orange-500 {
    background-color: #D97706 !important;
    color: white !important;
  }

  .text-orange-600 {
    color: #D97706 !important;
  }

  .border-orange-500 {
    border-color: #D97706 !important;
  }

  /* Aptitud Numérica - Verde azulado/Teal */
  .aptitude-N,
  .bg-teal-500 {
    background-color: #0F766E !important;
    color: white !important;
  }

  .text-teal-600 {
    color: #0F766E !important;
  }

  .border-teal-500 {
    border-color: #0F766E !important;
  }

  /* Aptitud Mecánica - Gris */
  .aptitude-M,
  .bg-gray-500 {
    background-color: #374151 !important;
    color: white !important;
  }

  .text-gray-600 {
    color: #374151 !important;
  }

  .border-gray-500 {
    border-color: #374151 !important;
  }

  /* Ortografía - Verde */
  .aptitude-O,
  .bg-green-500 {
    background-color: #16A34A !important;
    color: white !important;
  }

  .text-green-600 {
    color: #16A34A !important;
  }

  .border-green-500 {
    border-color: #16A34A !important;
  }

  /* BADGES Y BOTONES CON COLORES ESPECÍFICOS */
  .badge-completado,
  .bg-green-500.badge {
    background-color: #16A34A !important;
    color: white !important;
  }

  .badge-pendiente,
  .bg-orange-500.badge {
    background-color: #D97706 !important;
    color: white !important;
  }

  /* BOTONES DE ACCIÓN */
  .btn-iniciar-test {
    background-color: #2563EB !important; /* Azul para Verbal */
    color: white !important;
  }

  .btn-repetir-test {
    background-color: #D97706 !important; /* Naranja para repetir */
    color: white !important;
  }

  /* ASEGURAR QUE LOS ICONOS SE MUESTREN CORRECTAMENTE */
  .fas, .fa, [class*="fa-"],
  svg, .icon {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: inherit !important;
  }

  /* MANTENER ESTRUCTURA DE TARJETAS DE APTITUDES */
  .aptitude-card {
    background: white !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 12px !important;
    padding: 1.5rem !important;
    margin-bottom: 1rem !important;
    page-break-inside: avoid !important;
  }

  .aptitude-icon-container {
    width: 48px !important;
    height: 48px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-bottom: 0.5rem !important;
  }

  .aptitude-title {
    font-size: 18pt !important;
    font-weight: bold !important;
    color: #111827 !important;
    margin-bottom: 0.25rem !important;
  }

  .aptitude-description {
    font-size: 12pt !important;
    color: #6b7280 !important;
    line-height: 1.4 !important;
  }

  .aptitude-stats {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 1rem !important;
    margin-top: 1rem !important;
  }

  .stat-item {
    text-align: center !important;
    padding: 0.5rem !important;
    background: #f9fafb !important;
    border-radius: 8px !important;
  }

  .stat-number {
    font-size: 24pt !important;
    font-weight: bold !important;
    color: #111827 !important;
    display: block !important;
  }

  .stat-label {
    font-size: 10pt !important;
    color: #6b7280 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
  }

  /* ========================================
     REGLAS ULTRA-AGRESIVAS FINALES
     Máxima especificidad para sobrescribir todo
     ======================================== */

  /* FORZAR HEADERS AZULES - ÚLTIMA INSTANCIA */
  body .print-content .print-header,
  body .print-content .bg-blue-600,
  body .print-content .bg-gradient-to-r,
  body .print-content div.bg-blue-600,
  body .print-content div.bg-gradient-to-r,
  body .print-content div[class*="from-blue-600"],
  body .print-content div[class*="to-blue-700"] {
    background: #2563eb !important;
    background-color: #2563eb !important;
    background-image: none !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  /* FORZAR TEXTO BLANCO EN HEADERS */
  body .print-content .print-header *,
  body .print-content .bg-blue-600 *,
  body .print-content .bg-gradient-to-r *,
  body .print-content .print-header h1,
  body .print-content .print-header h2,
  body .print-content .print-header p,
  body .print-content .bg-blue-600 h1,
  body .print-content .bg-blue-600 h2,
  body .print-content .bg-blue-600 p,
  body .print-content .bg-gradient-to-r h1,
  body .print-content .bg-gradient-to-r h2,
  body .print-content .bg-gradient-to-r p {
    color: white !important;
  }

  /* FORZAR TEXTO EN NEGRITA */
  body .print-content .font-bold,
  body .print-content p.font-bold,
  body .print-content h1.font-bold,
  body .print-content h2.font-bold,
  body .print-content h3.font-bold {
    font-weight: 700 !important;
  }

  /* FORZAR COLORES DE APTITUDES */
  body .print-content .bg-orange-500 { background-color: #f97316 !important; }
  body .print-content .bg-red-500 { background-color: #ef4444 !important; }
  body .print-content .bg-green-500 { background-color: #22c55e !important; }
  body .print-content .bg-yellow-500 { background-color: #eab308 !important; }
  body .print-content .bg-gray-600 { background-color: #4b5563 !important; }
  body .print-content .bg-blue-500 { background-color: #3b82f6 !important; }

  /* FORZAR TEXTO NEGRO EN CONTENIDO NORMAL - ÚLTIMA INSTANCIA */
  body .print-content p:not(.print-header p):not(.bg-blue-600 p):not(.bg-gradient-to-r p),
  body .print-content span:not(.print-header span):not(.bg-blue-600 span):not(.bg-gradient-to-r span),
  body .print-content div:not(.print-header):not(.bg-blue-600):not(.bg-gradient-to-r) > *,
  body .print-content .print-value,
  body .print-content .print-label,
  body .print-content .text-gray-900,
  body .print-content .text-gray-800,
  body .print-content .text-gray-700,
  body .print-content .font-bold:not(.print-header .font-bold):not(.bg-blue-600 .font-bold),
  body .print-content .font-semibold:not(.print-header .font-semibold):not(.bg-blue-600 .font-semibold) {
    color: black !important;
  }
}

/* Estilos para pantalla - ocultar elementos de impresión */
.print-only {
  display: none;
}
