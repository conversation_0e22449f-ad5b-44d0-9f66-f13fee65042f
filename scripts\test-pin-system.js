#!/usr/bin/env node

/**
 * Script para probar el sistema de pines completo
 * Ejecuta pruebas manuales del flujo end-to-end
 */

// Cargar variables de entorno desde .env
import { config } from 'dotenv';
config();

import PinRechargeRequestsAPI from '../src/api/endpoints/pinRechargeRequests.js';
import PinNotificationsAPI from '../src/api/endpoints/pinNotifications.js';
import { supabase } from '../src/api/supabaseClient.js';

// Configuración de prueba
const TEST_CONFIG = {
  psychologist_id: '74c8230e-6f01-4b5d-ae72-cf5ac61db33e',
  admin_id: 'admin-test-id',
  test_pins: 50
};

class PinSystemTester {
  constructor() {
    this.testResults = [];
    this.testRequestId = null;
    this.testNotificationId = null;
  }

  async runAllTests() {
    console.log('🧪 Iniciando pruebas del sistema de pines...\n');
    
    try {
      await this.testRechargeRequestFlow();
      await this.testNotificationFlow();
      await this.testIntegratedFlow();
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ Error fatal en las pruebas:', error);
      process.exit(1);
    }
  }

  async testRechargeRequestFlow() {
    console.log('📝 === PRUEBAS DE SOLICITUDES DE RECARGA ===\n');

    // Test 1: Crear solicitud
    await this.runTest('Crear solicitud de recarga', async () => {
      const result = await PinRechargeRequestsAPI.createRequest({
        psychologist_id: TEST_CONFIG.psychologist_id,
        requested_pins: TEST_CONFIG.test_pins,
        urgency: 'normal',
        reason: 'Prueba manual del sistema - Necesito pines para testing',
        metadata: { test: true, created_by: 'manual-test' }
      });

      if (!result.success) {
        throw new Error(result.error || 'Error creando solicitud');
      }

      this.testRequestId = result.data.id;
      console.log(`   📋 Solicitud creada: ${this.testRequestId}`);
      
      return result.data;
    });

    // Test 2: Obtener solicitudes pendientes
    await this.runTest('Obtener solicitudes pendientes', async () => {
      const result = await PinRechargeRequestsAPI.getRequests({
        status: 'pending',
        limit: 10
      });

      if (!result.success) {
        throw new Error(result.error || 'Error obteniendo solicitudes');
      }

      const ourRequest = result.data.find(req => req.id === this.testRequestId);
      if (!ourRequest) {
        throw new Error('Nuestra solicitud no aparece en la lista');
      }

      console.log(`   📋 Encontradas ${result.data.length} solicitudes pendientes`);
      return result.data;
    });

    // Test 3: Aprobar solicitud
    await this.runTest('Aprobar solicitud', async () => {
      const result = await PinRechargeRequestsAPI.processRequest(this.testRequestId, {
        action: 'approve',
        admin_id: TEST_CONFIG.admin_id,
        admin_notes: 'Aprobado por prueba manual',
        approved_pins: TEST_CONFIG.test_pins - 5 // Aprobar menos de lo solicitado
      });

      if (!result.success) {
        throw new Error(result.error || 'Error aprobando solicitud');
      }

      console.log(`   ✅ Solicitud aprobada: ${result.data.approved_pins} pines`);
      return result.data;
    });

    // Test 4: Verificar en base de datos
    await this.runTest('Verificar aprobación en BD', async () => {
      const { data: request, error } = await supabase
        .from('pin_recharge_requests')
        .select('*')
        .eq('id', this.testRequestId)
        .single();

      if (error) {
        throw new Error(`Error consultando BD: ${error.message}`);
      }

      if (request.status !== 'approved') {
        throw new Error(`Estado incorrecto: ${request.status}`);
      }

      console.log(`   🔍 Verificado en BD: Estado ${request.status}`);
      return request;
    });

    console.log('✅ Pruebas de solicitudes completadas\n');
  }

  async testNotificationFlow() {
    console.log('🔔 === PRUEBAS DE NOTIFICACIONES ===\n');

    // Test 1: Crear notificación
    await this.runTest('Crear notificación', async () => {
      const result = await PinNotificationsAPI.createNotification({
        user_id: TEST_CONFIG.psychologist_id,
        type: 'test_notification',
        title: 'Prueba Manual',
        message: 'Esta es una notificación de prueba manual del sistema',
        severity: 'info',
        metadata: { test: true, created_by: 'manual-test' }
      });

      if (!result.success) {
        throw new Error(result.error || 'Error creando notificación');
      }

      this.testNotificationId = result.data.id;
      console.log(`   🔔 Notificación creada: ${this.testNotificationId}`);
      
      return result.data;
    });

    // Test 2: Obtener notificaciones
    await this.runTest('Obtener notificaciones del usuario', async () => {
      const result = await PinNotificationsAPI.getUserNotifications(TEST_CONFIG.psychologist_id, {
        limit: 10
      });

      if (!result.success) {
        throw new Error(result.error || 'Error obteniendo notificaciones');
      }

      console.log(`   📋 Encontradas ${result.data.length} notificaciones, ${result.unread_count} no leídas`);
      return result.data;
    });

    // Test 3: Marcar como leída
    await this.runTest('Marcar notificación como leída', async () => {
      const result = await PinNotificationsAPI.markAsRead(this.testNotificationId, TEST_CONFIG.psychologist_id);

      if (!result.success) {
        throw new Error(result.error || 'Error marcando como leída');
      }

      console.log(`   👁️ Notificación marcada como leída`);
      return result.data;
    });

    // Test 4: Crear notificación de pines bajos
    await this.runTest('Crear notificación de pines bajos', async () => {
      const result = await PinNotificationsAPI.createLowPinNotification(TEST_CONFIG.psychologist_id, 2, 5);

      if (!result.success) {
        throw new Error(result.error || 'Error creando notificación de pines bajos');
      }

      console.log(`   ⚠️ Notificación de pines bajos creada`);
      return result.data;
    });

    console.log('✅ Pruebas de notificaciones completadas\n');
  }

  async testIntegratedFlow() {
    console.log('🔄 === PRUEBA DE FLUJO INTEGRADO ===\n');

    await this.runTest('Flujo completo: Solicitar → Aprobar → Verificar', async () => {
      // 1. Crear solicitud
      console.log('   1️⃣ Creando solicitud...');
      const requestResult = await PinRechargeRequestsAPI.createRequest({
        psychologist_id: TEST_CONFIG.psychologist_id,
        requested_pins: 25,
        urgency: 'high',
        reason: 'Prueba de flujo integrado completo'
      });

      if (!requestResult.success) {
        throw new Error('Error en paso 1: ' + requestResult.error);
      }

      const requestId = requestResult.data.id;
      console.log(`   📋 Solicitud creada: ${requestId}`);

      // 2. Aprobar solicitud
      console.log('   2️⃣ Aprobando solicitud...');
      const approveResult = await PinRechargeRequestsAPI.processRequest(requestId, {
        action: 'approve',
        admin_id: TEST_CONFIG.admin_id,
        approved_pins: 25
      });

      if (!approveResult.success) {
        throw new Error('Error en paso 2: ' + approveResult.error);
      }

      console.log(`   ✅ Solicitud aprobada: ${approveResult.data.approved_pins} pines`);

      // 3. Verificar persistencia
      console.log('   3️⃣ Verificando persistencia...');
      const { data: finalRequest, error } = await supabase
        .from('pin_recharge_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      if (error) {
        throw new Error('Error en paso 3: ' + error.message);
      }

      if (finalRequest.status !== 'approved') {
        throw new Error(`Estado incorrecto en BD: ${finalRequest.status}`);
      }

      console.log(`   🔍 Persistencia verificada: ${finalRequest.status}`);

      // 4. Limpiar datos de prueba
      await supabase
        .from('pin_recharge_requests')
        .delete()
        .eq('id', requestId);

      return { requestId, status: finalRequest.status };
    });

    console.log('✅ Prueba de flujo integrado completada\n');
  }

  async runTest(testName, testFunction) {
    const startTime = Date.now();
    
    try {
      console.log(`⏳ ${testName}...`);
      const result = await testFunction();
      const duration = Date.now() - startTime;
      
      this.testResults.push({
        name: testName,
        status: 'PASS',
        duration,
        result
      });
      
      console.log(`✅ ${testName} - PASS (${duration}ms)\n`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.testResults.push({
        name: testName,
        status: 'FAIL',
        duration,
        error: error.message
      });
      
      console.log(`❌ ${testName} - FAIL (${duration}ms)`);
      console.log(`   Error: ${error.message}\n`);
    }
  }

  printResults() {
    console.log('📊 === RESUMEN DE PRUEBAS ===\n');
    
    const passed = this.testResults.filter(t => t.status === 'PASS').length;
    const failed = this.testResults.filter(t => t.status === 'FAIL').length;
    const total = this.testResults.length;
    const totalTime = this.testResults.reduce((sum, t) => sum + t.duration, 0);

    console.log(`✅ Exitosas: ${passed}`);
    console.log(`❌ Fallidas: ${failed}`);
    console.log(`📝 Total: ${total}`);
    console.log(`⏱️ Tiempo total: ${totalTime}ms\n`);

    if (failed > 0) {
      console.log('❌ Pruebas fallidas:');
      this.testResults
        .filter(t => t.status === 'FAIL')
        .forEach(test => {
          console.log(`   • ${test.name}: ${test.error}`);
        });
      console.log();
    }

    // Limpiar datos de prueba
    this.cleanup();

    if (failed === 0) {
      console.log('🎉 ¡Todas las pruebas pasaron exitosamente!');
      console.log('El sistema de pines está funcionando correctamente.\n');
    } else {
      console.log('⚠️ Algunas pruebas fallaron. Revisa los errores arriba.\n');
      process.exit(1);
    }
  }

  async cleanup() {
    try {
      console.log('🧹 Limpiando datos de prueba...');
      
      // Limpiar solicitudes de prueba
      if (this.testRequestId) {
        await supabase
          .from('pin_recharge_requests')
          .delete()
          .eq('id', this.testRequestId);
      }

      // Limpiar notificaciones de prueba
      if (this.testNotificationId) {
        await supabase
          .from('pin_notifications')
          .delete()
          .eq('id', this.testNotificationId);
      }

      // Limpiar otros datos de prueba
      await supabase
        .from('pin_recharge_requests')
        .delete()
        .eq('psychologist_id', TEST_CONFIG.psychologist_id)
        .contains('metadata', { test: true });

      await supabase
        .from('pin_notifications')
        .delete()
        .eq('user_id', TEST_CONFIG.psychologist_id)
        .contains('metadata', { test: true });

      console.log('✅ Datos de prueba limpiados\n');
      
    } catch (error) {
      console.warn('⚠️ Error limpiando datos de prueba:', error.message);
    }
  }
}

// Ejecutar pruebas si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new PinSystemTester();
  tester.runAllTests().catch(console.error);
}

export default PinSystemTester;
