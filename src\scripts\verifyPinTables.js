import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function verifyPinTables() {
  console.log('🔍 Verificando tablas del sistema de pines...\n');

  const requiredTables = [
    'psychologist_usage_control',
    'pin_usage_logs',
    'pin_notifications',
    'pin_recharge_requests'
  ];

  for (const tableName of requiredTables) {
    try {
      console.log(`📋 Verificando tabla: ${tableName}`);
      
      const { data, error, count } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.log(`❌ Error: ${error.message}`);
        if (error.code === 'PGRST106') {
          console.log(`   La tabla '${tableName}' no existe`);
        }
      } else {
        console.log(`✅ Tabla '${tableName}' existe con ${count || 0} registros`);
      }
    } catch (err) {
      console.log(`❌ Error inesperado con '${tableName}': ${err.message}`);
    }
    console.log('');
  }

  // Verificar estructura de psychologist_usage_control
  console.log('🔍 Verificando estructura de psychologist_usage_control...');
  try {
    const { data, error } = await supabase
      .from('psychologist_usage_control')
      .select('*')
      .limit(1);

    if (!error && data) {
      if (data.length > 0) {
        console.log('📋 Columnas encontradas:', Object.keys(data[0]));
        console.log('📄 Ejemplo de registro:', data[0]);
      } else {
        console.log('⚠️ Tabla vacía, no se puede verificar estructura');
      }
    }
  } catch (err) {
    console.log('❌ Error verificando estructura:', err.message);
  }

  // Verificar si hay psicólogos con controles de pines
  console.log('\n👨‍⚕️ Verificando psicólogos con control de pines...');
  try {
    const { data: psychsWithPins, error: psychError } = await supabase
      .from('psicologos')
      .select(`
        id,
        nombre,
        apellido,
        email,
        psychologist_usage_control!left (
          total_uses,
          used_uses,
          is_unlimited,
          is_active
        )
      `)
      .limit(5);

    if (psychError) {
      console.log('❌ Error consultando psicólogos:', psychError.message);
    } else {
      console.log(`📊 Psicólogos encontrados: ${psychsWithPins?.length || 0}`);
      psychsWithPins?.forEach((psy, index) => {
        const hasControl = psy.psychologist_usage_control && psy.psychologist_usage_control.length > 0;
        const control = hasControl ? psy.psychologist_usage_control[0] : null;
        
        console.log(`${index + 1}. ${psy.nombre} ${psy.apellido}`);
        console.log(`   Email: ${psy.email || 'Sin email'}`);
        console.log(`   Control de pines: ${hasControl ? '✅' : '❌'}`);
        if (hasControl && control) {
          console.log(`   Pines: ${control.total_uses || 0} total, ${control.used_uses || 0} usados`);
          console.log(`   Ilimitado: ${control.is_unlimited ? 'Sí' : 'No'}`);
          console.log(`   Activo: ${control.is_active ? 'Sí' : 'No'}`);
        }
        console.log('');
      });
    }
  } catch (err) {
    console.log('❌ Error verificando psicólogos:', err.message);
  }
}

verifyPinTables();
