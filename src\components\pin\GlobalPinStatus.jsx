import React, { useState, useEffect } from 'react';
import { FaCoins, FaExclamationTriangle, FaInfinity, FaSpinner, FaPlus } from 'react-icons/fa';
import { useAdvancedPinValidation } from '../../hooks/useAdvancedPinValidation';
import PinNotificationCenter from './PinNotificationCenter';
import PinRechargePrompt from './PinRechargePrompt';
import { PIN_CONSTANTS } from '../../services/pin/PinConstants';
import RechargeRequestService from '../../services/pin/RechargeRequestService';
import { toast } from 'react-toastify';

/**
 * Componente global para mostrar el estado de pines en la barra de navegación
 * Incluye indicador visual, notificaciones y acceso rápido a recarga
 */
const GlobalPinStatus = ({ 
  psychologistId, 
  className = '',
  showNotifications = true,
  showRechargePrompt = true 
}) => {
  const [showRechargeModal, setShowRechargeModal] = useState(false);
  const [isRequestingRecharge, setIsRequestingRecharge] = useState(false);

  const {
    validationResult,
    isValidating,
    canProceed,
    isBlocked,
    validateSingleReport
  } = useAdvancedPinValidation(psychologistId, {
    showToastAlerts: false,
    autoValidate: true
  });

  // Refrescar estado cada 5 minutos
  useEffect(() => {
    if (psychologistId) {
      const interval = setInterval(() => {
        validateSingleReport(1);
      }, 300000); // 5 minutos

      return () => clearInterval(interval);
    }
  }, [psychologistId, validateSingleReport]);

  // Función para solicitar recarga
  const handleRequestRecharge = async () => {
    if (isRequestingRecharge) return;

    try {
      setIsRequestingRecharge(true);

      const requestData = {
        requestedPins: remainingPins <= 5 ? 50 : 25, // Sugerir más pines si están muy bajos
        urgency: remainingPins === 0 ? 'urgent' : remainingPins <= 5 ? 'high' : 'normal',
        reason: remainingPins === 0
          ? 'Sin pines disponibles - Necesito generar informes urgentemente'
          : remainingPins <= 5
          ? 'Pines muy bajos - Necesito recarga para continuar con mis pacientes'
          : 'Solicito recarga de pines para mis próximos pacientes'
      };

      await RechargeRequestService.createRechargeRequest(psychologistId, requestData);

      toast.success('Solicitud de recarga enviada exitosamente. Será revisada por un administrador.');

    } catch (error) {
      console.error('Error requesting recharge:', error);
      toast.error('Error al enviar la solicitud de recarga. Intente nuevamente.');
    } finally {
      setIsRequestingRecharge(false);
    }
  };

  if (!psychologistId || isValidating) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <FaSpinner className="h-4 w-4 text-gray-400 animate-spin" />
        <span className="text-sm text-gray-500">Cargando...</span>
      </div>
    );
  }

  if (!validationResult) {
    return null;
  }

  const { remainingPins, isUnlimited } = validationResult;

  // Determinar el estado visual
  const getStatusConfig = () => {
    if (isUnlimited) {
      return {
        icon: FaInfinity,
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        borderColor: 'border-green-200',
        text: 'Ilimitado',
        severity: 'success'
      };
    }

    if (remainingPins === 0) {
      return {
        icon: FaExclamationTriangle,
        color: 'text-red-600',
        bgColor: 'bg-red-100',
        borderColor: 'border-red-200',
        text: '0 pines',
        severity: 'error'
      };
    }

    if (remainingPins <= PIN_CONSTANTS.THRESHOLDS.CRITICAL_PIN_WARNING) {
      return {
        icon: FaExclamationTriangle,
        color: 'text-red-600',
        bgColor: 'bg-red-100',
        borderColor: 'border-red-200',
        text: `${remainingPins} pines`,
        severity: 'critical'
      };
    }

    if (remainingPins <= PIN_CONSTANTS.THRESHOLDS.LOW_PIN_WARNING) {
      return {
        icon: FaExclamationTriangle,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100',
        borderColor: 'border-yellow-200',
        text: `${remainingPins} pines`,
        severity: 'warning'
      };
    }

    return {
      icon: FaCoins,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      borderColor: 'border-blue-200',
      text: `${remainingPins} pines`,
      severity: 'normal'
    };
  };

  const statusConfig = getStatusConfig();
  const IconComponent = statusConfig.icon;

  const handleStatusClick = () => {
    if (isBlocked && showRechargePrompt) {
      setShowRechargeModal(true);
    }
  };

  return (
    <>
      <div className={`flex items-center space-x-3 ${className}`}>
        {/* Indicador de estado de pines */}
        <div
          className={`flex items-center space-x-2 px-3 py-1.5 rounded-full border cursor-pointer transition-all hover:shadow-sm ${statusConfig.bgColor} ${statusConfig.borderColor}`}
          onClick={handleStatusClick}
          title={
            isUnlimited 
              ? 'Plan ilimitado activo'
              : isBlocked
                ? 'Sin pines disponibles - Click para recargar'
                : `${remainingPins} pines disponibles`
          }
        >
          <IconComponent className={`h-4 w-4 ${statusConfig.color}`} />
          <span className={`text-sm font-medium ${statusConfig.color}`}>
            {statusConfig.text}
          </span>
          
          {/* Indicador de estado crítico */}
          {statusConfig.severity === 'error' && (
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
          )}
          {statusConfig.severity === 'critical' && (
            <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" />
          )}
        </div>

        {/* Centro de notificaciones */}
        {showNotifications && (
          <PinNotificationCenter
            psychologistId={psychologistId}
            className="relative"
          />
        )}

        {/* Botón de solicitar recarga */}
        {(remainingPins <= PIN_CONSTANTS.THRESHOLDS.LOW_PIN_WARNING || remainingPins === 0) && (
          <button
            onClick={handleRequestRecharge}
            disabled={isRequestingRecharge}
            className={`flex items-center space-x-1 px-3 py-1.5 rounded-full text-xs font-medium transition-all ${
              isRequestingRecharge
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : remainingPins === 0
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-yellow-600 hover:bg-yellow-700 text-white'
            }`}
            title="Solicitar recarga de pines"
          >
            {isRequestingRecharge ? (
              <FaSpinner className="h-3 w-3 animate-spin" />
            ) : (
              <FaPlus className="h-3 w-3" />
            )}
            <span>
              {isRequestingRecharge ? 'Enviando...' : 'Solicitar'}
            </span>
          </button>
        )}
      </div>

      {/* Modal de recarga */}
      {showRechargeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="max-w-2xl w-full max-h-screen overflow-y-auto">
            <PinRechargePrompt
              psychologistId={psychologistId}
              currentPins={remainingPins || 0}
              requiredPins={1}
              onClose={() => setShowRechargeModal(false)}
              variant="modal"
            />
          </div>
        </div>
      )}
    </>
  );
};

/**
 * Versión compacta para espacios reducidos
 */
export const CompactGlobalPinStatus = (props) => {
  const {
    validationResult,
    isValidating
  } = useAdvancedPinValidation(props.psychologistId, {
    showToastAlerts: false,
    autoValidate: true
  });

  if (!props.psychologistId || isValidating || !validationResult) {
    return (
      <div className="flex items-center">
        <FaSpinner className="h-3 w-3 text-gray-400 animate-spin" />
      </div>
    );
  }

  const { remainingPins, isUnlimited } = validationResult;

  if (isUnlimited) {
    return (
      <div className="flex items-center text-green-600" title="Plan ilimitado">
        <FaInfinity className="h-3 w-3" />
      </div>
    );
  }

  const isLow = remainingPins <= PIN_CONSTANTS.THRESHOLDS.LOW_PIN_WARNING;
  const isCritical = remainingPins <= PIN_CONSTANTS.THRESHOLDS.CRITICAL_PIN_WARNING;

  return (
    <div 
      className={`flex items-center space-x-1 ${
        isCritical ? 'text-red-600' : isLow ? 'text-yellow-600' : 'text-blue-600'
      }`}
      title={`${remainingPins} pines disponibles`}
    >
      <FaCoins className="h-3 w-3" />
      <span className="text-xs font-medium">{remainingPins}</span>
      {isCritical && <div className="w-1 h-1 bg-red-500 rounded-full animate-pulse" />}
    </div>
  );
};

/**
 * Badge simple para mostrar solo el número
 */
export const PinCountBadge = ({ psychologistId, className = '' }) => {
  const {
    validationResult,
    isValidating
  } = useAdvancedPinValidation(psychologistId, {
    showToastAlerts: false,
    autoValidate: true
  });

  if (!psychologistId || isValidating || !validationResult) {
    return null;
  }

  const { remainingPins, isUnlimited } = validationResult;

  if (isUnlimited) {
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 ${className}`}>
        <FaInfinity className="mr-1 h-3 w-3" />
        Ilimitado
      </span>
    );
  }

  const isLow = remainingPins <= PIN_CONSTANTS.THRESHOLDS.LOW_PIN_WARNING;
  const isCritical = remainingPins <= PIN_CONSTANTS.THRESHOLDS.CRITICAL_PIN_WARNING;

  const badgeClass = isCritical 
    ? 'bg-red-100 text-red-800'
    : isLow 
      ? 'bg-yellow-100 text-yellow-800'
      : 'bg-blue-100 text-blue-800';

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${badgeClass} ${className}`}>
      <FaCoins className="mr-1 h-3 w-3" />
      {remainingPins} pines
    </span>
  );
};

export default GlobalPinStatus;
