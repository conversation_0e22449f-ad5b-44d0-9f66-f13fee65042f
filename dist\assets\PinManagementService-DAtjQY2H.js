var e=Object.defineProperty,s=Object.defineProperties,t=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,r=(s,t,i)=>t in s?e(s,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[t]=i,a=(e,s)=>{for(var t in s||(s={}))o.call(s,t)&&r(e,t,s[t]);if(i)for(var t of i(s))n.call(s,t)&&r(e,t,s[t]);return e},l=(e,s,t)=>new Promise((i,o)=>{var n=e=>{try{a(t.next(e))}catch(s){o(s)}},r=e=>{try{a(t.throw(e))}catch(s){o(s)}},a=e=>e.done?i(e.value):Promise.resolve(e.value).then(n,r);a((t=t.apply(e,s)).next())});import{s as c}from"./index-Bdl1jgS_.js";import{Q as d}from"./vendor-BqMjyOVw.js";const g=new class{getPsychologistsWithPinStats(){return l(this,null,function*(){try{const{data:e,error:s}=yield c.rpc("get_all_psychologists_pin_balance");if(s)throw s;const{data:t,error:i}=yield c.from("psychologist_usage_control").select("psychologist_id").eq("is_active",!0),o=new Set((null==t?void 0:t.map(e=>e.psychologist_id))||[]);return(e||[]).filter(e=>{const s=parseInt(e.total_asignado)>0,t=o.has(e.psych_id);return s||t}).map(e=>({psicologo_id:e.psych_id,nombre_psicologo:e.psych_name,email_psicologo:e.psych_email,total_asignado:parseInt(e.total_asignado)||0,total_consumido:parseInt(e.total_consumido)||0,pines_restantes:parseInt(e.pines_disponibles)||0,ultima_transaccion:e.ultima_transaccion,pacientes_asignados:parseInt(e.pacientes_asignados)||0,tests_completados:parseInt(e.tests_completados)||0,status:this._determineStatus(parseInt(e.pines_disponibles)||0)}))}catch(e){throw e}})}getAllAvailablePsychologists(){return l(this,null,function*(){try{const{data:e,error:s}=yield c.from("psicologos").select("id, nombre, apellido, email").order("nombre",{ascending:!0});if(s)throw s;return(e||[]).map(e=>({id:e.id,name:`${e.nombre} ${e.apellido}`.trim(),email:e.email||"Sin email",fullName:`${e.nombre} ${e.apellido}`.trim()}))}catch(e){throw e}})}assignPins(e,s,t="Asignación manual"){return l(this,null,function*(){if(!e||!s||s<=0){const e="Se requiere ID del psicólogo y una cantidad positiva de pines.";throw d.error(e),new Error(e)}try{const{data:i,error:o}=yield c.from("psicologos").select("id, nombre, apellido, email").eq("id",e).single();if(o||!i){const e="Psicólogo no encontrado.";throw d.error(e),new Error(e)}const n={psicologo_id:e,cantidad:s,tipo:"asignacion",motivo:t,metadata:{psychologist_name:`${i.nombre} ${i.apellido}`,psychologist_email:i.email,assigned_at:(new Date).toISOString(),method:"simple_service"}},{data:r,error:a}=yield c.from("pines_transacciones").insert(n).select().single();if(a)throw d.error("No se pudo completar la asignación de pines."),a;return d.success(`Se asignaron ${s} pines a ${i.nombre} ${i.apellido} correctamente.`),r}catch(i){throw i}})}getPsychologistBalance(e){return l(this,null,function*(){try{const{data:s,error:t}=yield c.rpc("get_psychologist_pin_balance",{p_psicologo_id:e});if(t)throw t;return s&&0!==s.length?s[0]:{psicologo_id:e,total_asignado:0,total_consumido:0,pines_disponibles:0,ultima_transaccion:null}}catch(s){throw s}})}getTransactionHistory(e=null,s=50){return l(this,null,function*(){try{let t=c.from("pines_transacciones").select("\n          *,\n          psicologos (\n            nombre,\n            apellido,\n            email\n          )\n        ").order("created_at",{ascending:!1}).limit(s);e&&(t=t.eq("psicologo_id",e));const{data:i,error:o}=yield t;if(o)throw o;return i||[]}catch(t){throw t}})}getSystemStats(){return l(this,null,function*(){try{const e=yield this.getPsychologistsWithPinStats();return{total_psychologists:e.length,total_pins_assigned:e.reduce((e,s)=>e+s.total_asignado,0),total_pins_consumed:e.reduce((e,s)=>e+s.total_consumido,0),total_pins_available:e.reduce((e,s)=>e+s.pines_restantes,0),psychologists_with_pins:e.filter(e=>e.pines_restantes>0).length,psychologists_without_pins:e.filter(e=>0===e.pines_restantes).length,total_patients:e.reduce((e,s)=>e+s.pacientes_asignados,0),total_tests:e.reduce((e,s)=>e+s.tests_completados,0)}}catch(e){throw e}})}consumePin(e){return l(this,arguments,function*(e,i="Consumo automático",o={}){try{const l=yield this.getPsychologistBalance(e);if(l.pines_disponibles<=0){const e="El psicólogo no tiene pines disponibles.";throw d.error(e),new Error(e)}const g={psicologo_id:e,cantidad:-1,tipo:"consumo",motivo:i,metadata:(n=a({},o),r={consumed_at:(new Date).toISOString(),remaining_pins_before:l.pines_disponibles,remaining_pins_after:l.pines_disponibles-1},s(n,t(r)))},{data:u,error:p}=yield c.from("pines_transacciones").insert(g).select().single();if(p)throw p;return u}catch(l){throw l}var n,r})}_determineStatus(e){return 0===e?"sin_pines":e<=5?"pocos_pines":"activo"}deleteTransaction(e){return l(this,null,function*(){try{const{data:s,error:t}=yield c.from("pines_transacciones").select("psicologo_id, cantidad, tipo").eq("id",e).single();if(t)throw t;const{data:i,error:o}=yield c.from("pines_transacciones").delete().eq("id",e).select().single();if(o)throw o;return yield this._refreshPsychologistMetrics(s.psicologo_id),{success:!0,data:i,affectedPsychologist:s.psicologo_id}}catch(s){return{success:!1,message:s.message}}})}deleteMultipleTransactions(e){return l(this,null,function*(){try{if(!e||0===e.length)throw new Error("No se proporcionaron IDs para eliminar");const{data:s,error:t}=yield c.from("pines_transacciones").select("psicologo_id, cantidad, tipo").in("id",e);if(t)throw t;const i=[...new Set(s.map(e=>e.psicologo_id))],{data:o,error:n}=yield c.from("pines_transacciones").delete().in("id",e).select();if(n)throw n;for(const e of i)yield this._refreshPsychologistMetrics(e);return{success:!0,deletedCount:o.length,data:o,affectedPsychologists:i}}catch(s){return{success:!1,message:s.message}}})}removePinsFromPsychologist(e,s,t="Eliminación manual de pines"){return l(this,null,function*(){try{if(!e||!s||s<=0)throw new Error("Se requiere ID del psicólogo y una cantidad positiva de pines.");const{data:i,error:o}=yield c.from("psicologos").select("id, nombre, apellido, email").eq("id",e).single();if(o||!i)throw new Error("Psicólogo no encontrado.");const n=yield this.getPsychologistBalance(e);if(n.pines_disponibles<s)throw new Error(`No se pueden eliminar ${s} pines. Solo hay ${n.pines_disponibles} disponibles.`);const r={psicologo_id:e,cantidad:-s,tipo:"eliminacion",motivo:t,metadata:{psychologist_name:`${i.nombre} ${i.apellido}`,psychologist_email:i.email,removed_at:(new Date).toISOString(),method:"manual_removal",original_amount:s}},{data:a,error:l}=yield c.from("pines_transacciones").insert([r]).select().single();if(l)throw l;return yield this._refreshPsychologistMetrics(e),{success:!0,data:a,removedAmount:s}}catch(i){return d.error(i.message),{success:!1,message:i.message}}})}removePsychologistPinAssignment(e,s="Eliminación completa de asignación"){return l(this,null,function*(){try{const{data:s,error:t}=yield c.from("psicologos").select("id, nombre, apellido, email").eq("id",e).single();if(t||!s)throw new Error("Psicólogo no encontrado.");const{data:i,error:o}=yield c.from("pines_transacciones").select("id, cantidad, tipo").eq("psicologo_id",e);if(o)throw o;if(!i||0===i.length){const{data:t,error:i}=yield c.from("psychologist_usage_control").update({is_active:!1,updated_at:(new Date).toISOString(),total_uses:0,used_uses:0,remaining_uses:0}).eq("psychologist_id",e).eq("is_active",!0).select();return yield this._refreshPsychologistMetrics(e),{success:!0,data:[],deletedTransactions:0,deletedUsageControl:(null==t?void 0:t.length)||0,psychologist:`${s.nombre} ${s.apellido}`,psychologist_name:`${s.nombre} ${s.apellido}`,message:(null==t?void 0:t.length)>0?"Psicólogo removido del sistema de control de pines":"Psicólogo ya estaba fuera del sistema"}}const n=i.map(e=>e.id),{data:r,error:a}=yield c.from("pines_transacciones").delete().in("id",n).select();if(a)throw a;const{data:l,error:d}=yield c.from("psychologist_usage_control").update({is_active:!1,updated_at:(new Date).toISOString(),total_uses:0,used_uses:0,remaining_uses:0}).eq("psychologist_id",e).eq("is_active",!0).select();return yield this._refreshPsychologistMetrics(e),{success:!0,data:r,deletedTransactions:r.length,deletedUsageControl:(null==l?void 0:l.length)||0,psychologist:`${s.nombre} ${s.apellido}`,psychologist_name:`${s.nombre} ${s.apellido}`,message:`Psicólogo removido completamente del sistema de pines. ${r.length} transacciones eliminadas.`}}catch(s){return d.error(s.message),{success:!1,message:s.message}}})}removeMultiplePsychologistAssignments(e,s="Eliminación múltiple de asignaciones"){return l(this,null,function*(){try{if(!e||0===e.length)throw new Error("No se proporcionaron IDs de psicólogos para eliminar");const i=[];let o=0,n=0,r=0,a=0;for(const l of e){try{const e=yield this.removePsychologistPinAssignment(l,s);e.success?(o++,r+=e.deletedTransactions,i.push({psychologistId:l,success:!0,deletedTransactions:e.deletedTransactions,psychologist:e.psychologist,psychologist_name:e.psychologist_name})):(n++,i.push({psychologistId:l,success:!1,error:e.message}))}catch(t){n++,i.push({psychologistId:l,success:!1,error:t.message})}yield new Promise(e=>setTimeout(e,500))}return{success:o>0,message:`Procesados ${e.length} psicólogos: ${o} exitosos, ${n} errores`,successCount:o,errorCount:n,totalDeletedTransactions:r,totalDeletedUsageControl:a,results:i}}catch(t){return d.error(t.message),{success:!1,message:t.message}}})}reactivatePsychologistInPinSystem(e,s=0,t=!1,i="Reactivación en sistema de pines"){return l(this,null,function*(){try{const{data:o,error:n}=yield c.from("psicologos").select("id, nombre, apellido, email").eq("id",e).single();if(n||!o)throw new Error(`Psicólogo no encontrado: ${(null==n?void 0:n.message)||"ID inválido"}`);const{data:r,error:a}=yield c.from("psychologist_usage_control").select("*").eq("psychologist_id",e).eq("is_active",!0).single();if(!a&&r)return{success:!0,message:"El psicólogo ya está activo en el sistema de pines",psychologist:`${o.nombre} ${o.apellido}`,alreadyActive:!0};const{data:l,error:d}=yield c.from("psychologist_usage_control").upsert({psychologist_id:e,total_uses:t?999999:s,used_uses:0,remaining_uses:t?999999:s,is_unlimited:t,plan_type:t?"unlimited":"assigned",is_active:!0,updated_at:(new Date).toISOString()},{onConflict:"psychologist_id",ignoreDuplicates:!1}).select().single();if(d)throw d;if(s>0){const t={psicologo_id:e,cantidad:s,tipo:"asignacion",motivo:i,metadata:{psychologist_name:`${o.nombre} ${o.apellido}`,psychologist_email:o.email,reactivation:!0,assigned_at:(new Date).toISOString()}},{error:n}=yield c.from("pines_transacciones").insert([t]);if(n)throw n}return yield this._refreshPsychologistMetrics(e),{success:!0,data:l,psychologist:`${o.nombre} ${o.apellido}`,psychologist_name:`${o.nombre} ${o.apellido}`,initialPins:s,isUnlimited:t,message:`Psicólogo reactivado en el sistema de pines${s>0?` con ${s} pines`:""}${t?" (plan ilimitado)":""}`}}catch(o){return d.error(o.message),{success:!1,message:o.message}}})}clearAllHistory(){return l(this,null,function*(){try{const{data:e,error:s}=yield c.from("pines_transacciones").select("id, psicologo_id");if(s)throw s;if(!e||0===e.length)return{success:!0,deletedCount:0,message:"No había transacciones para eliminar"};const t=[...new Set(e.map(e=>e.psicologo_id))],i=e.map(e=>e.id),{data:o,error:n}=yield c.from("pines_transacciones").delete().in("id",i).select();if(n)throw n;for(const r of t)yield this._refreshPsychologistMetrics(r);return{success:!0,deletedCount:o.length,data:o,affectedPsychologists:t,message:`Historial limpiado: ${o.length} transacciones eliminadas`}}catch(e){return d.error(e.message),{success:!1,message:e.message}}})}_refreshPsychologistMetrics(e){return l(this,null,function*(){try{const{data:s,error:t}=yield c.rpc("get_psychologist_pin_balance",{p_psicologo_id:e});if(t)return;return s}catch(s){}})}};const u=new class{getPsychologistsWithPinStats(){return l(this,null,function*(){return yield g.getPsychologistsWithPinStats()})}getAllAvailablePsychologists(){return l(this,null,function*(){return yield g.getAllAvailablePsychologists()})}assignPins(e,s,t="Asignación manual"){return l(this,null,function*(){return yield g.assignPins(e,s,t)})}getPsychologistBalance(e){return l(this,null,function*(){return yield g.getPsychologistBalance(e)})}getTransactionHistory(e=null,s=50){return l(this,null,function*(){return yield g.getTransactionHistory(e,s)})}deleteTransaction(e){return l(this,null,function*(){return yield g.deleteTransaction(e)})}deleteMultipleTransactions(e){return l(this,null,function*(){return yield g.deleteMultipleTransactions(e)})}getSystemStats(){return l(this,null,function*(){return yield g.getSystemStats()})}consumePin(e){return l(this,arguments,function*(e,s="Consumo automático",t={}){return yield g.consumePin(e,s,t)})}removePinsFromPsychologist(e,s,t="Eliminación manual de pines"){return l(this,null,function*(){return yield g.removePinsFromPsychologist(e,s,t)})}removePsychologistPinAssignment(e,s="Eliminación completa de asignación"){return l(this,null,function*(){return yield g.removePsychologistPinAssignment(e,s)})}removeMultiplePsychologistAssignments(e,s="Eliminación masiva de asignaciones"){return l(this,null,function*(){return yield g.removeMultiplePsychologistAssignments(e,s)})}clearAllHistory(){return l(this,null,function*(){return yield g.clearAllHistory()})}reactivatePsychologistInPinSystem(e,s=0,t=!1,i="Reactivación en sistema de pines"){return l(this,null,function*(){return yield g.reactivatePsychologistInPinSystem(e,s,t,i)})}};export{u as default};
