import React, { createContext, useState, useContext, useEffect } from 'react';
import supabase from '../api/supabaseClient';

/**
 * Contexto de autenticación temporal simplificado para debugging
 */
const SimpleAuthContextTemp = createContext();

export const SimpleAuthProviderTemp = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getSessionAndProfile = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();

        if (session?.user) {
          const { data: profile, error: profileError } = await supabase
            .from('usuarios')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (!profileError && profile) {
            const userWithProfile = {
              ...session.user,
              ...profile,
              tipo_usuario: profile.rol || profile.tipo_usuario || 'candidato'
            };
            setUser(userWithProfile);
          }
        }
      } catch (error) {
        console.error('Error obteniendo sesión:', error);
      } finally {
        setLoading(false);
      }
    };

    getSessionAndProfile();

    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        if (session?.user) {
          const { data: profile } = await supabase
            .from('usuarios')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (profile) {
            const userWithProfile = {
              ...session.user,
              ...profile,
              tipo_usuario: profile.rol || profile.tipo_usuario || 'candidato'
            };
            setUser(userWithProfile);
          }
        } else {
          setUser(null);
        }
        setLoading(false);
      }
    );

    return () => {
      if (authListener?.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, []);

  const login = async ({ identifier, password }) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({
        email: identifier,
        password
      });

      if (error) throw error;

      const { data: profile } = await supabase
        .from('usuarios')
        .select('*')
        .eq('id', data.user.id)
        .single();

      if (profile) {
        const userWithProfile = {
          ...data.user,
          ...profile,
          tipo_usuario: profile.rol || profile.tipo_usuario || 'candidato'
        };
        setUser(userWithProfile);
      }

      return { success: true };
    } catch (error) {
      console.error('Error en login:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      return { success: true };
    } catch (error) {
      console.error('Error en logout:', error);
      return { success: false, error: error.message };
    }
  };

  const value = {
    user,
    loading,
    error: null,
    login,
    logout,
    isAuthenticated: !!user,
    userRole: user?.tipo_usuario || null,
    isAdmin: user?.tipo_usuario === 'administrador',
    isPsicologo: user?.tipo_usuario === 'psicologo',
    isCandidato: user?.tipo_usuario === 'candidato',
    
    // Compatibility functions
    setUserType: () => console.warn('setUserType no disponible en modo temporal'),
    userName: user ? `${user.nombre || ''} ${user.apellido || ''}`.trim() : '',
    userFirstName: user?.nombre,
    userLastName: user?.apellido,
    sessionCreated: user?.fecha_creacion,
    lastAccess: user?.ultimo_acceso
  };

  return (
    <SimpleAuthContextTemp.Provider value={value}>
      {children}
    </SimpleAuthContextTemp.Provider>
  );
};

export const useSimpleAuthTemp = () => {
  const context = useContext(SimpleAuthContextTemp);
  if (!context) {
    throw new Error('useSimpleAuthTemp debe ser usado dentro de SimpleAuthProviderTemp');
  }
  return context;
};

// Alias para compatibilidad
export const useAuth = useSimpleAuthTemp;

export default SimpleAuthContextTemp;
