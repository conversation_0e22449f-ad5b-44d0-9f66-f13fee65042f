import React, { createContext, useState, useContext } from 'react';

/**
 * Contexto de autenticación temporal simplificado para debugging
 */
const SimpleAuthContextTemp = createContext();

export const SimpleAuthProviderTemp = ({ children }) => {
  const [user, setUser] = useState({
    id: '4116c661-bd7d-4af7-8117-966a00d63152',
    email: '<EMAIL>',
    nombre: 'Henry',
    apellido: 'Rueda',
    rol: 'administrador',
    tipo_usuario: 'administrador'
  });
  const [loading, setLoading] = useState(false);

  const login = async ({ email, password }) => {
    console.log('🔐 Login simulado con:', email);
    return { success: true };
  };

  const logout = async () => {
    console.log('🚪 Logout simulado');
    setUser(null);
    return { success: true };
  };

  const value = {
    user,
    loading,
    error: null,
    login,
    logout,
    isAuthenticated: !!user,
    userRole: user?.tipo_usuario || null,
    isAdmin: user?.tipo_usuario === 'administrador',
    isPsicologo: user?.tipo_usuario === 'psicologo',
    isCandidato: user?.tipo_usuario === 'candidato',
    
    // Compatibility functions
    setUserType: () => console.warn('setUserType no disponible en modo temporal'),
    userName: user ? `${user.nombre || ''} ${user.apellido || ''}`.trim() : '',
    userFirstName: user?.nombre,
    userLastName: user?.apellido,
    sessionCreated: user?.fecha_creacion,
    lastAccess: user?.ultimo_acceso
  };

  return (
    <SimpleAuthContextTemp.Provider value={value}>
      {children}
    </SimpleAuthContextTemp.Provider>
  );
};

export const useSimpleAuthTemp = () => {
  const context = useContext(SimpleAuthContextTemp);
  if (!context) {
    throw new Error('useSimpleAuthTemp debe ser usado dentro de SimpleAuthProviderTemp');
  }
  return context;
};

// Alias para compatibilidad
export const useAuth = useSimpleAuthTemp;

export default SimpleAuthContextTemp;
