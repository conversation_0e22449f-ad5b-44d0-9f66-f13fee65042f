import React, { useState } from 'react';
import InformeViewer from './InformeViewer';
import { FaFileAlt, FaEye, FaPrint } from 'react-icons/fa';

/**
 * Componente de prueba para verificar que la impresión del informe funcione correctamente
 * Incluye datos de ejemplo para probar la funcionalidad
 */
const TestInformePrint = () => {
  const [mostrarInforme, setMostrarInforme] = useState(false);

  // Datos de ejemplo para probar el informe
  const informeEjemplo = {
    id: 'test-001',
    titulo: 'Informe de Prueba - Impresión',
    tipo: 'evaluacion_individual',
    fechaGeneracion: new Date().toISOString(),
    paciente: {
      id: 'pac-test-001',
      nombre: 'Julieta',
      apellido: 'Hernández',
      documento: '1095826777',
      genero: 'Fe<PERSON><PERSON>',
      fecha_nacimiento: '2012-07-27',
      email: '<EMAIL>',
      telefono: '+57 ************'
    },
    estadisticas: {
      total_tests: 3,
      percentil_promedio: 51.7,
      aptitudes_altas: 0,
      aptitudes_bajas: 1
    },
    resultados: [
      {
        id: 'res-001',
        aptitud: { codigo: 'V', nombre: 'Aptitud Verbal' },
        puntaje_directo: 45,
        percentil: 52,
        tiempo_segundos: 1200,
        errores: 8,
        concentracion: 85.5,
        interpretacion: 'Medio'
      },
      {
        id: 'res-002',
        aptitud: { codigo: 'N', nombre: 'Aptitud Numérica' },
        puntaje_directo: 38,
        percentil: 30,
        tiempo_segundos: 900,
        errores: 12,
        concentracion: 78.0,
        interpretacion: 'Medio-Bajo'
      },
      {
        id: 'res-003',
        aptitud: { codigo: 'A', nombre: 'Atención y Concentración' },
        puntaje_directo: 72,
        percentil: 73,
        tiempo_segundos: 600,
        errores: 3,
        concentracion: 94.2,
        interpretacion: 'Medio-Alto'
      }
    ],
    evaluacion: {
      id: 'eval-test-001',
      fecha_evaluacion: '2025-08-20T10:30:00Z',
      tipo_evaluacion: 'BAT-7',
      estado: 'completada',
      tiempo_total: 2700, // 45 minutos
      observaciones: 'Evaluación de prueba para verificar la funcionalidad de impresión del informe.'
    },
    metadatos: {
      version: '1.0',
      generado_por: 'Sistema de Pruebas',
      configuracion_impresion: 'optimizada'
    }
  };

  const abrirInforme = () => {
    setMostrarInforme(true);
  };

  const cerrarInforme = () => {
    setMostrarInforme(false);
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="text-center mb-8">
          <FaPrint className="text-6xl text-blue-600 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            🧪 Prueba de Impresión de Informes
          </h1>
          <p className="text-gray-600 text-lg">
            Componente de prueba para verificar que la impresión y generación de PDF 
            mantenga el diseño y los datos del paciente correctamente
          </p>
        </div>

        {/* Información de la prueba */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-bold text-blue-800 mb-3">
            📋 Datos de Prueba
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Paciente:</strong> {informeEjemplo.paciente.nombre} {informeEjemplo.paciente.apellido}
            </div>
            <div>
              <strong>Documento:</strong> {informeEjemplo.paciente.documento}
            </div>
            <div>
              <strong>Género:</strong> {informeEjemplo.paciente.genero}
            </div>
            <div>
              <strong>Edad:</strong> 13 años
            </div>
            <div>
              <strong>Tests:</strong> {informeEjemplo.estadisticas.total_tests}
            </div>
            <div>
              <strong>Percentil Promedio:</strong> {informeEjemplo.estadisticas.percentil_promedio}
            </div>
          </div>
        </div>

        {/* Instrucciones */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-bold text-yellow-800 mb-3">
            📝 Instrucciones de Prueba
          </h3>
          <ol className="list-decimal list-inside space-y-2 text-sm text-yellow-700">
            <li>Haz clic en "Ver Informe de Prueba" para abrir el informe</li>
            <li>Una vez abierto, haz clic en el botón "PDF" o "Imprimir"</li>
            <li>Selecciona "Guardar como PDF" en el diálogo de impresión</li>
            <li>Verifica que el PDF mantenga:</li>
            <ul className="list-disc list-inside ml-6 mt-2 space-y-1">
              <li>Los datos del paciente (nombre, documento, género, etc.)</li>
              <li>Las estadísticas del resumen general</li>
              <li>Los gráficos y elementos visuales</li>
              <li>El formato y diseño original</li>
            </ul>
          </ol>
        </div>

        {/* Botón de prueba */}
        <div className="text-center">
          <button
            onClick={abrirInforme}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold flex items-center gap-3 hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg mx-auto"
          >
            <FaEye className="text-xl" />
            Ver Informe de Prueba
          </button>
          <p className="text-gray-500 text-sm mt-4">
            * Este informe contiene datos de ejemplo para pruebas
          </p>
        </div>
      </div>

      {/* Mostrar el informe usando un mock del servicio */}
      {mostrarInforme && (
        <MockInformeViewer
          informe={informeEjemplo}
          onClose={cerrarInforme}
        />
      )}
    </div>
  );
};

/**
 * Componente mock que simula el InformeViewer con datos predefinidos
 */
const MockInformeViewer = ({ informe, onClose }) => {
  // Simular el comportamiento del InformeViewer pero con datos mock
  return (
    <InformeViewer
      informeId="mock-test-001"
      onClose={onClose}
      // Inyectar datos mock directamente
      mockData={informe}
    />
  );
};

export default TestInformePrint;
