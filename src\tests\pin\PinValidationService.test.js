import { describe, it, expect, beforeEach, vi } from 'vitest';
import PinValidationService from '../../services/pin/PinValidationService';
import { PinControlService } from '../../services/pin/PinControlService';

// Mock del PinControlService
vi.mock('../../services/pin/PinControlService', () => ({
  PinControlService: {
    checkPsychologistUsage: vi.fn()
  }
}));

describe('PinValidationService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('validateReportGeneration', () => {
    it('should return invalid for missing psychologist ID', async () => {
      const result = await PinValidationService.validateReportGeneration(null, 1);
      
      expect(result.isValid).toBe(false);
      expect(result.canProceed).toBe(false);
      expect(result.reason).toBe('INVALID_PSYCHOLOGIST_ID');
    });

    it('should return invalid for invalid pin requirement', async () => {
      const result = await PinValidationService.validateReportGeneration('psy-123', 0);
      
      expect(result.isValid).toBe(false);
      expect(result.canProceed).toBe(false);
      expect(result.reason).toBe('INVALID_PIN_REQUIREMENT');
    });

    it('should return valid for unlimited plan', async () => {
      PinControlService.checkPsychologistUsage.mockResolvedValue({
        isUnlimited: true,
        remainingPins: null
      });

      const result = await PinValidationService.validateReportGeneration('psy-123', 1);
      
      expect(result.isValid).toBe(true);
      expect(result.canProceed).toBe(true);
      expect(result.reason).toBe('UNLIMITED_PLAN');
      expect(result.isUnlimited).toBe(true);
    });

    it('should return invalid for insufficient pins', async () => {
      PinControlService.checkPsychologistUsage.mockResolvedValue({
        isUnlimited: false,
        remainingPins: 2
      });

      const result = await PinValidationService.validateReportGeneration('psy-123', 5);
      
      expect(result.isValid).toBe(false);
      expect(result.canProceed).toBe(false);
      expect(result.reason).toBe('INSUFFICIENT_PINS');
      expect(result.shortfall).toBe(3);
    });

    it('should return valid with warning for low pins after operation', async () => {
      PinControlService.checkPsychologistUsage.mockResolvedValue({
        isUnlimited: false,
        remainingPins: 6
      });

      const result = await PinValidationService.validateReportGeneration('psy-123', 5);
      
      expect(result.isValid).toBe(true);
      expect(result.canProceed).toBe(true);
      expect(result.reason).toBe('PINS_AVAILABLE');
      expect(result.severity).toBe('warning');
      expect(result.pinsAfterOperation).toBe(1);
      expect(result.hasWarning).toBe(true);
    });

    it('should return valid without warning for sufficient pins', async () => {
      PinControlService.checkPsychologistUsage.mockResolvedValue({
        isUnlimited: false,
        remainingPins: 50
      });

      const result = await PinValidationService.validateReportGeneration('psy-123', 1);
      
      expect(result.isValid).toBe(true);
      expect(result.canProceed).toBe(true);
      expect(result.reason).toBe('PINS_AVAILABLE');
      expect(result.severity).toBe('success');
      expect(result.pinsAfterOperation).toBe(49);
      expect(result.hasWarning).toBe(false);
    });

    it('should handle service errors gracefully', async () => {
      PinControlService.checkPsychologistUsage.mockRejectedValue(new Error('Service error'));

      const result = await PinValidationService.validateReportGeneration('psy-123', 1);
      
      expect(result.isValid).toBe(false);
      expect(result.canProceed).toBe(false);
      expect(result.reason).toBe('VALIDATION_ERROR');
      expect(result.error).toBe('Service error');
    });
  });

  describe('validateBatchReportGeneration', () => {
    it('should return invalid for empty patient list', async () => {
      const result = await PinValidationService.validateBatchReportGeneration('psy-123', []);
      
      expect(result.isValid).toBe(false);
      expect(result.canProceed).toBe(false);
      expect(result.reason).toBe('INVALID_PATIENT_LIST');
    });

    it('should validate batch with correct pin count', async () => {
      PinControlService.checkPsychologistUsage.mockResolvedValue({
        isUnlimited: false,
        remainingPins: 10
      });

      const patientIds = ['patient-1', 'patient-2', 'patient-3'];
      const result = await PinValidationService.validateBatchReportGeneration('psy-123', patientIds);
      
      expect(result.batchInfo.patientCount).toBe(3);
      expect(result.batchInfo.requiredPins).toBe(3);
      expect(result.batchInfo.patientIds).toEqual(patientIds);
    });

    it('should return invalid for insufficient pins in batch', async () => {
      PinControlService.checkPsychologistUsage.mockResolvedValue({
        isUnlimited: false,
        remainingPins: 2
      });

      const patientIds = ['patient-1', 'patient-2', 'patient-3'];
      const result = await PinValidationService.validateBatchReportGeneration('psy-123', patientIds);
      
      expect(result.isValid).toBe(false);
      expect(result.canProceed).toBe(false);
      expect(result.reason).toBe('INSUFFICIENT_PINS');
      expect(result.shortfall).toBe(1);
    });
  });

  describe('createValidationSummary', () => {
    it('should create summary for allowed operation', () => {
      const validationResult = {
        isValid: true,
        canProceed: true,
        reason: 'PINS_AVAILABLE',
        userMessage: 'Puede generar el informe',
        severity: 'success',
        remainingPins: 10,
        requiredPins: 1,
        pinsAfterOperation: 9,
        isUnlimited: false
      };

      const summary = PinValidationService.createValidationSummary(validationResult);
      
      expect(summary.status).toBe('allowed');
      expect(summary.title).toBe('Operación Permitida');
      expect(summary.details.currentPins).toBe(10);
      expect(summary.details.requiredPins).toBe(1);
      expect(summary.details.pinsAfter).toBe(9);
      expect(summary.actionRequired).toBe(false);
    });

    it('should create summary for blocked operation', () => {
      const validationResult = {
        isValid: false,
        canProceed: false,
        reason: 'INSUFFICIENT_PINS',
        userMessage: 'No hay suficientes pines',
        severity: 'error',
        remainingPins: 0,
        requiredPins: 1,
        shortfall: 1,
        isUnlimited: false
      };

      const summary = PinValidationService.createValidationSummary(validationResult);
      
      expect(summary.status).toBe('blocked');
      expect(summary.title).toBe('Operación Bloqueada');
      expect(summary.actionRequired).toBe(true);
      expect(summary.suggestions).toContain('Necesita 1 pines adicionales');
    });

    it('should create summary for unlimited plan', () => {
      const validationResult = {
        isValid: true,
        canProceed: true,
        reason: 'UNLIMITED_PLAN',
        userMessage: 'Plan ilimitado activo',
        severity: 'success',
        isUnlimited: true
      };

      const summary = PinValidationService.createValidationSummary(validationResult);
      
      expect(summary.details.isUnlimited).toBe(true);
      expect(summary.status).toBe('allowed');
    });
  });

  describe('_generateSuggestions', () => {
    it('should generate suggestions for insufficient pins', () => {
      const validationResult = {
        reason: 'INSUFFICIENT_PINS',
        shortfall: 5,
        remainingPins: 2
      };

      const suggestions = PinValidationService._generateSuggestions(validationResult);
      
      expect(suggestions).toContain('Necesita 5 pines adicionales');
      expect(suggestions).toContain('Contacte al administrador para obtener más pines');
      expect(suggestions).toContain('Puede generar 2 informes con los pines actuales');
    });

    it('should generate suggestions for psychologist not found', () => {
      const validationResult = {
        reason: 'PSYCHOLOGIST_NOT_FOUND'
      };

      const suggestions = PinValidationService._generateSuggestions(validationResult);
      
      expect(suggestions).toContain('Verifique que su cuenta esté correctamente configurada');
      expect(suggestions).toContain('Contacte al administrador del sistema');
    });

    it('should generate suggestions for warning states', () => {
      const validationResult = {
        reason: 'PINS_AVAILABLE',
        severity: 'warning'
      };

      const suggestions = PinValidationService._generateSuggestions(validationResult);
      
      expect(suggestions).toContain('Considere recargar pines antes de que se agoten');
    });
  });
});

// Tests de integración
describe('PinValidationService Integration', () => {
  it('should handle complete validation flow', async () => {
    // Simular un flujo completo de validación
    PinControlService.checkPsychologistUsage.mockResolvedValue({
      isUnlimited: false,
      remainingPins: 25
    });

    const result = await PinValidationService.validateReportGeneration('psy-123', 1);
    const summary = PinValidationService.createValidationSummary(result);
    
    expect(result.isValid).toBe(true);
    expect(result.canProceed).toBe(true);
    expect(summary.status).toBe('allowed');
    expect(summary.details.currentPins).toBe(25);
  });

  it('should handle edge case with exactly required pins', async () => {
    PinControlService.checkPsychologistUsage.mockResolvedValue({
      isUnlimited: false,
      remainingPins: 1
    });

    const result = await PinValidationService.validateReportGeneration('psy-123', 1);
    
    expect(result.isValid).toBe(true);
    expect(result.canProceed).toBe(true);
    expect(result.pinsAfterOperation).toBe(0);
    expect(result.severity).toBe('critical');
  });
});
