import{E as e,u as s,r as a,j as l,Q as i}from"./vendor-BqMjyOVw.js";import{C as t,a as n,B as r,b as d,s as c}from"./index-Bdl1jgS_.js";import{o}from"./interpretacionesAptitudes-Bt_sak-B.js";const m=()=>{var m,x,p,u;const{reportId:h}=e(),b=s(),[g,j]=a.useState(null),[f,v]=a.useState(!0);a.useEffect(()=>{h&&(()=>{return e=null,s=null,a=function*(){try{v(!0);const{data:e,error:s}=yield c.from("informes").select("\n            id,\n            titulo,\n            contenido,\n            tipo_informe,\n            estado,\n            fecha_generacion,\n            generado_por,\n            observaciones,\n            pacientes:paciente_id (\n              id,\n              nombre,\n              apellido,\n              documento,\n              genero,\n              fecha_nacimiento,\n              email,\n              telefono\n            )\n          ").eq("id",h).single();if(s)return i.error("Error al cargar el informe"),void b("/admin/informes-guardados");j(e)}catch(e){i.error("Error al cargar el informe"),b("/admin/informes-guardados")}finally{v(!1)}},new Promise((l,i)=>{var t=e=>{try{r(a.next(e))}catch(s){i(s)}},n=e=>{try{r(a.throw(e))}catch(s){i(s)}},r=e=>e.done?l(e.value):Promise.resolve(e.value).then(t,n);r((a=a.apply(e,s)).next())});var e,s,a})()},[h,b]);const N=e=>{if(!e)return"N/A";const s=new Date,a=new Date(e);let l=s.getFullYear()-a.getFullYear();const i=s.getMonth()-a.getMonth();return(i<0||0===i&&s.getDate()<a.getDate())&&l--,l};if(f)return l.jsx("div",{className:"container mx-auto py-6",children:l.jsxs("div",{className:"py-16 text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-500",children:"Cargando informe guardado..."})]})});if(!g)return l.jsx("div",{className:"container mx-auto py-6",children:l.jsx(t,{children:l.jsx(n,{children:l.jsxs("div",{className:"py-8 text-center",children:[l.jsx("i",{className:"fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4"}),l.jsx("p",{className:"text-gray-500",children:"No se pudo cargar el informe."}),l.jsx(r,{onClick:()=>b("/admin/informes-guardados"),className:"mt-4",children:"Volver a Informes"})]})})})});const y=g.contenido,w=y.paciente||g.pacientes;g.tipo_informe;const D=y.resultados||[],E=D.length,C=D.filter(e=>{var s;return null==(s=e.puntajes)?void 0:s.percentil}),A=C.length>0?Math.round(C.reduce((e,s)=>e+s.puntajes.percentil,0)/C.length):0,k=C.filter(e=>e.puntajes.percentil>=75).length,z=C.filter(e=>e.puntajes.percentil<=25).length,P=e=>C.find(s=>{var a;return(null==(a=s.test)?void 0:a.codigo)===e}),G=P("R"),I=P("N"),S=[null==(m=null==G?void 0:G.puntajes)?void 0:m.percentil,null==(x=null==I?void 0:I.puntajes)?void 0:x.percentil].filter(e=>void 0!==e),_=S.length>0?Math.round(S.reduce((e,s)=>e+s,0)/S.length):null,q=P("V"),F=P("O"),$=[null==(p=null==q?void 0:q.puntajes)?void 0:p.percentil,null==(u=null==F?void 0:F.puntajes)?void 0:u.percentil].filter(e=>void 0!==e),L=$.length>0?Math.round($.reduce((e,s)=>e+s,0)/$.length):null,M=A,T=(e,s)=>{if(!s)return{nivel:"No evaluado",descripcion:"No hay datos suficientes para evaluar este índice."};let a,l,i;switch(a=s>=75?"Alto":s>=25?"Promedio":"Bajo",e){case"g":"Alto"===a?(l="Capacidad general elevada para comprender situaciones complejas, razonar y resolver problemas de manera efectiva.",i=["Habilidad para resolver eficientemente problemas complejos y novedosos","Buena capacidad para formular y contrastar hipótesis","Facilidad para abstraer información e integrarla con conocimiento previo","Elevado potencial para adquirir nuevos conocimientos"]):"Promedio"===a?(l="Capacidad general dentro del rango esperado para resolver problemas y comprender situaciones.",i=["Capacidad adecuada para resolver problemas de complejidad moderada","Habilidades de razonamiento en desarrollo","Potencial de aprendizaje dentro del rango promedio"]):(l="Dificultades en la capacidad general para resolver problemas complejos y comprender relaciones abstractas.",i=["Dificultades para aplicar el razonamiento a problemas complejos","Limitaciones para formar juicios que requieran abstracción","Posible necesidad de enseñanza más directiva y supervisada"]);break;case"Gf":"Alto"===a?(l="Excelente capacidad para el razonamiento inductivo y deductivo con problemas novedosos.",i=["Habilidad sobresaliente para aplicar razonamiento a problemas novedosos","Facilidad para identificar reglas y formular hipótesis","Nivel alto de razonamiento analítico","Buena integración de información visual y verbal"]):"Promedio"===a?(l="Capacidad adecuada para el razonamiento con contenidos abstractos y formales.",i=["Habilidades de razonamiento en desarrollo","Capacidad moderada para resolver problemas novedosos","Estrategias de resolución en proceso de consolidación"]):(l="Dificultades en el razonamiento inductivo y deductivo con problemas abstractos.",i=["Uso de estrategias poco eficaces para problemas novedosos","Falta de flexibilidad en soluciones alternativas","Dificultades para identificar reglas subyacentes","Integración defectuosa de información visual y verbal"]);break;case"Gc":"Alto"===a?(l="Excelente dominio de conocimientos adquiridos culturalmente y habilidades verbales.",i=["Habilidad para captar relaciones entre conceptos verbales","Buena capacidad de comprensión y expresión del lenguaje","Buen nivel de conocimiento léxico y ortográfico","Posiblemente buen nivel de cultura general"]):"Promedio"===a?(l="Conocimientos verbales y culturales dentro del rango esperado.",i=["Comprensión verbal adecuada para la edad","Conocimientos léxicos en desarrollo","Habilidades de expresión en proceso de consolidación"]):(l="Limitaciones en conocimientos verbales y habilidades de lenguaje adquiridas culturalmente.",i=["Procesamiento parcial de relaciones entre conceptos verbales","Dificultades en comprensión y expresión del lenguaje","Limitaciones en conocimiento léxico y ortográfico","Posible nivel bajo de cultura general"]);break;default:l="Interpretación no disponible para este índice.",i=[]}return{nivel:a,descripcion:l,caracteristicas:i}},B=D.map(e=>new Date(e.fecha_evaluacion)).filter(e=>!isNaN(e)),R=B.length>0?new Date(Math.min(...B)):null,H=B.length>0?new Date(Math.max(...B)):null;return l.jsxs("div",{className:"container mx-auto py-6 max-w-6xl",children:[l.jsxs("div",{className:"mb-6 text-center",children:[l.jsxs("div",{className:"flex items-center justify-center mb-4",children:[l.jsx("div",{className:`w-16 h-16 bg-${"masculino"===(null==w?void 0:w.genero)?"blue":"pink"}-100 rounded-full flex items-center justify-center mr-4`,children:l.jsx("i",{className:`fas ${"masculino"===(null==w?void 0:w.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} text-2xl`})}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-3xl font-bold text-blue-800",children:"Informe Completo de Evaluación - Admin"}),l.jsxs("p",{className:"text-gray-600",children:[null==w?void 0:w.nombre," ",null==w?void 0:w.apellido]})]})]}),l.jsxs("div",{className:"flex justify-center space-x-4 print-hide",children:[l.jsxs(r,{onClick:()=>b("/admin/informes-guardados"),variant:"outline",children:[l.jsx("i",{className:"fas fa-arrow-left mr-2"}),"Volver"]}),l.jsxs(r,{onClick:()=>{const e=document.querySelectorAll(".print-hide");e.forEach(e=>e.style.display="none");const s=document.querySelector(".container"),a=document.body,l=document.documentElement,i=null==s?void 0:s.className,t=null==a?void 0:a.className,n=null==l?void 0:l.className;s&&(s.className+=" print-optimize"),a&&(a.className+=" print-optimize"),l&&(l.className+=" print-optimize");const r=document.createElement("style");r.textContent="\n      @media print {\n        * {\n          -webkit-print-color-adjust: exact !important;\n          print-color-adjust: exact !important;\n        }\n        .space-y-6 > * + * {\n          margin-top: 0.5rem !important;\n        }\n        .mb-6 {\n          margin-bottom: 0.5rem !important;\n        }\n        .py-6 {\n          padding-top: 0.5rem !important;\n          padding-bottom: 0.5rem !important;\n        }\n      }\n    ",document.head.appendChild(r);const d=document.title;document.title=`Informe_${null==w?void 0:w.nombre}_${null==w?void 0:w.apellido}_${(new Date).toLocaleDateString("es-ES").replace(/\//g,"-")}`,window.print(),setTimeout(()=>{e.forEach(e=>e.style.display=""),s&&i&&(s.className=i),a&&t&&(a.className=t),l&&n&&(l.className=n),document.head.removeChild(r),document.title=d},1e3)},variant:"primary",children:[l.jsx("i",{className:"fas fa-file-pdf mr-2"}),"Generar PDF"]}),l.jsxs(r,{onClick:()=>window.print(),variant:"outline",children:[l.jsx("i",{className:"fas fa-print mr-2"}),"Imprimir"]})]})]}),l.jsx("div",{className:"print-only mb-6",children:l.jsx("div",{className:"informe-header-main",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center",children:[l.jsx("i",{className:"fas fa-file-medical-alt text-3xl mr-4"}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-3xl font-bold",children:"INFORME PSICOLÓGICO"}),l.jsx("h2",{className:"text-lg",children:"Batería de Aptitudes Diferenciales y Generales - BAT-7"}),l.jsx("p",{className:"text-sm",children:"Evaluación Psicológica Integral"})]})]}),l.jsxs("div",{className:"text-right",children:[l.jsxs("div",{className:"bg-white bg-opacity-20 px-3 py-1 rounded",children:[l.jsx("i",{className:"fas fa-file-pdf mr-2"}),l.jsx("span",{className:"text-sm",children:"PDF"})]}),l.jsxs("div",{className:"bg-white bg-opacity-20 px-3 py-1 rounded mt-2",children:[l.jsx("i",{className:"fas fa-print mr-2"}),l.jsx("span",{className:"text-sm",children:"Imprimir"})]})]})]})})}),l.jsxs(t,{className:"mb-6 print-keep-together shadow-lg border-l-4 border-blue-500 print-patient-info",children:[l.jsx(d,{className:"bg-gradient-to-r from-blue-50 to-green-50 border-b-2 border-blue-200",children:l.jsxs("div",{className:"flex items-center",children:[l.jsx("div",{className:`w-12 h-12 bg-${"masculino"===(null==w?void 0:w.genero)?"blue":"pink"}-100 rounded-full flex items-center justify-center mr-4`,children:l.jsx("i",{className:`fas ${"masculino"===(null==w?void 0:w.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} text-xl`})}),l.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[l.jsx("i",{className:"fas fa-user mr-2"}),"Información del Paciente"]})]})}),l.jsxs(n,{className:"bg-white",children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 print:hidden",children:[l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400",children:[l.jsx("p",{className:"text-xs font-medium text-blue-600 uppercase tracking-wide mb-1",children:"Nombre Completo"}),l.jsxs("p",{className:"text-lg font-bold text-gray-900",children:[null==w?void 0:w.nombre," ",null==w?void 0:w.apellido]})]}),(null==w?void 0:w.documento)&&l.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg border-l-4 border-gray-400",children:[l.jsx("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide mb-1",children:"Documento"}),l.jsx("p",{className:"text-base font-semibold text-gray-900",children:w.documento})]})]}),l.jsxs("div",{className:"space-y-4",children:[(null==w?void 0:w.fecha_nacimiento)&&l.jsxs("div",{className:"bg-green-50 p-4 rounded-lg border-l-4 border-green-400",children:[l.jsx("p",{className:"text-xs font-medium text-green-600 uppercase tracking-wide mb-1",children:"Fecha de Nacimiento"}),l.jsx("p",{className:"text-base font-semibold text-gray-900",children:new Date(w.fecha_nacimiento).toLocaleDateString("es-ES")})]}),l.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg border-l-4 border-purple-400",children:[l.jsx("p",{className:"text-xs font-medium text-purple-600 uppercase tracking-wide mb-1",children:"Edad"}),l.jsxs("p",{className:"text-base font-semibold text-gray-900",children:[N(null==w?void 0:w.fecha_nacimiento)," años"]})]})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:`bg-${"masculino"===(null==w?void 0:w.genero)?"blue":"pink"}-50 p-4 rounded-lg border-l-4 border-${"masculino"===(null==w?void 0:w.genero)?"blue":"pink"}-400`,children:[l.jsx("p",{className:`text-xs font-medium text-${"masculino"===(null==w?void 0:w.genero)?"blue":"pink"}-600 uppercase tracking-wide mb-1`,children:"Género"}),l.jsxs("p",{className:"text-base font-semibold text-gray-900 capitalize flex items-center",children:[l.jsx("i",{className:`fas ${"masculino"===(null==w?void 0:w.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} mr-2`}),null==w?void 0:w.genero]})]}),(null==w?void 0:w.email)&&l.jsxs("div",{className:"bg-orange-50 p-4 rounded-lg border-l-4 border-orange-400",children:[l.jsx("p",{className:"text-xs font-medium text-orange-600 uppercase tracking-wide mb-1",children:"Email"}),l.jsx("p",{className:"text-sm font-semibold text-gray-900 break-all",children:w.email})]})]})]}),l.jsx("div",{className:"hidden print:block",children:l.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[l.jsxs("div",{children:[l.jsxs("div",{className:"mb-2",children:[l.jsx("span",{className:"font-medium text-blue-600",children:"Nombre:"}),l.jsxs("span",{className:"ml-2 font-bold",children:[null==w?void 0:w.nombre," ",null==w?void 0:w.apellido]})]}),(null==w?void 0:w.documento)&&l.jsxs("div",{className:"mb-2",children:[l.jsx("span",{className:"font-medium text-gray-600",children:"Documento:"}),l.jsx("span",{className:"ml-2",children:w.documento})]}),(null==w?void 0:w.email)&&l.jsxs("div",{className:"mb-2",children:[l.jsx("span",{className:"font-medium text-orange-600",children:"Email:"}),l.jsx("span",{className:"ml-2 text-xs",children:w.email})]})]}),l.jsxs("div",{children:[(null==w?void 0:w.fecha_nacimiento)&&l.jsxs("div",{className:"mb-2",children:[l.jsx("span",{className:"font-medium text-green-600",children:"Fecha de Nacimiento:"}),l.jsx("span",{className:"ml-2",children:new Date(w.fecha_nacimiento).toLocaleDateString("es-ES")})]}),l.jsxs("div",{className:"mb-2",children:[l.jsx("span",{className:"font-medium text-purple-600",children:"Edad:"}),l.jsxs("span",{className:"ml-2",children:[N(null==w?void 0:w.fecha_nacimiento)," años"]})]}),l.jsxs("div",{className:"mb-2",children:[l.jsx("span",{className:`font-medium text-${"masculino"===(null==w?void 0:w.genero)?"blue":"pink"}-600`,children:"Género:"}),l.jsxs("span",{className:"ml-2 capitalize",children:[l.jsx("i",{className:`fas ${"masculino"===(null==w?void 0:w.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} mr-1`}),null==w?void 0:w.genero]})]})]})]})})]})]}),l.jsxs(t,{className:"mb-6 print-keep-together",children:[l.jsx(d,{className:"bg-purple-50 border-b",children:l.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[l.jsx("i",{className:"fas fa-chart-pie mr-2"}),"Resumen General"]})}),l.jsxs(n,{className:"print-compact",children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 print-grid-horizontal gap-6 mb-6",children:[l.jsxs("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[l.jsx("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:E}),l.jsx("div",{className:"text-sm font-medium text-blue-700",children:"Tests Completados"})]}),l.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[l.jsx("div",{className:"text-3xl font-bold text-green-600 mb-2",children:A}),l.jsx("div",{className:"text-sm font-medium text-green-700",children:"Percentil Promedio"})]}),l.jsxs("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[l.jsx("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:k}),l.jsx("div",{className:"text-sm font-medium text-purple-700",children:"Aptitudes Altas (≥75)"})]}),l.jsxs("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[l.jsx("div",{className:"text-3xl font-bold text-orange-600 mb-2",children:z}),l.jsx("div",{className:"text-sm font-medium text-orange-700",children:"Aptitudes a Reforzar (≤25)"})]})]}),l.jsxs("div",{className:"border-t pt-6",children:[l.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4 text-center",children:"Índices Especializados BAT-7"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 print-grid-horizontal gap-6",children:[l.jsxs("div",{className:"text-center p-4 bg-indigo-50 rounded-lg border-2 border-indigo-200",children:[l.jsx("div",{className:"text-2xl font-bold text-indigo-600 mb-2",children:A}),l.jsx("div",{className:"text-sm font-medium text-indigo-700",children:"Total BAT"}),l.jsx("div",{className:"text-xs text-indigo-600 mt-1",children:"Capacidad General"})]}),l.jsxs("div",{className:"text-center p-4 bg-cyan-50 rounded-lg border-2 border-cyan-200",children:[l.jsx("div",{className:"text-2xl font-bold text-cyan-600 mb-2",children:M}),l.jsx("div",{className:"text-sm font-medium text-cyan-700",children:"Índice g"}),l.jsx("div",{className:"text-xs text-cyan-600 mt-1",children:"Capacidad General"})]}),l.jsxs("div",{className:"text-center p-4 bg-teal-50 rounded-lg border-2 border-teal-200",children:[l.jsx("div",{className:"text-2xl font-bold text-teal-600 mb-2",children:_||"N/A"}),l.jsx("div",{className:"text-sm font-medium text-teal-700",children:"Índice Gf"}),l.jsx("div",{className:"text-xs text-teal-600 mt-1",children:"Inteligencia Fluida"})]}),l.jsxs("div",{className:"text-center p-4 bg-emerald-50 rounded-lg border-2 border-emerald-200",children:[l.jsx("div",{className:"text-2xl font-bold text-emerald-600 mb-2",children:L||"N/A"}),l.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Índice Gc"}),l.jsx("div",{className:"text-xs text-emerald-600 mt-1",children:"Inteligencia Cristalizada"})]})]})]}),l.jsxs("div",{className:"mt-6 flex justify-between text-sm text-gray-600",children:[l.jsxs("div",{children:[l.jsx("span",{className:"font-medium",children:"Primera evaluación:"})," ",R?R.toLocaleDateString("es-ES"):"N/A"]}),l.jsxs("div",{children:[l.jsx("span",{className:"font-medium",children:"Última evaluación:"})," ",H?H.toLocaleDateString("es-ES"):"N/A"]})]})]})]}),l.jsxs(t,{className:"mb-6 print-keep-together",children:[l.jsx(d,{className:"bg-gray-50 border-b",children:l.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[l.jsx("i",{className:"fas fa-list-alt mr-2"}),"Resultados Detallados por Aptitud"]})}),l.jsx(n,{className:"p-0",children:0===D.length?l.jsxs("div",{className:"py-8 text-center",children:[l.jsx("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 mb-4"}),l.jsx("p",{className:"text-gray-500",children:"No hay resultados de tests disponibles para este paciente."})]}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"w-full",children:[l.jsx("thead",{children:l.jsxs("tr",{className:"bg-slate-800 text-white",children:[l.jsx("th",{className:"px-4 py-3 text-left font-semibold",children:"S"}),l.jsx("th",{className:"px-4 py-3 text-left font-semibold",children:"APTITUDES EVALUADAS"}),l.jsx("th",{className:"px-4 py-3 text-center font-semibold",children:"PD"}),l.jsx("th",{className:"px-4 py-3 text-center font-semibold",children:"PC"}),l.jsx("th",{className:"px-4 py-3 text-left font-semibold",children:"PERFIL DE LAS APTITUDES"})]})}),l.jsx("tbody",{children:D.map((e,s)=>{var a,i,t,n,r,d;null==(a=e.test)||a.codigo;const c=(null==(i=e.puntajes)?void 0:i.percentil)||0;let o="bg-blue-500";return o=c>=80?"bg-orange-500":c>=60?"bg-blue-500":c>=40?"bg-blue-400":"bg-blue-300",l.jsxs("tr",{className:s%2==0?"bg-white":"bg-gray-50",children:[l.jsx("td",{className:"px-4 py-3",children:l.jsx("div",{className:"w-8 h-8 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:null==(t=e.test)?void 0:t.codigo})}),l.jsx("td",{className:"px-4 py-3",children:l.jsx("div",{className:"font-medium text-gray-900",children:null==(n=e.test)?void 0:n.nombre})}),l.jsx("td",{className:"px-4 py-3 text-center font-bold text-gray-900",children:(null==(r=e.puntajes)?void 0:r.puntaje_directo)||"N/A"}),l.jsx("td",{className:"px-4 py-3 text-center font-bold text-gray-900",children:(null==(d=e.puntajes)?void 0:d.percentil)||"N/A"}),l.jsx("td",{className:"px-4 py-3",children:l.jsx("div",{className:"flex items-center",children:l.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-6 mr-3",children:l.jsx("div",{className:`${o} h-6 rounded-full flex items-center justify-end pr-2`,style:{width:`${Math.max(c,5)}%`},children:l.jsx("span",{className:"text-white text-xs font-bold",children:c>0?c:""})})})})})]},s)})})]})})})]}),D.length>0&&l.jsxs(t,{className:"mb-6 print-keep-together",children:[l.jsx(d,{className:"bg-purple-50 border-b",children:l.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[l.jsx("i",{className:"fas fa-brain mr-2"}),"Interpretación Cualitativa de Aptitudes"]})}),l.jsx(n,{children:l.jsx("div",{className:"space-y-6",children:D.map((e,s)=>{var a,i,t,n,r;const d=o(null==(a=e.test)?void 0:a.codigo,(null==(i=e.puntajes)?void 0:i.percentil)||0);return d?l.jsxs("div",{className:"border-l-4 border-blue-500 pl-6 py-4 bg-gray-50 rounded-r-lg",children:[l.jsxs("div",{className:"flex items-center mb-3",children:[l.jsx("div",{className:"w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold mr-3",children:null==(t=e.test)?void 0:t.codigo}),l.jsxs("div",{children:[l.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:[null==(n=e.test)?void 0:n.nombre," - Nivel ",d.nivel]}),l.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",(null==(r=e.puntajes)?void 0:r.percentil)||"N/A"]})]})]}),l.jsxs("div",{className:"mb-4",children:[l.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),l.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:d.descripcion})]}),l.jsxs("div",{children:[l.jsxs("h4",{className:"font-medium text-gray-800 mb-2",children:["Características ","Alto"===d.nivel?"Fortalezas":"Áreas de Mejora",":"]}),l.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:d.caracteristicas.map((e,s)=>l.jsx("li",{className:"leading-relaxed",children:e},s))})]})]},s):null})})})]}),l.jsxs(t,{className:"mb-6 print-keep-together",children:[l.jsx(d,{className:"bg-indigo-50 border-b",children:l.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[l.jsx("i",{className:"fas fa-chart-line mr-2"}),"Interpretación Cualitativa de Índices Especializados"]})}),l.jsx(n,{children:l.jsxs("div",{className:"space-y-6",children:[A&&l.jsxs("div",{className:"border-l-4 border-indigo-500 pl-6 py-4 bg-indigo-50 rounded-r-lg",children:[l.jsxs("div",{className:"flex items-center mb-3",children:[l.jsx("div",{className:"w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"g"}),l.jsxs("div",{children:[l.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice g - Capacidad General: ",T("g",M).nivel]}),l.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",M]})]})]}),l.jsxs("div",{className:"mb-4",children:[l.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),l.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:T("g",M).descripcion})]}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"}),l.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:T("g",M).caracteristicas.map((e,s)=>l.jsx("li",{className:"leading-relaxed",children:e},s))})]})]}),_&&l.jsxs("div",{className:"border-l-4 border-teal-500 pl-6 py-4 bg-teal-50 rounded-r-lg",children:[l.jsxs("div",{className:"flex items-center mb-3",children:[l.jsx("div",{className:"w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"Gf"}),l.jsxs("div",{children:[l.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice Gf - Inteligencia Fluida: ",T("Gf",_).nivel]}),l.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",_," (basado en R + N)"]})]})]}),l.jsxs("div",{className:"mb-4",children:[l.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),l.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:T("Gf",_).descripcion})]}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"}),l.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:T("Gf",_).caracteristicas.map((e,s)=>l.jsx("li",{className:"leading-relaxed",children:e},s))})]})]}),L&&l.jsxs("div",{className:"border-l-4 border-emerald-500 pl-6 py-4 bg-emerald-50 rounded-r-lg",children:[l.jsxs("div",{className:"flex items-center mb-3",children:[l.jsx("div",{className:"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"Gc"}),l.jsxs("div",{children:[l.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice Gc - Inteligencia Cristalizada: ",T("Gc",L).nivel]}),l.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",L," (basado en V + O)"]})]})]}),l.jsxs("div",{className:"mb-4",children:[l.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),l.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:T("Gc",L).descripcion})]}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"}),l.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:T("Gc",L).caracteristicas.map((e,s)=>l.jsx("li",{className:"leading-relaxed",children:e},s))})]})]}),_&&L&&Math.abs(_-L)>15&&l.jsxs("div",{className:"border-l-4 border-yellow-500 pl-6 py-4 bg-yellow-50 rounded-r-lg",children:[l.jsxs("div",{className:"flex items-center mb-3",children:[l.jsx("div",{className:"w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:l.jsx("i",{className:"fas fa-balance-scale"})}),l.jsx("div",{children:l.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Análisis de Disparidad entre Índices"})})]}),l.jsx("div",{children:l.jsxs("p",{className:"text-gray-700 text-sm leading-relaxed",children:["Se observa una diferencia significativa entre la Inteligencia Fluida (Gf: ",_,") y la Inteligencia Cristalizada (Gc: ",L,"). Esta disparidad sugiere un perfil cognitivo heterogéneo que requiere consideración especial en las recomendaciones de intervención.",_>L?" El evaluado muestra mayor fortaleza en razonamiento abstracto que en conocimientos adquiridos.":" El evaluado muestra mayor fortaleza en conocimientos adquiridos que en razonamiento abstracto."]})})]})]})})]}),l.jsxs(t,{className:"mb-6 print-keep-together",children:[l.jsx(d,{className:"bg-yellow-50 border-b",children:l.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[l.jsx("i",{className:"fas fa-lightbulb mr-2"}),"Recomendaciones Generales"]})}),l.jsx(n,{children:l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{children:[l.jsx("h4",{className:"font-semibold text-gray-800 mb-2",children:"Análisis General del Rendimiento"}),l.jsxs("p",{className:"text-gray-600 text-sm leading-relaxed",children:["El evaluado presenta un percentil promedio de ",A,", lo que indica un rendimiento"," ",A>=70?"por encima del promedio":A>=30?"en el rango promedio":"por debajo del promedio"," ","en las aptitudes evaluadas."]})]}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-semibold text-gray-800 mb-2",children:"Recomendaciones Específicas"}),l.jsxs("ul",{className:"text-gray-600 text-sm space-y-1 list-disc list-inside",children:[l.jsx("li",{children:"Realizar seguimiento periódico del progreso en todas las aptitudes"}),l.jsx("li",{children:"Considerar la aplicación de tests complementarios según las necesidades identificadas"}),l.jsx("li",{children:"Mantener un registro detallado de las intervenciones y su efectividad"}),k>0&&l.jsx("li",{children:"Considerar actividades de enriquecimiento en las aptitudes con alto rendimiento"}),z>0&&l.jsx("li",{children:"Implementar estrategias de apoyo en las aptitudes con rendimiento bajo"})]})]})]})})]}),l.jsxs("div",{className:"text-center text-sm text-gray-500 border-t pt-4",children:[l.jsxs("p",{children:["Informe generado el ",new Date(g.fecha_generacion).toLocaleDateString("es-ES")," a las ",new Date(g.fecha_generacion).toLocaleTimeString("es-ES")]}),l.jsx("p",{className:"mt-1",children:"Sistema de Evaluación Psicológica - BAT-7 - Panel de Administración"})]})]})};export{m as default};
