var e=(e,a,i)=>new Promise((s,r)=>{var t=e=>{try{l(i.next(e))}catch(a){r(a)}},n=e=>{try{l(i.throw(e))}catch(a){r(a)}},l=e=>e.done?s(e.value):Promise.resolve(e.value).then(t,n);l((i=i.apply(e,a)).next())});import{r as a,j as i}from"./vendor-CIyllXGj.js";import{s,C as r,b as t,a as n,B as l}from"./index-CrXvaDRr.js";import{e as o}from"./enhancedSupabaseService-CmvqXOxn.js";const d=(e,a,i)=>{i.error||i.isOffline},u=()=>e(null,null,function*(){const e=yield o.getInstitutions();d(0,0,e);const a=yield o.createInstitution({nombre:`Test Institución ${Date.now()}`,direccion:"Dirección de prueba",telefono:"123456789"});if(d(0,0,a),a.data&&!a.error){const e=a.data.id,i=yield o.updateInstitution(e,{nombre:`${a.data.nombre} (Actualizada)`,direccion:"Dirección actualizada"});d(0,0,i);const s=yield o.deleteInstitution(e);d(0,0,s)}return"Pruebas de instituciones completadas"}),c=()=>e(null,null,function*(){const e=yield o.getInstitutions();if(!e.data||0===e.data.length)return"Error: No hay instituciones disponibles";const a=yield o.getPsychologists();d(0,0,a);const i=Date.now(),r=`test.psicologo.${i}@example.com`,{data:t,error:n}=yield s.auth.signUp({email:r,password:"Temporal123!",options:{data:{rol:"psicologo",nombre_completo:`Test Psicólogo ${i}`}}});if(n)return"Error al crear usuario para psicólogo";const l=yield o.createPsychologist({nombre:"Test",apellidos:`Psicólogo ${i}`,email:r,documento_identidad:`DOC-${i}`,telefono:"987654321",institucion_id:e.data[0].id,usuario_id:t.user.id});if(d(0,0,l),l.data&&!l.error){const e=l.data.id,a=yield o.updatePsychologist(e,{nombre:"Test (Actualizado)",apellidos:`Psicólogo ${i}`,documento_identidad:`DOC-${i}-UPD`,telefono:"987654322"});d(0,0,a);const s=yield o.deletePsychologist(e);d(0,0,s)}return"Pruebas de psicólogos completadas"}),m=()=>e(null,null,function*(){const e=yield o.getInstitutions();if(!e.data||0===e.data.length)return"Error: No hay instituciones disponibles";const a=yield o.getPsychologists(),i=a.data&&a.data.length>0?a.data[0].id:null,s=yield o.getPatients();d(0,0,s);const r=Date.now(),t=yield o.createPatient({nombre:`Test Paciente ${r}`,fecha_nacimiento:"2000-01-01",genero:"Masculino",institucion_id:e.data[0].id,psicologo_id:i,notas:"Paciente de prueba"});if(d(0,0,t),t.data&&!t.error){const e=t.data.id,a=yield o.updatePatient(e,{nombre:`Test Paciente ${r} (Actualizado)`,fecha_nacimiento:"2000-02-02",notas:"Paciente de prueba actualizado"});d(0,0,a);const i=yield o.deletePatient(e);d(0,0,i)}return"Pruebas de pacientes completadas"}),b=()=>e(null,null,function*(){const e=o.getSyncStatus();if(e.pendingCount>0){e.operations.forEach((e,a)=>{});const a=yield o.syncPendingOperations();a.errors.length>0&&a.errors.forEach((e,a)=>{})}return"Pruebas de sincronización completadas"}),g=()=>e(null,null,function*(){try{const{data:{user:e}}=yield s.auth.getUser();return e?(yield u(),yield c(),yield m(),yield b(),"Todas las pruebas completadas con éxito"):"Error: Usuario no autenticado"}catch(e){return`Error al ejecutar pruebas: ${e.message}`}}),p=u,N=c,f=m,v=b,h=()=>{const[s,o]=a.useState(!1),[d,u]=a.useState([]),[c,m]=a.useState("all"),b=[{value:"all",label:"Todas las pruebas"},{value:"instituciones",label:"Pruebas de Instituciones"},{value:"psicologos",label:"Pruebas de Psicólogos"},{value:"pacientes",label:"Pruebas de Pacientes"},{value:"sincronizacion",label:"Pruebas de Sincronización"}];return i.jsxDEV("div",{className:"container mx-auto py-6",children:[i.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Página de Pruebas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:80,columnNumber:7},void 0),i.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[i.jsxDEV(r,{className:"md:col-span-1",children:[i.jsxDEV(t,{children:i.jsxDEV("h2",{className:"text-lg font-medium",children:"Panel de Control"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:86,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:85,columnNumber:11},void 0),i.jsxDEV(n,{children:i.jsxDEV("div",{className:"space-y-4",children:[i.jsxDEV("div",{children:[i.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Seleccionar prueba"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:91,columnNumber:17},void 0),i.jsxDEV("select",{className:"form-select w-full",value:c,onChange:e=>m(e.target.value),disabled:s,children:b.map(e=>i.jsxDEV("option",{value:e.value,children:e.label},e.value,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:101,columnNumber:21},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:94,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:90,columnNumber:15},void 0),i.jsxDEV(l,{variant:"primary",className:"w-full",onClick:()=>{return a=c,e(null,null,function*(){o(!0);try{let e;switch(a){case"all":e=yield g();break;case"instituciones":e=yield p();break;case"psicologos":e=yield N();break;case"pacientes":e=yield f();break;case"sincronizacion":e=yield v();break;default:e="Prueba no válida"}u(i=>[{id:Date.now(),test:a,result:e,timestamp:(new Date).toLocaleString()},...i])}catch(e){u(i=>[{id:Date.now(),test:a,result:`Error: ${e.message}`,timestamp:(new Date).toLocaleString(),error:!0},...i])}finally{o(!1)}});var a},disabled:s,children:s?"Ejecutando...":"Ejecutar Prueba"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:108,columnNumber:15},void 0),i.jsxDEV("div",{className:"text-sm text-gray-500",children:[i.jsxDEV("p",{children:"Esta página permite ejecutar pruebas manuales para verificar el funcionamiento de las operaciones CRUD."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:118,columnNumber:17},void 0),i.jsxDEV("p",{className:"mt-2",children:"Las pruebas se ejecutan contra la base de datos real, así que úsala con precaución."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:119,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:117,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:89,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:88,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:84,columnNumber:9},void 0),i.jsxDEV(r,{className:"md:col-span-2",children:[i.jsxDEV(t,{children:i.jsxDEV("h2",{className:"text-lg font-medium",children:"Resultados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:128,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:127,columnNumber:11},void 0),i.jsxDEV(n,{children:0===d.length?i.jsxDEV("div",{className:"text-center py-8 text-gray-500",children:"No hay resultados. Ejecuta una prueba para ver los resultados aquí."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:132,columnNumber:15},void 0):i.jsxDEV("div",{className:"space-y-4",children:d.map(e=>{var a;return i.jsxDEV("div",{className:"p-4 rounded-lg border "+(e.error?"border-red-200 bg-red-50":"border-green-200 bg-green-50"),children:[i.jsxDEV("div",{className:"flex justify-between items-start",children:[i.jsxDEV("div",{children:[i.jsxDEV("h3",{className:"font-medium",children:(null==(a=b.find(a=>a.value===e.test))?void 0:a.label)||e.test},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:144,columnNumber:25},void 0),i.jsxDEV("p",{className:"text-sm text-gray-500",children:e.timestamp},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:147,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:143,columnNumber:23},void 0),i.jsxDEV("span",{className:"px-2 py-1 text-xs rounded-full "+(e.error?"bg-red-100 text-red-800":"bg-green-100 text-green-800"),children:e.error?"Error":"Éxito"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:149,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:142,columnNumber:21},void 0),i.jsxDEV("div",{className:"mt-2",children:i.jsxDEV("pre",{className:"text-sm whitespace-pre-wrap bg-white p-2 rounded border",children:e.result},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:154,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:153,columnNumber:21},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:138,columnNumber:19},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:136,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:130,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:126,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:82,columnNumber:7},void 0),i.jsxDEV("div",{className:"mt-6 text-sm text-gray-500",children:i.jsxDEV("p",{children:"Nota: Los resultados detallados de las pruebas se muestran en la consola del navegador."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:167,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:166,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/TestPage.jsx",lineNumber:79,columnNumber:5},void 0)};export{h as default};
