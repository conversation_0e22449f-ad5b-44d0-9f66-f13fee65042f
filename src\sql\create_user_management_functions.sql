-- Funciones SQL para gestión completa de usuarios
-- EJECUTAR ESTE SCRIPT EN EL EDITOR SQL DE SUPABASE

-- =====================================================
-- FUNCIÓN PARA AUTENTICAR USUARIOS
-- =====================================================

CREATE OR REPLACE FUNCTION authenticate_user(
  identifier TEXT,
  password TEXT
)
RETURNS JSON AS $$
DECLARE
  user_record RECORD;
  auth_user_id UUID;
BEGIN
  -- <PERSON>car usuario por email o documento
  SELECT u.*, au.email
  INTO user_record
  FROM public.usuarios u
  JOIN auth.users au ON u.id = au.id
  WHERE (au.email = identifier OR u.documento = identifier)
    AND u.activo = true;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Usuario no encontrado o inactivo'
    );
  END IF;
  
  -- Intentar autenticar con Supabase Auth
  -- Nota: Esta función debe ser llamada desde el cliente
  -- Aquí solo validamos que el usuario existe y está activo
  
  -- Actualizar último acceso
  UPDATE public.usuarios 
  SET ultimo_acceso = now()
  WHERE id = user_record.id;
  
  RETURN json_build_object(
    'success', true,
    'user', json_build_object(
      'id', user_record.id,
      'email', user_record.email,
      'documento', user_record.documento,
      'nombre', user_record.nombre,
      'apellido', user_record.apellido,
      'rol', user_record.tipo_usuario,
      'activo', user_record.activo
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FUNCIÓN PARA CREAR USUARIOS
-- =====================================================

CREATE OR REPLACE FUNCTION create_user(
  p_nombre TEXT,
  p_apellido TEXT,
  p_email TEXT,
  p_documento TEXT,
  p_rol TEXT,
  p_password TEXT,
  p_profile_data JSON DEFAULT '{}'::json
)
RETURNS JSON AS $$
DECLARE
  new_user_id UUID;
  auth_result RECORD;
BEGIN
  -- Validar datos requeridos
  IF p_email IS NULL OR p_password IS NULL OR p_nombre IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Email, contraseña y nombre son requeridos'
    );
  END IF;
  
  -- Validar que el email no exista
  IF EXISTS (SELECT 1 FROM auth.users WHERE email = p_email) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'El email ya está registrado'
    );
  END IF;
  
  -- Validar que el documento no exista (si se proporciona)
  IF p_documento IS NOT NULL AND EXISTS (SELECT 1 FROM public.usuarios WHERE documento = p_documento) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'El documento ya está registrado'
    );
  END IF;
  
  -- Generar UUID para el nuevo usuario
  new_user_id := gen_random_uuid();
  
  -- Insertar en la tabla usuarios
  INSERT INTO public.usuarios (
    id,
    documento,
    nombre,
    apellido,
    tipo_usuario,
    activo,
    fecha_creacion
  ) VALUES (
    new_user_id,
    p_documento,
    p_nombre,
    p_apellido,
    p_rol,
    true,
    now()
  );
  
  RETURN json_build_object(
    'success', true,
    'user_id', new_user_id,
    'message', 'Usuario creado exitosamente'
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Error al crear usuario: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FUNCIÓN PARA ACTUALIZAR USUARIOS
-- =====================================================

CREATE OR REPLACE FUNCTION update_user(
  p_user_id UUID,
  p_nombre TEXT DEFAULT NULL,
  p_apellido TEXT DEFAULT NULL,
  p_email TEXT DEFAULT NULL,
  p_documento TEXT DEFAULT NULL,
  p_activo BOOLEAN DEFAULT NULL,
  p_profile_data JSON DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  user_exists BOOLEAN;
BEGIN
  -- Verificar que el usuario existe
  SELECT EXISTS (SELECT 1 FROM public.usuarios WHERE id = p_user_id) INTO user_exists;
  
  IF NOT user_exists THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Usuario no encontrado'
    );
  END IF;
  
  -- Actualizar campos proporcionados
  UPDATE public.usuarios 
  SET 
    nombre = COALESCE(p_nombre, nombre),
    apellido = COALESCE(p_apellido, apellido),
    documento = COALESCE(p_documento, documento),
    activo = COALESCE(p_activo, activo),
    updated_at = now()
  WHERE id = p_user_id;
  
  RETURN json_build_object(
    'success', true,
    'message', 'Usuario actualizado exitosamente'
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Error al actualizar usuario: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FUNCIÓN PARA ELIMINAR USUARIOS
-- =====================================================

CREATE OR REPLACE FUNCTION delete_user(
  p_user_id UUID
)
RETURNS JSON AS $$
DECLARE
  user_exists BOOLEAN;
  user_name TEXT;
BEGIN
  -- Verificar que el usuario existe y obtener su nombre
  SELECT EXISTS (SELECT 1 FROM public.usuarios WHERE id = p_user_id) INTO user_exists;
  
  IF NOT user_exists THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Usuario no encontrado'
    );
  END IF;
  
  -- Obtener nombre del usuario para el mensaje
  SELECT CONCAT(nombre, ' ', apellido) INTO user_name
  FROM public.usuarios 
  WHERE id = p_user_id;
  
  -- Eliminar usuario (CASCADE eliminará de auth.users también)
  DELETE FROM public.usuarios WHERE id = p_user_id;
  
  RETURN json_build_object(
    'success', true,
    'message', 'Usuario ' || COALESCE(user_name, 'desconocido') || ' eliminado exitosamente'
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Error al eliminar usuario: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FUNCIÓN PARA LISTAR USUARIOS
-- =====================================================

CREATE OR REPLACE FUNCTION list_users(
  p_limit INTEGER DEFAULT 50,
  p_offset INTEGER DEFAULT 0,
  p_rol TEXT DEFAULT NULL,
  p_activo BOOLEAN DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  users_data JSON;
  total_count INTEGER;
BEGIN
  -- Contar total de usuarios con filtros
  SELECT COUNT(*)
  INTO total_count
  FROM public.usuarios u
  JOIN auth.users au ON u.id = au.id
  WHERE (p_rol IS NULL OR u.tipo_usuario = p_rol)
    AND (p_activo IS NULL OR u.activo = p_activo);
  
  -- Obtener usuarios con filtros y paginación
  SELECT json_agg(
    json_build_object(
      'id', u.id,
      'email', au.email,
      'documento', u.documento,
      'nombre', u.nombre,
      'apellido', u.apellido,
      'tipo_usuario', u.tipo_usuario,
      'activo', u.activo,
      'fecha_creacion', u.fecha_creacion,
      'ultimo_acceso', u.ultimo_acceso
    )
  )
  INTO users_data
  FROM public.usuarios u
  JOIN auth.users au ON u.id = au.id
  WHERE (p_rol IS NULL OR u.tipo_usuario = p_rol)
    AND (p_activo IS NULL OR u.activo = p_activo)
  ORDER BY u.fecha_creacion DESC
  LIMIT p_limit
  OFFSET p_offset;
  
  RETURN json_build_object(
    'success', true,
    'users', COALESCE(users_data, '[]'::json),
    'total', total_count,
    'limit', p_limit,
    'offset', p_offset
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Error al listar usuarios: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- OTORGAR PERMISOS
-- =====================================================

-- Otorgar permisos de ejecución a los roles necesarios
GRANT EXECUTE ON FUNCTION authenticate_user(TEXT, TEXT) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION create_user(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, JSON) TO authenticated;
GRANT EXECUTE ON FUNCTION update_user(UUID, TEXT, TEXT, TEXT, TEXT, BOOLEAN, JSON) TO authenticated;
GRANT EXECUTE ON FUNCTION delete_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION list_users(INTEGER, INTEGER, TEXT, BOOLEAN) TO authenticated;

-- =====================================================
-- VERIFICACIÓN
-- =====================================================

SELECT 'Funciones de gestión de usuarios creadas exitosamente' as status;
