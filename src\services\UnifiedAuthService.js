import supabase from '../api/supabaseClient';

/**
 * Servicio de autenticación unificado para todos los roles
 * Maneja la autenticación contra la tabla central 'usuarios'
 */
class UnifiedAuthService {
  /**
   * Autentica un usuario usando email/documento y contraseña
   * @param {string} identifier - Email o documento del usuario
   * @param {string} password - Contraseña del usuario
   * @returns {Promise<Object>} Resultado de la autenticación
   */
  static async login(identifier, password) {
    try {
      console.log('🔐 Iniciando autenticación unificada...');

      const isEmail = identifier.includes('@');
      let loginData;
      let userProfile;

      if (isEmail) {
        // Login directo con email usando Supabase Auth
        console.log('📧 Autenticando con email...');
        const { data, error } = await supabase.auth.signInWithPassword({
          email: identifier.trim(),
          password: password
        });

        if (error) {
          console.error('❌ Error en autenticación con email:', error);
          return {
            success: false,
            error: this.getAuthErrorMessage(error)
          };
        }

        loginData = data;

        // Obtener perfil del usuario
        const { data: profile, error: profileError } = await supabase
          .from('usuarios')
          .select('*')
          .eq('id', data.user.id)
          .single();

        if (profileError) {
          console.error('❌ Error obteniendo perfil:', profileError);
          await supabase.auth.signOut(); // Limpiar sesión
          return {
            success: false,
            error: 'Usuario no encontrado en el sistema'
          };
        }

        userProfile = profile;

      } else {
        // Login con documento - buscar email asociado
        console.log('📄 Autenticando con documento...');
        const { data: profile, error: profileError } = await supabase
          .from('usuarios')
          .select('*')
          .eq('documento', identifier.trim())
          .single();

        if (profileError || !profile) {
          console.error('❌ Usuario no encontrado con documento:', identifier);
          return {
            success: false,
            error: 'Usuario no encontrado con ese documento'
          };
        }

        if (!profile.activo) {
          return {
            success: false,
            error: 'Usuario inactivo. Contacte al administrador'
          };
        }

        // Obtener email del usuario desde auth.users usando una consulta directa
        const { data: authUsers, error: authError } = await supabase
          .from('auth.users')
          .select('email')
          .eq('id', profile.id)
          .single();

        let userEmail;
        if (authError || !authUsers) {
          // Método alternativo: usar RPC para obtener el email
          const { data: emailData, error: emailError } = await supabase.rpc('get_user_email', {
            user_id: profile.id
          });

          if (emailError || !emailData) {
            console.error('❌ Error obteniendo email del usuario:', emailError);
            return {
              success: false,
              error: 'Error al obtener datos de autenticación'
            };
          }
          userEmail = emailData;
        } else {
          userEmail = authUsers.email;
        }

        // Hacer login con el email encontrado
        const { data, error } = await supabase.auth.signInWithPassword({
          email: userEmail,
          password: password
        });

        if (error) {
          console.error('❌ Error en autenticación con documento:', error);
          return {
            success: false,
            error: this.getAuthErrorMessage(error)
          };
        }

        loginData = data;
        userProfile = profile;
      }

      // Verificar que el usuario esté activo
      if (!userProfile.activo) {
        await supabase.auth.signOut(); // Cerrar sesión si está inactivo
        return {
          success: false,
          error: 'Usuario inactivo. Contacte al administrador'
        };
      }

      // Actualizar último acceso
      await supabase
        .from('usuarios')
        .update({ ultimo_acceso: new Date().toISOString() })
        .eq('id', userProfile.id);

      console.log('✅ Autenticación exitosa:', loginData.user.email);

      // Preparar datos del usuario
      const userData = {
        id: userProfile.id,
        email: loginData.user.email,
        documento: userProfile.documento,
        nombre: userProfile.nombre,
        apellido: userProfile.apellido,
        rol: userProfile.rol || 'candidato',
        tipo_usuario: userProfile.rol || 'candidato',
        activo: userProfile.activo
      };

      // Guardar datos de sesión
      localStorage.setItem('isLoggedIn', 'true');
      localStorage.setItem('userRole', userData.rol);
      localStorage.setItem('userData', JSON.stringify(userData));
      localStorage.setItem('userEmail', userData.email);

      return {
        success: true,
        user: userData,
        session: loginData.session,
        requirePasswordChange: false
      };

    } catch (error) {
      console.error('❌ Error de autenticación:', error);
      return {
        success: false,
        error: error.message || 'Error interno del servidor'
      };
    }
  }

  /**
   * Convierte errores de Supabase Auth en mensajes amigables
   * @param {Object} error - Error de Supabase Auth
   * @returns {string} Mensaje de error amigable
   */
  static getAuthErrorMessage(error) {
    switch (error.message) {
      case 'Invalid login credentials':
        return 'Credenciales incorrectas. Verifique su email/documento y contraseña';
      case 'Email not confirmed':
        return 'Email no confirmado. Revise su bandeja de entrada';
      case 'Too many requests':
        return 'Demasiados intentos. Intente más tarde';
      case 'User not found':
        return 'Usuario no encontrado';
      default:
        return error.message || 'Error de autenticación';
    }
  }

  /**
   * Crea un nuevo usuario en el sistema
   * @param {Object} userData - Datos del usuario
   * @returns {Promise<Object>} Resultado de la operación
   */
  static async createUser(userData) {
    try {
      console.log('👤 Creando nuevo usuario:', userData.rol);

      // Verificar que el documento no exista
      const { data: existingUser, error: checkError } = await supabase
        .from('usuarios')
        .select('id, documento')
        .eq('documento', userData.documento)
        .single();

      if (existingUser) {
        console.warn('⚠️ Usuario ya existe con documento:', userData.documento);
        return {
          success: false,
          error: 'Ya existe un usuario con ese documento'
        };
      }

      // Crear usuario en auth.users
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            nombre: userData.nombre,
            apellido: userData.apellido,
            documento: userData.documento,
            rol: userData.rol
          }
        }
      });

      if (authError) {
        console.error('❌ Error creando usuario en auth:', authError);
        return {
          success: false,
          error: 'Error al crear usuario: ' + authError.message
        };
      }

      if (!authData.user) {
        console.error('❌ No se obtuvo usuario de auth.users');
        return {
          success: false,
          error: 'Error al crear usuario en el sistema de autenticación'
        };
      }

      // Crear perfil en tabla usuarios
      const { error: profileError } = await supabase
        .from('usuarios')
        .insert({
          id: authData.user.id,
          nombre: userData.nombre,
          apellido: userData.apellido,
          documento: userData.documento,
          email: userData.email,
          rol: userData.rol,
          activo: true,
          fecha_creacion: new Date().toISOString(),
          ultimo_acceso: new Date().toISOString()
        });

      if (profileError) {
        console.error('❌ Error creando perfil:', profileError);

        // Intentar limpiar el usuario de auth si falló el perfil
        try {
          await supabase.auth.admin.deleteUser(authData.user.id);
        } catch (cleanupError) {
          console.warn('⚠️ No se pudo limpiar usuario de auth:', cleanupError);
        }

        return {
          success: false,
          error: 'Error al crear perfil de usuario: ' + profileError.message
        };
      }

      console.log('✅ Usuario creado exitosamente:', authData.user.id);
      return {
        success: true,
        userId: authData.user.id,
        message: `Usuario ${userData.nombre} ${userData.apellido} creado exitosamente`
      };

    } catch (error) {
      console.error('❌ Error al crear usuario:', error);
      return {
        success: false,
        error: error.message || 'Error interno del servidor'
      };
    }
  }

  /**
   * Actualiza un usuario existente
   * @param {string} userId - ID del usuario
   * @param {Object} updateData - Datos a actualizar
   * @returns {Promise<Object>} Resultado de la operación
   */
  static async updateUser(userId, updateData) {
    try {
      console.log('🔄 Actualizando usuario:', userId);

      // Verificar que el usuario existe
      const { data: userExists, error: checkError } = await supabase
        .from('usuarios')
        .select('id, nombre, apellido, rol')
        .eq('id', userId)
        .single();

      if (checkError || !userExists) {
        console.error('❌ Usuario no encontrado:', checkError);
        return {
          success: false,
          error: 'Usuario no encontrado'
        };
      }

      // Preparar datos de actualización
      const updateFields = {};

      if (updateData.nombre !== undefined) updateFields.nombre = updateData.nombre;
      if (updateData.apellido !== undefined) updateFields.apellido = updateData.apellido;
      if (updateData.documento !== undefined) updateFields.documento = updateData.documento;
      if (updateData.activo !== undefined) updateFields.activo = updateData.activo;
      if (updateData.rol !== undefined) updateFields.rol = updateData.rol;
      if (updateData.email !== undefined) updateFields.email = updateData.email;

      // Agregar ultimo_acceso como timestamp de actualización
      updateFields.ultimo_acceso = new Date().toISOString();

      // Actualizar en la tabla usuarios
      const { error: updateError } = await supabase
        .from('usuarios')
        .update(updateFields)
        .eq('id', userId);

      if (updateError) {
        console.error('❌ Error actualizando usuario:', updateError);
        return {
          success: false,
          error: 'Error al actualizar usuario: ' + updateError.message
        };
      }

      console.log('✅ Usuario actualizado exitosamente');
      return {
        success: true,
        message: `Usuario ${userExists.nombre} ${userExists.apellido} actualizado exitosamente`
      };

    } catch (error) {
      console.error('❌ Error al actualizar usuario:', error);
      return {
        success: false,
        error: error.message || 'Error interno del servidor'
      };
    }
  }

  /**
   * Lista usuarios con filtros y paginación
   * @param {Object} options - Opciones de filtrado
   * @returns {Promise<Object>} Lista de usuarios
   */
  static async listUsers(options = {}) {
    try {
      console.log('📋 Listando usuarios con filtros:', options);

      const { data, error } = await supabase.rpc('list_users', {
        p_limit: options.limit || 50,
        p_offset: options.offset || 0,
        p_rol: options.rol || null,
        p_activo: options.activo !== undefined ? options.activo : null
      });

      if (error) {
        console.error('❌ Error al listar usuarios:', error);
        throw new Error(error.message);
      }

      if (!data.success) {
        console.warn('⚠️ Fallo al listar usuarios:', data.error);
        return {
          success: false,
          error: data.error
        };
      }

      console.log(`✅ ${data.users.length} usuarios obtenidos de ${data.total} total`);
      return {
        success: true,
        users: data.users,
        total: data.total,
        limit: data.limit,
        offset: data.offset
      };

    } catch (error) {
      console.error('❌ Error al listar usuarios:', error);
      return {
        success: false,
        error: error.message || 'Error interno del servidor'
      };
    }
  }

  /**
   * Obtiene un usuario por ID
   * @param {string} userId - ID del usuario
   * @returns {Promise<Object>} Datos del usuario
   */
  static async getUserById(userId) {
    try {
      console.log('👤 Obteniendo usuario por ID:', userId);

      const { data, error } = await supabase.rpc('get_user_by_id', {
        p_user_id: userId
      });

      if (error) {
        console.error('❌ Error al obtener usuario:', error);
        throw new Error(error.message);
      }

      if (!data.success) {
        console.warn('⚠️ Usuario no encontrado:', data.error);
        return {
          success: false,
          error: data.error
        };
      }

      console.log('✅ Usuario obtenido exitosamente');
      return {
        success: true,
        user: data.user
      };

    } catch (error) {
      console.error('❌ Error al obtener usuario:', error);
      return {
        success: false,
        error: error.message || 'Error interno del servidor'
      };
    }
  }

  /**
   * Establece una contraseña temporal para un usuario
   * @param {string} userId - ID del usuario
   * @param {string} tempPassword - Contraseña temporal
   * @returns {Promise<Object>} Resultado de la operación
   */
  static async setTempPassword(userId, tempPassword) {
    try {
      console.log('🔑 Estableciendo contraseña temporal para usuario:', userId);

      const { data, error } = await supabase.rpc('set_user_temp_password', {
        p_user_id: userId,
        temp_password: tempPassword
      });

      if (error) {
        console.error('❌ Error al establecer contraseña temporal:', error);
        throw new Error(error.message);
      }

      if (!data.success) {
        console.warn('⚠️ Fallo al establecer contraseña temporal:', data.error);
        return {
          success: false,
          error: data.error
        };
      }

      console.log('✅ Contraseña temporal establecida exitosamente');
      return {
        success: true,
        message: data.message
      };

    } catch (error) {
      console.error('❌ Error al establecer contraseña temporal:', error);
      return {
        success: false,
        error: error.message || 'Error interno del servidor'
      };
    }
  }

  /**
   * Cambia la contraseña de un usuario
   * @param {string} userId - ID del usuario
   * @param {string} oldPassword - Contraseña actual
   * @param {string} newPassword - Nueva contraseña
   * @returns {Promise<Object>} Resultado de la operación
   */
  static async changePassword(userId, oldPassword, newPassword) {
    try {
      console.log('🔄 Cambiando contraseña para usuario:', userId);

      const { data, error } = await supabase.rpc('change_user_password', {
        p_user_id: userId,
        old_password: oldPassword,
        new_password: newPassword
      });

      if (error) {
        console.error('❌ Error al cambiar contraseña:', error);
        throw new Error(error.message);
      }

      if (!data.success) {
        console.warn('⚠️ Fallo al cambiar contraseña:', data.error);
        return {
          success: false,
          error: data.error
        };
      }

      console.log('✅ Contraseña cambiada exitosamente');
      
      // Actualizar datos en localStorage si es necesario
      const userData = this.getCurrentUser();
      if (userData && userData.id === userId) {
        userData.require_password_change = false;
        userData.has_temp_password = false;
        localStorage.setItem('userData', JSON.stringify(userData));
      }

      return {
        success: true,
        message: data.message
      };

    } catch (error) {
      console.error('❌ Error al cambiar contraseña:', error);
      return {
        success: false,
        error: error.message || 'Error interno del servidor'
      };
    }
  }

  /**
   * Obtiene los datos del usuario actual desde localStorage
   * @returns {Object|null} Datos del usuario o null si no está logueado
   */
  static getCurrentUser() {
    try {
      const userData = localStorage.getItem('userData');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('❌ Error al obtener datos del usuario:', error);
      return null;
    }
  }

  /**
   * Verifica si hay un usuario logueado
   * @returns {boolean} True si hay un usuario logueado
   */
  static isLoggedIn() {
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    const userData = this.getCurrentUser();
    
    return isLoggedIn && userData !== null;
  }

  /**
   * Verifica si el usuario actual tiene un rol específico
   * @param {string} role - Rol a verificar
   * @returns {boolean} True si el usuario tiene el rol especificado
   */
  static hasRole(role) {
    const userData = this.getCurrentUser();
    return userData && userData.rol === role;
  }

  /**
   * Cierra la sesión del usuario
   */
  static logout() {
    console.log('🚪 Cerrando sesión de usuario');
    
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('userRole');
    localStorage.removeItem('userData');
    localStorage.removeItem('userEmail');
    
    console.log('✅ Sesión cerrada');
  }

  /**
   * Elimina un usuario del sistema
   * @param {string} userId - ID del usuario a eliminar
   * @returns {Promise<Object>} Resultado de la operación
   */
  static async deleteUser(userId) {
    try {
      console.log('🗑️ Eliminando usuario:', userId);

      // Verificar que el usuario existe
      const { data: userExists, error: checkError } = await supabase
        .from('usuarios')
        .select('id, nombre, apellido, rol, activo')
        .eq('id', userId)
        .single();

      if (checkError || !userExists) {
        console.error('❌ Usuario no encontrado:', checkError);
        return {
          success: false,
          error: 'Usuario no encontrado'
        };
      }

      // Verificar que no es el último administrador activo
      const userRole = userExists.rol;
      if (userRole === 'administrador') {
        const { data: adminCount, error: countError } = await supabase
          .from('usuarios')
          .select('id', { count: 'exact' })
          .eq('rol', 'administrador')
          .eq('activo', true)
          .neq('id', userId);

        if (countError) {
          console.error('❌ Error verificando administradores:', countError);
          return {
            success: false,
            error: 'Error verificando permisos'
          };
        }

        if (adminCount.length === 0) {
          console.warn('⚠️ Intento de eliminar último administrador');
          return {
            success: false,
            error: 'No se puede eliminar el último administrador activo'
          };
        }
      }

      // Eliminar usuario de la tabla usuarios
      const { error: deleteError } = await supabase
        .from('usuarios')
        .delete()
        .eq('id', userId);

      if (deleteError) {
        console.error('❌ Error eliminando usuario:', deleteError);
        return {
          success: false,
          error: 'Error al eliminar usuario: ' + deleteError.message
        };
      }

      console.log('✅ Usuario eliminado exitosamente');
      return {
        success: true,
        message: `Usuario ${userExists.nombre} ${userExists.apellido} eliminado exitosamente`
      };

    } catch (error) {
      console.error('❌ Error al eliminar usuario:', error);
      return {
        success: false,
        error: error.message || 'Error interno del servidor'
      };
    }
  }

  /**
   * Genera una contraseña temporal aleatoria
   * @param {number} length - Longitud de la contraseña (por defecto 8)
   * @returns {string} Contraseña temporal generada
   */
  static generateTempPassword(length = 8) {
    const charset = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
    let password = '';

    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    return password;
  }

  /**
   * Valida el formato de una contraseña
   * @param {string} password - Contraseña a validar
   * @returns {Object} Resultado de la validación
   */
  static validatePassword(password) {
    const errors = [];
    
    if (!password || password.length < 6) {
      errors.push('La contraseña debe tener al menos 6 caracteres');
    }
    
    if (password.length > 50) {
      errors.push('La contraseña no puede tener más de 50 caracteres');
    }
    
    if (!/[A-Za-z]/.test(password)) {
      errors.push('La contraseña debe contener al menos una letra');
    }
    
    if (!/[0-9]/.test(password)) {
      errors.push('La contraseña debe contener al menos un número');
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
}

export default UnifiedAuthService;
