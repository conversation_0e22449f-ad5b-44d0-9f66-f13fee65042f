/**
 * @file AptitudeConfigService.js
 * @description Centralized configuration for BAT-7 aptitudes and scoring
 */

import { APTITUDE_CONFIG, PERCENTIL_LEVELS, getAptitudeConfig, getPercentilLevel } from '../constants/aptitudeConstants';

class AptitudeConfigService {
  /**
   * Aptitude configuration mapping - usando configuración centralizada
   */
  static APTITUDE_CONFIG = APTITUDE_CONFIG;


  /**
   * Percentile level configuration - usando configuración centralizada
   */
  static PERCENTILE_LEVELS = PERCENTIL_LEVELS;

  /**
   * Get aptitude configuration by code - usando función centralizada
   * @param {string} code - Aptitude code
   * @returns {Object} Aptitude configuration
   */
  static getAptitudeConfig(code) {
    return getAptitudeConfig(code);
  }

  /**
   * Get percentile level configuration - usando función centralizada
   * @param {number} percentile - Percentile score
   * @returns {Object} Level configuration
   */
  static getPercentileLevel(percentile) {
    return getPercentilLevel(percentile);
  }

  /**
   * Get all aptitude codes
   * @returns {Array<string>} Array of aptitude codes
   */
  static getAllAptitudeCodes() {
    return Object.keys(this.APTITUDE_CONFIG);
  }

  /**
   * Get aptitudes for intelligence indices calculation
   * @returns {Object} Grouped aptitudes for intelligence calculation
   */
  static getIntelligenceGroupings() {
    return {
      fluid: ['E', 'R'], // Spatial and Reasoning
      crystallized: ['V', 'O'], // Verbal and Spelling
      processing: ['A', 'CON'], // Attention and Concentration
      quantitative: ['N', 'M'] // Numerical and Mechanical
    };
  }

  /**
   * Calculate intelligence indices from results
   * @param {Array} results - Test results array
   * @returns {Object} Intelligence indices
   */
  static calculateIntelligenceIndices(results) {
    if (!results || results.length === 0) {
      return {
        capacidadGeneral: 0,
        inteligenciaFluida: 0,
        inteligenciaCristalizada: 0,
        procesamientoCognitivo: 0,
        aptitudCuantitativa: 0
      };
    }

    const groupings = this.getIntelligenceGroupings();
    
    // Calculate general capacity (g) as average of all percentiles
    const allPercentiles = results.map(r => r.percentil || r.puntaje_pc || 0);
    const capacidadGeneral = Math.round(
      allPercentiles.reduce((sum, p) => sum + p, 0) / allPercentiles.length
    );

    // Calculate specific intelligence indices
    const calculateGroupAverage = (codes) => {
      const groupResults = results.filter(r => 
        codes.includes(r.aptitudes?.codigo || r.test || r.aptitud)
      );
      if (groupResults.length === 0) return capacidadGeneral;
      
      const percentiles = groupResults.map(r => r.percentil || r.puntaje_pc || 0);
      return Math.round(percentiles.reduce((sum, p) => sum + p, 0) / percentiles.length);
    };

    return {
      capacidadGeneral,
      inteligenciaFluida: calculateGroupAverage(groupings.fluid),
      inteligenciaCristalizada: calculateGroupAverage(groupings.crystallized),
      procesamientoCognitivo: calculateGroupAverage(groupings.processing),
      aptitudCuantitativa: calculateGroupAverage(groupings.quantitative)
    };
  }
}

export default AptitudeConfigService;