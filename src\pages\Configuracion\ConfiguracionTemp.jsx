import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '../../context/SimpleAuthContextTemp';
import {
  FaChartLine,
  FaUsers,
  FaShieldAlt,
  FaUserMd,
  FaChartBar,
  FaCog,
  FaFlask
} from 'react-icons/fa';
import PageHeader from '../../components/ui/PageHeader';

// Importaciones de los módulos de administración
import AdminDashboard from '../../components/admin/AdminDashboard';
import SimpleUserManagementPanel from '../../components/admin/SimpleUserManagementPanel';
import PageAccessPanel from '../../components/admin/PageAccessPanel';
import PatientAssignmentPanel from '../../components/admin/PatientAssignmentPanel';
import UsageControlPanel from '../../components/admin/UsageControlPanelComplete';

// TestPinSystem component removed - using real data only
import SimpleUserSettings from '../../components/settings/SimpleUserSettings';

const Configuracion = () => {
  const { user, userRole } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');

  // Verificar si el usuario es administrador
  const isAdmin = userRole === 'administrador' || user?.tipo_usuario === 'administrador';

  // Configuración de pestañas
  const tabs = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: FaChartLine,
      component: AdminDashboard,
      adminOnly: true,
      description: 'Resumen general del sistema'
    },
    {
      id: 'users',
      name: 'Gestión de Usuarios',
      icon: FaUsers,
      component: SimpleUserManagementPanel,
      adminOnly: true,
      description: 'Administra usuarios del sistema'
    },
    {
      id: 'access',
      name: 'Control de Acceso',
      icon: FaShieldAlt,
      component: PageAccessPanel,
      adminOnly: true,
      description: 'Gestiona permisos y accesos'
    },
    {
      id: 'patients',
      name: 'Asignación de Pacientes',
      icon: FaUserMd,
      component: PatientAssignmentPanel,
      adminOnly: true,
      description: 'Asigna pacientes a psicólogos'
    },
    {
      id: 'pins',
      name: 'Control de Pines',
      icon: FaChartBar,
      component: UsageControlPanel,
      adminOnly: true,
      description: 'Gestiona el sistema de pines'
    },
    {
      id: 'settings',
      name: 'Configuración Personal',
      icon: FaCog,
      component: SimpleUserSettings,
      adminOnly: false,
      description: 'Configuración de usuario'
    }
  ];

  // Filtrar pestañas según permisos
  const availableTabs = tabs.filter(tab => !tab.adminOnly || isAdmin);

  const renderTabContent = () => {
    const currentTab = tabs.find(tab => tab.id === activeTab);

    if (!currentTab) {
      return <div>Pestaña no encontrada</div>;
    }

    // Verificar permisos
    if (currentTab.adminOnly && !isAdmin) {
      return (
        <div className="text-center py-12">
          <FaShieldAlt className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Acceso Restringido</h3>
          <p className="mt-1 text-sm text-gray-500">
            No tienes permisos para acceder a esta sección.
          </p>
        </div>
      );
    }

    const Component = currentTab.component;
    return <Component />;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader
        title="Configuración del Sistema"
        subtitle="Administra usuarios, permisos y configuraciones generales"
        icon={FaCog}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navegación de pestañas */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              {availableTabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Contenido de la pestaña activa */}
          <div className="p-6">
            {renderTabContent()}
          </div>
        </div>

        {/* Información del sistema eliminada - No es necesaria según solicitud del usuario */}
      </div>
    </div>
  );
};

export default Configuracion;
