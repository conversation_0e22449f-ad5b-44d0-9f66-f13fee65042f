import React, { useState, useEffect } from 'react';
import { supabase } from '../../api/supabaseClient';
import supabaseService from '../../services/supabaseService';

/**
 * Componente para probar la conexión a Supabase y la carga de psicólogos
 */
const SupabaseConnectionTest = () => {
  const [connectionStatus, setConnectionStatus] = useState('testing');
  const [directQuery, setDirectQuery] = useState({ loading: false, data: null, error: null });
  const [serviceQuery, setServiceQuery] = useState({ loading: false, data: null, error: null });

  // Test de conexión básica
  const testConnection = async () => {
    try {
      setConnectionStatus('testing');
      const { data, error } = await supabase.from('psicologos').select('count', { count: 'exact', head: true });
      
      if (error) {
        console.error('❌ Error de conexión:', error);
        setConnectionStatus('error');
      } else {
        console.log('✅ Conexión exitosa, total de registros:', data);
        setConnectionStatus('connected');
      }
    } catch (error) {
      console.error('❌ Error de conexión:', error);
      setConnectionStatus('error');
    }
  };

  // Test de consulta directa
  const testDirectQuery = async () => {
    try {
      setDirectQuery({ loading: true, data: null, error: null });
      
      const { data, error } = await supabase
        .from('psicologos')
        .select('*')
        .limit(5);
      
      if (error) {
        setDirectQuery({ loading: false, data: null, error: error.message });
      } else {
        setDirectQuery({ loading: false, data, error: null });
      }
    } catch (error) {
      setDirectQuery({ loading: false, data: null, error: error.message });
    }
  };

  // Test del servicio
  const testService = async () => {
    try {
      setServiceQuery({ loading: true, data: null, error: null });
      
      const { data, error } = await supabaseService.getPsychologists();
      
      if (error) {
        setServiceQuery({ loading: false, data: null, error: error.message });
      } else {
        setServiceQuery({ loading: false, data, error: null });
      }
    } catch (error) {
      setServiceQuery({ loading: false, data: null, error: error.message });
    }
  };

  useEffect(() => {
    testConnection();
    testDirectQuery();
    testService();
  }, []);

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Test de Conexión a Supabase</h2>
      
      {/* Estado de conexión */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Estado de Conexión</h3>
        <div className={`p-3 rounded ${
          connectionStatus === 'connected' ? 'bg-green-100 text-green-800' :
          connectionStatus === 'error' ? 'bg-red-100 text-red-800' :
          'bg-yellow-100 text-yellow-800'
        }`}>
          {connectionStatus === 'connected' && '✅ Conectado a Supabase'}
          {connectionStatus === 'error' && '❌ Error de conexión'}
          {connectionStatus === 'testing' && '🔄 Probando conexión...'}
        </div>
      </div>

      {/* Consulta directa */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Consulta Directa a Supabase</h3>
        {directQuery.loading && <p className="text-blue-600">Cargando...</p>}
        {directQuery.error && <p className="text-red-600">Error: {directQuery.error}</p>}
        {directQuery.data && (
          <div>
            <p className="mb-2">Registros encontrados: {directQuery.data.length}</p>
            <div className="bg-gray-100 p-4 rounded max-h-40 overflow-y-auto">
              <pre>{JSON.stringify(directQuery.data, null, 2)}</pre>
            </div>
          </div>
        )}
      </div>

      {/* Consulta del servicio */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Consulta del Servicio</h3>
        {serviceQuery.loading && <p className="text-blue-600">Cargando...</p>}
        {serviceQuery.error && <p className="text-red-600">Error: {serviceQuery.error}</p>}
        {serviceQuery.data && (
          <div>
            <p className="mb-2">Registros encontrados: {serviceQuery.data.length}</p>
            <div className="bg-gray-100 p-4 rounded max-h-40 overflow-y-auto">
              <pre>{JSON.stringify(serviceQuery.data, null, 2)}</pre>
            </div>
          </div>
        )}
      </div>

      {/* Botones de recarga */}
      <div className="flex space-x-4">
        <button
          onClick={testConnection}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Probar Conexión
        </button>
        <button
          onClick={testDirectQuery}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
        >
          Consulta Directa
        </button>
        <button
          onClick={testService}
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
        >
          Consulta del Servicio
        </button>
      </div>
    </div>
  );
};

export default SupabaseConnectionTest;
