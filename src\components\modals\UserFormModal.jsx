import React, { useState, useEffect } from 'react';
import { FaEye, FaEyeSlash, FaTimes } from 'react-icons/fa';
import { 
  USER_ROLES, 
  ROLE_LABELS, 
  VALIDATION_CONFIG, 
  ERROR_MESSAGES,
  FORM_CONFIG 
} from '../../constants/userManagement';

/**
 * Modal reutilizable para crear y editar usuarios
 */
const UserFormModal = ({ 
  isOpen, 
  onClose, 
  onSubmit, 
  title, 
  initialData = null, 
  loading = false 
}) => {
  // Estados del formulario
  const [formData, setFormData] = useState(FORM_CONFIG.INITIAL_USER_DATA);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({});

  // Cargar datos iniciales cuando se abre el modal
  useEffect(() => {
    if (isOpen) {
      if (initialData) {
        // Modo edición
        setFormData({
          ...initialData,
          password: '',
          confirmPassword: ''
        });
      } else {
        // Modo creación
        setFormData(FORM_CONFIG.INITIAL_USER_DATA);
      }
      setErrors({});
    }
  }, [isOpen, initialData]);

  // Función para validar el formulario
  const validateForm = () => {
    const newErrors = {};

    // Validar campos requeridos
    if (!formData.nombre.trim()) {
      newErrors.nombre = 'El nombre es requerido';
    } else if (formData.nombre.length > VALIDATION_CONFIG.MAX_NAME_LENGTH) {
      newErrors.nombre = `El nombre no puede exceder ${VALIDATION_CONFIG.MAX_NAME_LENGTH} caracteres`;
    }

    if (!formData.apellido.trim()) {
      newErrors.apellido = 'El apellido es requerido';
    } else if (formData.apellido.length > VALIDATION_CONFIG.MAX_NAME_LENGTH) {
      newErrors.apellido = `El apellido no puede exceder ${VALIDATION_CONFIG.MAX_NAME_LENGTH} caracteres`;
    }

    if (!formData.email.trim()) {
      newErrors.email = 'El email es requerido';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'El email no tiene un formato válido';
    } else if (formData.email.length > VALIDATION_CONFIG.MAX_EMAIL_LENGTH) {
      newErrors.email = `El email no puede exceder ${VALIDATION_CONFIG.MAX_EMAIL_LENGTH} caracteres`;
    }

    // Validar contraseña (solo en modo creación o si se está cambiando)
    if (!initialData || formData.password) {
      if (!formData.password) {
        newErrors.password = 'La contraseña es requerida';
      } else if (formData.password.length < VALIDATION_CONFIG.MIN_PASSWORD_LENGTH) {
        newErrors.password = `La contraseña debe tener al menos ${VALIDATION_CONFIG.MIN_PASSWORD_LENGTH} caracteres`;
      }

      // Validar confirmación de contraseña
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = ERROR_MESSAGES.PASSWORDS_DONT_MATCH;
      }
    }

    // Validar documento (opcional pero con límite)
    if (formData.documento && formData.documento.length > VALIDATION_CONFIG.MAX_DOCUMENT_LENGTH) {
      newErrors.documento = `El documento no puede exceder ${VALIDATION_CONFIG.MAX_DOCUMENT_LENGTH} caracteres`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Manejar cambios en los campos
  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Limpiar error del campo cuando el usuario empiece a escribir
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  // Manejar envío del formulario
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  // Manejar cierre del modal
  const handleClose = () => {
    setFormData(FORM_CONFIG.INITIAL_USER_DATA);
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  const isEditMode = !!initialData;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div className="mt-3">
          {/* Header */}
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              {title}
            </h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <FaTimes className="w-5 h-5" />
            </button>
          </div>

          {/* Formulario */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleChange('email', e.target.value)}
                disabled={isEditMode}
                className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isEditMode 
                    ? 'bg-gray-100 text-gray-500 border-gray-300' 
                    : 'border-gray-300'
                } ${errors.email ? 'border-red-500' : ''}`}
                placeholder="<EMAIL>"
              />
              {isEditMode && (
                <p className="text-xs text-gray-500 mt-1">El email no se puede modificar</p>
              )}
              {errors.email && (
                <p className="text-xs text-red-600 mt-1">{errors.email}</p>
              )}
            </div>

            {/* Contraseña */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {isEditMode ? 'Nueva Contraseña (opcional)' : 'Contraseña *'}
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => handleChange('password', e.target.value)}
                  className={`w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.password ? 'border-red-500' : ''
                  }`}
                  placeholder={isEditMode ? 'Dejar vacío para no cambiar' : 'Mínimo 6 caracteres'}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <FaEyeSlash className="w-4 h-4" /> : <FaEye className="w-4 h-4" />}
                </button>
              </div>
              {errors.password && (
                <p className="text-xs text-red-600 mt-1">{errors.password}</p>
              )}
            </div>

            {/* Confirmar contraseña */}
            {(!isEditMode || formData.password) && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Confirmar Contraseña *
                </label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={(e) => handleChange('confirmPassword', e.target.value)}
                    className={`w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.confirmPassword ? 'border-red-500' : ''
                    }`}
                    placeholder="Confirma la contraseña"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                  >
                    {showConfirmPassword ? <FaEyeSlash className="w-4 h-4" /> : <FaEye className="w-4 h-4" />}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-xs text-red-600 mt-1">{errors.confirmPassword}</p>
                )}
              </div>
            )}

            {/* Nombre y Apellido */}
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nombre *
                </label>
                <input
                  type="text"
                  value={formData.nombre}
                  onChange={(e) => handleChange('nombre', e.target.value)}
                  className={`w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.nombre ? 'border-red-500' : ''
                  }`}
                  placeholder="Nombre"
                />
                {errors.nombre && (
                  <p className="text-xs text-red-600 mt-1">{errors.nombre}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Apellido *
                </label>
                <input
                  type="text"
                  value={formData.apellido}
                  onChange={(e) => handleChange('apellido', e.target.value)}
                  className={`w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.apellido ? 'border-red-500' : ''
                  }`}
                  placeholder="Apellido"
                />
                {errors.apellido && (
                  <p className="text-xs text-red-600 mt-1">{errors.apellido}</p>
                )}
              </div>
            </div>

            {/* Documento */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Documento
              </label>
              <input
                type="text"
                value={formData.documento}
                onChange={(e) => handleChange('documento', e.target.value)}
                className={`w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.documento ? 'border-red-500' : ''
                }`}
                placeholder="Número de documento"
              />
              {errors.documento && (
                <p className="text-xs text-red-600 mt-1">{errors.documento}</p>
              )}
            </div>

            {/* Rol */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Rol *
              </label>
              <select
                value={formData.rol}
                onChange={(e) => handleChange('rol', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {Object.entries(USER_ROLES).map(([key, value]) => (
                  <option key={key} value={value}>
                    {ROLE_LABELS[value]}
                  </option>
                ))}
              </select>
            </div>

            {/* Estado activo */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="activo"
                checked={formData.activo}
                onChange={(e) => handleChange('activo', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="activo" className="ml-2 text-sm text-gray-700">
                Usuario activo
              </label>
            </div>

            {/* Botones */}
            <div className="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                onClick={handleClose}
                disabled={loading}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:opacity-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center space-x-2"
              >
                {loading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                )}
                <span>
                  {loading 
                    ? (isEditMode ? 'Guardando...' : 'Creando...') 
                    : (isEditMode ? 'Guardar Cambios' : 'Crear Usuario')
                  }
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UserFormModal;
