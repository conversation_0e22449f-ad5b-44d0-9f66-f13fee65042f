/**
 * Script para probar las interpretaciones desde Supabase
 */

import { createClient } from '@supabase/supabase-js';

// Configuración directa de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseKey);

// Función para obtener nivel por percentil
function obtenerNivelPorPercentil(percentil) {
  if (percentil > 95) return { id: 7, nombre: 'Muy Alto', rango: 'Percentil > 95' };
  if (percentil >= 81) return { id: 6, nombre: 'Alto', rango: 'Percentil 81 - 95' };
  if (percentil >= 61) return { id: 5, nombre: 'Medio-Alto', rango: 'Percentil 61 - 80' };
  if (percentil >= 41) return { id: 4, nombre: 'Medio', rango: 'Percentil 41 - 60' };
  if (percentil >= 21) return { id: 3, nombre: 'Medio-Bajo', rango: 'Percentil 21 - 40' };
  if (percentil >= 6) return { id: 2, nombre: 'Bajo', rango: 'Percentil 6 - 20' };
  return { id: 1, nombre: 'Muy Bajo', rango: 'Percentil ≤ 5' };
}

// Función para obtener nombre de aptitud
function getNombreAptitud(codigo) {
  const nombres = {
    'V': 'Aptitud Verbal',
    'E': 'Aptitud Espacial',
    'A': 'Atención',
    'R': 'Razonamiento',
    'N': 'Aptitud Numérica',
    'M': 'Aptitud Mecánica',
    'O': 'Ortografía'
  };
  return nombres[codigo] || 'Aptitud Desconocida';
}

// Función para obtener interpretación oficial
async function obtenerInterpretacionOficial(aptitudCodigo, percentil) {
  try {
    console.log(`🔍 Obteniendo interpretación para ${aptitudCodigo}-${percentil}`);
    
    // Determinar el nivel basado en el percentil
    const nivel = obtenerNivelPorPercentil(percentil);
    
    // Obtener interpretación directamente de la tabla
    const { data, error } = await supabase
      .from('interpretaciones_oficiales')
      .select('*')
      .eq('aptitud_codigo', aptitudCodigo)
      .eq('nivel_id', nivel.id)
      .eq('es_oficial', true)
      .single();

    if (error) {
      console.error('❌ Error obteniendo interpretación:', error);
      return null;
    }

    if (!data) {
      console.warn(`⚠️ No se encontró interpretación para ${aptitudCodigo}-${percentil} (nivel ${nivel.id})`);
      return null;
    }

    console.log(`✅ Interpretación encontrada para ${aptitudCodigo}-${percentil}`);

    return {
      aptitud_codigo: data.aptitud_codigo,
      aptitud_nombre: getNombreAptitud(data.aptitud_codigo),
      nivel_id: data.nivel_id,
      nivel_nombre: nivel.nombre,
      percentil_rango: nivel.rango,
      percentil_valor: percentil,
      rendimiento: data.rendimiento,
      academico: data.academico,
      vocacional: data.vocacional,
      fuente: 'Supabase - Oficial'
    };

  } catch (error) {
    console.error('💥 Error en obtenerInterpretacionOficial:', error);
    return null;
  }
}

// Casos de prueba
const testCases = [
  { aptitud: 'V', percentil: 5 },   // Muy Bajo
  { aptitud: 'V', percentil: 50 },  // Medio
  { aptitud: 'R', percentil: 5 },   // Muy Bajo
  { aptitud: 'R', percentil: 70 },  // Medio-Alto
  { aptitud: 'C', percentil: 1 },   // Concentración Muy Bajo
  { aptitud: 'C', percentil: 30 },  // Concentración Medio-Bajo
  { aptitud: 'C', percentil: 50 },  // Concentración Medio
  { aptitud: 'N', percentil: 15 },  // Bajo (no debería existir)
  { aptitud: 'O', percentil: 85 },  // Alto (no debería existir)
  { aptitud: 'A', percentil: 90 }   // Alto (no debería existir)
];

async function runTests() {
  console.log('🧪 Iniciando pruebas de interpretaciones...\n');
  
  for (const testCase of testCases) {
    console.log(`\n📋 Probando ${testCase.aptitud} - Percentil ${testCase.percentil}`);
    
    const interpretacion = await obtenerInterpretacionOficial(testCase.aptitud, testCase.percentil);
    
    if (interpretacion) {
      console.log(`   ✅ Nivel: ${interpretacion.nivel_nombre}`);
      console.log(`   📝 Rendimiento: ${interpretacion.rendimiento.substring(0, 100)}...`);
      console.log(`   🎓 Académico: ${interpretacion.academico.substring(0, 100)}...`);
      console.log(`   💼 Vocacional: ${interpretacion.vocacional.substring(0, 100)}...`);
    } else {
      console.log(`   ❌ No se pudo obtener interpretación`);
    }
  }
  
  console.log('\n🎉 Pruebas completadas');
}

// Ejecutar pruebas
runTests().catch(error => {
  console.error('💥 Error en las pruebas:', error);
});
