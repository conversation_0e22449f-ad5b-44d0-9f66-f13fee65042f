import{j as e,a7 as s,a8 as a,O as t,Z as l,r,a9 as n,h as i,z as o,aa as c,ab as d,ac as x,ad as m,F as p,ae as u,af as h,ag as g}from"./vendor-BqMjyOVw.js";import{s as b,C as j,b as N,a as f,e as v}from"./index-Bdl1jgS_.js";const y=({patient:l})=>{var r,n,i;const o=l.edad||(e=>{if(!e)return null;const s=new Date,a=new Date(e);if(isNaN(a.getTime()))return null;let t=s.getFullYear()-a.getFullYear();const l=s.getMonth()-a.getMonth();return(l<0||0===l&&s.getDate()<a.getDate())&&t--,t>=0?t:null})(l.fecha_nacimiento);return e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden patient-card",children:e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"patient-avatar "+("masculino"===(null==(r=l.genero)?void 0:r.toLowerCase())?"patient-avatar-male":"femenino"===(null==(n=l.genero)?void 0:n.toLowerCase())?"patient-avatar-female":"patient-avatar-other"),children:((e,s)=>{if(!e)return"";const a=e.charAt(0).toUpperCase(),t=s?s.charAt(0).toUpperCase():"";return t?`${a}${t}`:a})(l.nombre,l.apellido||l.apellidos)}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"patient-name",children:`${l.nombre||""} ${l.apellido||l.apellidos||""}`.trim()}),e.jsx("p",{className:"patient-education",children:l.nivel_educativo?l.nivel_educativo:l.grado||"4° Medio"})]})]}),e.jsxs("div",{className:"patient-info-grid",children:[e.jsxs("div",{children:[e.jsx("span",{className:"patient-info-label",children:"Edad:"})," ",o?`${o} años`:"No disponible"]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"patient-info-label",children:"Sexo:"}),e.jsxs("span",{className:"flex items-center",children:[(()=>{const r=l.genero?l.genero.toLowerCase():"";return"masculino"===r?e.jsx(s,{className:"text-blue-600"}):"femenino"===r?e.jsx(a,{className:"text-pink-600"}):e.jsx(t,{className:"text-gray-600"})})(),e.jsx("span",{className:"ml-1",children:l.genero||"No especificado"})]})]})]}),e.jsx("div",{className:"patient-psychologist",children:l.psicologo_id?e.jsxs("span",{children:["Psicólogo: ",(null==(i=l.psicologo)?void 0:i.nombre)||"Asignado"]}):e.jsx("span",{children:"Sin psicólogo asignado"})})]})})};y.propTypes={patient:l.shape({id:l.string,nombre:l.string.isRequired,apellido:l.string,apellidos:l.string,fecha_nacimiento:l.string,genero:l.string,edad:l.number,nivel_educativo:l.string,grado:l.string,psicologo_id:l.string,psicologo:l.object}).isRequired};const w=({patients:s,loading:a,onSelectPatient:t})=>a?null:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Nombre"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Documento"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Género"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fecha Nacimiento"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Psicólogo"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acciones"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(s=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"flex items-center",children:e.jsx("div",{className:"ml-4",children:e.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[s.nombre," ",s.apellidos]})})})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:s.documento_identidad})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:s.genero})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:new Date(s.fecha_nacimiento).toLocaleDateString()})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:s.psicologo?`${s.psicologo.nombre} ${s.psicologo.apellido}`:"No asignado"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsx("button",{onClick:()=>t(s),className:"text-blue-600 hover:text-blue-900",children:"Ver detalles"})})]},s.id))})]})}),k=()=>{const[s,a]=r.useState([]),[l,k]=r.useState([]),[C,S]=r.useState(!0),[_,$]=r.useState(0),[F,P]=r.useState(1),[D,L]=r.useState(12),[A,M]=r.useState(""),[q,O]=r.useState("asc"),[T,V]=r.useState("nombre"),[z,E]=r.useState(""),[R,U]=r.useState("grid"),Y=r.useCallback(()=>{return e=null,s=null,t=function*(){try{S(!0);let e=b.from("pacientes").select("\n          *,\n          psicologo:psicologo_id (\n            id, nombre, apellido\n          )\n        ",{count:"exact"});A&&(e=e.or(`nombre.ilike.%${A}%,apellidos.ilike.%${A}%,documento_identidad.ilike.%${A}%`)),z&&(e=e.eq("genero",z)),e=e.order(T,{ascending:"asc"===q});const s=(F-1)*D,t=s+D-1,{data:l,error:r,count:n}=yield e.range(s,t);if(r)throw r;a(l||[]),k(l||[]),$(n||0)}catch(e){}finally{S(!1)}},new Promise((a,l)=>{var r=e=>{try{i(t.next(e))}catch(s){l(s)}},n=e=>{try{i(t.throw(e))}catch(s){l(s)}},i=e=>e.done?a(e.value):Promise.resolve(e.value).then(r,n);i((t=t.apply(e,s)).next())});var e,s,t},[A,z,q,T,F,D]);r.useEffect(()=>{Y()},[Y]);const B=r.useCallback(n.debounce(e=>{M(e),P(1)},500),[]),G=()=>{M(""),E(""),V("nombre"),O("asc"),P(1);const e=document.querySelector('input[type="text"]');e&&(e.value="")},Z=()=>{window.scrollTo({top:0,behavior:"smooth"})},H=e=>{P(e),Z()},I=Math.ceil(_/D),J=e=>{};return e.jsxs("div",{className:"container mx-auto py-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"inline-flex items-center justify-center mb-3",children:[e.jsx("div",{className:"bg-yellow-400 p-3 rounded-full mr-3 shadow-md",children:e.jsx(t,{className:"text-white text-2xl"})}),e.jsx("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-800 to-indigo-950 bg-clip-text text-transparent",children:"Pacientes"})]}),e.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Lista de pacientes registrados en el sistema para evaluaciones psicométricas"})]})}),e.jsx("div",{className:"mb-8 bg-white p-4 rounded-lg shadow-sm",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsxs("div",{className:"relative flex-grow",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(i,{className:"text-blue-400"})}),e.jsx("input",{type:"text",className:"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 shadow-sm",placeholder:"Buscar paciente por nombre, apellido o documento...",onChange:e=>{const s=e.target.value;e.target.value=s,B(s)}})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsxs("div",{className:"relative inline-block",children:[e.jsxs("select",{className:"appearance-none pl-8 pr-4 py-2 border border-gray-200 rounded-lg bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 shadow-sm",value:z,onChange:e=>{E(e.target.value),P(1)},children:[e.jsx("option",{value:"",children:"Todos los géneros"}),e.jsx("option",{value:"Masculino",children:"Masculino"}),e.jsx("option",{value:"Femenino",children:"Femenino"}),e.jsx("option",{value:"Otro",children:"Otro"})]}),e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(o,{className:"text-blue-400"})})]}),e.jsxs("div",{className:"relative inline-block",children:[e.jsxs("select",{className:"appearance-none pl-8 pr-4 py-2 border border-gray-200 rounded-lg bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 shadow-sm",value:T,onChange:e=>{V(e.target.value),P(1)},children:[e.jsx("option",{value:"nombre",children:"Nombre"}),e.jsx("option",{value:"apellidos",children:"Apellidos"}),e.jsx("option",{value:"fecha_nacimiento",children:"Fecha de nacimiento"}),e.jsx("option",{value:"documento_identidad",children:"Documento"})]}),e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(o,{className:"text-blue-400"})})]}),e.jsxs("button",{className:"flex items-center px-4 py-2 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm",onClick:()=>{O(e=>"asc"===e?"desc":"asc"),P(1)},children:["asc"===q?e.jsx(c,{className:"mr-2 text-blue-500"}):e.jsx(d,{className:"mr-2 text-blue-500"}),e.jsx("span",{children:"Ordenar"})]}),e.jsx("button",{className:"flex items-center px-4 py-2 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm",onClick:()=>U("grid"===R?"table":"grid"),children:"grid"===R?e.jsxs(e.Fragment,{children:[e.jsx(x,{className:"mr-2 text-blue-500"}),e.jsx("span",{children:"Vista tabla"})]}):e.jsxs(e.Fragment,{children:[e.jsx(m,{className:"mr-2 text-blue-500"}),e.jsx("span",{children:"Vista tarjetas"})]})}),(A||z||"nombre"!==T||"asc"!==q)&&e.jsx("button",{className:"px-4 py-2 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm",onClick:G,children:"Limpiar filtros"})]})]})}),e.jsxs(j,{className:"shadow-md border-0 rounded-xl overflow-hidden",children:[e.jsx(N,{className:"bg-gradient-to-r from-blue-900 to-indigo-950 text-white border-0",children:e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Lista de Pacientes"}),e.jsx("span",{className:"ml-3 bg-white text-blue-600 rounded-full px-3 py-1 text-sm font-medium",children:_})]})}),e.jsxs(f,{className:"p-6",children:[C?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(p,{className:"animate-spin text-blue-500 text-4xl mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"Cargando pacientes..."})]}):0===l.length?e.jsx("div",{className:"text-center py-12",children:A||z?e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"No se encontraron pacientes que coincidan con los filtros aplicados"}),e.jsx("button",{className:"text-blue-500 hover:text-blue-700 font-medium",onClick:G,children:"Limpiar filtros"})]}):e.jsx("p",{className:"text-gray-500",children:"No hay pacientes registrados"})}):"grid"===R?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-2",children:l.map(s=>e.jsx(y,{patient:s,onClick:()=>{}},s.id))}):e.jsx(w,{patients:l,loading:C,onSelectPatient:J}),I>1&&e.jsx("div",{className:"flex items-center justify-center mt-6",children:e.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[e.jsxs("button",{onClick:()=>H(F-1),disabled:1===F,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium "+(1===F?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"),children:[e.jsx("span",{className:"sr-only",children:"Anterior"}),e.jsx(u,{className:"h-5 w-5","aria-hidden":"true"})]}),[...Array(I)].map((s,a)=>{const t=a+1;return 1===t||t===I||t>=F-1&&t<=F+1?e.jsx("button",{onClick:()=>H(t),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium "+(F===t?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"),children:t},t):2===t&&F>3||t===I-1&&F<I-2?e.jsx("span",{className:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700",children:"..."},t):null}),e.jsxs("button",{onClick:()=>H(F+1),disabled:F===I,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium "+(F===I?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"),children:[e.jsx("span",{className:"sr-only",children:"Siguiente"}),e.jsx(h,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),e.jsxs(v,{className:"bg-gray-50 border-t border-gray-100 p-4 flex justify-between items-center",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Mostrando ",l.length," de ",s.length," pacientes"]}),e.jsxs("button",{onClick:Z,className:"flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:[e.jsx(g,{className:"mr-1"})," Volver arriba"]})]})]})]})};export{k as default};
