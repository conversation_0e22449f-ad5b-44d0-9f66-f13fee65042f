/*
 * ========================================
 * SISTEMA ULTRA-ROBUSTO DE IMPRESIÓN
 * Fidelidad Visual 100% - Cross-Browser
 * ========================================
 * 
 * Este CSS garantiza que el PDF sea IDÉNTICO a la pantalla
 * Compatible con Chrome, Firefox, Safari, Edge
 * Utiliza máxima especificidad y múltiples capas de protección
 */

@media print {
  /* ========================================
     CONFIGURACIÓN DE PÁGINA OPTIMIZADA
     ======================================== */
  @page {
    margin: 0.5in !important;
    size: A4 !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* ========================================
     FORZAR PRESERVACIÓN DE COLORES GLOBAL
     Compatible con todos los navegadores
     ======================================== */
  *,
  *::before,
  *::after,
  html,
  body,
  div,
  span,
  h1, h2, h3, h4, h5, h6,
  p, a, strong, em, i, b,
  svg, path, circle, rect,
  table, tr, td, th,
  ul, ol, li,
  img, canvas {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  /* ========================================
     CONFIGURACIÓN BASE DEL DOCUMENTO
     ======================================== */
  html, body {
    background: white !important;
    background-color: white !important;
    color: black !important;
    font-family: Arial, sans-serif !important;
    font-size: 12pt !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: auto !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* ========================================
     CONTENEDOR PRINCIPAL DE IMPRESIÓN
     ======================================== */
  .print-content,
  [class*="print-content"],
  div[class*="print-content"] {
    background: white !important;
    background-color: white !important;
    color: black !important;
    font-family: Arial, sans-serif !important;
    font-size: 12pt !important;
    line-height: 1.4 !important;
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 2rem !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* ========================================
     OCULTAR ELEMENTOS NO DESEADOS
     ======================================== */
  .print-hide,
  .no-print,
  .sidebar,
  header,
  nav,
  .modal-overlay,
  .modal-close,
  button:not(.print-keep),
  .fixed,
  .absolute:not(.print-content) {
    display: none !important;
    visibility: hidden !important;
  }

  /* ========================================
     HEADERS AZULES - FIDELIDAD TOTAL
     Múltiples selectores para máxima compatibilidad
     ======================================== */
  
  /* Nivel 1: Selectores base */
  .print-content .print-header,
  .print-content .bg-blue-600,
  .print-content .bg-blue-500,
  .print-content .bg-gradient-to-r,
  .print-content [class*="bg-gradient"],
  .print-content [class*="from-blue"],
  .print-content [class*="to-blue"],
  
  /* Nivel 2: Con div específico */
  .print-content div.print-header,
  .print-content div.bg-blue-600,
  .print-content div.bg-blue-500,
  .print-content div.bg-gradient-to-r,
  .print-content div[class*="bg-gradient"],
  .print-content div[class*="from-blue"],
  .print-content div[class*="to-blue"],
  
  /* Nivel 3: Máxima especificidad */
  div.print-content .print-header,
  div.print-content .bg-blue-600,
  div.print-content .bg-blue-500,
  div.print-content .bg-gradient-to-r,
  div.print-content [class*="bg-gradient"],
  div.print-content [class*="from-blue"],
  div.print-content [class*="to-blue"],
  
  /* Nivel 4: Ultra-especificidad con body */
  body .print-content .print-header,
  body .print-content .bg-blue-600,
  body .print-content .bg-blue-500,
  body .print-content .bg-gradient-to-r,
  body .print-content [class*="bg-gradient"],
  body .print-content [class*="from-blue"],
  body .print-content [class*="to-blue"],
  
  /* Nivel 5: Ultra-especificidad con html */
  html body .print-content .print-header,
  html body .print-content .bg-blue-600,
  html body .print-content .bg-blue-500,
  html body .print-content .bg-gradient-to-r {
    
    /* Fondo azul forzado con múltiples propiedades */
    background: #2563eb !important;
    background-color: #2563eb !important;
    background-image: none !important;
    
    /* Texto blanco forzado */
    color: white !important;
    
    /* Preservación de colores cross-browser */
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    
    /* Layout y visibilidad */
    display: block !important;
    visibility: visible !important;
    width: 100% !important;
    box-sizing: border-box !important;
    border: none !important;
    
    /* Padding y margin consistentes */
    padding: 1.5rem !important;
    margin-bottom: 1rem !important;
    border-radius: 0.5rem !important;
  }

  /* ========================================
     TEXTO BLANCO EN HEADERS AZULES
     ======================================== */
  .print-content .print-header *,
  .print-content .bg-blue-600 *,
  .print-content .bg-blue-500 *,
  .print-content .bg-gradient-to-r *,
  .print-content [class*="bg-gradient"] *,
  .print-content [class*="from-blue"] *,
  div.print-content .print-header *,
  div.print-content .bg-blue-600 *,
  div.print-content .bg-blue-500 *,
  div.print-content .bg-gradient-to-r *,
  body .print-content .print-header *,
  body .print-content .bg-blue-600 *,
  body .print-content .bg-gradient-to-r *,
  html body .print-content .print-header *,
  html body .print-content .bg-blue-600 *,
  html body .print-content .bg-gradient-to-r * {
    color: white !important;
    visibility: visible !important;
  }

  /* ========================================
     TÍTULOS EN HEADERS
     ======================================== */
  .print-content .print-header h1,
  .print-content .print-header h2,
  .print-content .bg-blue-600 h1,
  .print-content .bg-blue-600 h2,
  .print-content .bg-gradient-to-r h1,
  .print-content .bg-gradient-to-r h2,
  body .print-content .print-header h1,
  body .print-content .print-header h2,
  body .print-content .bg-blue-600 h1,
  body .print-content .bg-blue-600 h2,
  body .print-content .bg-gradient-to-r h1,
  body .print-content .bg-gradient-to-r h2 {
    color: white !important;
    font-size: 1.875rem !important;
    font-weight: 700 !important;
    margin: 0 !important;
    text-align: left !important;
    display: block !important;
    visibility: visible !important;
    line-height: 1.2 !important;
  }

  /* ========================================
     TEXTO NEGRO PARA CONTENIDO NORMAL
     ======================================== */
  .print-content p:not(.print-header p):not(.bg-blue-600 p):not(.bg-gradient-to-r p),
  .print-content span:not(.print-header span):not(.bg-blue-600 span):not(.bg-gradient-to-r span),
  .print-content div:not(.print-header):not(.bg-blue-600):not(.bg-gradient-to-r),
  .print-content h3,
  .print-content h4,
  .print-content h5,
  .print-content h6,
  .print-content .text-gray-900,
  .print-content .text-gray-800,
  .print-content .text-gray-700,
  .print-content .text-black,
  div.print-content p:not(.print-header p):not(.bg-blue-600 p),
  div.print-content span:not(.print-header span):not(.bg-blue-600 span),
  div.print-content h3,
  div.print-content h4,
  div.print-content h5,
  div.print-content h6,
  body .print-content p:not(.print-header p):not(.bg-blue-600 p),
  body .print-content span:not(.print-header span):not(.bg-blue-600 span),
  body .print-content h3,
  body .print-content h4,
  body .print-content h5,
  body .print-content h6 {
    color: black !important;
    visibility: visible !important;
  }

  /* ========================================
     ICONOS DE APTITUDES - COLORES ESPECÍFICOS
     ======================================== */

  /* Naranja - Razonamiento */
  .print-content .bg-orange-500,
  .print-content .bg-orange-600,
  .print-content div.bg-orange-500,
  .print-content div.bg-orange-600,
  div.print-content .bg-orange-500,
  div.print-content .bg-orange-600,
  body .print-content .bg-orange-500,
  body .print-content .bg-orange-600,
  html body .print-content .bg-orange-500,
  html body .print-content .bg-orange-600 {
    background: #f97316 !important;
    background-color: #f97316 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Azul - Verbal */
  .print-content .bg-blue-500,
  .print-content div.bg-blue-500,
  div.print-content .bg-blue-500,
  body .print-content .bg-blue-500,
  html body .print-content .bg-blue-500 {
    background: #3b82f6 !important;
    background-color: #3b82f6 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Verde - Numérico y Ortografía */
  .print-content .bg-green-500,
  .print-content .bg-green-600,
  .print-content div.bg-green-500,
  .print-content div.bg-green-600,
  div.print-content .bg-green-500,
  div.print-content .bg-green-600,
  body .print-content .bg-green-500,
  body .print-content .bg-green-600,
  html body .print-content .bg-green-500,
  html body .print-content .bg-green-600 {
    background: #22c55e !important;
    background-color: #22c55e !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Rojo - Atención */
  .print-content .bg-red-500,
  .print-content .bg-red-600,
  .print-content div.bg-red-500,
  .print-content div.bg-red-600,
  div.print-content .bg-red-500,
  div.print-content .bg-red-600,
  body .print-content .bg-red-500,
  body .print-content .bg-red-600,
  html body .print-content .bg-red-500,
  html body .print-content .bg-red-600 {
    background: #ef4444 !important;
    background-color: #ef4444 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Amarillo - Concentración */
  .print-content .bg-yellow-500,
  .print-content .bg-yellow-600,
  .print-content div.bg-yellow-500,
  .print-content div.bg-yellow-600,
  div.print-content .bg-yellow-500,
  div.print-content .bg-yellow-600,
  body .print-content .bg-yellow-500,
  body .print-content .bg-yellow-600,
  html body .print-content .bg-yellow-500,
  html body .print-content .bg-yellow-600 {
    background: #eab308 !important;
    background-color: #eab308 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Gris - Mecánico */
  .print-content .bg-gray-500,
  .print-content .bg-gray-600,
  .print-content div.bg-gray-500,
  .print-content div.bg-gray-600,
  div.print-content .bg-gray-500,
  div.print-content .bg-gray-600,
  body .print-content .bg-gray-500,
  body .print-content .bg-gray-600,
  html body .print-content .bg-gray-500,
  html body .print-content .bg-gray-600 {
    background: #6b7280 !important;
    background-color: #6b7280 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* ========================================
     TEXTO BLANCO EN ICONOS DE APTITUDES
     ======================================== */
  .print-content .bg-orange-500 *,
  .print-content .bg-blue-500 *,
  .print-content .bg-green-500 *,
  .print-content .bg-red-500 *,
  .print-content .bg-yellow-500 *,
  .print-content .bg-gray-500 *,
  .print-content .bg-gray-600 *,
  div.print-content .bg-orange-500 *,
  div.print-content .bg-blue-500 *,
  div.print-content .bg-green-500 *,
  div.print-content .bg-red-500 *,
  div.print-content .bg-yellow-500 *,
  div.print-content .bg-gray-500 *,
  div.print-content .bg-gray-600 *,
  body .print-content .bg-orange-500 *,
  body .print-content .bg-blue-500 *,
  body .print-content .bg-green-500 *,
  body .print-content .bg-red-500 *,
  body .print-content .bg-yellow-500 *,
  body .print-content .bg-gray-500 *,
  body .print-content .bg-gray-600 * {
    color: white !important;
  }

  /* ========================================
     TIPOGRAFÍA - PESOS Y TAMAÑOS
     ======================================== */

  /* Texto en negrita */
  .print-content .font-bold,
  .print-content p.font-bold,
  .print-content h1.font-bold,
  .print-content h2.font-bold,
  .print-content h3.font-bold,
  .print-content h4.font-bold,
  .print-content span.font-bold,
  div.print-content .font-bold,
  div.print-content p.font-bold,
  div.print-content h1.font-bold,
  div.print-content h2.font-bold,
  div.print-content h3.font-bold,
  body .print-content .font-bold,
  body .print-content p.font-bold,
  body .print-content h1.font-bold,
  body .print-content h2.font-bold,
  body .print-content h3.font-bold {
    font-weight: 700 !important;
  }

  /* Texto semi-negrita */
  .print-content .font-semibold,
  .print-content p.font-semibold,
  div.print-content .font-semibold,
  div.print-content p.font-semibold,
  body .print-content .font-semibold,
  body .print-content p.font-semibold {
    font-weight: 600 !important;
  }

  /* Texto medio */
  .print-content .font-medium,
  .print-content p.font-medium,
  div.print-content .font-medium,
  div.print-content p.font-medium,
  body .print-content .font-medium,
  body .print-content p.font-medium {
    font-weight: 500 !important;
  }

  /* Tamaños de texto */
  .print-content .text-3xl { font-size: 1.875rem !important; }
  .print-content .text-2xl { font-size: 1.5rem !important; }
  .print-content .text-xl { font-size: 1.25rem !important; }
  .print-content .text-lg { font-size: 1.125rem !important; }
  .print-content .text-base { font-size: 1rem !important; }
  .print-content .text-sm { font-size: 0.875rem !important; }
  .print-content .text-xs { font-size: 0.75rem !important; }

  /* ========================================
     LAYOUT Y ESPACIADO
     ======================================== */

  /* Flexbox */
  .print-content .flex,
  div.print-content .flex,
  body .print-content .flex {
    display: flex !important;
  }

  .print-content .items-center,
  div.print-content .items-center,
  body .print-content .items-center {
    align-items: center !important;
  }

  .print-content .justify-between,
  div.print-content .justify-between,
  body .print-content .justify-between {
    justify-content: space-between !important;
  }

  .print-content .justify-center,
  div.print-content .justify-center,
  body .print-content .justify-center {
    justify-content: center !important;
  }

  /* Grid */
  .print-content .grid,
  div.print-content .grid,
  body .print-content .grid {
    display: grid !important;
  }

  .print-content .grid-cols-2,
  div.print-content .grid-cols-2,
  body .print-content .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  .print-content .grid-cols-3,
  div.print-content .grid-cols-3,
  body .print-content .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }

  /* Alineación de texto */
  .print-content .text-center,
  div.print-content .text-center,
  body .print-content .text-center {
    text-align: center !important;
  }

  .print-content .text-right,
  div.print-content .text-right,
  body .print-content .text-right {
    text-align: right !important;
  }

  .print-content .text-left,
  div.print-content .text-left,
  body .print-content .text-left {
    text-align: left !important;
  }

  /* ========================================
     ESPACIADO - MARGINS Y PADDING
     ======================================== */

  /* Padding */
  .print-content .p-8 { padding: 2rem !important; }
  .print-content .p-6 { padding: 1.5rem !important; }
  .print-content .p-4 { padding: 1rem !important; }
  .print-content .p-3 { padding: 0.75rem !important; }
  .print-content .p-2 { padding: 0.5rem !important; }
  .print-content .p-1 { padding: 0.25rem !important; }

  /* Margin Bottom */
  .print-content .mb-8 { margin-bottom: 2rem !important; }
  .print-content .mb-6 { margin-bottom: 1.5rem !important; }
  .print-content .mb-4 { margin-bottom: 1rem !important; }
  .print-content .mb-3 { margin-bottom: 0.75rem !important; }
  .print-content .mb-2 { margin-bottom: 0.5rem !important; }
  .print-content .mb-1 { margin-bottom: 0.25rem !important; }

  /* Margin Right */
  .print-content .mr-4 { margin-right: 1rem !important; }
  .print-content .mr-3 { margin-right: 0.75rem !important; }
  .print-content .mr-2 { margin-right: 0.5rem !important; }
  .print-content .mr-1 { margin-right: 0.25rem !important; }

  /* ========================================
     BORDES Y ESQUINAS
     ======================================== */
  .print-content .border { border: 1px solid #e5e7eb !important; }
  .print-content .border-gray-200 { border-color: #e5e7eb !important; }
  .print-content .rounded-lg { border-radius: 0.5rem !important; }
  .print-content .rounded-full { border-radius: 9999px !important; }
  .print-content .rounded { border-radius: 0.25rem !important; }

  /* ========================================
     RESULTADOS GRÁFICOS POR APTITUD
     Sección específica con barras de percentiles
     ======================================== */

  /* Tabla de resultados gráficos */
  .print-content table,
  .print-content thead,
  .print-content tbody,
  .print-content tr,
  .print-content td,
  .print-content th,
  body .print-content table,
  body .print-content thead,
  body .print-content tbody,
  body .print-content tr,
  body .print-content td,
  body .print-content th {
    border-collapse: collapse !important;
    border-spacing: 0 !important;
    width: 100% !important;
    visibility: visible !important;
    display: table !important;
  }

  /* Header de la tabla */
  .print-content thead,
  .print-content thead tr,
  .print-content thead th,
  body .print-content thead,
  body .print-content thead tr,
  body .print-content thead th {
    background: #1f2937 !important;
    background-color: #1f2937 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    font-weight: 700 !important;
    text-align: center !important;
    padding: 0.75rem 1rem !important;
  }

  /* Celdas de la tabla */
  .print-content tbody td,
  body .print-content tbody td {
    padding: 1rem !important;
    border-bottom: 1px solid #e5e7eb !important;
    vertical-align: middle !important;
  }

  /* BARRAS DE PERCENTILES - ULTRA ESPECÍFICAS */
  .print-content .bg-gray-200,
  .print-content div[class*="bg-gray-200"],
  body .print-content .bg-gray-200,
  body .print-content div[class*="bg-gray-200"] {
    background: #e5e7eb !important;
    background-color: #e5e7eb !important;
    border-radius: 9999px !important;
    height: 1.5rem !important;
    width: 100% !important;
    position: relative !important;
    overflow: hidden !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Barras de percentil con colores específicos */
  .print-content .h-6[style*="backgroundColor"],
  .print-content div[style*="backgroundColor"]:not(.bg-blue-600):not(.bg-gradient-to-r),
  body .print-content .h-6[style*="backgroundColor"],
  body .print-content div[style*="backgroundColor"]:not(.bg-blue-600):not(.bg-gradient-to-r) {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color: white !important;
    font-weight: 700 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 9999px !important;
    height: 1.5rem !important;
  }

  /* Texto dentro de las barras */
  .print-content .h-6[style*="backgroundColor"] span,
  .print-content div[style*="backgroundColor"] span,
  body .print-content .h-6[style*="backgroundColor"] span,
  body .print-content div[style*="backgroundColor"] span {
    color: white !important;
    font-weight: 700 !important;
    font-size: 0.875rem !important;
  }

  /* LEYENDA DE NIVELES - ULTRA ESPECÍFICA */
  .print-content .bg-gray-50,
  body .print-content .bg-gray-50,
  .print-content .bg-emerald-50,
  body .print-content .bg-emerald-50,
  .print-content .bg-yellow-50,
  body .print-content .bg-yellow-50,
  .print-content .bg-teal-50,
  body .print-content .bg-teal-50,
  .print-content .bg-cyan-50,
  body .print-content .bg-cyan-50,
  .print-content .bg-indigo-50,
  body .print-content .bg-indigo-50,
  .print-content .bg-purple-50,
  body .print-content .bg-purple-50,
  .print-content .bg-orange-50,
  body .print-content .bg-orange-50,
  .print-intelligence-card.yellow,
  .print-intelligence-card.orange,
  .print-intelligence-card.gray {
    background: white !important;
    background-color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Cuadrados de colores en la leyenda */
  .print-content .w-4.h-4,
  body .print-content .w-4.h-4 {
    width: 1rem !important;
    height: 1rem !important;
    border-radius: 0.25rem !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    display: inline-block !important;
    margin-right: 0.5rem !important;
  }

  /* Colores específicos de la leyenda */
  .print-content .bg-red-500,
  body .print-content .bg-red-500 {
    background: #ef4444 !important;
    background-color: #ef4444 !important;
  }

  .print-content .bg-orange-500,
  body .print-content .bg-orange-500 {
    background: #f97316 !important;
    background-color: #f97316 !important;
  }

  .print-content .bg-yellow-500,
  body .print-content .bg-yellow-500 {
    background: #eab308 !important;
    background-color: #eab308 !important;
  }

  .print-content .bg-green-500,
  body .print-content .bg-green-500 {
    background: #22c55e !important;
    background-color: #22c55e !important;
  }

  .print-content .bg-blue-500,
  body .print-content .bg-blue-500 {
    background: #3b82f6 !important;
    background-color: #3b82f6 !important;
  }

  .print-content .bg-purple-500,
  body .print-content .bg-purple-500 {
    background: #8b5cf6 !important;
    background-color: #8b5cf6 !important;
  }

  /* Texto de la leyenda */
  .print-content .font-medium,
  body .print-content .font-medium {
    font-weight: 500 !important;
    color: black !important;
  }

  /* ICONOS DE APTITUDES EN LA TABLA */
  .print-content .w-12.h-12,
  body .print-content .w-12.h-12 {
    width: 3rem !important;
    height: 3rem !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Texto de percentiles grandes */
  .print-content .text-4xl,
  body .print-content .text-4xl {
    font-size: 2.25rem !important;
    font-weight: 700 !important;
    color: black !important;
  }

  /* CENTRADO PERFECTO DE VALORES EN BARRAS DE PERCENTILES */
  .print-content .h-6[style*="backgroundColor"] span,
  body .print-content .h-6[style*="backgroundColor"] span {
    position: absolute !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    white-space: nowrap !important;
    font-size: 0.875rem !important;
    font-weight: 700 !important;
    line-height: 1 !important;
    color: white !important;
    text-align: center !important;
    display: block !important;
  }

  /* Contenedor de barras de percentiles */
  .print-content .h-6[style*="backgroundColor"],
  body .print-content .h-6[style*="backgroundColor"] {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 1.5rem !important;
    border-radius: 9999px !important;
  }

  /* BORDES LATERALES DE COLOR PARA APTITUDES */
  .print-content .print-aptitude-card.border-l-4,
  body .print-content .print-aptitude-card.border-l-4 {
    border-left-width: 4px !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  /* Colores específicos de bordes laterales */
  .print-content .border-red-500,
  body .print-content .border-red-500 {
    border-left-color: #ef4444 !important;
  }

  .print-content .border-orange-500,
  body .print-content .border-orange-500 {
    border-left-color: #f97316 !important;
  }

  .print-content .border-yellow-500,
  body .print-content .border-yellow-500 {
    border-left-color: #eab308 !important;
  }

  .print-content .border-green-500,
  body .print-content .border-green-500 {
    border-left-color: #22c55e !important;
  }

  .print-content .border-blue-500,
  body .print-content .border-blue-500 {
    border-left-color: #3b82f6 !important;
  }

  .print-content .border-gray-500,
  body .print-content .border-gray-500 {
    border-left-color: #6b7280 !important;
  }

  /* Etiquetas de percentil */
  .print-content .text-sm.text-gray-600,
  body .print-content .text-sm.text-gray-600 {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
  }
}
