var e=Object.defineProperty,s=Object.defineProperties,r=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,l=(s,r,t)=>r in s?e(s,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[r]=t,i=(e,s,r)=>new Promise((t,a)=>{var n=e=>{try{i(r.next(e))}catch(s){a(s)}},l=e=>{try{i(r.throw(e))}catch(s){a(s)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(n,l);i((r=r.apply(e,s)).next())});import{a2 as o,a3 as c,G as d,a4 as m,j as x,J as u,k as h,m as p,r as g,a5 as j,Q as b,F as f,W as y,O as N}from"./vendor-BqMjyOVw.js";import{C as v,b as w,a as C}from"./index-Bdl1jgS_.js";import{u as I,P,A as $}from"./PinRechargePrompt-B8It1Ju9.js";import"./PinValidationService-Ki4hIVgd.js";import"./PinLogger-C2v3yGM1.js";const A=({validationResult:e,showDetails:s=!0,className:r="",onClose:t=null})=>{if(!e)return null;const{canProceed:a,severity:n,userMessage:l,details:i,suggestions:g=[],batchInfo:j}=e,b={success:{bgColor:"bg-green-50",borderColor:"border-green-200",textColor:"text-green-800",iconColor:"text-green-600",icon:m},warning:{bgColor:"bg-yellow-50",borderColor:"border-yellow-200",textColor:"text-yellow-800",iconColor:"text-yellow-600",icon:d},critical:{bgColor:"bg-red-50",borderColor:"border-red-200",textColor:"text-red-800",iconColor:"text-red-600",icon:d},error:{bgColor:"bg-red-50",borderColor:"border-red-200",textColor:"text-red-800",iconColor:"text-red-600",icon:c},info:{bgColor:"bg-blue-50",borderColor:"border-blue-200",textColor:"text-blue-800",iconColor:"text-blue-600",icon:o}},f=b[n]||b.info,y=f.icon;return x.jsx("div",{className:`${f.bgColor} ${f.borderColor} border rounded-lg p-4 ${r}`,children:x.jsxs("div",{className:"flex items-start",children:[x.jsx("div",{className:"flex-shrink-0",children:x.jsx(y,{className:`h-5 w-5 ${f.iconColor}`})}),x.jsxs("div",{className:"ml-3 flex-1",children:[x.jsxs("div",{className:"flex items-center justify-between",children:[x.jsx("h3",{className:`text-sm font-medium ${f.textColor}`,children:a?"Operación Permitida":"Operación Bloqueada"}),t&&x.jsx("button",{onClick:t,className:`${f.textColor} hover:opacity-75`,children:x.jsx(c,{className:"h-4 w-4"})})]}),x.jsx("div",{className:`mt-1 text-sm ${f.textColor}`,children:l}),s&&i&&x.jsxs("div",{className:"mt-3 space-y-2",children:[!i.isUnlimited&&x.jsxs("div",{className:"flex items-center space-x-4 text-xs",children:[x.jsxs("div",{className:"flex items-center",children:[x.jsx(u,{className:`h-3 w-3 ${f.iconColor} mr-1`}),x.jsxs("span",{children:["Disponibles: ",i.currentPins||0]})]}),x.jsxs("div",{className:"flex items-center",children:[x.jsx(h,{className:`h-3 w-3 ${f.iconColor} mr-1`}),x.jsxs("span",{children:["Requeridos: ",i.requiredPins||1]})]}),void 0!==i.pinsAfter&&x.jsxs("div",{className:"flex items-center",children:[x.jsx(u,{className:`h-3 w-3 ${f.iconColor} mr-1`}),x.jsxs("span",{children:["Quedarán: ",i.pinsAfter]})]})]}),j&&x.jsxs("div",{className:"flex items-center text-xs",children:[x.jsx(p,{className:`h-3 w-3 ${f.iconColor} mr-1`}),x.jsxs("span",{children:["Generación en lote: ",j.patientCount," pacientes"]})]}),i.isUnlimited&&x.jsxs("div",{className:"flex items-center text-xs",children:[x.jsx(m,{className:"h-3 w-3 text-green-600 mr-1"}),x.jsx("span",{children:"Plan ilimitado activo"})]})]}),g.length>0&&x.jsxs("div",{className:"mt-3",children:[x.jsx("h4",{className:`text-xs font-medium ${f.textColor} mb-1`,children:"Sugerencias:"}),x.jsx("ul",{className:`text-xs ${f.textColor} space-y-1`,children:g.map((e,s)=>x.jsxs("li",{className:"flex items-start",children:[x.jsx("span",{className:"mr-1",children:"•"}),x.jsx("span",{children:e})]},s))})]})]})]})})},S=({validationResult:e,onConfirm:s,onCancel:r,confirmText:t="Continuar",cancelText:a="Cancelar"})=>{if(!e||!e.canProceed)return null;const{severity:n,userMessage:l,details:i,hasWarning:o}=e;return o||"success"!==n?x.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:x.jsxs("div",{className:"flex items-start",children:[x.jsx(d,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),x.jsxs("div",{className:"ml-3 flex-1",children:[x.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"Confirmar Operación"}),x.jsx("p",{className:"mt-1 text-sm text-yellow-700",children:l}),i&&!i.isUnlimited&&x.jsxs("p",{className:"mt-2 text-xs text-yellow-600",children:["Después de esta operación quedarán ",i.pinsAfter," pines disponibles."]}),x.jsxs("div",{className:"mt-4 flex space-x-3",children:[x.jsx("button",{onClick:s,className:"bg-yellow-600 text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-yellow-700 transition-colors",children:t}),x.jsx("button",{onClick:r,className:"bg-gray-300 text-gray-700 px-3 py-1.5 rounded text-sm font-medium hover:bg-gray-400 transition-colors",children:a})]})]})]})}):null},R=({psychologistId:e,patientId:s,patientIds:r=null,onGenerateReport:t,requiredPins:a=1,buttonText:n="Generar Informe",buttonClass:l="bg-blue-600 hover:bg-blue-700 text-white",disabled:o=!1,showValidationDetails:c=!0,size:m="md",variant:u="primary"})=>{const[p,N]=g.useState(!1),[v,w]=g.useState(!1),[C,R]=g.useState(!1),[G,O]=g.useState(null),[T,V]=g.useState(!1),[D,k]=g.useState(null),q=Array.isArray(r)&&r.length>0,E=q?r.length:a,{validationResult:M,isValidating:z,hasWarning:B,isBlocked:L,validateSingleReport:U,validateBatchReports:W}=I(e,{showToastAlerts:!1,autoValidate:!0,onValidationChange:e=>{e.canProceed?(O(null),R(!1)):(O(e),"INSUFFICIENT_PINS"!==e.reason&&"NO_PINS_AVAILABLE"!==e.reason||R(!0))}});g.useEffect(()=>{i(null,null,function*(){if(e)try{const s=yield $.validateAdminAccess(e,E);if(s.isAdmin)return V(!0),void k(s);V(!1),k(null),q?W(r):U(E)}catch(s){V(!1),k(null)}})},[e,r,E,q,U,W]);const F=`\n    ${{primary:"bg-blue-600 hover:bg-blue-700 text-white",secondary:"bg-gray-600 hover:bg-gray-700 text-white",success:"bg-green-600 hover:bg-green-700 text-white",warning:"bg-yellow-600 hover:bg-yellow-700 text-white",danger:"bg-red-600 hover:bg-red-700 text-white"}[u]||l}\n    ${{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[m]}\n    font-medium rounded-lg transition-colors duration-200\n    flex items-center justify-center space-x-2\n    disabled:opacity-50 disabled:cursor-not-allowed\n    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\n  `.trim(),J=()=>i(null,null,function*(){try{N(!0),w(!1),t&&(q?yield t(r):yield t(s));const e=T?q?`${r.length} informes generados exitosamente (Acceso de administrador)`:"Informe generado exitosamente (Acceso de administrador)":q?`${r.length} informes generados exitosamente`:"Informe generado exitosamente";b.success(e),setTimeout(()=>{q?W(r):U(E)},1e3)}catch(e){b.error("Error al generar el informe")}finally{N(!1)}}),_=o||p||z||L&&!T;let H=n;z?H="Validando...":p?H=q?"Generando informes...":"Generando informe...":L&&!T?H="Bloqueado":T&&(H=`${n} (Admin)`);let Q=h;return z||p?Q=f:L&&!T?Q=y:T?Q=j:B&&(Q=d),x.jsxs("div",{className:"space-y-3",children:[T&&D&&c&&x.jsx("div",{className:"bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-3",children:x.jsxs("div",{className:"flex items-center",children:[x.jsx(j,{className:"text-purple-600 mr-2"}),x.jsxs("div",{children:[x.jsx("p",{className:"text-sm font-medium text-purple-900",children:"Acceso de Administrador"}),x.jsx("p",{className:"text-xs text-purple-700",children:"Sin restricciones de pines - Acceso completo al sistema"})]})]})}),!T&&c&&M&&x.jsx(A,{validationResult:M,showDetails:!0,className:"text-sm"}),x.jsxs("button",{onClick:()=>i(null,null,function*(){if(o||p||z)return;if(T&&D)return void(yield J());let e;e=q?yield W(r):yield U(E),e.canProceed?e.hasWarning||"warning"===e.severity||"critical"===e.severity?w(!0):yield J():b.error(e.userMessage)}),disabled:_,className:F,title:L?(null==M?void 0:M.userMessage)||"No se puede generar el informe":`${H} - ${E} pin${1!==E?"s":""} requerido${1!==E?"s":""}`,children:[x.jsx(Q,{className:"h-4 w-4 "+(z||p?"animate-spin":"")}),x.jsx("span",{children:H}),!L&&E>1&&x.jsxs("span",{className:"bg-white bg-opacity-20 px-2 py-0.5 rounded-full text-xs",children:[E," pines"]})]}),v&&M&&x.jsx(S,{validationResult:M,onConfirm:J,onCancel:()=>w(!1),confirmText:q?"Generar Informes":"Generar Informe",cancelText:"Cancelar"}),q&&r.length>0&&x.jsxs("div",{className:"text-xs text-gray-600 text-center",children:["Generación en lote: ",r.length," pacientes"]}),C&&M&&x.jsx(P,{psychologistId:e,currentPins:M.remainingPins||0,requiredPins:E,onClose:()=>R(!1),variant:"inline"})]})},G=e=>{var i,o,c;return x.jsx(R,(o=((e,s)=>{for(var r in s||(s={}))a.call(s,r)&&l(e,r,s[r]);if(t)for(var r of t(s))n.call(s,r)&&l(e,r,s[r]);return e})({},e),c={buttonText:`Generar ${(null==(i=e.patientIds)?void 0:i.length)||0} Informes`,variant:"success"},s(o,r(c))))},O=()=>{const[e,s]=g.useState([]),[r,t]=g.useState([]),[a,n]=g.useState(""),[l,o]=g.useState(!0),{validationResult:c}=I(a,{showToastAlerts:!1,autoValidate:!0});g.useEffect(()=>{d()},[]);const d=()=>i(null,null,function*(){try{o(!0);t([{id:"patient-1",name:"Juan Pérez",hasResults:!0},{id:"patient-2",name:"María García",hasResults:!0},{id:"patient-3",name:"Carlos López",hasResults:!0},{id:"patient-4",name:"Ana Martínez",hasResults:!0},{id:"patient-5",name:"Luis Rodríguez",hasResults:!0}]),n("demo-psychologist-id")}catch(e){b.error("Error cargando datos de demostración")}finally{o(!1)}}),m=e=>i(null,null,function*(){try{const s=r.find(s=>s.id===e);yield new Promise(e=>setTimeout(e,2e3)),b.success(`Informe generado para ${s.name}`)}catch(s){b.error("Error al generar el informe")}});return l?x.jsx("div",{className:"flex items-center justify-center min-h-screen",children:x.jsxs("div",{className:"text-center",children:[x.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),x.jsx("p",{className:"text-gray-600",children:"Cargando demostración..."})]})}):x.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-6xl",children:[x.jsxs("div",{className:"mb-8",children:[x.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Sistema Mejorado de Generación de Informes"}),x.jsx("p",{className:"text-gray-600",children:"Demostración del nuevo sistema de validación de pines con controles automáticos"})]}),c&&x.jsx("div",{className:"mb-6",children:x.jsx(A,{validationResult:c,showDetails:!0})}),x.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[x.jsxs(v,{children:[x.jsx(w,{className:"bg-blue-50 border-b",children:x.jsxs("div",{className:"flex items-center",children:[x.jsx(N,{className:"text-blue-600 mr-2"}),x.jsx("h2",{className:"text-lg font-semibold text-gray-800",children:"Generación Individual"})]})}),x.jsxs(C,{className:"space-y-4",children:[x.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Generar informes individuales con validación automática de pines"}),r.map(e=>x.jsxs("div",{className:"border rounded-lg p-4",children:[x.jsxs("div",{className:"flex items-center justify-between mb-3",children:[x.jsxs("div",{children:[x.jsx("h3",{className:"font-medium text-gray-900",children:e.name}),x.jsxs("p",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]}),x.jsx("div",{className:"flex items-center space-x-2",children:e.hasResults&&x.jsx("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:"Con resultados"})})]}),x.jsx(R,{psychologistId:a,patientId:e.id,onGenerateReport:m,buttonText:"Generar Informe",size:"sm",showValidationDetails:!1})]},e.id))]})]}),x.jsxs(v,{children:[x.jsx(w,{className:"bg-green-50 border-b",children:x.jsxs("div",{className:"flex items-center",children:[x.jsx(p,{className:"text-green-600 mr-2"}),x.jsx("h2",{className:"text-lg font-semibold text-gray-800",children:"Generación en Lote"})]})}),x.jsxs(C,{className:"space-y-4",children:[x.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Seleccionar múltiples pacientes para generar informes en lote"}),x.jsx("div",{className:"space-y-2",children:r.map(r=>x.jsxs("label",{className:"flex items-center space-x-3 p-2 hover:bg-gray-50 rounded",children:[x.jsx("input",{type:"checkbox",checked:e.includes(r.id),onChange:()=>{return e=r.id,void s(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e]);var e},className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),x.jsx("span",{className:"text-sm text-gray-900",children:r.name})]},r.id))}),e.length>0&&x.jsxs("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg",children:[x.jsxs("p",{className:"text-sm text-blue-800 mb-3",children:[e.length," pacientes seleccionados"]}),x.jsx(G,{psychologistId:a,patientIds:e,onGenerateReport:e=>i(null,null,function*(){try{yield new Promise(e=>setTimeout(e,3e3)),b.success(`${e.length} informes generados exitosamente`),s([])}catch(r){b.error("Error al generar los informes")}}),showValidationDetails:!0})]}),0===e.length&&x.jsxs("div",{className:"text-center py-8 text-gray-500",children:[x.jsx(h,{className:"mx-auto h-12 w-12 text-gray-300 mb-2"}),x.jsx("p",{children:"Seleccione pacientes para generar informes en lote"})]})]})]})]}),x.jsxs(v,{className:"mt-6",children:[x.jsx(w,{className:"bg-yellow-50 border-b",children:x.jsx("h2",{className:"text-lg font-semibold text-gray-800",children:"Características del Sistema Mejorado"})}),x.jsx(C,{children:x.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[x.jsxs("div",{children:[x.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"✅ Validaciones Implementadas"}),x.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[x.jsx("li",{children:"• Verificación previa de pines disponibles"}),x.jsx("li",{children:"• Bloqueo automático sin pines"}),x.jsx("li",{children:"• Alertas de pines bajos"}),x.jsx("li",{children:"• Validación en lote"}),x.jsx("li",{children:"• Confirmación para operaciones críticas"})]})]}),x.jsxs("div",{children:[x.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"🚀 Mejoras Adicionales"}),x.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[x.jsx("li",{children:"• Notificaciones automáticas"}),x.jsx("li",{children:"• Control por sesión de paciente"}),x.jsx("li",{children:"• Historial de consumo transparente"}),x.jsx("li",{children:"• Sistema de recarga de pines"}),x.jsx("li",{children:"• Reportes detallados de uso"})]})]})]})})]})]})};export{O as default};
