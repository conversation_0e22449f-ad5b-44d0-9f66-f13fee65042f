var e=(e,s,a)=>new Promise((i,r)=>{var t=e=>{try{l(a.next(e))}catch(s){r(s)}},n=e=>{try{l(a.throw(e))}catch(s){r(s)}},l=e=>e.done?i(e.value):Promise.resolve(e.value).then(t,n);l((a=a.apply(e,s)).next())});import{r as s,j as a,L as i,Q as r}from"./vendor-BqMjyOVw.js";import{B as t,C as n,a as l,b as o,s as d}from"./index-Bdl1jgS_.js";const c=()=>{const[c,m]=s.useState([]),[x,u]=s.useState(!0),[f,h]=s.useState("all");s.useEffect(()=>{e(null,null,function*(){try{u(!0);const{data:e,error:s}=yield d.from("informes").select("\n            id,\n            titulo,\n            tipo_informe,\n            estado,\n            fecha_generacion,\n            generado_por,\n            observaciones,\n            pacientes:paciente_id (\n              id,\n              nombre,\n              apellido,\n              documento,\n              genero,\n              email\n            ),\n            resultados:resultado_id (\n              id,\n              aptitudes:aptitud_id (\n                codigo,\n                nombre\n              )\n            )\n          ").order("fecha_generacion",{ascending:!1});if(s)return void r.error("Error al cargar los informes guardados");m(e||[])}catch(e){r.error("Error al cargar los informes guardados")}finally{u(!1)}})},[]);const p=e=>"evaluacion_completa"===e?"fas fa-file-medical":"fas fa-file-alt",g=c.filter(e=>"all"===f||("individual"===f?"evaluacion_individual"===e.tipo_informe:"complete"!==f||"evaluacion_completa"===e.tipo_informe));return a.jsxs("div",{className:"container mx-auto py-6",children:[a.jsxs("div",{className:"flex justify-between items-center mb-6",children:[a.jsxs("div",{children:[a.jsxs("h1",{className:"text-2xl font-bold text-blue-800",children:[a.jsx("i",{className:"fas fa-archive mr-3 text-blue-600"}),"Informes Guardados - Administración"]}),a.jsxs("p",{className:"text-gray-600 mt-1",children:[g.length," informe",1!==g.length?"s":""," guardado",1!==g.length?"s":""]})]}),a.jsxs("div",{className:"flex space-x-2",children:[a.jsx(t,{onClick:()=>h("all"),variant:"all"===f?"primary":"outline",size:"sm",children:"Todos"}),a.jsx(t,{onClick:()=>h("individual"),variant:"individual"===f?"primary":"outline",size:"sm",children:"Individuales"}),a.jsx(t,{onClick:()=>h("complete"),variant:"complete"===f?"primary":"outline",size:"sm",children:"Completos"})]})]}),x?a.jsxs("div",{className:"py-16 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"Cargando informes guardados..."})]}):a.jsx(a.Fragment,{children:0===g.length?a.jsx(n,{children:a.jsx(l,{children:a.jsxs("div",{className:"py-8 text-center",children:[a.jsx("i",{className:"fas fa-folder-open text-4xl text-gray-300 mb-4"}),a.jsx("p",{className:"text-gray-500",children:"No hay informes guardados disponibles."}),a.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Los informes aparecerán aquí una vez que se generen y guarden."}),a.jsxs(t,{as:i,to:"/admin/reports",className:"mt-4",children:[a.jsx("i",{className:"fas fa-plus mr-2"}),"Ver Resultados"]})]})})}):a.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:g.map(s=>{var x,u,f,h,g,v;const j="evaluacion_completa"===s.tipo_informe?"blue":"green";var b;return a.jsxs(n,{className:`overflow-hidden shadow-lg border border-${j}-200 hover:shadow-xl transition-shadow duration-300`,children:[a.jsx(o,{className:`bg-gradient-to-r from-${j}-500 to-${j}-600 border-b border-${j}-300`,children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3 border-2 border-white border-opacity-30",children:a.jsx("i",{className:`${p(s.tipo_informe)} text-white text-lg`})}),a.jsxs("div",{children:[a.jsx("h3",{className:"text-white font-semibold text-sm",children:"evaluacion_completa"===s.tipo_informe?"Informe Completo":"Informe Individual"}),a.jsx("p",{className:"text-white text-opacity-80 text-xs",children:new Date(s.fecha_generacion).toLocaleDateString("es-ES")})]})]}),a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${b=s.estado,{generado:"bg-blue-100 text-blue-800",revisado:"bg-yellow-100 text-yellow-800",finalizado:"bg-green-100 text-green-800"}[b]||"bg-gray-100 text-gray-800"}`,children:s.estado})]})}),a.jsx(l,{className:"p-4",children:a.jsxs("div",{className:"space-y-3",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-10 h-10 rounded-full flex items-center justify-center text-white text-sm mr-3 "+("masculino"===(null==(x=s.pacientes)?void 0:x.genero)?"bg-blue-500":"bg-pink-500"),children:a.jsx("i",{className:"fas "+("masculino"===(null==(u=s.pacientes)?void 0:u.genero)?"fa-mars":"fa-venus")})}),a.jsxs("div",{children:[a.jsxs("p",{className:"font-semibold text-gray-900 text-sm",children:[null==(f=s.pacientes)?void 0:f.nombre," ",null==(h=s.pacientes)?void 0:h.apellido]}),(null==(g=s.pacientes)?void 0:g.documento)&&a.jsxs("p",{className:"text-gray-500 text-xs",children:["Doc: ",s.pacientes.documento]})]})]}),"evaluacion_individual"===s.tipo_informe&&(null==(v=s.resultados)?void 0:v.aptitudes)&&a.jsxs("div",{className:"bg-gray-50 p-2 rounded-lg",children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Test Evaluado:"}),a.jsxs("p",{className:"text-sm font-medium text-gray-700",children:[s.resultados.aptitudes.codigo," - ",s.resultados.aptitudes.nombre]})]}),a.jsxs("div",{children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Título:"}),a.jsx("p",{className:"text-sm text-gray-700 line-clamp-2",children:s.titulo})]}),s.observaciones&&a.jsxs("div",{children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Observaciones:"}),a.jsx("p",{className:"text-sm text-gray-700 line-clamp-2",children:s.observaciones})]}),a.jsxs("div",{className:"text-xs text-gray-500 space-y-1",children:[a.jsxs("p",{children:["Generado por: ",s.generado_por||"Sistema"]}),a.jsxs("p",{children:["Fecha: ",new Date(s.fecha_generacion).toLocaleString("es-ES")]})]})]})}),a.jsx("div",{className:"bg-gray-50 px-4 py-3 border-t",children:a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs(t,{as:i,to:`/admin/informe-guardado/${s.id}`,variant:"primary",size:"sm",children:[a.jsx("i",{className:"fas fa-eye mr-1"}),"Ver Informe"]}),a.jsxs("div",{className:"flex space-x-2",children:[a.jsx(t,{onClick:()=>window.open(`/admin/informe-guardado/${s.id}`,"_blank"),variant:"outline",size:"sm",children:a.jsx("i",{className:"fas fa-external-link-alt"})}),a.jsx(t,{onClick:()=>{return a=s.id,e(null,null,function*(){if(window.confirm("¿Estás seguro de que deseas eliminar este informe?"))try{const{error:e}=yield d.from("informes").delete().eq("id",a);if(e)throw e;m(c.filter(e=>e.id!==a)),r.success("Informe eliminado correctamente")}catch(e){r.error("Error al eliminar el informe")}});var a},variant:"outline",size:"sm",className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:a.jsx("i",{className:"fas fa-trash"})})]})]})})]},s.id)})})})]})};export{c as default};
