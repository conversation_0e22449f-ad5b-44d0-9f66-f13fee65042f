import { describe, it, expect, beforeEach, vi } from 'vitest';
import { BatchProcessingService } from '../../services/pin/BatchProcessingService';
import PinValidationService from '../../services/pin/PinValidationService';
import InformesService from '../../services/InformesService';

// Mocks
vi.mock('../../services/pin/PinValidationService');
vi.mock('../../services/InformesService');

describe('BatchProcessingService', () => {
  const mockPsychologistId = 'psy-123';
  const mockPatientIds = ['patient-1', 'patient-2', 'patient-3'];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('processBatchReports', () => {
    it('should validate batch before processing', async () => {
      const mockValidation = {
        canProceed: false,
        userMessage: 'Insufficient pins'
      };

      PinValidationService.validateBatchReportGeneration.mockResolvedValue(mockValidation);

      await expect(
        BatchProcessingService.processBatchReports(mockPsychologistId, mockPatientIds)
      ).rejects.toThrow('Validación de lote falló: Insufficient pins');

      expect(PinValidationService.validateBatchReportGeneration).toHaveBeenCalledWith(
        mockPsychologistId,
        mockPatientIds
      );
    });

    it('should use individual strategy by default', async () => {
      const mockValidation = { canProceed: true };
      PinValidationService.validateBatchReportGeneration.mockResolvedValue(mockValidation);
      
      // Mock individual strategy method
      const mockResult = {
        strategy: BatchProcessingService.BATCH_STRATEGIES.INDIVIDUAL,
        successful: [],
        failed: [],
        totalProcessed: 0,
        totalSuccessful: 0,
        totalFailed: 0,
        pinsConsumed: 0
      };

      const processSpy = vi.spyOn(BatchProcessingService, '_processIndividualStrategy')
        .mockResolvedValue(mockResult);

      const result = await BatchProcessingService.processBatchReports(
        mockPsychologistId,
        mockPatientIds
      );

      expect(processSpy).toHaveBeenCalledWith(
        mockPsychologistId,
        mockPatientIds,
        expect.any(Object)
      );
      expect(result).toEqual(mockResult);
    });

    it('should use specified strategy', async () => {
      const mockValidation = { canProceed: true };
      PinValidationService.validateBatchReportGeneration.mockResolvedValue(mockValidation);
      
      const mockResult = {
        strategy: BatchProcessingService.BATCH_STRATEGIES.BULK_UPFRONT,
        successful: [],
        failed: [],
        totalProcessed: 0,
        totalSuccessful: 0,
        totalFailed: 0,
        pinsConsumed: 0
      };

      const processSpy = vi.spyOn(BatchProcessingService, '_processBulkUpfrontStrategy')
        .mockResolvedValue(mockResult);

      const result = await BatchProcessingService.processBatchReports(
        mockPsychologistId,
        mockPatientIds,
        { strategy: BatchProcessingService.BATCH_STRATEGIES.BULK_UPFRONT }
      );

      expect(processSpy).toHaveBeenCalled();
      expect(result.strategy).toBe(BatchProcessingService.BATCH_STRATEGIES.BULK_UPFRONT);
    });

    it('should throw error for unsupported strategy', async () => {
      const mockValidation = { canProceed: true };
      PinValidationService.validateBatchReportGeneration.mockResolvedValue(mockValidation);

      await expect(
        BatchProcessingService.processBatchReports(
          mockPsychologistId,
          mockPatientIds,
          { strategy: 'INVALID_STRATEGY' }
        )
      ).rejects.toThrow('Estrategia no soportada: INVALID_STRATEGY');
    });
  });

  describe('_processIndividualStrategy', () => {
    it('should process each patient individually', async () => {
      const mockValidation = { canProceed: true };
      const mockInformeId = 'informe-123';

      PinValidationService.validateReportGeneration
        .mockResolvedValueOnce(mockValidation)
        .mockResolvedValueOnce(mockValidation)
        .mockResolvedValueOnce(mockValidation);

      InformesService.generarInformeCompleto
        .mockResolvedValueOnce(mockInformeId + '-1')
        .mockResolvedValueOnce(mockInformeId + '-2')
        .mockResolvedValueOnce(mockInformeId + '-3');

      const result = await BatchProcessingService._processIndividualStrategy(
        mockPsychologistId,
        mockPatientIds,
        { continueOnError: true }
      );

      expect(result.strategy).toBe(BatchProcessingService.BATCH_STRATEGIES.INDIVIDUAL);
      expect(result.totalProcessed).toBe(3);
      expect(result.totalSuccessful).toBe(3);
      expect(result.totalFailed).toBe(0);
      expect(result.pinsConsumed).toBe(3);
      expect(result.successful).toHaveLength(3);
      expect(result.failed).toHaveLength(0);
    });

    it('should handle individual validation failures', async () => {
      const mockValidationSuccess = { canProceed: true };
      const mockValidationFailure = { 
        canProceed: false, 
        userMessage: 'No pins available' 
      };

      PinValidationService.validateReportGeneration
        .mockResolvedValueOnce(mockValidationSuccess)
        .mockResolvedValueOnce(mockValidationFailure)
        .mockResolvedValueOnce(mockValidationSuccess);

      InformesService.generarInformeCompleto
        .mockResolvedValueOnce('informe-1')
        .mockResolvedValueOnce('informe-3');

      const result = await BatchProcessingService._processIndividualStrategy(
        mockPsychologistId,
        mockPatientIds,
        { continueOnError: true }
      );

      expect(result.totalProcessed).toBe(3);
      expect(result.totalSuccessful).toBe(2);
      expect(result.totalFailed).toBe(1);
      expect(result.pinsConsumed).toBe(2);
      expect(result.failed[0].reason).toBe('validation_failed');
    });

    it('should handle report generation errors', async () => {
      const mockValidation = { canProceed: true };
      const error = new Error('Report generation failed');

      PinValidationService.validateReportGeneration.mockResolvedValue(mockValidation);
      InformesService.generarInformeCompleto
        .mockResolvedValueOnce('informe-1')
        .mockRejectedValueOnce(error)
        .mockResolvedValueOnce('informe-3');

      const result = await BatchProcessingService._processIndividualStrategy(
        mockPsychologistId,
        mockPatientIds,
        { continueOnError: true }
      );

      expect(result.totalProcessed).toBe(3);
      expect(result.totalSuccessful).toBe(2);
      expect(result.totalFailed).toBe(1);
      expect(result.failed[0].reason).toBe('processing_error');
      expect(result.failed[0].error).toBe('Report generation failed');
    });

    it('should stop on first error when continueOnError is false', async () => {
      const mockValidation = { canProceed: true };
      const error = new Error('Report generation failed');

      PinValidationService.validateReportGeneration.mockResolvedValue(mockValidation);
      InformesService.generarInformeCompleto
        .mockResolvedValueOnce('informe-1')
        .mockRejectedValueOnce(error);

      const result = await BatchProcessingService._processIndividualStrategy(
        mockPsychologistId,
        mockPatientIds,
        { continueOnError: false }
      );

      expect(result.totalProcessed).toBe(2);
      expect(result.totalSuccessful).toBe(1);
      expect(result.totalFailed).toBe(1);
      expect(InformesService.generarInformeCompleto).toHaveBeenCalledTimes(2);
    });
  });

  describe('_processBulkSuccessStrategy', () => {
    it('should process all reports first, then consume pins for successful ones', async () => {
      const mockPinControlService = {
        consumePin: vi.fn().mockResolvedValue(true)
      };

      // Mock dynamic import
      vi.doMock('../../services/pin/PinControlService.js', () => ({
        default: mockPinControlService
      }));

      InformesService.generarInformeCompleto
        .mockResolvedValueOnce('informe-1')
        .mockRejectedValueOnce(new Error('Failed'))
        .mockResolvedValueOnce('informe-3');

      const result = await BatchProcessingService._processBulkSuccessStrategy(
        mockPsychologistId,
        mockPatientIds,
        {}
      );

      expect(result.strategy).toBe(BatchProcessingService.BATCH_STRATEGIES.BULK_SUCCESS);
      expect(result.totalProcessed).toBe(3);
      expect(result.totalSuccessful).toBe(2);
      expect(result.totalFailed).toBe(1);
      expect(result.pinsConsumed).toBe(2);
    });
  });

  describe('getRecommendedStrategy', () => {
    it('should recommend individual strategy for small batches', () => {
      const strategy = BatchProcessingService.getRecommendedStrategy(3, 100);
      expect(strategy).toBe(BatchProcessingService.BATCH_STRATEGIES.INDIVIDUAL);
    });

    it('should recommend bulk success for limited pins', () => {
      const strategy = BatchProcessingService.getRecommendedStrategy(10, 11);
      expect(strategy).toBe(BatchProcessingService.BATCH_STRATEGIES.BULK_SUCCESS);
    });

    it('should recommend optimistic for high reliability and risk tolerance', () => {
      const strategy = BatchProcessingService.getRecommendedStrategy(
        10, 
        50, 
        { reliability: 0.95, riskTolerance: 'high' }
      );
      expect(strategy).toBe(BatchProcessingService.BATCH_STRATEGIES.OPTIMISTIC);
    });

    it('should recommend bulk upfront for many available pins', () => {
      const strategy = BatchProcessingService.getRecommendedStrategy(10, 25);
      expect(strategy).toBe(BatchProcessingService.BATCH_STRATEGIES.BULK_UPFRONT);
    });

    it('should default to individual strategy', () => {
      const strategy = BatchProcessingService.getRecommendedStrategy(
        10, 
        15, 
        { reliability: 0.8, riskTolerance: 'low' }
      );
      expect(strategy).toBe(BatchProcessingService.BATCH_STRATEGIES.INDIVIDUAL);
    });
  });

  describe('error handling', () => {
    it('should handle validation service errors', async () => {
      const error = new Error('Validation service error');
      PinValidationService.validateBatchReportGeneration.mockRejectedValue(error);

      await expect(
        BatchProcessingService.processBatchReports(mockPsychologistId, mockPatientIds)
      ).rejects.toThrow('Validation service error');
    });

    it('should include timing information in results', async () => {
      const mockValidation = { canProceed: true };
      PinValidationService.validateBatchReportGeneration.mockResolvedValue(mockValidation);
      
      const mockResult = {
        strategy: BatchProcessingService.BATCH_STRATEGIES.INDIVIDUAL,
        successful: [],
        failed: [],
        totalProcessed: 0,
        totalSuccessful: 0,
        totalFailed: 0,
        pinsConsumed: 0,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        duration: 1000
      };

      vi.spyOn(BatchProcessingService, '_processIndividualStrategy')
        .mockResolvedValue(mockResult);

      const result = await BatchProcessingService.processBatchReports(
        mockPsychologistId,
        mockPatientIds
      );

      expect(result.startTime).toBeDefined();
      expect(result.endTime).toBeDefined();
      expect(result.duration).toBeDefined();
    });
  });

  describe('strategy constants', () => {
    it('should have all required strategy constants', () => {
      expect(BatchProcessingService.BATCH_STRATEGIES.INDIVIDUAL).toBe('individual');
      expect(BatchProcessingService.BATCH_STRATEGIES.BULK_UPFRONT).toBe('bulk_upfront');
      expect(BatchProcessingService.BATCH_STRATEGIES.BULK_SUCCESS).toBe('bulk_success');
      expect(BatchProcessingService.BATCH_STRATEGIES.OPTIMISTIC).toBe('optimistic');
    });
  });
});
