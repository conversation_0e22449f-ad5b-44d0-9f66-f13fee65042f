/**
 * ========================================
 * INFORME MODAL PROFESSIONAL - MIGRADO A PDF
 * html2canvas + jsPDF - Fidelidad Visual 100%
 * ========================================
 * 
 * Modal profesional para mostrar y generar PDFs de informes psicológicos
 * with clean, clinical design following psychological assessment standards
 */

import React, { memo, useRef } from 'react';
import PropTypes from 'prop-types';
import { FaTimes, FaDownload, FaPrint, FaFileAlt } from 'react-icons/fa';
import { Button } from '../ui/Button';
import { useInformeData } from '../../hooks/useInformeData';
import { useInterpretacionesReales } from '../../hooks/useInterpretacionesReales';
import { useInformePDF } from '../../hooks/usePDFGenerator';
import InformePrintableContent from './InformePrintableContent';
import './InformeModalProfessional.css';
import '../../styles/informe-print-ultra-robust.css';

/**
 * Modal profesional para informes con generación de PDF ultra-precisa
 */
const InformeModalProfessional = memo(({ 
  isOpen, 
  onClose, 
  patient, 
  results, 
  reportContent 
}) => {
  // Referencias
  const printRef = useRef();

  // Hooks para datos del informe
  const {
    normalizedResultsData,
    intelligenceIndices,
    generalStats,
    getAptitudeConfig,
    getPercentilLevel
  } = useInformeData(results);

  // Hook para obtener interpretaciones reales
  const { loading: interpretacionesLoading, obtenerInterpretacion } = useInterpretacionesReales(normalizedResultsData);

  // Definir patientData temprano para evitar errores de inicialización
  const patientData = reportContent?.contenido?.paciente || reportContent?.pacientes || patient;

  // Hook personalizado para generación de PDF ultra-precisa
  const {
    isGenerating: isGeneratingPDF,
    generatePDF, // Tamaño natural (SIN escalado)
    generatePDFScaled, // Con escalado automático
    error: pdfError,
    canGenerate
  } = useInformePDF(patientData, {
    showToast: true, // Mostrar notificaciones automáticamente
    margin: 0 // Sin márgenes para tamaño natural
  });

  // Función para generar PDF usando el hook personalizado
  const handleGeneratePDF = async () => {
    if (!printRef.current) {
      console.warn('⚠️ [InformeModal] No hay contenido para generar PDF');
      return;
    }

    console.log('🎯 [InformeModal] Iniciando generación de PDF ultra-preciso...');

    // Usar el hook personalizado con resolución equilibrada
    const success = await generatePDF(printRef.current, {
      html2canvasOptions: {
        scale: 1.5, // Escala equilibrada para buena calidad sin exceso
        useCORS: true,
        backgroundColor: '#ffffff',
        logging: process.env.NODE_ENV === 'development'
      },
      onBeforeCapture: async (element) => {
        console.log('📸 [InformeModal] Preparando captura...');

        // Diagnóstico final antes de captura
        const diagnostics = {
          blueHeaders: element.querySelectorAll('.print-header, .bg-blue-600, .bg-gradient-to-r'),
          aptitudeIcons: element.querySelectorAll('.bg-orange-500, .bg-red-500, .bg-green-500, .bg-yellow-500, .bg-gray-600'),
          percentileBars: element.querySelectorAll('.h-6[style*="backgroundColor"]'),
          allElements: element.querySelectorAll('*')
        };

        console.log('🔍 [InformeModal] DIAGNÓSTICO PRE-CAPTURA:');
        console.log(`📊 Headers azules: ${diagnostics.blueHeaders.length}`);
        console.log(`🎨 Iconos de aptitudes: ${diagnostics.aptitudeIcons.length}`);
        console.log(`📈 Barras de percentiles: ${diagnostics.percentileBars.length}`);
        console.log(`🌐 Total elementos: ${diagnostics.allElements.length}`);

        // Verificar algunos elementos críticos
        diagnostics.blueHeaders.forEach((header, index) => {
          const styles = window.getComputedStyle(header);
          console.log(`🔵 Header ${index + 1}: bg=${styles.backgroundColor}, color=${styles.color}`);
        });
      }
    });

    if (success) {
      console.log('✅ [InformeModal] PDF generado exitosamente');
    } else {
      console.error('❌ [InformeModal] Error generando PDF');
    }
  };

  // Función legacy para compatibilidad (ahora usa la nueva implementación)
  const handlePrint = () => {
    console.log('🖨️ [InformeModal] Redirigiendo a generación de PDF...');
    handleGeneratePDF();
  };

  // Función para descargar PDF (alias de handleGeneratePDF)
  const handleDownload = () => {
    console.log('💾 [InformeModal] Descargando PDF...');
    handleGeneratePDF();
  };

  // El hook usePDFGenerator maneja automáticamente la limpieza de recursos

  // No renderizar si no está abierto
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-6xl h-[95vh] flex flex-col">
        {/* Header del modal */}
        <div className="flex-shrink-0 border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FaFileAlt className="text-blue-600 text-2xl mr-3" />
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Informe Psicológico</h2>
                <p className="text-gray-600 text-lg font-medium">Batería de Aptitudes Diferenciales y Generales - BAT-7</p>
                <p className="text-gray-500 text-sm mt-1">Evaluación Psicológica Integral</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={handleDownload}
                variant="outline"
                size="sm"
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
                disabled={isGeneratingPDF}
                title="Descargar PDF del informe psicológico"
              >
                <FaDownload className="mr-1" />
                {isGeneratingPDF ? 'Generando...' : 'Descargar PDF'}
              </Button>
              <Button
                onClick={onClose}
                variant="outline"
                size="sm"
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
                disabled={isGeneratingPDF}
              >
                <FaTimes />
              </Button>
            </div>
          </div>
        </div>

        {/* Contenido scrolleable con vista previa */}
        <div className="overflow-y-auto max-h-[calc(95vh-120px)]">
          <InformePrintableContent
            ref={printRef}
            patientData={patientData}
            normalizedResultsData={normalizedResultsData}
            intelligenceIndices={intelligenceIndices}
            generalStats={generalStats}
            getAptitudeConfig={getAptitudeConfig}
            getPercentilLevel={getPercentilLevel}
            reportContent={reportContent}
            interpretacionesLoading={interpretacionesLoading}
            obtenerInterpretacion={obtenerInterpretacion}
          />
        </div>
      </div>
    </div>
  );
});

InformeModalProfessional.displayName = 'InformeModalProfessional';

// PropTypes para validación de tipos
InformeModalProfessional.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  patient: PropTypes.object,
  results: PropTypes.array,
  reportContent: PropTypes.object
};

export default InformeModalProfessional;
